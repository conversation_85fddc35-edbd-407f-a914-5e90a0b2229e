{"name": "@chainsight/web3-dapp-core", "version": "1.0.0", "description": "Web3 DApp core utilities for wallet connection and blockchain interaction", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest"}, "dependencies": {"ethers": "^6.7.0", "viem": "^1.10.0", "wagmi": "^1.4.0", "@web3modal/react": "^2.7.0", "@web3modal/ethereum": "^2.7.0", "web3": "^4.1.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}}