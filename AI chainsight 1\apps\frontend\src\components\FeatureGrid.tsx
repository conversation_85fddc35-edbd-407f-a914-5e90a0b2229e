'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  Zap, 
  Brain, 
  Camera, 
  TrendingUp, 
  FileText, 
  MessageSquare, 
  Palette, 
  Settings,
  ArrowRight 
} from 'lucide-react'

const features = [
  {
    icon: Zap,
    title: 'Crypto Engine',
    description: 'Deploy ERC20 tokens, manage liquidity pools, and interact with DeFi protocols seamlessly.',
    href: '/crypto',
    color: 'from-blue-500 to-cyan-500',
    features: ['Token Creation', 'LP Provisioning', 'DeFi Integration']
  },
  {
    icon: Brain,
    title: 'AI Core',
    description: 'Advanced machine learning models for financial predictions and market analysis.',
    href: '/ai',
    color: 'from-purple-500 to-pink-500',
    features: ['LSTM Models', 'Price Prediction', 'Market Sentiment']
  },
  {
    icon: Camera,
    title: 'Face Scanner',
    description: 'Real-time face recognition and biometric authentication with live webcam integration.',
    href: '/face-scanner',
    color: 'from-green-500 to-emerald-500',
    features: ['Live Detection', 'Biometric Auth', 'Identity Verification']
  },
  {
    icon: TrendingUp,
    title: 'Finance Analyzer',
    description: 'Comprehensive stock and cryptocurrency market analysis with real-time data feeds.',
    href: '/finance',
    color: 'from-orange-500 to-red-500',
    features: ['Real-time Data', 'Technical Analysis', 'Portfolio Tracking']
  },
  {
    icon: FileText,
    title: 'Legal HR Core',
    description: 'Intelligent document processing for resumes, contracts, and legal compliance.',
    href: '/legal-hr',
    color: 'from-indigo-500 to-blue-500',
    features: ['Resume Parsing', 'Contract Analysis', 'Job Matching']
  },
  {
    icon: MessageSquare,
    title: 'Chat Assistant',
    description: 'AI-powered conversational interface for customer support and platform interaction.',
    href: '/chat',
    color: 'from-teal-500 to-green-500',
    features: ['Natural Language', 'Context Aware', 'Multi-domain']
  },
  {
    icon: Palette,
    title: 'Animation Designer',
    description: 'Create and export professional animations, SVGs, and interactive visual content.',
    href: '/animation',
    color: 'from-pink-500 to-rose-500',
    features: ['SVG Generation', 'Lottie Export', 'Interactive Design']
  },
  {
    icon: Settings,
    title: 'Task Orchestrator',
    description: 'Centralized plugin management and workflow automation across all platform modules.',
    href: '/orchestrator',
    color: 'from-gray-500 to-slate-500',
    features: ['Plugin Management', 'Workflow Automation', 'API Orchestration']
  },
]

export function FeatureGrid() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {features.map((feature, index) => (
        <motion.div
          key={feature.title}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          whileHover={{ y: -5 }}
          className="group"
        >
          <Link href={feature.href}>
            <div className="luxury-card h-full cursor-pointer group-hover:border-luxury-gold/40 transition-all duration-300">
              {/* Icon with gradient background */}
              <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.color} p-3 mb-4 group-hover:scale-110 transition-transform duration-300`}>
                <feature.icon className="w-full h-full text-white" />
              </div>

              {/* Title */}
              <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-luxury-gold transition-colors duration-300">
                {feature.title}
              </h3>

              {/* Description */}
              <p className="text-gray-400 text-sm mb-4 leading-relaxed">
                {feature.description}
              </p>

              {/* Feature List */}
              <div className="space-y-2 mb-4">
                {feature.features.map((item, idx) => (
                  <div key={idx} className="flex items-center text-xs text-gray-500">
                    <div className="w-1.5 h-1.5 bg-luxury-gold rounded-full mr-2" />
                    {item}
                  </div>
                ))}
              </div>

              {/* Action Button */}
              <div className="flex items-center justify-between mt-auto pt-4 border-t border-luxury-slate/50">
                <span className="text-sm text-luxury-gold font-medium">
                  Explore Module
                </span>
                <ArrowRight className="w-4 h-4 text-luxury-gold group-hover:translate-x-1 transition-transform duration-300" />
              </div>
            </div>
          </Link>
        </motion.div>
      ))}
    </div>
  )
}
