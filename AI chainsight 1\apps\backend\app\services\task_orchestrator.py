import asyncio
import logging
import uuid
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum
import json

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskPriority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

class Task:
    def __init__(self, task_id: str, name: str, task_type: str, 
                 payload: Dict[str, Any], priority: TaskPriority = TaskPriority.MEDIUM):
        self.id = task_id
        self.name = name
        self.type = task_type
        self.payload = payload
        self.priority = priority
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self.result: Optional[Dict[str, Any]] = None
        self.error: Optional[str] = None
        self.progress = 0
        self.dependencies: List[str] = []
        self.retry_count = 0
        self.max_retries = 3

class TaskOrchestrator:
    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        self.task_handlers: Dict[str, Callable] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.max_concurrent_tasks = 10
        self.is_running = False
        
        # Register default task handlers
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default task handlers"""
        self.task_handlers.update({
            "crypto_deploy_token": self._handle_crypto_deploy_token,
            "crypto_add_liquidity": self._handle_crypto_add_liquidity,
            "ai_generate_content": self._handle_ai_generate_content,
            "ai_analyze_sentiment": self._handle_ai_analyze_sentiment,
            "finance_predict_price": self._handle_finance_predict_price,
            "finance_analyze_market": self._handle_finance_analyze_market,
            "face_scan_image": self._handle_face_scan_image,
            "face_compare_faces": self._handle_face_compare_faces,
            "legal_analyze_resume": self._handle_legal_analyze_resume,
            "legal_analyze_contract": self._handle_legal_analyze_contract,
            "system_cleanup": self._handle_system_cleanup,
            "system_backup": self._handle_system_backup
        })
    
    async def start(self):
        """Start the task orchestrator"""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info("Task orchestrator started")
        
        # Start the main processing loop
        asyncio.create_task(self._process_tasks())
    
    async def stop(self):
        """Stop the task orchestrator"""
        self.is_running = False
        
        # Cancel all running tasks
        for task_id, running_task in self.running_tasks.items():
            running_task.cancel()
            logger.info(f"Cancelled task: {task_id}")
        
        self.running_tasks.clear()
        logger.info("Task orchestrator stopped")
    
    async def submit_task(self, name: str, task_type: str, payload: Dict[str, Any],
                         priority: TaskPriority = TaskPriority.MEDIUM,
                         dependencies: List[str] = None) -> str:
        """Submit a new task"""
        task_id = str(uuid.uuid4())
        
        task = Task(task_id, name, task_type, payload, priority)
        if dependencies:
            task.dependencies = dependencies
        
        self.tasks[task_id] = task
        
        logger.info(f"Task submitted: {task_id} ({name})")
        return task_id
    
    async def get_task(self, task_id: str) -> Optional[Task]:
        """Get task by ID"""
        return self.tasks.get(task_id)
    
    async def get_tasks(self, status: Optional[TaskStatus] = None,
                       task_type: Optional[str] = None,
                       limit: int = 100) -> List[Task]:
        """Get tasks with optional filtering"""
        tasks = list(self.tasks.values())
        
        if status:
            tasks = [t for t in tasks if t.status == status]
        
        if task_type:
            tasks = [t for t in tasks if t.type == task_type]
        
        # Sort by priority and creation time
        tasks.sort(key=lambda t: (t.priority.value, t.created_at), reverse=True)
        
        return tasks[:limit]
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a task"""
        task = self.tasks.get(task_id)
        if not task:
            return False
        
        if task.status == TaskStatus.RUNNING:
            # Cancel the running asyncio task
            running_task = self.running_tasks.get(task_id)
            if running_task:
                running_task.cancel()
                del self.running_tasks[task_id]
        
        task.status = TaskStatus.CANCELLED
        task.completed_at = datetime.now()
        
        logger.info(f"Task cancelled: {task_id}")
        return True
    
    async def retry_task(self, task_id: str) -> bool:
        """Retry a failed task"""
        task = self.tasks.get(task_id)
        if not task or task.status != TaskStatus.FAILED:
            return False
        
        if task.retry_count >= task.max_retries:
            logger.warning(f"Task {task_id} has exceeded max retries")
            return False
        
        task.status = TaskStatus.PENDING
        task.error = None
        task.retry_count += 1
        
        logger.info(f"Task retry scheduled: {task_id} (attempt {task.retry_count})")
        return True
    
    async def _process_tasks(self):
        """Main task processing loop"""
        while self.is_running:
            try:
                # Get pending tasks that can be executed
                ready_tasks = await self._get_ready_tasks()
                
                # Execute tasks up to the concurrency limit
                available_slots = self.max_concurrent_tasks - len(self.running_tasks)
                tasks_to_execute = ready_tasks[:available_slots]
                
                for task in tasks_to_execute:
                    asyncio.create_task(self._execute_task(task))
                
                # Clean up completed running tasks
                await self._cleanup_completed_tasks()
                
                # Wait before next iteration
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in task processing loop: {e}")
                await asyncio.sleep(5)
    
    async def _get_ready_tasks(self) -> List[Task]:
        """Get tasks that are ready to execute"""
        ready_tasks = []
        
        for task in self.tasks.values():
            if task.status != TaskStatus.PENDING:
                continue
            
            # Check if dependencies are satisfied
            if await self._dependencies_satisfied(task):
                ready_tasks.append(task)
        
        # Sort by priority
        ready_tasks.sort(key=lambda t: t.priority.value, reverse=True)
        return ready_tasks
    
    async def _dependencies_satisfied(self, task: Task) -> bool:
        """Check if task dependencies are satisfied"""
        for dep_id in task.dependencies:
            dep_task = self.tasks.get(dep_id)
            if not dep_task or dep_task.status != TaskStatus.COMPLETED:
                return False
        return True
    
    async def _execute_task(self, task: Task):
        """Execute a single task"""
        task_id = task.id
        
        try:
            # Mark task as running
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            
            logger.info(f"Executing task: {task_id} ({task.name})")
            
            # Get task handler
            handler = self.task_handlers.get(task.type)
            if not handler:
                raise Exception(f"No handler found for task type: {task.type}")
            
            # Create asyncio task for execution
            execution_task = asyncio.create_task(handler(task))
            self.running_tasks[task_id] = execution_task
            
            # Execute the task
            result = await execution_task
            
            # Mark task as completed
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            task.progress = 100
            
            logger.info(f"Task completed: {task_id}")
            
        except asyncio.CancelledError:
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now()
            logger.info(f"Task cancelled: {task_id}")
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error = str(e)
            
            logger.error(f"Task failed: {task_id} - {e}")
            
            # Schedule retry if within limits
            if task.retry_count < task.max_retries:
                await asyncio.sleep(5)  # Wait before retry
                await self.retry_task(task_id)
        
        finally:
            # Remove from running tasks
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    async def _cleanup_completed_tasks(self):
        """Clean up completed running tasks"""
        completed_task_ids = []
        
        for task_id, running_task in self.running_tasks.items():
            if running_task.done():
                completed_task_ids.append(task_id)
        
        for task_id in completed_task_ids:
            del self.running_tasks[task_id]
    
    # Task Handlers
    async def _handle_crypto_deploy_token(self, task: Task) -> Dict[str, Any]:
        """Handle crypto token deployment"""
        await asyncio.sleep(2)  # Simulate deployment time
        return {
            "contract_address": f"0x{uuid.uuid4().hex[:40]}",
            "transaction_hash": f"0x{uuid.uuid4().hex}",
            "gas_used": 2100000
        }
    
    async def _handle_crypto_add_liquidity(self, task: Task) -> Dict[str, Any]:
        """Handle adding liquidity"""
        await asyncio.sleep(1.5)
        return {
            "liquidity_tokens": 1000000,
            "transaction_hash": f"0x{uuid.uuid4().hex}",
            "pool_address": f"0x{uuid.uuid4().hex[:40]}"
        }
    
    async def _handle_ai_generate_content(self, task: Task) -> Dict[str, Any]:
        """Handle AI content generation"""
        await asyncio.sleep(1)
        return {
            "content": "Generated AI content based on the provided prompt.",
            "tokens_used": 150,
            "model": "gpt-3.5-turbo"
        }
    
    async def _handle_ai_analyze_sentiment(self, task: Task) -> Dict[str, Any]:
        """Handle sentiment analysis"""
        await asyncio.sleep(0.5)
        return {
            "sentiment": "positive",
            "confidence": 0.85,
            "scores": {"positive": 0.85, "negative": 0.10, "neutral": 0.05}
        }
    
    async def _handle_finance_predict_price(self, task: Task) -> Dict[str, Any]:
        """Handle price prediction"""
        await asyncio.sleep(3)
        return {
            "predicted_price": 45000.50,
            "confidence": 0.78,
            "model": "LSTM-Transformer"
        }
    
    async def _handle_finance_analyze_market(self, task: Task) -> Dict[str, Any]:
        """Handle market analysis"""
        await asyncio.sleep(2)
        return {
            "market_sentiment": "bullish",
            "trend_strength": 0.72,
            "key_indicators": ["RSI: 65", "MACD: Bullish"]
        }
    
    async def _handle_face_scan_image(self, task: Task) -> Dict[str, Any]:
        """Handle face scanning"""
        await asyncio.sleep(1.5)
        return {
            "faces_detected": 2,
            "confidence_scores": [0.95, 0.87],
            "processing_time": 1.5
        }
    
    async def _handle_face_compare_faces(self, task: Task) -> Dict[str, Any]:
        """Handle face comparison"""
        await asyncio.sleep(1)
        return {
            "similarity_score": 0.89,
            "is_match": True,
            "confidence": 0.92
        }
    
    async def _handle_legal_analyze_resume(self, task: Task) -> Dict[str, Any]:
        """Handle resume analysis"""
        await asyncio.sleep(2)
        return {
            "candidate_score": 85,
            "skills_matched": 12,
            "experience_level": "Senior"
        }
    
    async def _handle_legal_analyze_contract(self, task: Task) -> Dict[str, Any]:
        """Handle contract analysis"""
        await asyncio.sleep(2.5)
        return {
            "contract_type": "Employment Agreement",
            "risk_level": "Medium",
            "key_terms_count": 15
        }
    
    async def _handle_system_cleanup(self, task: Task) -> Dict[str, Any]:
        """Handle system cleanup"""
        await asyncio.sleep(5)
        return {
            "files_cleaned": 150,
            "space_freed": "2.5GB",
            "duration": "5 seconds"
        }
    
    async def _handle_system_backup(self, task: Task) -> Dict[str, Any]:
        """Handle system backup"""
        await asyncio.sleep(10)
        return {
            "backup_size": "10GB",
            "backup_location": "/backups/chainsight_backup.tar.gz",
            "duration": "10 seconds"
        }

# Global task orchestrator instance
task_orchestrator = TaskOrchestrator()
