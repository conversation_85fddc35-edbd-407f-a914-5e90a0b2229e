export interface ParsedResume {
  id: string;
  personal_info: PersonalInfo;
  contact_info: ContactInfo;
  summary?: string;
  skills: Skill[];
  experience: Experience[];
  education: Education[];
  certifications: Certification[];
  languages: Language[];
  projects: Project[];
  achievements: Achievement[];
  references: Reference[];
  metadata: ResumeMetadata;
}

export interface PersonalInfo {
  first_name?: string;
  last_name?: string;
  full_name?: string;
  title?: string;
  location?: Location;
  date_of_birth?: Date;
  nationality?: string;
  visa_status?: string;
}

export interface ContactInfo {
  email?: string;
  phone?: string;
  linkedin?: string;
  github?: string;
  website?: string;
  portfolio?: string;
  address?: Address;
}

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  full_address?: string;
}

export interface Location {
  city?: string;
  state?: string;
  country?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface Skill {
  name: string;
  category: string;
  level?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  years_experience?: number;
  confidence_score: number;
  context?: string;
  verified?: boolean;
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  start_date?: Date;
  end_date?: Date;
  is_current: boolean;
  duration_months?: number;
  location?: Location;
  description?: string;
  responsibilities: string[];
  achievements: string[];
  skills_used: string[];
  industry?: string;
  company_size?: string;
  employment_type?: 'full-time' | 'part-time' | 'contract' | 'freelance' | 'internship';
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field_of_study?: string;
  start_date?: Date;
  end_date?: Date;
  gpa?: number;
  honors?: string[];
  relevant_coursework?: string[];
  location?: Location;
  is_completed: boolean;
}

export interface Certification {
  id: string;
  name: string;
  issuing_organization: string;
  issue_date?: Date;
  expiry_date?: Date;
  credential_id?: string;
  verification_url?: string;
  is_active: boolean;
}

export interface Language {
  language: string;
  proficiency: 'basic' | 'conversational' | 'fluent' | 'native';
  certifications?: string[];
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  start_date?: Date;
  end_date?: Date;
  technologies: string[];
  role?: string;
  url?: string;
  repository?: string;
  achievements?: string[];
}

export interface Achievement {
  id: string;
  title: string;
  description?: string;
  date?: Date;
  organization?: string;
  category?: string;
}

export interface Reference {
  id: string;
  name: string;
  title?: string;
  company?: string;
  email?: string;
  phone?: string;
  relationship?: string;
}

export interface ResumeMetadata {
  file_name?: string;
  file_type?: string;
  file_size?: number;
  pages?: number;
  parsing_confidence: number;
  parsing_time: number;
  extracted_text_length: number;
  language_detected?: string;
  created_at: Date;
  updated_at: Date;
}

export interface LegalDocument {
  id: string;
  title?: string;
  type: string;
  content: string;
  clauses: LegalClause[];
  parties: Party[];
  dates: ImportantDate[];
  financial_terms: FinancialTerm[];
  obligations: Obligation[];
  risks: Risk[];
  metadata: DocumentMetadata;
}

export interface LegalClause {
  id: string;
  type: string;
  title?: string;
  content: string;
  importance: 'low' | 'medium' | 'high' | 'critical';
  confidence_score: number;
  position: {
    start: number;
    end: number;
    page?: number;
  };
  related_clauses?: string[];
  risks?: string[];
  recommendations?: string[];
}

export interface Party {
  id: string;
  name: string;
  type: 'individual' | 'company' | 'organization';
  role: string;
  contact_info?: ContactInfo;
  legal_entity_info?: LegalEntityInfo;
}

export interface LegalEntityInfo {
  registration_number?: string;
  jurisdiction?: string;
  legal_form?: string;
  tax_id?: string;
  address?: Address;
}

export interface ImportantDate {
  id: string;
  type: string;
  date: Date;
  description?: string;
  importance: 'low' | 'medium' | 'high' | 'critical';
  reminder_needed?: boolean;
}

export interface FinancialTerm {
  id: string;
  type: 'payment' | 'penalty' | 'fee' | 'compensation' | 'deposit';
  amount?: number;
  currency?: string;
  description: string;
  due_date?: Date;
  conditions?: string[];
}

export interface Obligation {
  id: string;
  party_id: string;
  description: string;
  type: 'deliverable' | 'payment' | 'compliance' | 'performance';
  deadline?: Date;
  conditions?: string[];
  penalties?: string[];
}

export interface Risk {
  id: string;
  type: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  probability: 'low' | 'medium' | 'high';
  mitigation_strategies?: string[];
  related_clauses?: string[];
}

export interface DocumentMetadata {
  file_name?: string;
  file_type?: string;
  file_size?: number;
  pages?: number;
  language?: string;
  created_at: Date;
  updated_at: Date;
  processing_time: number;
  confidence_score: number;
  version?: string;
  author?: string;
  subject?: string;
  keywords?: string[];
}

export interface ContractAnalysis {
  document_id: string;
  summary: string;
  key_terms: KeyTerm[];
  risk_assessment: RiskAssessment;
  compliance_check: ComplianceResult;
  recommendations: Recommendation[];
  comparison?: ContractComparison;
  analysis_metadata: AnalysisMetadata;
}

export interface KeyTerm {
  term: string;
  definition?: string;
  importance: 'low' | 'medium' | 'high' | 'critical';
  occurrences: number;
  context: string[];
}

export interface RiskAssessment {
  overall_risk_score: number;
  risk_categories: RiskCategory[];
  critical_issues: string[];
  recommendations: string[];
}

export interface RiskCategory {
  category: string;
  score: number;
  issues: string[];
  mitigation_strategies: string[];
}

export interface ComplianceResult {
  overall_compliance_score: number;
  standards_checked: string[];
  violations: ComplianceViolation[];
  recommendations: string[];
  certification_status?: string;
}

export interface ComplianceViolation {
  standard: string;
  violation_type: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  location?: {
    page?: number;
    section?: string;
    clause_id?: string;
  };
  remediation_steps: string[];
}

export interface Recommendation {
  id: string;
  type: 'legal' | 'business' | 'compliance' | 'risk';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  description: string;
  rationale: string;
  implementation_steps?: string[];
  estimated_impact?: string;
}

export interface ContractComparison {
  compared_with: string;
  differences: ContractDifference[];
  similarities: string[];
  recommendations: string[];
}

export interface ContractDifference {
  section: string;
  type: 'addition' | 'deletion' | 'modification';
  description: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
}

export interface AnalysisMetadata {
  analysis_date: Date;
  analysis_version: string;
  models_used: string[];
  processing_time: number;
  confidence_score: number;
  reviewer?: string;
  review_status?: 'pending' | 'reviewed' | 'approved';
}

export interface ProcessingResult {
  success: boolean;
  document_id?: string;
  processing_time: number;
  confidence_score: number;
  errors?: ProcessingError[];
  warnings?: ProcessingWarning[];
  extracted_data?: any;
}

export interface ProcessingError {
  type: string;
  message: string;
  location?: {
    page?: number;
    section?: string;
    line?: number;
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface ProcessingWarning {
  type: string;
  message: string;
  location?: {
    page?: number;
    section?: string;
    line?: number;
  };
  recommendation?: string;
}

export interface SkillMatch {
  skill: string;
  match_score: number;
  required_level?: string;
  candidate_level?: string;
  gap_analysis?: string;
}

export interface CandidateProfile {
  id: string;
  resume: ParsedResume;
  skill_summary: SkillSummary;
  experience_summary: ExperienceSummary;
  education_summary: EducationSummary;
  overall_score: number;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
}

export interface SkillSummary {
  total_skills: number;
  skill_categories: Record<string, number>;
  top_skills: Skill[];
  skill_gaps?: string[];
  skill_trends?: SkillTrend[];
}

export interface SkillTrend {
  skill: string;
  trend: 'growing' | 'stable' | 'declining';
  market_demand: 'low' | 'medium' | 'high';
  salary_impact?: number;
}

export interface ExperienceSummary {
  total_years: number;
  industries: string[];
  company_types: string[];
  roles: string[];
  career_progression: CareerProgression;
  employment_gaps?: EmploymentGap[];
}

export interface CareerProgression {
  progression_score: number;
  trend: 'upward' | 'lateral' | 'downward' | 'mixed';
  promotions: number;
  role_changes: number;
  industry_changes: number;
}

export interface EmploymentGap {
  start_date: Date;
  end_date: Date;
  duration_months: number;
  explanation?: string;
  impact_score: number;
}

export interface EducationSummary {
  highest_degree: string;
  relevant_education: boolean;
  education_score: number;
  certifications_count: number;
  continuous_learning_score: number;
}

export interface JobRequirement {
  id: string;
  title: string;
  description: string;
  required_skills: RequiredSkill[];
  preferred_skills: RequiredSkill[];
  experience_requirements: ExperienceRequirement[];
  education_requirements: EducationRequirement[];
  location?: Location;
  salary_range?: SalaryRange;
}

export interface RequiredSkill {
  skill: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  importance: 'nice-to-have' | 'preferred' | 'required' | 'critical';
  years_experience?: number;
}

export interface ExperienceRequirement {
  minimum_years: number;
  industry?: string;
  role_type?: string;
  specific_experience?: string;
}

export interface EducationRequirement {
  level: 'high-school' | 'associate' | 'bachelor' | 'master' | 'phd';
  field?: string;
  required: boolean;
  alternatives?: string[];
}

export interface SalaryRange {
  min: number;
  max: number;
  currency: string;
  frequency: 'hourly' | 'monthly' | 'yearly';
}

export interface CandidateMatch {
  candidate_id: string;
  job_id: string;
  overall_match_score: number;
  skill_match: SkillMatchResult;
  experience_match: ExperienceMatchResult;
  education_match: EducationMatchResult;
  location_match?: LocationMatchResult;
  salary_match?: SalaryMatchResult;
  recommendations: string[];
  interview_questions?: string[];
}

export interface SkillMatchResult {
  score: number;
  matched_skills: SkillMatch[];
  missing_skills: string[];
  additional_skills: string[];
}

export interface ExperienceMatchResult {
  score: number;
  years_match: boolean;
  industry_match: boolean;
  role_match: boolean;
  gaps: string[];
}

export interface EducationMatchResult {
  score: number;
  degree_match: boolean;
  field_match: boolean;
  alternatives_available: boolean;
}

export interface LocationMatchResult {
  score: number;
  distance_km?: number;
  remote_possible: boolean;
  relocation_required: boolean;
}

export interface SalaryMatchResult {
  score: number;
  candidate_expectation?: SalaryRange;
  market_rate?: SalaryRange;
  negotiation_room: number;
}
