export const MARKET_INDICATORS = {
  TECHNICAL: {
    RSI: 'rsi',
    MACD: 'macd',
    BOLLINGER_BANDS: 'bollinger_bands',
    STOCHASTIC: 'stochastic',
    ATR: 'atr',
    ADX: 'adx',
    WILLIAMS_R: 'williams_r',
    CCI: 'cci'
  },
  FUNDAMENTAL: {
    PE_RATIO: 'pe_ratio',
    PB_RATIO: 'pb_ratio',
    DEBT_TO_EQUITY: 'debt_to_equity',
    ROE: 'roe',
    REVENUE_GROWTH: 'revenue_growth',
    EARNINGS_GROWTH: 'earnings_growth'
  },
  SENTIMENT: {
    FEAR_GREED: 'fear_greed_index',
    NEWS_SENTIMENT: 'news_sentiment',
    SOCIAL_SENTIMENT: 'social_sentiment',
    OPTIONS_FLOW: 'options_flow'
  }
} as const;

export const RISK_METRICS = {
  VAR_95: 'var_95',
  VAR_99: 'var_99',
  EXPECTED_SHORTFALL: 'expected_shortfall',
  SHARPE_RATIO: 'sharpe_ratio',
  SORTINO_RATIO: 'sortino_ratio',
  MAX_DRAWDOWN: 'max_drawdown',
  VOLATILITY: 'volatility',
  BETA: 'beta',
  ALPHA: 'alpha'
} as const;

export const TECHNICAL_PATTERNS = {
  BULLISH: {
    HAMMER: 'hammer',
    DOJI: 'doji',
    ENGULFING: 'bullish_engulfing',
    MORNING_STAR: 'morning_star',
    PIERCING_LINE: 'piercing_line'
  },
  BEARISH: {
    SHOOTING_STAR: 'shooting_star',
    DARK_CLOUD: 'dark_cloud_cover',
    EVENING_STAR: 'evening_star',
    BEARISH_ENGULFING: 'bearish_engulfing',
    HANGING_MAN: 'hanging_man'
  },
  CONTINUATION: {
    FLAG: 'flag',
    PENNANT: 'pennant',
    TRIANGLE: 'triangle',
    WEDGE: 'wedge'
  }
} as const;

export const ALERT_TYPES = {
  PRICE_ABOVE: 'price_above',
  PRICE_BELOW: 'price_below',
  VOLUME_SPIKE: 'volume_spike',
  RSI_OVERBOUGHT: 'rsi_overbought',
  RSI_OVERSOLD: 'rsi_oversold',
  MACD_CROSSOVER: 'macd_crossover',
  BOLLINGER_BREAKOUT: 'bollinger_breakout',
  SUPPORT_BREAK: 'support_break',
  RESISTANCE_BREAK: 'resistance_break',
  NEWS_SENTIMENT: 'news_sentiment_change'
} as const;

export const TIMEFRAMES = {
  MINUTE_1: '1m',
  MINUTE_5: '5m',
  MINUTE_15: '15m',
  MINUTE_30: '30m',
  HOUR_1: '1h',
  HOUR_4: '4h',
  HOUR_12: '12h',
  DAY_1: '1d',
  WEEK_1: '1w',
  MONTH_1: '1M'
} as const;

export const ASSET_TYPES = {
  STOCK: 'stock',
  CRYPTO: 'crypto',
  FOREX: 'forex',
  COMMODITY: 'commodity',
  BOND: 'bond',
  ETF: 'etf',
  INDEX: 'index'
} as const;

export const MARKET_PHASES = {
  ACCUMULATION: 'accumulation',
  MARKUP: 'markup',
  DISTRIBUTION: 'distribution',
  MARKDOWN: 'markdown'
} as const;

export const OPTIMIZATION_TYPES = {
  MAX_SHARPE: 'max_sharpe',
  MIN_RISK: 'min_risk',
  MAX_RETURN: 'max_return',
  RISK_PARITY: 'risk_parity',
  EQUAL_WEIGHT: 'equal_weight'
} as const;

export const DEFAULT_PARAMETERS = {
  RSI_PERIOD: 14,
  MACD_FAST: 12,
  MACD_SLOW: 26,
  MACD_SIGNAL: 9,
  BOLLINGER_PERIOD: 20,
  BOLLINGER_STD: 2,
  ATR_PERIOD: 14,
  ADX_PERIOD: 14,
  STOCHASTIC_K: 14,
  STOCHASTIC_D: 3,
  WILLIAMS_R_PERIOD: 14,
  CCI_PERIOD: 20,
  
  // Risk parameters
  CONFIDENCE_LEVEL_95: 0.95,
  CONFIDENCE_LEVEL_99: 0.99,
  RISK_FREE_RATE: 0.02, // 2% annual
  
  // Portfolio optimization
  MAX_WEIGHT: 0.4, // 40% max allocation
  MIN_WEIGHT: 0.01, // 1% min allocation
  REBALANCE_THRESHOLD: 0.05, // 5% drift threshold
  
  // Backtesting
  INITIAL_CAPITAL: 100000,
  COMMISSION_RATE: 0.001, // 0.1%
  SLIPPAGE: 0.0005, // 0.05%
  
  // Alerts
  PRICE_CHANGE_THRESHOLD: 0.05, // 5%
  VOLUME_SPIKE_THRESHOLD: 2.0, // 2x average
  RSI_OVERBOUGHT: 70,
  RSI_OVERSOLD: 30
} as const;

export const DATA_SOURCES = {
  BINANCE: 'binance',
  COINBASE: 'coinbase',
  ALPHA_VANTAGE: 'alpha_vantage',
  YAHOO_FINANCE: 'yahoo_finance',
  QUANDL: 'quandl',
  IEX_CLOUD: 'iex_cloud',
  POLYGON: 'polygon',
  TWELVE_DATA: 'twelve_data'
} as const;

export const SENTIMENT_SOURCES = {
  TWITTER: 'twitter',
  REDDIT: 'reddit',
  NEWS_API: 'news_api',
  FEAR_GREED_INDEX: 'fear_greed_index',
  CRYPTO_FEAR_GREED: 'crypto_fear_greed',
  ANALYST_RATINGS: 'analyst_ratings'
} as const;
