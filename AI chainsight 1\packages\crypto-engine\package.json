{"name": "@chainsight/crypto-engine", "version": "1.0.0", "description": "Hardhat-based ERC20 token deployment and DeFi utilities", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "hardhat compile && tsc", "dev": "tsc --watch", "clean": "rm -rf dist artifacts cache", "test": "hardhat test", "deploy": "hardhat run scripts/deploy.ts", "node": "hardhat node"}, "dependencies": {"@openzeppelin/contracts": "^4.9.0", "ethers": "^6.7.0", "hardhat": "^2.17.0", "@nomiclabs/hardhat-ethers": "^2.2.0", "@typechain/hardhat": "^6.1.0", "typechain": "^8.3.0"}, "devDependencies": {"@types/node": "^20.0.0", "@nomicfoundation/hardhat-toolbox": "^3.0.0", "typescript": "^5.0.0", "dotenv": "^16.3.0"}}