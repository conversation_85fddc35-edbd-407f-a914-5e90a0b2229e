export const MODEL_TYPES = {
  LSTM: 'lstm',
  TRANSFORMER: 'transformer',
  ENSEMBLE: 'ensemble'
} as const;

export const PREDICTION_TIMEFRAMES = {
  MINUTE_1: '1m',
  MINUTE_5: '5m',
  MINUTE_15: '15m',
  HOUR_1: '1h',
  HOUR_4: '4h',
  DAY_1: '1d',
  WEEK_1: '1w',
  MONTH_1: '1M'
} as const;

export const FEATURE_TYPES = {
  PRICE: 'price',
  VOLUME: 'volume',
  TECHNICAL: 'technical',
  SENTIMENT: 'sentiment',
  MACRO: 'macro'
} as const;

export const TECHNICAL_INDICATORS = {
  SMA: 'sma',
  EMA: 'ema',
  RSI: 'rsi',
  MACD: 'macd',
  BOLLINGER_BANDS: 'bollinger_bands',
  STOCHASTIC: 'stochastic',
  ATR: 'atr',
  VOLUME_SMA: 'volume_sma'
} as const;

export const DEFAULT_MODEL_CONFIG = {
  lstm: {
    sequence_length: 60,
    prediction_horizon: 1,
    features: ['close', 'volume', 'sma_20', 'rsi'],
    hyperparameters: {
      learning_rate: 0.001,
      batch_size: 32,
      epochs: 100,
      dropout_rate: 0.2,
      hidden_units: 50,
      num_layers: 2
    }
  },
  transformer: {
    sequence_length: 100,
    prediction_horizon: 1,
    features: ['close', 'volume', 'sma_20', 'ema_12', 'rsi', 'macd'],
    hyperparameters: {
      learning_rate: 0.0001,
      batch_size: 16,
      epochs: 150,
      dropout_rate: 0.1,
      hidden_units: 128,
      num_layers: 4,
      attention_heads: 8
    }
  }
} as const;

export const PERFORMANCE_THRESHOLDS = {
  EXCELLENT: { mape: 5, r2_score: 0.9 },
  GOOD: { mape: 10, r2_score: 0.8 },
  FAIR: { mape: 20, r2_score: 0.6 },
  POOR: { mape: 30, r2_score: 0.4 }
} as const;
