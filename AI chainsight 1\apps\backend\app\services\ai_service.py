import openai
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import uuid
import json

from ..core.config import settings

logger = logging.getLogger(__name__)

class AIService:
    def __init__(self):
        # Initialize OpenAI client
        openai.api_key = settings.OPENAI_API_KEY
        self.client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
        
        # Session storage (in production, use Redis or database)
        self.sessions = {}
    
    async def generate_response(self, message: str, session_id: Optional[str] = None, 
                              context: Optional[str] = None) -> Dict[str, Any]:
        """Generate AI response to user message"""
        try:
            # Generate session ID if not provided
            if not session_id:
                session_id = str(uuid.uuid4())
            
            # Get or create session context
            if session_id not in self.sessions:
                self.sessions[session_id] = {
                    "messages": [],
                    "created_at": datetime.now()
                }
            
            session = self.sessions[session_id]
            
            # Build conversation history
            messages = [
                {
                    "role": "system",
                    "content": """You are Con<PERSON><PERSON>ch, an advanced AI assistant for the Chainsight platform. 
                    You specialize in Web3, cryptocurrency, blockchain technology, finance, and AI applications.
                    Provide helpful, accurate, and engaging responses. Be professional yet friendly.
                    Focus on practical solutions and actionable insights."""
                }
            ]
            
            # Add context if provided
            if context:
                messages.append({
                    "role": "system",
                    "content": f"Additional context: {context}"
                })
            
            # Add conversation history (last 10 messages)
            messages.extend(session["messages"][-10:])
            
            # Add current user message
            messages.append({
                "role": "user",
                "content": message
            })
            
            # Generate response using OpenAI
            try:
                response = self.client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=messages,
                    max_tokens=500,
                    temperature=0.7,
                    top_p=1,
                    frequency_penalty=0,
                    presence_penalty=0
                )
                
                ai_response = response.choices[0].message.content
                
            except Exception as openai_error:
                logger.warning(f"OpenAI API error: {openai_error}")
                # Fallback response
                ai_response = self._get_fallback_response(message)
            
            # Update session history
            session["messages"].extend([
                {"role": "user", "content": message},
                {"role": "assistant", "content": ai_response}
            ])
            
            # Keep only last 20 messages to manage memory
            if len(session["messages"]) > 20:
                session["messages"] = session["messages"][-20:]
            
            return {
                "response": ai_response,
                "sessionId": session_id,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"AI response generation failed: {e}")
            return {
                "response": "I apologize, but I'm experiencing technical difficulties. Please try again in a moment.",
                "sessionId": session_id or str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat()
            }
    
    def _get_fallback_response(self, message: str) -> str:
        """Generate fallback response when OpenAI is unavailable"""
        message_lower = message.lower()
        
        # Crypto/Web3 related responses
        if any(word in message_lower for word in ['token', 'crypto', 'blockchain', 'web3', 'ethereum']):
            return """I can help you with cryptocurrency and Web3 questions! The Chainsight platform offers:
            
• Token creation and deployment
• Liquidity management
• Wallet integration
• Smart contract interactions
• Real-time market data

What specific aspect would you like to explore?"""
        
        # Finance related responses
        elif any(word in message_lower for word in ['finance', 'trading', 'investment', 'market', 'stock']):
            return """I'm here to assist with financial analysis and market insights! Chainsight provides:
            
• Market trend analysis
• Price predictions
• Portfolio optimization
• Risk assessment
• Real-time financial data

How can I help with your financial needs?"""
        
        # AI/Technology related responses
        elif any(word in message_lower for word in ['ai', 'artificial intelligence', 'machine learning', 'analysis']):
            return """I specialize in AI-powered solutions! Chainsight offers:
            
• Intelligent market analysis
• Automated trading strategies
• Predictive modeling
• Data analytics
• AI-driven insights

What AI capabilities are you interested in?"""
        
        # General greeting responses
        elif any(word in message_lower for word in ['hello', 'hi', 'hey', 'greetings']):
            return """Hello! I'm Connectouch, your AI assistant for the Chainsight platform. I'm here to help you with:
            
• Cryptocurrency and Web3 technologies
• Financial analysis and trading
• AI-powered insights
• Platform features and capabilities

How can I assist you today?"""
        
        # Help/support responses
        elif any(word in message_lower for word in ['help', 'support', 'how', 'what']):
            return """I'm here to help! I can assist you with:
            
• Token creation and management
• Liquidity operations
• Market analysis
• AI-powered predictions
• Platform navigation
• Technical questions

What would you like to know more about?"""
        
        # Default response
        else:
            return """Thank you for your message! I'm Connectouch, your AI assistant for the Chainsight platform. 
            
I specialize in Web3, cryptocurrency, finance, and AI applications. I can help you with token creation, 
market analysis, trading strategies, and much more.

Could you please provide more details about what you'd like to explore?"""
    
    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of text"""
        try:
            # Simple sentiment analysis (in production, use proper NLP models)
            positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'happy', 'positive']
            negative_words = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'sad', 'negative', 'poor', 'disappointing']
            
            text_lower = text.lower()
            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)
            
            if positive_count > negative_count:
                sentiment = "positive"
                confidence = min(0.9, 0.5 + (positive_count - negative_count) * 0.1)
            elif negative_count > positive_count:
                sentiment = "negative"
                confidence = min(0.9, 0.5 + (negative_count - positive_count) * 0.1)
            else:
                sentiment = "neutral"
                confidence = 0.5
            
            return {
                "sentiment": sentiment,
                "confidence": confidence,
                "scores": {
                    "positive": positive_count / max(1, len(text.split())),
                    "negative": negative_count / max(1, len(text.split())),
                    "neutral": 1 - (positive_count + negative_count) / max(1, len(text.split()))
                }
            }
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {e}")
            return {
                "sentiment": "neutral",
                "confidence": 0.5,
                "scores": {"positive": 0.33, "negative": 0.33, "neutral": 0.34}
            }
    
    async def summarize_text(self, text: str, max_length: int = 150) -> Dict[str, Any]:
        """Summarize text"""
        try:
            # Simple extractive summarization (in production, use proper models)
            sentences = text.split('. ')
            
            if len(sentences) <= 2:
                return {"summary": text}
            
            # Take first and last sentences, and middle if needed
            if len(text) <= max_length:
                summary = text
            else:
                # Simple approach: take first sentence and truncate if needed
                summary = sentences[0]
                if len(summary) > max_length:
                    summary = summary[:max_length-3] + "..."
            
            return {"summary": summary}
            
        except Exception as e:
            logger.error(f"Text summarization failed: {e}")
            return {"summary": text[:max_length] + "..." if len(text) > max_length else text}
    
    async def generate_content(self, prompt: str, content_type: str = "general", 
                             max_tokens: int = 500) -> Dict[str, Any]:
        """Generate content based on prompt"""
        try:
            # Content type specific prompts
            system_prompts = {
                "marketing": "You are a marketing expert. Create engaging, persuasive content.",
                "technical": "You are a technical writer. Create clear, accurate technical content.",
                "creative": "You are a creative writer. Create imaginative, engaging content.",
                "business": "You are a business analyst. Create professional, strategic content.",
                "general": "You are a helpful assistant. Create informative, well-structured content."
            }
            
            system_prompt = system_prompts.get(content_type, system_prompts["general"])
            
            try:
                response = self.client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=max_tokens,
                    temperature=0.8
                )
                
                content = response.choices[0].message.content
                tokens_used = response.usage.total_tokens if response.usage else 0
                
            except Exception as openai_error:
                logger.warning(f"OpenAI API error in content generation: {openai_error}")
                # Fallback content generation
                content = f"Based on your request about '{prompt}', here's some relevant information that would be helpful for {content_type} purposes. This is a placeholder response while our AI service is being optimized."
                tokens_used = 0
            
            return {
                "content": content,
                "tokensUsed": tokens_used
            }
            
        except Exception as e:
            logger.error(f"Content generation failed: {e}")
            return {
                "content": "I apologize, but I'm unable to generate content at the moment. Please try again later.",
                "tokensUsed": 0
            }
