{"name": "chainsight", "version": "1.0.0", "description": "Chainsight by Connectouch - Modular fullstack AI platform", "private": true, "workspaces": ["apps/*", "packages/*", "plugins/*", "libs/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "start": "turbo run start", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "devDependencies": {"turbo": "^1.10.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/connectouch/chainsight"}, "author": "Connectouch", "license": "MIT"}