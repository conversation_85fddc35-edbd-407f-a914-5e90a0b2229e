export { FaceScanner } from './core/FaceScanner';
export { FaceDetector } from './detection/FaceDetector';
export { FaceRecognizer } from './recognition/FaceRecognizer';
export { FacialAnalyzer } from './analysis/FacialAnalyzer';
export { BiometricEngine } from './biometrics/BiometricEngine';
export { FaceDatabase } from './database/FaceDatabase';
export { LivenessDetector } from './liveness/LivenessDetector';
export { EmotionAnalyzer } from './emotion/EmotionAnalyzer';
export { AgeGenderEstimator } from './demographics/AgeGenderEstimator';

// Types
export type {
  FaceDetection,
  FaceLandmarks,
  FaceEmbedding,
  FaceMatch,
  BiometricTemplate,
  EmotionResult,
  DemographicResult,
  LivenessResult,
  ScanResult,
  FaceAnalysis
} from './types';

// Constants
export {
  DETECTION_MODELS,
  RECOGNITION_MODELS,
  EMOTION_LABELS,
  LANDMARK_INDICES,
  CONFIDENCE_THRESHOLDS
} from './constants';
