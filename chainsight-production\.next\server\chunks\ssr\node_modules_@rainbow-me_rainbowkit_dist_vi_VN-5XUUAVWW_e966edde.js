module.exports = {

"[project]/node_modules/@rainbow-me/rainbowkit/dist/vi_VN-5XUUAVWW.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>vi_VN_default)
});
"use client";
// src/locales/vi_VN.json
var vi_VN_default = '{\n  "connect_wallet": {\n    "label": "K\u1EBFt n\u1ED1i V\xED",\n    "wrong_network": {\n      "label": "M\u1EA1ng sai"\n    }\n  },\n  "intro": {\n    "title": "V\xED l\xE0 g\xEC?",\n    "description": "M\u1ED9t chi\u1EBFc v\xED \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng \u0111\u1EC3 g\u1EEDi, nh\u1EADn, l\u01B0u tr\u1EEF v\xE0 hi\u1EC3n th\u1ECB t\xE0i s\u1EA3n s\u1ED1. N\xF3 c\u0169ng l\xE0 m\u1ED9t c\xE1ch m\u1EDBi \u0111\u1EC3 \u0111\u0103ng nh\u1EADp, m\xE0 kh\xF4ng c\u1EA7n t\u1EA1o t\xE0i kho\u1EA3n v\xE0 m\u1EADt kh\u1EA9u m\u1EDBi tr\xEAn m\u1ED7i trang web.",\n    "digital_asset": {\n      "title": "Ng\xF4i nh\xE0 cho t\xE0i s\u1EA3n s\u1ED1 c\u1EE7a b\u1EA1n",\n      "description": "V\xED \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng \u0111\u1EC3 g\u1EEDi, nh\u1EADn, l\u01B0u tr\u1EEF v\xE0 hi\u1EC3n th\u1ECB c\xE1c t\xE0i s\u1EA3n s\u1ED1 nh\u01B0 Ethereum v\xE0 NFT."\n    },\n    "login": {\n      "title": "M\u1ED9t c\xE1ch m\u1EDBi \u0111\u1EC3 \u0111\u0103ng nh\u1EADp",\n      "description": "Thay v\xEC t\u1EA1o t\xE0i kho\u1EA3n v\xE0 m\u1EADt kh\u1EA9u m\u1EDBi tr\xEAn m\u1ED7i trang web, ch\u1EC9 c\u1EA7n k\u1EBFt n\u1ED1i v\xED c\u1EE7a b\u1EA1n."\n    },\n    "get": {\n      "label": "L\u1EA5y m\u1ED9t chi\u1EBFc v\xED"\n    },\n    "learn_more": {\n      "label": "T\xECm hi\u1EC3u th\xEAm"\n    }\n  },\n  "sign_in": {\n    "label": "X\xE1c minh t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n",\n    "description": "\u0110\u1EC3 ho\xE0n th\xE0nh k\u1EBFt n\u1ED1i, b\u1EA1n ph\u1EA3i k\xFD m\u1ED9t th\xF4ng \u0111i\u1EC7p trong v\xED c\u1EE7a b\u1EA1n \u0111\u1EC3 x\xE1c minh r\u1EB1ng b\u1EA1n l\xE0 ch\u1EE7 s\u1EDF h\u1EEFu c\u1EE7a t\xE0i kho\u1EA3n n\xE0y.",\n    "message": {\n      "send": "K\xFD th\xF4ng \u0111i\u1EC7p",\n      "preparing": "\u0110ang chu\u1EA9n b\u1ECB th\xF4ng \u0111i\u1EC7p...",\n      "cancel": "H\u1EE7y b\u1ECF",\n      "preparing_error": "L\u1ED7i chu\u1EA9n b\u1ECB th\xF4ng \u0111i\u1EC7p, vui l\xF2ng th\u1EED l\u1EA1i!"\n    },\n    "signature": {\n      "waiting": "\u0110ang ch\u1EDD ch\u1EEF k\xFD...",\n      "verifying": "\u0110ang x\xE1c minh ch\u1EEF k\xFD...",\n      "signing_error": "L\u1ED7i k\xFD th\xF4ng \u0111i\u1EC7p, vui l\xF2ng th\u1EED l\u1EA1i!",\n      "verifying_error": "L\u1ED7i x\xE1c minh ch\u1EEF k\xFD, vui l\xF2ng th\u1EED l\u1EA1i!",\n      "oops_error": "\xD4i, c\xF3 g\xEC \u0111\xF3 kh\xF4ng \u1ED5n!"\n    }\n  },\n  "connect": {\n    "label": "K\u1EBFt n\u1ED1i",\n    "title": "K\u1EBFt n\u1ED1i m\u1ED9t chi\u1EBFc v\xED",\n    "new_to_ethereum": {\n      "description": "M\u1EDBi s\u1EED d\u1EE5ng v\xED Ethereum?",\n      "learn_more": {\n        "label": "T\xECm hi\u1EC3u th\xEAm"\n      }\n    },\n    "learn_more": {\n      "label": "T\xECm hi\u1EC3u th\xEAm"\n    },\n    "recent": "G\u1EA7n \u0111\xE2y",\n    "status": {\n      "opening": "\u0110ang m\u1EDF %{wallet}...",\n      "connecting": "\u0110ang k\u1EBFt n\u1ED1i",\n      "connect_mobile": "Ti\u1EBFp t\u1EE5c trong %{wallet}",\n      "not_installed": "%{wallet} ch\u01B0a \u0111\u01B0\u1EE3c c\xE0i \u0111\u1EB7t",\n      "not_available": "%{wallet} kh\xF4ng kh\u1EA3 d\u1EE5ng",\n      "confirm": "X\xE1c nh\u1EADn k\u1EBFt n\u1ED1i trong ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng",\n      "confirm_mobile": "Ch\u1EA5p nh\u1EADn y\xEAu c\u1EA7u k\u1EBFt n\u1ED1i trong v\xED"\n    },\n    "secondary_action": {\n      "get": {\n        "description": "Kh\xF4ng c\xF3 %{wallet}?",\n        "label": "L\u1EA4Y"\n      },\n      "install": {\n        "label": "C\xC0I \u0110\u1EB6T"\n      },\n      "retry": {\n        "label": "TH\u1EEC L\u1EA0I"\n      }\n    },\n    "walletconnect": {\n      "description": {\n        "full": "C\u1EA7n modal WalletConnect ch\xEDnh th\u1EE9c?",\n        "compact": "C\u1EA7n modal WalletConnect?"\n      },\n      "open": {\n        "label": "M\u1EDE"\n      }\n    }\n  },\n  "connect_scan": {\n    "title": "Qu\xE9t b\u1EB1ng %{wallet}",\n    "fallback_title": "Qu\xE9t b\u1EB1ng \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n"\n  },\n  "connector_group": {\n    "installed": "\u0110\xE3 c\xE0i \u0111\u1EB7t",\n    "recommended": "\u0110\u1EC1 xu\u1EA5t",\n    "other": "Kh\xE1c",\n    "popular": "Ph\u1ED5 bi\u1EBFn",\n    "more": "Th\xEAm",\n    "others": "Kh\xE1c"\n  },\n  "get": {\n    "title": "L\u1EA5y m\u1ED9t chi\u1EBFc v\xED",\n    "action": {\n      "label": "L\u1EA4Y"\n    },\n    "mobile": {\n      "description": "V\xED di \u0111\u1ED9ng"\n    },\n    "extension": {\n      "description": "Ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng tr\xECnh duy\u1EC7t"\n    },\n    "mobile_and_extension": {\n      "description": "V\xED di \u0111\u1ED9ng v\xE0 Ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng"\n    },\n    "mobile_and_desktop": {\n      "description": "V\xED di \u0111\u1ED9ng v\xE0 m\xE1y t\xEDnh \u0111\u1EC3 b\xE0n"\n    },\n    "looking_for": {\n      "title": "Kh\xF4ng ph\u1EA3i c\xE1i b\u1EA1n \u0111ang t\xECm ki\u1EBFm?",\n      "mobile": {\n        "description": "Ch\u1ECDn m\u1ED9t chi\u1EBFc v\xED tr\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 b\u1EAFt \u0111\u1EA7u v\u1EDBi m\u1ED9t nh\xE0 cung c\u1EA5p v\xED kh\xE1c."\n      },\n      "desktop": {\n        "compact_description": "Ch\u1ECDn m\u1ED9t chi\u1EBFc v\xED tr\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 b\u1EAFt \u0111\u1EA7u v\u1EDBi m\u1ED9t nh\xE0 cung c\u1EA5p v\xED kh\xE1c.",\n        "wide_description": "Ch\u1ECDn m\u1ED9t chi\u1EBFc v\xED b\xEAn tr\xE1i \u0111\u1EC3 b\u1EAFt \u0111\u1EA7u v\u1EDBi m\u1ED9t nh\xE0 cung c\u1EA5p v\xED kh\xE1c."\n      }\n    }\n  },\n  "get_options": {\n    "title": "B\u1EAFt \u0111\u1EA7u v\u1EDBi %{wallet}",\n    "short_title": "L\u1EA5y %{wallet}",\n    "mobile": {\n      "title": "%{wallet} cho di \u0111\u1ED9ng",\n      "description": "S\u1EED d\u1EE5ng v\xED di \u0111\u1ED9ng \u0111\u1EC3 kh\xE1m ph\xE1 th\u1EBF gi\u1EDBi c\u1EE7a Ethereum.",\n      "download": {\n        "label": "T\u1EA3i \u1EE9ng d\u1EE5ng"\n      }\n    },\n    "extension": {\n      "title": "%{wallet} cho %{browser}",\n      "description": "Truy c\u1EADp v\xED c\u1EE7a b\u1EA1n ngay t\u1EEB tr\xECnh duy\u1EC7t web y\xEAu th\xEDch c\u1EE7a b\u1EA1n.",\n      "download": {\n        "label": "Th\xEAm v\xE0o %{browser}"\n      }\n    },\n    "desktop": {\n      "title": "%{wallet} cho %{platform}",\n      "description": "Truy c\u1EADp v\xED c\u1EE7a b\u1EA1n natively t\u1EEB m\xE1y t\xEDnh \u0111\u1EC3 b\xE0n m\u1EA1nh m\u1EBD c\u1EE7a b\u1EA1n.",\n      "download": {\n        "label": "Th\xEAm v\xE0o %{platform}"\n      }\n    }\n  },\n  "get_mobile": {\n    "title": "C\xE0i \u0111\u1EB7t %{wallet}",\n    "description": "Qu\xE9t b\u1EB1ng \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n \u0111\u1EC3 t\u1EA3i v\u1EC1 tr\xEAn iOS ho\u1EB7c Android",\n    "continue": {\n      "label": "Ti\u1EBFp t\u1EE5c"\n    }\n  },\n  "get_instructions": {\n    "mobile": {\n      "connect": {\n        "label": "K\u1EBFt n\u1ED1i"\n      },\n      "learn_more": {\n        "label": "T\xECm hi\u1EC3u th\xEAm"\n      }\n    },\n    "extension": {\n      "refresh": {\n        "label": "L\xE0m m\u1EDBi"\n      },\n      "learn_more": {\n        "label": "T\xECm hi\u1EC3u th\xEAm"\n      }\n    },\n    "desktop": {\n      "connect": {\n        "label": "K\u1EBFt n\u1ED1i"\n      },\n      "learn_more": {\n        "label": "T\xECm hi\u1EC3u th\xEAm"\n      }\n    }\n  },\n  "chains": {\n    "title": "Chuy\u1EC3n \u0111\u1ED5i M\u1EA1ng",\n    "wrong_network": "Ph\xE1t hi\u1EC7n m\u1EA1ng sai, chuy\u1EC3n \u0111\u1ED5i ho\u1EB7c ng\u1EAFt k\u1EBFt n\u1ED1i \u0111\u1EC3 ti\u1EBFp t\u1EE5c.",\n    "confirm": "X\xE1c nh\u1EADn trong V\xED",\n    "switching_not_supported": "V\xED c\u1EE7a b\u1EA1n kh\xF4ng h\u1ED7 tr\u1EE3 chuy\u1EC3n \u0111\u1ED5i m\u1EA1ng t\u1EEB %{appName}. Th\u1EED chuy\u1EC3n \u0111\u1ED5i m\u1EA1ng t\u1EEB trong v\xED c\u1EE7a b\u1EA1n thay v\xEC.",\n    "switching_not_supported_fallback": "V\xED c\u1EE7a b\u1EA1n kh\xF4ng h\u1ED7 tr\u1EE3 chuy\u1EC3n \u0111\u1ED5i m\u1EA1ng t\u1EEB \u1EE9ng d\u1EE5ng n\xE0y. Th\u1EED chuy\u1EC3n \u0111\u1ED5i m\u1EA1ng t\u1EEB trong v\xED c\u1EE7a b\u1EA1n thay v\xEC.",\n    "disconnect": "Ng\u1EAFt k\u1EBFt n\u1ED1i",\n    "connected": "\u0110\xE3 k\u1EBFt n\u1ED1i"\n  },\n  "profile": {\n    "disconnect": {\n      "label": "Ng\u1EAFt k\u1EBFt n\u1ED1i"\n    },\n    "copy_address": {\n      "label": "Sao ch\xE9p \u0110\u1ECBa ch\u1EC9",\n      "copied": "\u0110\xE3 sao ch\xE9p!"\n    },\n    "explorer": {\n      "label": "Xem th\xEAm tr\xEAn explorer"\n    },\n    "transactions": {\n      "description": "C\xE1c giao d\u1ECBch %{appName} s\u1EBD xu\u1EA5t hi\u1EC7n \u1EDF \u0111\xE2y...",\n      "description_fallback": "C\xE1c giao d\u1ECBch c\u1EE7a b\u1EA1n s\u1EBD xu\u1EA5t hi\u1EC7n \u1EDF \u0111\xE2y...",\n      "recent": {\n        "title": "Giao d\u1ECBch g\u1EA7n \u0111\xE2y"\n      },\n      "clear": {\n        "label": "X\xF3a t\u1EA5t c\u1EA3"\n      }\n    }\n  },\n  "wallet_connectors": {\n    "argent": {\n      "qr_code": {\n        "step1": {\n          "description": "\u0110\u1EB7t Argent l\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Argent"\n        },\n        "step2": {\n          "description": "T\u1EA1o v\xED v\xE0 t\xEAn ng\u01B0\u1EDDi d\xF9ng, ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt Qu\xE9t QR"\n        }\n      }\n    },\n    "berasig": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng BeraSig",\n          "description": "Ch\xFAng t\xF4i \u0111\u1EC1 xu\u1EA5t ghim BeraSig v\xE0o thanh t\xE1c v\u1EE5 c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp v\xED d\u1EC5 d\xE0ng h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o m\u1ED9t V\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "best": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Best Wallet",\n          "description": "Th\xEAm Best Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR v\xE0 qu\xE9t",\n          "description": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR tr\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n, qu\xE9t m\xE3 v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "bifrost": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn \u0111\u1EB7t v\xED Bifrost l\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Bifrost Wallet"\n        },\n        "step2": {\n          "description": "T\u1EA1o ho\u1EB7c nh\u1EADp v\xED b\u1EB1ng c\xE1ch s\u1EED d\u1EE5ng c\u1EE5m t\u1EEB kh\xF4i ph\u1EE5c c\u1EE7a b\u1EA1n.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t"\n        }\n      }\n    },\n    "bitget": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn \u0111\u1EB7t v\xED Bitget l\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Bitget Wallet"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim v\xED Bitget v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Bitget Wallet"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      }\n    },\n    "bitski": {\n      "extension": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Bitski v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Bitski"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      }\n    },\n    "bitverse": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Bitverse Wallet",\n          "description": "Th\xEAm Bitverse Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR v\xE0 qu\xE9t",\n          "description": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR tr\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n, qu\xE9t m\xE3 v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "bloom": {\n      "desktop": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Bloom Wallet",\n          "description": "Ch\xFAng t\xF4i khuy\u1EBFn ngh\u1ECB \u0111\u1EB7t Bloom Wallet l\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n."\n        },\n        "step2": {\n          "description": "T\u1EA1o ho\u1EB7c nh\u1EADp v\xED b\u1EB1ng c\xE1ch s\u1EED d\u1EE5ng c\u1EE5m t\u1EEB kh\xF4i ph\u1EE5c c\u1EE7a b\u1EA1n.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n c\xF3 v\xED, h\xE3y nh\u1EA5p v\xE0o K\u1EBFt n\u1ED1i \u0111\u1EC3 k\u1EBFt n\u1ED1i qua Bloom. M\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i trong \u1EE9ng d\u1EE5ng s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n x\xE1c nh\u1EADn k\u1EBFt n\u1ED1i.",\n          "title": "Nh\u1EA5n v\xE0o K\u1EBFt n\u1ED1i"\n        }\n      }\n    },\n    "bybit": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn \u0111\u1EB7t Bybit v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Bybit"\n        },\n        "step2": {\n          "description": "B\u1EA1n c\xF3 th\u1EC3 d\u1EC5 d\xE0ng sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng t\xEDnh n\u0103ng sao l\u01B0u tr\xEAn \u0111i\u1EC7n tho\u1EA1i c\u1EE7a ch\xFAng t\xF4i.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Nh\u1EA5p v\xE0o g\xF3c tr\xEAn b\xEAn ph\u1EA3i c\u1EE7a tr\xECnh duy\u1EC7t v\xE0 ghim Bybit Wallet \u0111\u1EC3 truy c\u1EADp d\u1EC5 d\xE0ng.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Bybit Wallet"\n        },\n        "step2": {\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n thi\u1EBFt l\u1EADp Bybit Wallet, nh\u1EA5p v\xE0o b\xEAn d\u01B0\u1EDBi \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      }\n    },\n    "binance": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn \u0111\u1EB7t Binance v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Binance"\n        },\n        "step2": {\n          "description": "B\u1EA1n c\xF3 th\u1EC3 d\u1EC5 d\xE0ng sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng t\xEDnh n\u0103ng sao l\u01B0u tr\xEAn \u0111i\u1EC7n tho\u1EA1i c\u1EE7a ch\xFAng t\xF4i.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt WalletConnect"\n        }\n      }\n    },\n    "coin98": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn \u0111\u1EB7t v\xED Coin98 l\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Coin98 Wallet"\n        },\n        "step2": {\n          "description": "B\u1EA1n c\xF3 th\u1EC3 d\u1EC5 d\xE0ng sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng t\xEDnh n\u0103ng sao l\u01B0u tr\xEAn \u0111i\u1EC7n tho\u1EA1i c\u1EE7a ch\xFAng t\xF4i.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt WalletConnect"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Nh\u1EA5p v\xE0o g\xF3c tr\xEAn b\xEAn ph\u1EA3i c\u1EE7a tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n v\xE0 ghim v\xED Coin98 \u0111\u1EC3 truy c\u1EADp d\u1EC5 d\xE0ng.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Coin98 Wallet"\n        },\n        "step2": {\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED Coin98, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      }\n    },\n    "coinbase": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn \u0111\u1EB7t v\xED Coinbase l\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Coinbase Wallet"\n        },\n        "step2": {\n          "description": "B\u1EA1n c\xF3 th\u1EC3 d\u1EC5 d\xE0ng sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng t\xEDnh n\u0103ng sao l\u01B0u tr\xEAn \u0111\xE1m m\xE2y.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim v\xED Coinbase v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Coinbase Wallet"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      }\n    },\n    "compass": {\n      "extension": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Compass Wallet v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Compass Wallet"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      }\n    },\n    "core": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn \u0111\u1EB7t Core l\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Core"\n        },\n        "step2": {\n          "description": "B\u1EA1n c\xF3 th\u1EC3 d\u1EC5 d\xE0ng sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng t\xEDnh n\u0103ng sao l\u01B0u tr\xEAn \u0111i\u1EC7n tho\u1EA1i c\u1EE7a ch\xFAng t\xF4i.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt WalletConnect"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Core v\xE0o thanh t\xE1c v\u1EE5 c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Core"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      }\n    },\n    "fox": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn th\xEAm FoxWallet v\xE0o m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng FoxWallet"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t"\n        }\n      }\n    },\n    "frontier": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn th\xEAm Frontier Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Frontier Wallet"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Frontier Wallet v\xE0o thanh t\xE1c v\u1EE5 c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Frontier Wallet"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      }\n    },\n    "im_token": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng imToken",\n          "description": "Th\xEAm \u1EE9ng d\u1EE5ng imToken v\xE0o m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng M\xE1y qu\xE9t \u1EDF g\xF3c tr\xEAn c\xF9ng b\xEAn ph\u1EA3i",\n          "description": "Ch\u1ECDn K\u1EBFt n\u1ED1i m\u1EDBi, sau \u0111\xF3 qu\xE9t m\xE3 QR v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "iopay": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn \u0111\u1EB7t ioPay v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng ioPay"\n        },\n        "step2": {\n          "description": "B\u1EA1n c\xF3 th\u1EC3 d\u1EC5 d\xE0ng sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng t\xEDnh n\u0103ng sao l\u01B0u tr\xEAn \u0111i\u1EC7n tho\u1EA1i c\u1EE7a ch\xFAng t\xF4i.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt WalletConnect"\n        }\n      }\n    },\n    "kaikas": {\n      "extension": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Kaikas v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Kaikas"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Kaikas",\n          "description": "\u0110\u1EB7t \u1EE9ng d\u1EE5ng Kaikas v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng M\xE1y qu\xE9t \u1EDF g\xF3c tr\xEAn c\xF9ng b\xEAn ph\u1EA3i",\n          "description": "Ch\u1ECDn K\u1EBFt n\u1ED1i m\u1EDBi, sau \u0111\xF3 qu\xE9t m\xE3 QR v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "kaia": {\n      "extension": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Kaia v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Kaia"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Kaia",\n          "description": "\u0110\u1EB7t \u1EE9ng d\u1EE5ng Kaia l\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng M\xE1y qu\xE9t \u1EDF g\xF3c tr\xEAn c\xF9ng b\xEAn ph\u1EA3i",\n          "description": "Ch\u1ECDn K\u1EBFt n\u1ED1i m\u1EDBi, sau \u0111\xF3 qu\xE9t m\xE3 QR v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "kraken": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Kraken Wallet",\n          "description": "Th\xEAm Kraken Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR v\xE0 qu\xE9t",\n          "description": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR tr\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n, qu\xE9t m\xE3 v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "kresus": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Kresus Wallet",\n          "description": "Th\xEAm Kresus Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR v\xE0 qu\xE9t",\n          "description": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR tr\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n, qu\xE9t m\xE3 v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "magicEden": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Magic Eden",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Magic Eden v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED c\u1EE7a b\u1EA1n d\u1EC5 d\xE0ng h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn sao l\u01B0u v\xED c\u1EE7a b\u1EA1n b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. Kh\xF4ng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB kh\xF4i ph\u1EE5c b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "metamask": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng MetaMask",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn th\xEAm MetaMask v\xE0o m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t",\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng MetaMask",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim MetaMask v\xE0o thanh t\xE1c v\u1EE5 c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "nestwallet": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng NestWallet",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim NestWallet v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "okx": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng OKX Wallet",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn th\xEAm OKX Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t",\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng OKX Wallet",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim OKX Wallet v\xE0o thanh t\xE1c v\u1EE5 c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "omni": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Omni",\n          "description": "Th\xEAm Omni v\xE0o m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR v\xE0 qu\xE9t",\n          "description": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR tr\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n, qu\xE9t m\xE3 v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "1inch": {\n      "qr_code": {\n        "step1": {\n          "description": "\u0110\u1EB7t 1inch Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng 1inch Wallet"\n        },\n        "step2": {\n          "description": "T\u1EA1o v\xED v\xE0 t\xEAn ng\u01B0\u1EDDi d\xF9ng, ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt Qu\xE9t QR"\n        }\n      }\n    },\n    "token_pocket": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng TokenPocket",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn th\xEAm TokenPocket v\xE0o m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t",\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng TokenPocket",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim TokenPocket v\xE0o thanh t\xE1c v\u1EE5 c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "trust": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Trust Wallet",\n          "description": "Th\xEAm Trust Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o WalletConnect trong C\xE0i \u0111\u1EB7t",\n          "description": "Ch\u1ECDn K\u1EBFt n\u1ED1i m\u1EDBi, sau \u0111\xF3 qu\xE9t m\xE3 QR v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Trust Wallet",\n          "description": "Nh\u1EA5p v\xE0o g\xF3c tr\xEAn c\xF9ng b\xEAn ph\u1EA3i c\u1EE7a tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n v\xE0 ghim Trust Wallet \u0111\u1EC3 d\u1EC5 d\xE0ng truy c\u1EADp."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "Sau khi b\u1EA1n thi\u1EBFt l\u1EADp Trust Wallet, nh\u1EA5p v\xE0o b\xEAn d\u01B0\u1EDBi \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "uniswap": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Uniswap",\n          "description": "Th\xEAm Uniswap Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR v\xE0 qu\xE9t",\n          "description": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR tr\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n, qu\xE9t m\xE3 v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "zerion": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Zerion",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn \u0111\u1EB7t Zerion tr\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t",\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Zerion",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Zerion v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "rainbow": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Rainbow",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn \u0111\u1EB7t Rainbow tr\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "B\u1EA1n c\xF3 th\u1EC3 d\u1EC5 d\xE0ng sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng t\xEDnh n\u0103ng sao l\u01B0u tr\xEAn \u0111i\u1EC7n tho\u1EA1i c\u1EE7a ch\xFAng t\xF4i."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t",\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n        }\n      }\n    },\n    "enkrypt": {\n      "extension": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Enkrypt Wallet v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Enkrypt Wallet"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      }\n    },\n    "frame": {\n      "extension": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Frame v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n.",\n          "title": "C\xE0i \u0111\u1EB7t Frame v\xE0 ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng k\xE8m theo"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      }\n    },\n    "one_key": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng OneKey Wallet",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim OneKey Wallet v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "paraswap": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng ParaSwap",\n          "description": "Th\xEAm ParaSwap Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR v\xE0 qu\xE9t",\n          "description": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR tr\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n, qu\xE9t m\xE3 v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "phantom": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Phantom",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Phantom v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED d\u1EC5 d\xE0ng h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn sao l\u01B0u v\xED c\u1EE7a b\u1EA1n b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. Kh\xF4ng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB kh\xF4i ph\u1EE5c b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "rabby": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Rabby",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Rabby v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "ronin": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn \u0111\u1EB7t Ronin Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Ronin Wallet"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Ronin Wallet v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n.",\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Ronin Wallet"\n        },\n        "step2": {\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng.",\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n"\n        }\n      }\n    },\n    "ramper": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Ramper",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Ramper v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED c\u1EE7a b\u1EA1n d\u1EC5 d\xE0ng h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o m\u1ED9t V\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "safeheron": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Core",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Safeheron v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "taho": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Taho",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Taho v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "wigwam": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Wigwam",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Wigwam v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED c\u1EE7a b\u1EA1n nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "talisman": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Talisman",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Talisman v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp m\u1ED9t Ethereum Wallet",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn sao l\u01B0u v\xED c\u1EE7a b\u1EA1n b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. Kh\xF4ng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB kh\xF4i ph\u1EE5c c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "xdefi": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng XDEFI Wallet",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim XDEFI Wallet v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "zeal": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Zeal",\n          "description": "Th\xEAm Zeal Wallet v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR v\xE0 qu\xE9t",\n          "description": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng QR tr\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n, qu\xE9t m\xE3 v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Zeal",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Zeal v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "safepal": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng SafePal Wallet",\n          "description": "Nh\u1EA5p v\xE0o ph\xEDa tr\xEAn b\xEAn ph\u1EA3i tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n v\xE0 ghim SafePal Wallet \u0111\u1EC3 truy c\u1EADp d\u1EC5 d\xE0ng."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "Sau khi c\xE0i \u0111\u1EB7t SafePal Wallet, b\u1EA5m v\xE0o b\xEAn d\u01B0\u1EDBi \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng SafePal Wallet",\n          "description": "\u0110\u1EB7t SafePal Wallet tr\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o WalletConnect trong C\xE0i \u0111\u1EB7t",\n          "description": "Ch\u1ECDn K\u1EBFt n\u1ED1i m\u1EDBi, sau \u0111\xF3 qu\xE9t m\xE3 QR v\xE0 x\xE1c nh\u1EADn nh\u1EAFc nh\u1EDF \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "desig": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Desig",\n          "description": "Ch\xFAng t\xF4i khuy\xEAn b\u1EA1n n\xEAn ghim Desig v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED d\u1EC5 d\xE0ng h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o m\u1ED9t V\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "subwallet": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng SubWallet",\n          "description": "Ch\xFAng t\xF4i khuy\u1EBFn ngh\u1ECB ghim SubWallet v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED c\u1EE7a b\u1EA1n nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn sao l\u01B0u v\xED c\u1EE7a b\u1EA1n b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. Kh\xF4ng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB kh\xF4i ph\u1EE5c c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng SubWallet",\n          "description": "Ch\xFAng t\xF4i khuy\u1EBFn ngh\u1ECB \u0111\u1EB7t SubWallet l\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t",\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n        }\n      }\n    },\n    "clv": {\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng CLV Wallet",\n          "description": "Ch\xFAng t\xF4i khuy\u1EBFn ngh\u1ECB ghim CLV Wallet v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp v\xED c\u1EE7a b\u1EA1n nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng CLV Wallet",\n          "description": "Ch\xFAng t\xF4i khuy\u1EBFn ngh\u1ECB \u0111\u1EB7t CLV Wallet l\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn r\u1EB1ng b\u1EA1n \u0111\xE3 sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. \u0110\u1EEBng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t",\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n        }\n      }\n    },\n    "okto": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Okto",\n          "description": "Th\xEAm Okto v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh ch\xF3ng"\n        },\n        "step2": {\n          "title": "T\u1EA1o v\xED MPC",\n          "description": "T\u1EA1o t\xE0i kho\u1EA3n v\xE0 t\u1EA1o v\xED"\n        },\n        "step3": {\n          "title": "Nh\u1EA5n v\xE0o WalletConnect trong C\xE0i \u0111\u1EB7t",\n          "description": "Nh\u1EA5n v\xE0o bi\u1EC3u t\u01B0\u1EE3ng Qu\xE9t m\xE3 QR \u1EDF tr\xEAn c\xF9ng b\xEAn ph\u1EA3i v\xE0 x\xE1c nh\u1EADn l\u1EDDi nh\u1EAFc \u0111\u1EC3 k\u1EBFt n\u1ED1i."\n        }\n      }\n    },\n    "ledger": {\n      "desktop": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Ledger Live",\n          "description": "Ch\xFAng t\xF4i khuy\u1EBFn ngh\u1ECB \u0111\u1EB7t Ledger Live l\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "Thi\u1EBFt l\u1EADp Ledger c\u1EE7a b\u1EA1n",\n          "description": "Thi\u1EBFt l\u1EADp m\u1ED9t Ledger m\u1EDBi ho\u1EB7c k\u1EBFt n\u1ED1i v\u1EDBi m\u1ED9t c\xE1i hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "K\u1EBFt n\u1ED1i",\n          "description": "M\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Ledger Live",\n          "description": "Ch\xFAng t\xF4i khuy\u1EBFn ngh\u1ECB \u0111\u1EB7t Ledger Live l\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "Thi\u1EBFt l\u1EADp Ledger c\u1EE7a b\u1EA1n",\n          "description": "B\u1EA1n c\xF3 th\u1EC3 \u0111\u1ED3ng b\u1ED9 h\xF3a v\u1EDBi \u1EE9ng d\u1EE5ng m\xE1y t\xEDnh \u0111\u1EC3 b\xE0n ho\u1EB7c k\u1EBFt n\u1ED1i Ledger c\u1EE7a b\u1EA1n."\n        },\n        "step3": {\n          "title": "Qu\xE9t m\xE3",\n          "description": "Nh\u1EA5n v\xE0o WalletConnect sau \u0111\xF3 Chuy\u1EC3n sang M\xE1y qu\xE9t. Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n        }\n      }\n    },\n    "valora": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Valora",\n          "description": "Ch\xFAng t\xF4i khuy\u1EBFn ngh\u1ECB \u0111\u1EB7t Valora tr\xEAn m\xE0n h\xECnh ch\xEDnh c\u1EE7a b\u1EA1n \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t",\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n        }\n      }\n    },\n    "gate": {\n      "qr_code": {\n        "step1": {\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng Gate",\n          "description": "Ch\xFAng t\xF4i khuy\u1EBFn ngh\u1ECB \u0111\u1EB7t Gate l\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n        },\n        "step3": {\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t",\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "C\xE0i \u0111\u1EB7t ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng Gate",\n          "description": "Ch\xFAng t\xF4i khuy\u1EBFn ngh\u1ECB ghim Gate v\xE0o thanh t\xE1c v\u1EE5 \u0111\u1EC3 truy c\u1EADp d\u1EC5 d\xE0ng v\xE0o v\xED c\u1EE7a b\u1EA1n."\n        },\n        "step2": {\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n          "description": "H\xE3y ch\u1EAFc ch\u1EAFn sao l\u01B0u v\xED c\u1EE7a b\u1EA1n b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p an to\xE0n. Kh\xF4ng bao gi\u1EDD chia s\u1EBB c\u1EE5m t\u1EEB kh\xF4i ph\u1EE5c b\xED m\u1EADt c\u1EE7a b\u1EA1n v\u1EDBi b\u1EA5t k\u1EF3 ai."\n        },\n        "step3": {\n          "title": "L\xE0m m\u1EDBi tr\xECnh duy\u1EC7t c\u1EE7a b\u1EA1n",\n          "description": "M\u1ED9t khi b\u1EA1n thi\u1EBFt l\u1EADp xong v\xED c\u1EE7a m\xECnh, nh\u1EA5p v\xE0o d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 l\xE0m m\u1EDBi tr\xECnh duy\u1EC7t v\xE0 t\u1EA3i l\u1EA1i ti\u1EC7n \xEDch m\u1EDF r\u1ED9ng."\n        }\n      }\n    },\n    "xportal": {\n      "qr_code": {\n        "step1": {\n          "description": "\u0110\u1EB7t xPortal l\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng xPortal"\n        },\n        "step2": {\n          "description": "T\u1EA1o v\xED ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt Qu\xE9t QR"\n        }\n      }\n    },\n    "mew": {\n      "qr_code": {\n        "step1": {\n          "description": "Ch\xFAng t\xF4i khuy\u1EBFn ngh\u1ECB \u0111\u1EB7t MEW Wallet l\xEAn m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp nhanh h\u01A1n.",\n          "title": "M\u1EDF \u1EE9ng d\u1EE5ng MEW Wallet"\n        },\n        "step2": {\n          "description": "B\u1EA1n c\xF3 th\u1EC3 d\u1EC5 d\xE0ng sao l\u01B0u v\xED c\u1EE7a m\xECnh b\u1EB1ng t\xEDnh n\u0103ng sao l\u01B0u tr\xEAn \u0111\xE1m m\xE2y.",\n          "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED"\n        },\n        "step3": {\n          "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh.",\n          "title": "Nh\u1EA5n n\xFAt qu\xE9t"\n        }\n      }\n    }\n  },\n  "zilpay": {\n    "qr_code": {\n      "step1": {\n        "title": "M\u1EDF \u1EE9ng d\u1EE5ng ZilPay",\n        "description": "Th\xEAm ZilPay v\xE0o m\xE0n h\xECnh ch\xEDnh \u0111\u1EC3 truy c\u1EADp v\xED nhanh h\u01A1n."\n      },\n      "step2": {\n        "title": "T\u1EA1o ho\u1EB7c Nh\u1EADp v\xED",\n        "description": "T\u1EA1o v\xED m\u1EDBi ho\u1EB7c nh\u1EADp v\xED hi\u1EC7n c\xF3."\n      },\n      "step3": {\n        "title": "Nh\u1EA5n n\xFAt qu\xE9t",\n        "description": "Sau khi b\u1EA1n qu\xE9t, m\u1ED9t l\u1EDDi nh\u1EAFc k\u1EBFt n\u1ED1i s\u1EBD xu\u1EA5t hi\u1EC7n \u0111\u1EC3 b\u1EA1n k\u1EBFt n\u1ED1i v\xED c\u1EE7a m\xECnh."\n      }\n    }\n  }\n}\n';
;
}}),

};

//# sourceMappingURL=node_modules_%40rainbow-me_rainbowkit_dist_vi_VN-5XUUAVWW_e966edde.js.map