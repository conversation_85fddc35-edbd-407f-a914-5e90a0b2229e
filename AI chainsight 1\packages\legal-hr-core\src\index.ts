export { LegalHREngine } from './core/LegalHREngine';
export { ResumeParser } from './resume/ResumeParser';
export { ContractAnalyzer } from './legal/ContractAnalyzer';
export { DocumentProcessor } from './processing/DocumentProcessor';
export { SkillExtractor } from './skills/SkillExtractor';
export { ExperienceAnalyzer } from './experience/ExperienceAnalyzer';
export { LegalClauseDetector } from './legal/LegalClauseDetector';
export { ComplianceChecker } from './compliance/ComplianceChecker';
export { DocumentClassifier } from './classification/DocumentClassifier';

// Types
export type {
  ParsedResume,
  LegalDocument,
  ContractAnalysis,
  Skill,
  Experience,
  Education,
  LegalClause,
  ComplianceResult,
  DocumentMetadata,
  ProcessingResult
} from './types';

// Constants
export {
  DOCUMENT_TYPES,
  SKILL_CATEGORIES,
  EXPERIENCE_TYPES,
  LEGAL_CLAUSE_TYPES,
  COMPLIANCE_STANDARDS
} from './constants';
