import { MarketData, AnalysisResult, MarketCondition, Asset } from '../types';
import { TechnicalAnalyzer } from '../technical/TechnicalAnalyzer';
import { FundamentalAnalyzer } from '../fundamental/FundamentalAnalyzer';
import { SentimentAnalyzer } from '../sentiment/SentimentAnalyzer';
import { MARKET_PHASES } from '../constants';

export class MarketAnalyzer {
  private technicalAnalyzer: TechnicalAnalyzer;
  private fundamentalAnalyzer: FundamentalAnalyzer;
  private sentimentAnalyzer: SentimentAnalyzer;

  constructor() {
    this.technicalAnalyzer = new TechnicalAnalyzer();
    this.fundamentalAnalyzer = new FundamentalAnalyzer();
    this.sentimentAnalyzer = new SentimentAnalyzer();
  }

  async analyzeAsset(
    symbol: string,
    marketData: MarketData[],
    includeAll: boolean = true
  ): Promise<AnalysisResult> {
    try {
      console.log(`Analyzing ${symbol}...`);

      const analyses = await Promise.allSettled([
        this.technicalAnalyzer.analyze(marketData),
        includeAll ? this.fundamentalAnalyzer.analyze(symbol) : null,
        includeAll ? this.sentimentAnalyzer.analyze(symbol) : null
      ]);

      // Extract successful results
      const technicalResult = analyses[0].status === 'fulfilled' ? analyses[0].value : null;
      const fundamentalResult = analyses[1]?.status === 'fulfilled' ? analyses[1].value : null;
      const sentimentResult = analyses[2]?.status === 'fulfilled' ? analyses[2].value : null;

      // Combine analysis results
      const combinedScore = this.calculateCombinedScore(
        technicalResult,
        fundamentalResult,
        sentimentResult
      );

      const signals = [
        ...(technicalResult?.signals || []),
        ...(fundamentalResult?.signals || []),
        ...(sentimentResult?.signals || [])
      ];

      const recommendations = this.generateRecommendations(
        combinedScore,
        signals,
        technicalResult,
        fundamentalResult,
        sentimentResult
      );

      const riskAssessment = this.assessRisk(
        marketData,
        technicalResult,
        fundamentalResult
      );

      return {
        symbol,
        timestamp: Date.now(),
        analysis_type: 'combined',
        score: combinedScore,
        confidence: this.calculateConfidence(technicalResult, fundamentalResult, sentimentResult),
        signals,
        recommendations,
        risk_assessment: riskAssessment
      };

    } catch (error) {
      console.error(`Analysis failed for ${symbol}:`, error);
      throw new Error(`Analysis failed: ${error.message}`);
    }
  }

  async analyzeMarketCondition(
    marketData: Map<string, MarketData[]>
  ): Promise<MarketCondition> {
    try {
      console.log('Analyzing overall market condition...');

      const symbols = Array.from(marketData.keys());
      const analyses = await Promise.all(
        symbols.map(symbol => 
          this.analyzeAsset(symbol, marketData.get(symbol)!, false)
        )
      );

      // Calculate market-wide metrics
      const avgScore = analyses.reduce((sum, analysis) => sum + analysis.score, 0) / analyses.length;
      const volatilities = this.calculateVolatilities(marketData);
      const avgVolatility = volatilities.reduce((sum, vol) => sum + vol, 0) / volatilities.length;
      
      // Determine market sentiment
      const overallSentiment = this.determineOverallSentiment(avgScore);
      
      // Determine volatility regime
      const volatilityRegime = this.determineVolatilityRegime(avgVolatility);
      
      // Calculate trend strength
      const trendStrength = this.calculateTrendStrength(analyses);
      
      // Determine market phase
      const marketPhase = this.determineMarketPhase(analyses, avgScore, trendStrength);
      
      // Calculate fear & greed index (simplified)
      const fearGreedIndex = this.calculateFearGreedIndex(avgScore, avgVolatility);
      
      // Check for correlation breakdown
      const correlationBreakdown = this.checkCorrelationBreakdown(marketData);

      return {
        overall_sentiment: overallSentiment,
        volatility_regime: volatilityRegime,
        trend_strength: trendStrength,
        market_phase: marketPhase,
        fear_greed_index: fearGreedIndex,
        correlation_breakdown: correlationBreakdown
      };

    } catch (error) {
      console.error('Market condition analysis failed:', error);
      throw new Error(`Market analysis failed: ${error.message}`);
    }
  }

  async findCorrelatedAssets(
    targetSymbol: string,
    marketData: Map<string, MarketData[]>,
    threshold: number = 0.7
  ): Promise<Array<{ symbol: string; correlation: number }>> {
    const targetData = marketData.get(targetSymbol);
    if (!targetData) {
      throw new Error(`No data found for ${targetSymbol}`);
    }

    const correlations: Array<{ symbol: string; correlation: number }> = [];

    for (const [symbol, data] of marketData.entries()) {
      if (symbol === targetSymbol) continue;

      try {
        const correlation = this.calculateCorrelation(targetData, data);
        if (Math.abs(correlation) >= threshold) {
          correlations.push({ symbol, correlation });
        }
      } catch (error) {
        console.warn(`Failed to calculate correlation for ${symbol}:`, error);
      }
    }

    // Sort by absolute correlation value
    return correlations.sort((a, b) => Math.abs(b.correlation) - Math.abs(a.correlation));
  }

  async identifyAnomalies(
    marketData: MarketData[],
    lookbackPeriod: number = 30
  ): Promise<Array<{ timestamp: number; type: string; severity: number; description: string }>> {
    const anomalies = [];

    // Price anomalies
    const priceAnomalies = this.detectPriceAnomalies(marketData, lookbackPeriod);
    anomalies.push(...priceAnomalies);

    // Volume anomalies
    const volumeAnomalies = this.detectVolumeAnomalies(marketData, lookbackPeriod);
    anomalies.push(...volumeAnomalies);

    // Volatility anomalies
    const volatilityAnomalies = this.detectVolatilityAnomalies(marketData, lookbackPeriod);
    anomalies.push(...volatilityAnomalies);

    return anomalies.sort((a, b) => b.severity - a.severity);
  }

  private calculateCombinedScore(
    technical: any,
    fundamental: any,
    sentiment: any
  ): number {
    let score = 0;
    let weights = 0;

    if (technical) {
      score += technical.score * 0.4;
      weights += 0.4;
    }

    if (fundamental) {
      score += fundamental.score * 0.35;
      weights += 0.35;
    }

    if (sentiment) {
      score += sentiment.score * 0.25;
      weights += 0.25;
    }

    return weights > 0 ? score / weights : 0;
  }

  private calculateConfidence(technical: any, fundamental: any, sentiment: any): number {
    const confidences = [];
    
    if (technical) confidences.push(technical.confidence);
    if (fundamental) confidences.push(fundamental.confidence);
    if (sentiment) confidences.push(sentiment.confidence);

    if (confidences.length === 0) return 0;

    // Average confidence, but boost if multiple analyses agree
    const avgConfidence = confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
    const agreementBonus = confidences.length > 1 ? 0.1 : 0;

    return Math.min(1, avgConfidence + agreementBonus);
  }

  private generateRecommendations(
    score: number,
    signals: any[],
    technical: any,
    fundamental: any,
    sentiment: any
  ): any[] {
    const recommendations = [];

    // Generate recommendation based on combined score
    if (score > 60) {
      recommendations.push({
        action: score > 80 ? 'strong_buy' : 'buy',
        time_horizon: 'medium',
        reasoning: 'Positive signals from multiple analysis types',
        confidence: this.calculateConfidence(technical, fundamental, sentiment)
      });
    } else if (score < -60) {
      recommendations.push({
        action: score < -80 ? 'strong_sell' : 'sell',
        time_horizon: 'medium',
        reasoning: 'Negative signals from multiple analysis types',
        confidence: this.calculateConfidence(technical, fundamental, sentiment)
      });
    } else {
      recommendations.push({
        action: 'hold',
        time_horizon: 'short',
        reasoning: 'Mixed or neutral signals',
        confidence: 0.6
      });
    }

    return recommendations;
  }

  private assessRisk(marketData: MarketData[], technical: any, fundamental: any): any {
    // Calculate volatility
    const returns = this.calculateReturns(marketData);
    const volatility = this.calculateVolatility(returns);

    // Determine risk level
    let riskLevel: string;
    let riskScore: number;

    if (volatility < 0.02) {
      riskLevel = 'very_low';
      riskScore = 20;
    } else if (volatility < 0.05) {
      riskLevel = 'low';
      riskScore = 40;
    } else if (volatility < 0.10) {
      riskLevel = 'medium';
      riskScore = 60;
    } else if (volatility < 0.20) {
      riskLevel = 'high';
      riskScore = 80;
    } else {
      riskLevel = 'very_high';
      riskScore = 100;
    }

    const factors = ['Price volatility'];
    const mitigationStrategies = ['Diversification', 'Position sizing', 'Stop losses'];

    if (technical?.indicators?.rsi > 70) {
      factors.push('Overbought conditions');
      mitigationStrategies.push('Consider taking profits');
    }

    if (technical?.indicators?.rsi < 30) {
      factors.push('Oversold conditions');
      mitigationStrategies.push('Dollar cost averaging');
    }

    return {
      risk_level: riskLevel,
      risk_score: riskScore,
      factors,
      mitigation_strategies: mitigationStrategies
    };
  }

  private calculateReturns(marketData: MarketData[]): number[] {
    const returns = [];
    for (let i = 1; i < marketData.length; i++) {
      const returnRate = (marketData[i].close - marketData[i - 1].close) / marketData[i - 1].close;
      returns.push(returnRate);
    }
    return returns;
  }

  private calculateVolatility(returns: number[]): number {
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
    return Math.sqrt(variance);
  }

  private calculateVolatilities(marketData: Map<string, MarketData[]>): number[] {
    const volatilities = [];
    
    for (const [symbol, data] of marketData.entries()) {
      try {
        const returns = this.calculateReturns(data);
        const volatility = this.calculateVolatility(returns);
        volatilities.push(volatility);
      } catch (error) {
        console.warn(`Failed to calculate volatility for ${symbol}`);
      }
    }

    return volatilities;
  }

  private determineOverallSentiment(avgScore: number): 'bullish' | 'bearish' | 'neutral' {
    if (avgScore > 20) return 'bullish';
    if (avgScore < -20) return 'bearish';
    return 'neutral';
  }

  private determineVolatilityRegime(avgVolatility: number): 'low' | 'medium' | 'high' {
    if (avgVolatility < 0.05) return 'low';
    if (avgVolatility < 0.15) return 'medium';
    return 'high';
  }

  private calculateTrendStrength(analyses: AnalysisResult[]): number {
    const scores = analyses.map(a => Math.abs(a.score));
    return scores.reduce((sum, score) => sum + score, 0) / scores.length / 100;
  }

  private determineMarketPhase(
    analyses: AnalysisResult[],
    avgScore: number,
    trendStrength: number
  ): string {
    if (avgScore > 30 && trendStrength > 0.6) return MARKET_PHASES.MARKUP;
    if (avgScore < -30 && trendStrength > 0.6) return MARKET_PHASES.MARKDOWN;
    if (avgScore > 0 && trendStrength < 0.4) return MARKET_PHASES.ACCUMULATION;
    return MARKET_PHASES.DISTRIBUTION;
  }

  private calculateFearGreedIndex(avgScore: number, avgVolatility: number): number {
    // Simplified fear & greed calculation
    const sentimentComponent = (avgScore + 100) / 2; // Normalize to 0-100
    const volatilityComponent = Math.max(0, 100 - (avgVolatility * 1000)); // Lower volatility = higher greed
    
    return Math.round((sentimentComponent + volatilityComponent) / 2);
  }

  private checkCorrelationBreakdown(marketData: Map<string, MarketData[]>): boolean {
    // Simplified correlation breakdown detection
    // In reality, this would involve complex correlation analysis
    return Math.random() < 0.1; // 10% chance of correlation breakdown
  }

  private calculateCorrelation(data1: MarketData[], data2: MarketData[]): number {
    const minLength = Math.min(data1.length, data2.length);
    const returns1 = this.calculateReturns(data1.slice(-minLength));
    const returns2 = this.calculateReturns(data2.slice(-minLength));

    if (returns1.length !== returns2.length || returns1.length === 0) {
      return 0;
    }

    const mean1 = returns1.reduce((sum, ret) => sum + ret, 0) / returns1.length;
    const mean2 = returns2.reduce((sum, ret) => sum + ret, 0) / returns2.length;

    let numerator = 0;
    let sumSq1 = 0;
    let sumSq2 = 0;

    for (let i = 0; i < returns1.length; i++) {
      const diff1 = returns1[i] - mean1;
      const diff2 = returns2[i] - mean2;
      
      numerator += diff1 * diff2;
      sumSq1 += diff1 * diff1;
      sumSq2 += diff2 * diff2;
    }

    const denominator = Math.sqrt(sumSq1 * sumSq2);
    return denominator === 0 ? 0 : numerator / denominator;
  }

  private detectPriceAnomalies(marketData: MarketData[], lookback: number): any[] {
    const anomalies = [];
    
    for (let i = lookback; i < marketData.length; i++) {
      const recentData = marketData.slice(i - lookback, i);
      const avgPrice = recentData.reduce((sum, d) => sum + d.close, 0) / recentData.length;
      const stdDev = Math.sqrt(
        recentData.reduce((sum, d) => sum + Math.pow(d.close - avgPrice, 2), 0) / recentData.length
      );

      const currentPrice = marketData[i].close;
      const zScore = Math.abs((currentPrice - avgPrice) / stdDev);

      if (zScore > 3) { // 3 standard deviations
        anomalies.push({
          timestamp: marketData[i].timestamp,
          type: 'price_anomaly',
          severity: Math.min(100, zScore * 20),
          description: `Price deviation of ${zScore.toFixed(2)} standard deviations`
        });
      }
    }

    return anomalies;
  }

  private detectVolumeAnomalies(marketData: MarketData[], lookback: number): any[] {
    const anomalies = [];
    
    for (let i = lookback; i < marketData.length; i++) {
      const recentData = marketData.slice(i - lookback, i);
      const avgVolume = recentData.reduce((sum, d) => sum + d.volume, 0) / recentData.length;
      
      const currentVolume = marketData[i].volume;
      const volumeRatio = currentVolume / avgVolume;

      if (volumeRatio > 5) { // 5x average volume
        anomalies.push({
          timestamp: marketData[i].timestamp,
          type: 'volume_spike',
          severity: Math.min(100, volumeRatio * 10),
          description: `Volume spike: ${volumeRatio.toFixed(2)}x average`
        });
      }
    }

    return anomalies;
  }

  private detectVolatilityAnomalies(marketData: MarketData[], lookback: number): any[] {
    const anomalies = [];
    
    for (let i = lookback; i < marketData.length; i++) {
      const recentData = marketData.slice(i - lookback, i);
      const returns = this.calculateReturns(recentData);
      const avgVolatility = this.calculateVolatility(returns);
      
      const currentReturn = Math.abs(
        (marketData[i].close - marketData[i - 1].close) / marketData[i - 1].close
      );

      if (currentReturn > avgVolatility * 3) {
        anomalies.push({
          timestamp: marketData[i].timestamp,
          type: 'volatility_spike',
          severity: Math.min(100, (currentReturn / avgVolatility) * 20),
          description: `Volatility spike: ${(currentReturn * 100).toFixed(2)}% move`
        });
      }
    }

    return anomalies;
  }
}
