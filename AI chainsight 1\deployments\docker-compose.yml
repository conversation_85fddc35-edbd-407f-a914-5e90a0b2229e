version: '3.8'

services:
  # Frontend Application
  frontend:
    build:
      context: ../apps/frontend
      dockerfile: ../../deployments/frontend/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://backend:8000
      - NEXT_PUBLIC_WS_URL=ws://backend:8000/ws
    depends_on:
      - backend
    networks:
      - chainsight-network
    restart: unless-stopped
    volumes:
      - frontend-data:/app/.next
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ../apps/backend
      dockerfile: ../../deployments/backend/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=***************************************************/chainsight
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-super-secret-key-change-in-production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=production
      - CORS_ORIGINS=http://localhost:3000,http://frontend:3000
    depends_on:
      - postgres
      - redis
    networks:
      - chainsight-network
    restart: unless-stopped
    volumes:
      - backend-uploads:/app/uploads
      - backend-logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=chainsight
      - POSTGRES_USER=chainsight
      - POSTGRES_PASSWORD=chainsight123
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - chainsight-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chainsight"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - chainsight-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass chainsight123
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - frontend
      - backend
    networks:
      - chainsight-network
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx-logs:/var/log/nginx
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker (for background tasks)
  celery-worker:
    build:
      context: ../apps/backend
      dockerfile: ../../deployments/backend/Dockerfile
    command: celery -A app.celery worker --loglevel=info
    environment:
      - DATABASE_URL=***************************************************/chainsight
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-super-secret-key-change-in-production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=production
    depends_on:
      - postgres
      - redis
    networks:
      - chainsight-network
    restart: unless-stopped
    volumes:
      - backend-uploads:/app/uploads
      - backend-logs:/app/logs

  # Celery Beat (for scheduled tasks)
  celery-beat:
    build:
      context: ../apps/backend
      dockerfile: ../../deployments/backend/Dockerfile
    command: celery -A app.celery beat --loglevel=info
    environment:
      - DATABASE_URL=***************************************************/chainsight
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-super-secret-key-change-in-production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=production
    depends_on:
      - postgres
      - redis
    networks:
      - chainsight-network
    restart: unless-stopped
    volumes:
      - backend-uploads:/app/uploads
      - backend-logs:/app/logs

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - chainsight-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - chainsight-network
    restart: unless-stopped

networks:
  chainsight-network:
    driver: bridge

volumes:
  postgres-data:
  redis-data:
  frontend-data:
  backend-uploads:
  backend-logs:
  nginx-logs:
  prometheus-data:
  grafana-data:
