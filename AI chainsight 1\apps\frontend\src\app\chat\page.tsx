'use client'

import { motion } from 'framer-motion'
import { MessageSquare } from 'lucide-react'
import { AIAssistant } from '@/components/AIAssistant'

export default function ChatPage() {
  return (
    <div className="min-h-screen pt-20 pb-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center space-x-2 mb-4">
            <MessageSquare className="w-8 h-8 text-luxury-gold" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-luxury-gold to-yellow-400 bg-clip-text text-transparent">
              Connectouch AI Assistant
            </h1>
          </div>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            Your intelligent companion for navigating Chainsight's features and capabilities
          </p>
        </motion.div>

        {/* AI Assistant Component */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="h-[600px]"
        >
          <AIAssistant />
        </motion.div>

        {/* Feature Highlights */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6"
        >
          {[
            {
              title: 'Natural Language Processing',
              description: 'Communicate naturally with advanced AI understanding'
            },
            {
              title: 'Multi-Domain Expertise',
              description: 'Get help with crypto, finance, AI, and enterprise features'
            },
            {
              title: 'Real-time Responses',
              description: 'Instant, contextual answers powered by OpenAI'
            }
          ].map((feature, index) => (
            <div key={index} className="luxury-card text-center">
              <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
              <p className="text-gray-400 text-sm">{feature.description}</p>
            </div>
          ))}
        </motion.div>
      </div>
    </div>
  )
}
