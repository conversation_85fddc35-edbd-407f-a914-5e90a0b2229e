{"name": "@chainsight/animation-designer", "version": "1.0.0", "description": "Advanced SVG and Lottie animation generator with real export functionality", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"lottie-web": "^5.12.2", "svg.js": "^3.2.0", "canvas": "^2.11.2", "sharp": "^0.32.5", "jszip": "^3.10.1", "uuid": "^9.0.0", "lodash": "^4.17.21", "color": "^4.2.3", "bezier-easing": "^2.1.0"}, "devDependencies": {"@types/lodash": "^4.14.195", "@types/node": "^20.5.0", "@types/uuid": "^9.0.2", "@types/color": "^3.0.3", "typescript": "^5.1.6", "jest": "^29.6.2", "@types/jest": "^29.5.3", "ts-jest": "^29.1.1", "eslint": "^8.46.0", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1"}, "keywords": ["animation", "svg", "lottie", "motion-graphics", "design", "export"], "author": "Chainsight Team", "license": "MIT"}