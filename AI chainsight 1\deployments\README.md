# Chainsight Deployment Guide

This directory contains all the necessary files to deploy the Chainsight platform using Docker and Docker Compose.

## Quick Start

1. **Prerequisites**
   ```bash
   # Install Docker and Docker Compose
   # Make sure you have at least 8GB RAM and 20GB disk space
   ```

2. **Environment Setup**
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Edit .env file with your configuration
   nano .env
   ```

3. **Deploy the Platform**
   ```bash
   # Build and start all services
   docker-compose up -d
   
   # Check service status
   docker-compose ps
   
   # View logs
   docker-compose logs -f
   ```

4. **Access the Platform**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - Grafana Monitoring: http://localhost:3001 (admin/admin123)
   - Prometheus Metrics: http://localhost:9090

## Architecture

```
┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │    Frontend     │
│  (Port 80/443)  │────│   (Port 3000)   │
└─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐
│     Backend     │    │   PostgreSQL    │
│   (Port 8000)   │────│   (Port 5432)   │
└─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐
│      Redis      │    │ Celery Workers  │
│   (Port 6379)   │────│  (Background)   │
└─────────────────┘    └─────────────────┘
```

## Services

### Core Services
- **Frontend**: Next.js application serving the user interface
- **Backend**: FastAPI application providing REST API and WebSocket endpoints
- **PostgreSQL**: Primary database for persistent data storage
- **Redis**: Cache and message broker for background tasks
- **Nginx**: Reverse proxy and load balancer

### Background Services
- **Celery Worker**: Processes background tasks (AI analysis, file processing)
- **Celery Beat**: Scheduler for periodic tasks

### Monitoring Services
- **Prometheus**: Metrics collection and monitoring
- **Grafana**: Visualization and alerting dashboard

## Configuration

### Environment Variables

Create a `.env` file in the deployments directory:

```bash
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Database Configuration
POSTGRES_DB=chainsight
POSTGRES_USER=chainsight
POSTGRES_PASSWORD=chainsight123

# Redis Configuration
REDIS_PASSWORD=chainsight123

# Security
SECRET_KEY=your-super-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key

# Application Settings
ENVIRONMENT=production
DEBUG=false
CORS_ORIGINS=http://localhost:3000

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Monitoring
GRAFANA_ADMIN_PASSWORD=admin123
```

### SSL/HTTPS Configuration

For production deployment with SSL:

1. Place your SSL certificates in `deployments/nginx/ssl/`
2. Uncomment the HTTPS server block in `nginx/nginx.conf`
3. Update the certificate paths in the configuration

## Scaling

### Horizontal Scaling

Scale individual services:

```bash
# Scale backend workers
docker-compose up -d --scale backend=3

# Scale celery workers
docker-compose up -d --scale celery-worker=5

# Scale frontend instances
docker-compose up -d --scale frontend=2
```

### Resource Limits

Add resource limits to `docker-compose.yml`:

```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

## Monitoring and Logging

### Health Checks

All services include health checks. Monitor service health:

```bash
# Check all service health
docker-compose ps

# View specific service logs
docker-compose logs backend

# Follow logs in real-time
docker-compose logs -f --tail=100
```

### Metrics and Monitoring

- **Prometheus**: Collects metrics from all services
- **Grafana**: Provides dashboards for visualization
- **Application Logs**: Centralized logging for debugging

Access Grafana at http://localhost:3001 with admin/admin123

### Log Management

Logs are stored in Docker volumes and can be accessed:

```bash
# View application logs
docker-compose exec backend tail -f /app/logs/app.log

# View nginx access logs
docker-compose exec nginx tail -f /var/log/nginx/access.log
```

## Backup and Recovery

### Database Backup

```bash
# Create database backup
docker-compose exec postgres pg_dump -U chainsight chainsight > backup.sql

# Restore from backup
docker-compose exec -T postgres psql -U chainsight chainsight < backup.sql
```

### Volume Backup

```bash
# Backup all volumes
docker run --rm -v chainsight_postgres-data:/data -v $(pwd):/backup alpine tar czf /backup/postgres-backup.tar.gz -C /data .
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check if ports are in use
   netstat -tulpn | grep :3000
   
   # Change ports in docker-compose.yml if needed
   ```

2. **Memory Issues**
   ```bash
   # Check Docker memory usage
   docker stats
   
   # Increase Docker memory limit in Docker Desktop
   ```

3. **Database Connection Issues**
   ```bash
   # Check database logs
   docker-compose logs postgres
   
   # Test database connection
   docker-compose exec postgres psql -U chainsight -d chainsight -c "SELECT 1;"
   ```

4. **SSL Certificate Issues**
   ```bash
   # Generate self-signed certificates for testing
   openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
     -keyout nginx/ssl/key.pem -out nginx/ssl/cert.pem
   ```

### Service Restart

```bash
# Restart specific service
docker-compose restart backend

# Restart all services
docker-compose restart

# Rebuild and restart
docker-compose up -d --build
```

## Security Considerations

### Production Security Checklist

- [ ] Change all default passwords
- [ ] Use strong, unique secret keys
- [ ] Enable SSL/HTTPS
- [ ] Configure firewall rules
- [ ] Set up regular backups
- [ ] Enable monitoring and alerting
- [ ] Update Docker images regularly
- [ ] Use non-root users in containers
- [ ] Implement rate limiting
- [ ] Configure CORS properly

### Network Security

```bash
# Create custom network with encryption
docker network create --driver overlay --opt encrypted chainsight-secure
```

## Maintenance

### Updates

```bash
# Pull latest images
docker-compose pull

# Rebuild and restart
docker-compose up -d --build

# Clean up old images
docker image prune -f
```

### Cleanup

```bash
# Stop all services
docker-compose down

# Remove volumes (WARNING: This deletes all data)
docker-compose down -v

# Clean up everything
docker system prune -a
```

## Support

For issues and support:
1. Check the logs: `docker-compose logs`
2. Review this documentation
3. Check the main project README
4. Create an issue in the project repository

## Performance Tuning

### Database Optimization

```sql
-- Connect to PostgreSQL and run optimization queries
-- Analyze table statistics
ANALYZE;

-- Check slow queries
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;
```

### Redis Optimization

```bash
# Monitor Redis performance
docker-compose exec redis redis-cli info stats
docker-compose exec redis redis-cli monitor
```

### Application Performance

- Monitor response times in Grafana
- Check memory usage and CPU utilization
- Optimize database queries
- Implement caching strategies
- Use CDN for static assets
