module.exports = {

"[project]/node_modules/@rainbow-me/rainbowkit/dist/tr_TR-P7QAUUZU.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>tr_TR_default)
});
"use client";
// src/locales/tr_TR.json
var tr_TR_default = `{
  "connect_wallet": {
    "label": "C\xFCzdan\u0131 Ba\u011Fla",
    "wrong_network": {
      "label": "Yanl\u0131\u015F a\u011F"
    }
  },
  "intro": {
    "title": "C\xFCzdan nedir?",
    "description": "Bir c\xFCzdan, dijital varl\u0131klar\u0131 g\xF6ndermek, almak, saklamak ve g\xF6r\xFCnt\xFClemek i\xE7in kullan\u0131l\u0131r. <PERSON>yn\u0131 zamanda her web sitesinde yeni hesaplar ve \u015Fifreler olu\u015Fturman\u0131za gerek kalmadan oturum a\xE7man\u0131n yeni bir yoludur.",
    "digital_asset": {
      "title": "Dijital Varl\u0131klar\u0131n\u0131z \u0130\xE7in Bir Ev",
      "description": "C\xFCzdanlar, Ethereum ve NFT'ler gibi dijital varl\u0131klar\u0131 g\xF6ndermek, almak, depolamak ve g\xF6r\xFCnt\xFClemek i\xE7in kullan\u0131l\u0131r."
    },
    "login": {
      "title": "Yeni Bir Giri\u015F Yolu",
      "description": "Her web sitesinde yeni hesap ve parolalar olu\u015Fturmak yerine, sadece c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flay\u0131n."
    },
    "get": {
      "label": "Bir C\xFCzdan Edinin"
    },
    "learn_more": {
      "label": "Daha fazla bilgi edinin"
    }
  },
  "sign_in": {
    "label": "Hesab\u0131n\u0131z\u0131 do\u011Frulay\u0131n",
    "description": "Ba\u011Flant\u0131y\u0131 tamamlamak i\xE7in, bu hesab\u0131n sahibi oldu\u011Funuzu do\u011Frulamak i\xE7in c\xFCzdan\u0131n\u0131zdaki bir mesaja imza atmal\u0131s\u0131n\u0131z.",
    "message": {
      "send": "Mesaj\u0131 g\xF6nder",
      "preparing": "Mesaj haz\u0131rlan\u0131yor...",
      "cancel": "\u0130ptal",
      "preparing_error": "Mesaj\u0131 haz\u0131rlarken hata olu\u015Ftu, l\xFCtfen tekrar deneyin!"
    },
    "signature": {
      "waiting": "\u0130mza bekleniyor...",
      "verifying": "\u0130mza do\u011Frulan\u0131yor...",
      "signing_error": "Mesaj\u0131 imzalarken hata olu\u015Ftu, l\xFCtfen tekrar deneyin!",
      "verifying_error": "\u0130mza do\u011Frulan\u0131rken hata olu\u015Ftu, l\xFCtfen tekrar deneyin!",
      "oops_error": "Hata, bir \u015Feyler yanl\u0131\u015F gitti!"
    }
  },
  "connect": {
    "label": "Ba\u011Flan",
    "title": "Bir C\xFCzdan\u0131 Ba\u011Fla",
    "new_to_ethereum": {
      "description": "Ethereum c\xFCzdanlar\u0131na yeni misiniz?",
      "learn_more": {
        "label": "Daha fazla bilgi edinin"
      }
    },
    "learn_more": {
      "label": "Daha fazla bilgi edinin"
    },
    "recent": "Son",
    "status": {
      "opening": "%{wallet}a\xE7\u0131l\u0131yor...",
      "connecting": "Ba\u011Flan\u0131yor",
      "connect_mobile": "%{wallet}'da devam edin",
      "not_installed": "%{wallet} y\xFCkl\xFC de\u011Fil",
      "not_available": "%{wallet} kullan\u0131labilir de\u011Fil",
      "confirm": "Ba\u011Flant\u0131y\u0131 eklentide onaylay\u0131n",
      "confirm_mobile": "C\xFCzdan\u0131nda ba\u011Flant\u0131 iste\u011Fini kabul et"
    },
    "secondary_action": {
      "get": {
        "description": "%{wallet}yok mu?",
        "label": "AL"
      },
      "install": {
        "label": "Y\xDCKLE"
      },
      "retry": {
        "label": "YEN\u0130DEN DENE"
      }
    },
    "walletconnect": {
      "description": {
        "full": "Resmi WalletConnect modal\u0131na m\u0131 ihtiyac\u0131n\u0131z var?",
        "compact": "WalletConnect modal\u0131na m\u0131 ihtiyac\u0131n\u0131z var?"
      },
      "open": {
        "label": "A\xC7"
      }
    }
  },
  "connect_scan": {
    "title": "%{wallet}ile tarama yap\u0131n",
    "fallback_title": "Telefonunuzla tarama yap\u0131n"
  },
  "connector_group": {
    "installed": "Y\xFCklendi",
    "recommended": "Tavsiye Edilen",
    "other": "Di\u011Fer",
    "popular": "Pop\xFCler",
    "more": "Daha Fazla",
    "others": "Di\u011Ferleri"
  },
  "get": {
    "title": "Bir C\xFCzdan Edinin",
    "action": {
      "label": "AL"
    },
    "mobile": {
      "description": "Mobil C\xFCzdan"
    },
    "extension": {
      "description": "Taray\u0131c\u0131 Eklentisi"
    },
    "mobile_and_extension": {
      "description": "Mobil C\xFCzdan ve Eklenti"
    },
    "mobile_and_desktop": {
      "description": "Mobil ve Masa\xFCst\xFC C\xFCzdan"
    },
    "looking_for": {
      "title": "Arad\u0131\u011F\u0131n\u0131z \u015Fey bu de\u011Fil mi?",
      "mobile": {
        "description": "Ana ekranda ba\u015Fka bir c\xFCzdan sa\u011Flay\u0131c\u0131s\u0131yla ba\u015Flamak i\xE7in bir c\xFCzdan se\xE7in."
      },
      "desktop": {
        "compact_description": "Ana ekranda ba\u015Fka bir c\xFCzdan sa\u011Flay\u0131c\u0131s\u0131yla ba\u015Flamak i\xE7in bir c\xFCzdan se\xE7in.",
        "wide_description": "Ba\u015Fka bir c\xFCzdan sa\u011Flay\u0131c\u0131s\u0131yla ba\u015Flamak i\xE7in sol tarafta bir c\xFCzdan se\xE7in."
      }
    }
  },
  "get_options": {
    "title": "%{wallet}ile ba\u015Flay\u0131n",
    "short_title": "%{wallet}Edinin",
    "mobile": {
      "title": "%{wallet} Mobil \u0130\xE7in",
      "description": "Mobil c\xFCzdan\u0131 kullanarak Ethereum d\xFCnyas\u0131n\u0131 ke\u015Ffedin.",
      "download": {
        "label": "Uygulamay\u0131 al\u0131n"
      }
    },
    "extension": {
      "title": "%{wallet} i\xE7in %{browser}",
      "description": "C\xFCzdan\u0131n\u0131za favori web taray\u0131c\u0131n\u0131zdan do\u011Frudan eri\u015Fin.",
      "download": {
        "label": "%{browser}'e ekle"
      }
    },
    "desktop": {
      "title": "%{wallet} i\xE7in %{platform}",
      "description": "G\xFC\xE7l\xFC masa\xFCst\xFCn\xFCzden c\xFCzdan\u0131n\u0131za yerel olarak eri\u015Fin.",
      "download": {
        "label": "%{platform}ekleyin"
      }
    }
  },
  "get_mobile": {
    "title": "%{wallet}'i y\xFCkleyin",
    "description": "iOS veya Android'de indirmek i\xE7in telefonunuzla taray\u0131n",
    "continue": {
      "label": "Devam et"
    }
  },
  "get_instructions": {
    "mobile": {
      "connect": {
        "label": "Ba\u011Flan"
      },
      "learn_more": {
        "label": "Daha fazla bilgi edinin"
      }
    },
    "extension": {
      "refresh": {
        "label": "Yenile"
      },
      "learn_more": {
        "label": "Daha fazla bilgi edinin"
      }
    },
    "desktop": {
      "connect": {
        "label": "Ba\u011Flan"
      },
      "learn_more": {
        "label": "Daha fazla bilgi edinin"
      }
    }
  },
  "chains": {
    "title": "A\u011Flar\u0131 De\u011Fi\u015Ftir",
    "wrong_network": "Yanl\u0131\u015F a\u011F alg\u0131land\u0131, devam etmek i\xE7in ba\u011Flant\u0131y\u0131 kesin veya de\u011Fi\u015Ftirin.",
    "confirm": "C\xFCzdan\u0131nda Onayla",
    "switching_not_supported": "C\xFCzdan\u0131n\u0131z %{appName}. a\u011Flar\u0131 de\u011Fi\u015Ftirmeyi desteklemiyor. Bunun yerine c\xFCzdan\u0131n\u0131zdan a\u011Flar\u0131 de\u011Fi\u015Ftirmeyi deneyin.",
    "switching_not_supported_fallback": "C\xFCzdan\u0131n\u0131z bu uygulamadan a\u011Flar\u0131 de\u011Fi\u015Ftirmeyi desteklemiyor. Bunun yerine c\xFCzdan\u0131n\u0131zdaki a\u011Flar\u0131 de\u011Fi\u015Ftirmeyi deneyin.",
    "disconnect": "Ba\u011Flant\u0131y\u0131 Kes",
    "connected": "Ba\u011Fl\u0131"
  },
  "profile": {
    "disconnect": {
      "label": "Ba\u011Flant\u0131y\u0131 Kes"
    },
    "copy_address": {
      "label": "Adresi Kopyala",
      "copied": "Kopyaland\u0131!"
    },
    "explorer": {
      "label": "Explorer \xFCzerinde daha fazlas\u0131n\u0131 g\xF6r\xFCn"
    },
    "transactions": {
      "description": "%{appName} i\u015Flem burada g\xF6r\xFCnecek...",
      "description_fallback": "\u0130\u015Flemleriniz burada g\xF6r\xFCnecek...",
      "recent": {
        "title": "Son \u0130\u015Flemler"
      },
      "clear": {
        "label": "Hepsini Temizle"
      }
    }
  },
  "wallet_connectors": {
    "argent": {
      "qr_code": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Argent'i ana ekran\u0131n\u0131za koyun.",
          "title": "Argent uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "Bir c\xFCzdan ve kullan\u0131c\u0131 ad\u0131 olu\u015Fturun veya mevcut bir c\xFCzdan\u0131 i\xE7e aktar\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "QR taray\u0131c\u0131 d\xFC\u011Fmesine dokunun"
        }
      }
    },
    "berasig": {
      "extension": {
        "step1": {
          "title": "BeraSig eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha kolay eri\u015Fim sa\u011Flamak i\xE7in BeraSig'i g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin."
        }
      }
    },
    "best": {
      "qr_code": {
        "step1": {
          "title": "Best Wallet uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Best Wallet'\u0131 ana ekran\u0131n\u0131za ekleyin."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "QR simgesine dokunun ve taray\u0131n",
          "description": "Ana ekran\u0131n\u0131zdaki QR simgesine dokunun, kodu taray\u0131n ve ba\u011Flanmay\u0131 onaylamak i\xE7in istemi kabul edin."
        }
      }
    },
    "bifrost": {
      "qr_code": {
        "step1": {
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in Bifrost C\xFCzdan'\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz.",
          "title": "Bifrost C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "Kurtarma ifadenizle bir c\xFCzdan olu\u015Fturun veya i\xE7e aktar\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarama i\u015Flemi sonras\u0131nda, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6z\xFCkecektir.",
          "title": "Taray\u0131c\u0131 d\xFC\u011Fmesine dokunun"
        }
      }
    },
    "bitget": {
      "qr_code": {
        "step1": {
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in Bitget C\xFCzdan\u0131n\u0131z\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz.",
          "title": "Bitget C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarama yapt\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "Tarama d\xFC\u011Fmesine dokunun"
        }
      },
      "extension": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Bitget C\xFCzdan\u0131n\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz.",
          "title": "Bitget C\xFCzdan eklentisini y\xFCkleyin"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemekten emin olun. Gizli ifadenizi hi\xE7 kimseyle payla\u015Fmay\u0131n.",
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n"
        },
        "step3": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      }
    },
    "bitski": {
      "extension": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Bitski'yi g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz.",
          "title": "Bitski eklentisini y\xFCkleyin"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekledi\u011Finizden emin olun. Gizli ifadenizi kimseyle payla\u015Fmay\u0131n.",
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n"
        },
        "step3": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      }
    },
    "bitverse": {
      "qr_code": {
        "step1": {
          "title": "Bitverse C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Bitverse C\xFCzdan'\u0131 ana ekran\u0131n\u0131za ekleyin."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "QR simgesine dokunun ve taray\u0131n",
          "description": "Ana ekran\u0131n\u0131zdaki QR simgesine dokunun, kodu taray\u0131n ve ba\u011Flanmay\u0131 onaylamak i\xE7in istemi kabul edin."
        }
      }
    },
    "bloom": {
      "desktop": {
        "step1": {
          "title": "Bloom C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in Bloom C\xFCzdan'\u0131 ana ekran\u0131n\u0131za koymay\u0131 \xF6neririz."
        },
        "step2": {
          "description": "Kurtarma ifadenizle bir c\xFCzdan olu\u015Fturun veya i\xE7e aktar\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Bir c\xFCzdan\u0131n\u0131z olduktan sonra, Bloom \xFCzerinden ba\u011Flanmak i\xE7in Ba\u011Flan'a t\u0131klay\u0131n. Uygulamada ba\u011Flant\u0131y\u0131 onaylaman\u0131z i\xE7in bir ba\u011Flant\u0131 istemi belirecektir.",
          "title": "Ba\u011Flan'a t\u0131klay\u0131n"
        }
      }
    },
    "bybit": {
      "qr_code": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Bybit'i ana ekran\u0131n\u0131za koymay\u0131 \xF6neririz.",
          "title": "Bybit uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "Telefonunuzdaki yedekleme \xF6zelli\u011Fimizi kullanarak c\xFCzdan\u0131n\u0131z\u0131 kolayca yedekleyebilirsiniz.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "Taray\u0131c\u0131 d\xFC\u011Fmesine dokunun"
        }
      },
      "extension": {
        "step1": {
          "description": "Taray\u0131c\u0131n\u0131z\u0131n sa\u011F \xFCst k\xF6\u015Fesine t\u0131klay\u0131n ve kolay eri\u015Fim i\xE7in Bybit C\xFCzdan'\u0131 sabitleyin.",
          "title": "Bybit C\xFCzdan uzant\u0131s\u0131n\u0131 y\xFCkleyin"
        },
        "step2": {
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n.",
          "title": "Bir c\xFCzdan olu\u015Fturun veya i\xE7e aktar\u0131n"
        },
        "step3": {
          "description": "Bybit C\xFCzdan'\u0131 ayarlad\u0131ktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve uzant\u0131y\u0131 y\xFCklemek i\xE7in a\u015Fa\u011F\u0131daki butona t\u0131klay\u0131n.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      }
    },
    "binance": {
      "qr_code": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Binance'u ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz.",
          "title": "Binance uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "Telefonunuzdaki yedekleme \xF6zelli\u011Fimizi kullanarak c\xFCzdan\u0131n\u0131z\u0131 kolayca yedekleyebilirsiniz.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "C\xFCzdanBa\u011Flant\u0131s\u0131 d\xFC\u011Fmesine dokunun"
        }
      }
    },
    "coin98": {
      "qr_code": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Coin98 C\xFCzdan\u0131n\u0131z\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz.",
          "title": "Coin98 C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "Telefonunuzdaki yedekleme \xF6zelli\u011Fimizi kullanarak c\xFCzdan\u0131n\u0131z\u0131 kolayca yedekleyebilirsiniz.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarama i\u015Flemi yapt\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "C\xFCzdanBa\u011Flant\u0131s\u0131 d\xFC\u011Fmesine dokunun"
        }
      },
      "extension": {
        "step1": {
          "description": "Taray\u0131c\u0131n\u0131z\u0131n sa\u011F \xFCst k\xF6\u015Fesinde t\u0131klay\u0131n ve Coin98 C\xFCzdan\u0131n\u0131z\u0131 kolay eri\u015Fim i\xE7in sabitleyin.",
          "title": "Coin98 C\xFCzdan eklentisini y\xFCkleyin"
        },
        "step2": {
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n.",
          "title": "Bir c\xFCzdan olu\u015Fturun veya i\xE7e aktar\u0131n"
        },
        "step3": {
          "description": "Coin98 C\xFCzdan'\u0131 kurduktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      }
    },
    "coinbase": {
      "qr_code": {
        "step1": {
          "description": "Coinbase C\xFCzdan'\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz, b\xF6ylece daha h\u0131zl\u0131 eri\u015Fim sa\u011Flan\u0131r.",
          "title": "Coinbase Wallet uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 bulut yedekleme \xF6zelli\u011Fini kullanarak kolayca yedekleyebilirsiniz.",
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n"
        },
        "step3": {
          "description": "Tarama yapt\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flaman\u0131z i\xE7in bir ba\u011Flant\u0131 istemi belirecektir.",
          "title": "Tarama d\xFC\u011Fmesine dokunun"
        }
      },
      "extension": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Coinbase Wallet'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz.",
          "title": "Coinbase Wallet uzant\u0131s\u0131n\u0131 y\xFCkleyin"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekleyin. Gizli ifadenizi asla ba\u015Fkalar\u0131yla payla\u015Fmay\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      }
    },
    "compass": {
      "extension": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Compass Wallet'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz.",
          "title": "Compass Wallet uzant\u0131s\u0131n\u0131 y\xFCkleyin"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      }
    },
    "core": {
      "qr_code": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Core'u ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz.",
          "title": "Core uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131n yede\u011Fini telefonunuzda bulunan yedekleme \xF6zelli\u011Fimizi kullanarak kolayca alabilirsiniz.",
          "title": "C\xFCzdan Olu\u015Ftur veya \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarama yapt\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak \xFCzere bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "WalletConnect d\xFC\u011Fmesine dokunun"
        }
      },
      "extension": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Core'u g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz.",
          "title": "Core eklentisini y\xFCkleyin"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye dikkat edin. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n.",
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n"
        },
        "step3": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klayarak taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      }
    },
    "fox": {
      "qr_code": {
        "step1": {
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in FoxWallet'\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz.",
          "title": "FoxWallet uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekledi\u011Finizden emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarama yapt\u0131ktan sonra c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flaman\u0131z i\xE7in bir ba\u011Flant\u0131 istemi belirecektir.",
          "title": "Tarama d\xFC\u011Fmesine dokunun"
        }
      }
    },
    "frontier": {
      "qr_code": {
        "step1": {
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in Frontier C\xFCzdan\u0131n\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz.",
          "title": "Frontier C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekledi\u011Finizden emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Taramadan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "Tarama d\xFC\u011Fmesine dokunun"
        }
      },
      "extension": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Frontier C\xFCzdan\u0131n\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz.",
          "title": "Frontier C\xFCzdan eklentisini y\xFCkleyin"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedekledi\u011Finizden emin olun. Gizli ifadenizi hi\xE7 kimseyle payla\u015Fmay\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 ayarlad\u0131ktan sonra, taray\u0131c\u0131y\u0131 yenilemeye ve eklentiyi y\xFCklemeye ba\u015Flamak i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 Yenileyin"
        }
      }
    },
    "im_token": {
      "qr_code": {
        "step1": {
          "title": "imToken uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in imToken uygulamas\u0131n\u0131 ana ekran\u0131n\u0131za koyun."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut bir c\xFCzdan\u0131 i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "Sa\u011F \xFCst k\xF6\u015Fede Taray\u0131c\u0131 Simgesine dokunun",
          "description": "Yeni Ba\u011Flant\u0131'y\u0131 se\xE7in, ard\u0131ndan QR kodunu taray\u0131n ve ba\u011Flant\u0131y\u0131 onaylamak i\xE7in istemi onaylay\u0131n."
        }
      }
    },
    "iopay": {
      "qr_code": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in ioPay'i ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz.",
          "title": "ioPay uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "Telefonunuzdaki yedekleme \xF6zelli\u011Fimizi kullanarak c\xFCzdan\u0131n\u0131z\u0131 kolayca yedekleyebilirsiniz.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "C\xFCzdanBa\u011Flant\u0131s\u0131 d\xFC\u011Fmesine dokunun"
        }
      }
    },
    "kaikas": {
      "extension": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Kaikas'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemeyi \xF6neririz.",
          "title": "Kaikas uzant\u0131s\u0131n\u0131 y\xFCkleyin"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      },
      "qr_code": {
        "step1": {
          "title": "Kaikas uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Kaikas uygulamas\u0131n\u0131 ana ekran\u0131n\u0131za koyun."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "Sa\u011F \xFCst k\xF6\u015Fede Taray\u0131c\u0131 Simgesine dokunun",
          "description": "Yeni Ba\u011Flant\u0131'y\u0131 se\xE7in, ard\u0131ndan QR kodunu taray\u0131n ve ba\u011Flant\u0131y\u0131 onaylamak i\xE7in istemi onaylay\u0131n."
        }
      }
    },
    "kaia": {
      "extension": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Kaia'y\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz.",
          "title": "Kaia uzant\u0131s\u0131n\u0131 y\xFCkleyin"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      },
      "qr_code": {
        "step1": {
          "title": "Kaia uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Kaia uygulamas\u0131n\u0131 ana ekran\u0131n\u0131za koyun."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "Sa\u011F \xFCst k\xF6\u015Fede Taray\u0131c\u0131 Simgesine dokunun",
          "description": "Yeni Ba\u011Flant\u0131'y\u0131 se\xE7in, ard\u0131ndan QR kodunu taray\u0131n ve ba\u011Flant\u0131y\u0131 onaylamak i\xE7in istemi onaylay\u0131n."
        }
      }
    },
    "kraken": {
      "qr_code": {
        "step1": {
          "title": "Kraken Wallet uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Kraken Wallet'\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "QR simgesine dokunun ve taray\u0131n",
          "description": "Ana ekran\u0131n\u0131zdaki QR simgesine dokunun, kodu taray\u0131n ve ba\u011Flanmay\u0131 onaylamak i\xE7in istemi kabul edin."
        }
      }
    },
    "kresus": {
      "qr_code": {
        "step1": {
          "title": "Kresus C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Kresus C\xFCzdan\u0131n\u0131 ana ekran\u0131n\u0131za ekleyin."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "QR simgesine dokunun ve taray\u0131n",
          "description": "Ana ekran\u0131n\u0131zdaki QR simgesine dokunun, kodu taray\u0131n ve ba\u011Flanmay\u0131 onaylamak i\xE7in istemi kabul edin."
        }
      }
    },
    "magicEden": {
      "extension": {
        "step1": {
          "title": "Magic Eden eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha kolay eri\u015Fim sa\u011Flamak i\xE7in Magic Eden'i g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekledi\u011Finizden emin olun. Gizli kurtarma ifadenizi kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin."
        }
      }
    },
    "metamask": {
      "qr_code": {
        "step1": {
          "title": "MetaMask uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in MetaMask'\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekleyin. Gizli kurtarma ifadenizi asla ba\u015Fkalar\u0131yla payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Tarama d\xFC\u011Fmesine dokunun",
          "description": "Taramay\u0131 yapt\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi belirecektir."
        }
      },
      "extension": {
        "step1": {
          "title": "MetaMask eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in MetaMask'i g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedekledi\u011Finizden emin olun. Gizli ifadenizi hi\xE7 kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 Yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 ayarlad\u0131ktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      }
    },
    "nestwallet": {
      "extension": {
        "step1": {
          "title": "NestWallet eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim sa\u011Flamak i\xE7in NestWallet'i g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin."
        }
      }
    },
    "okx": {
      "qr_code": {
        "step1": {
          "title": "OKX Wallet uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in OKX Wallet'\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekledi\u011Finizden emin olun. Gizli c\xFCmlenizi asla kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Tarama d\xFC\u011Fmesine dokunun",
          "description": "Tarama yapt\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flama istemi g\xF6r\xFCnecektir."
        }
      },
      "extension": {
        "step1": {
          "title": "OKX C\xFCzdan eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in OKX C\xFCzdan'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekledi\u011Finizden emin olun. Gizli c\xFCmlenizi asla kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      }
    },
    "omni": {
      "qr_code": {
        "step1": {
          "title": "Omni uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Omni'yi ana ekran\u0131n\u0131za ekleyin."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun ya da \u0130\xE7e Aktar\u0131n",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "QR simgesine dokunun ve taray\u0131n",
          "description": "Ana ekran\u0131n\u0131zdaki QR simgesine dokunun, kodu taray\u0131n ve ba\u011Flanmak i\xE7in istemi onaylay\u0131n."
        }
      }
    },
    "1inch": {
      "qr_code": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in 1inch C\xFCzdan'\u0131 ana ekran\u0131n\u0131za koyun.",
          "title": "1inch C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "Bir c\xFCzdan ve kullan\u0131c\u0131 ad\u0131 olu\u015Fturun veya mevcut bir c\xFCzdan\u0131 i\xE7e aktar\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "QR taray\u0131c\u0131 d\xFC\u011Fmesine dokunun"
        }
      }
    },
    "token_pocket": {
      "qr_code": {
        "step1": {
          "title": "TokenPocket uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in TokenPocket'\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun veya C\xFCzdan\u0131 \u0130\xE7e Aktar\u0131n",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekleyin. Gizli ifadenizi asla ba\u015Fkalar\u0131yla payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Tarama d\xFC\u011Fmesine dokunun",
          "description": "Taramay\u0131 yapt\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi belirecektir."
        }
      },
      "extension": {
        "step1": {
          "title": "TokenPocket eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in TokenPocket'i g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedekledi\u011Finizden emin olun. Gizli c\xFCmlenizi asla ba\u015Fkalar\u0131yla payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 ayarlad\u0131ktan sonra, taray\u0131c\u0131y\u0131 yenilemekte ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      }
    },
    "trust": {
      "qr_code": {
        "step1": {
          "title": "Trust Wallet uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Trust Wallet'\u0131 ana ekran\u0131n\u0131za koyun."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut bir tane i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "Ayarlar'da WalletConnect'e dokunun",
          "description": "Yeni Ba\u011Flant\u0131'y\u0131 se\xE7in, ard\u0131ndan QR kodunu taray\u0131n ve ba\u011Flanmak i\xE7in istemi onaylay\u0131n."
        }
      },
      "extension": {
        "step1": {
          "title": "Trust Wallet eklentisini y\xFCkleyin",
          "description": "Taray\u0131c\u0131n\u0131z\u0131n sa\u011F \xFCst k\xF6\u015Fesine t\u0131klay\u0131n ve kolay eri\u015Fim i\xE7in Trust Wallet'i sabitleyin."
        },
        "step2": {
          "title": "Bir c\xFCzdan olu\u015Fturun veya i\xE7e aktar\u0131n",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut bir tane i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "Trust Wallet'\u0131 kurduktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      }
    },
    "uniswap": {
      "qr_code": {
        "step1": {
          "title": "Uniswap uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Uniswap C\xFCzdan\u0131n\u0131z\u0131 ana ekran\u0131n\u0131za ekleyin."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "QR ikonuna dokunun ve tarama yap\u0131n",
          "description": "Ana ekran\u0131n\u0131zdaki QR simgesine dokunun, kodu taray\u0131n ve ba\u011Flanmay\u0131 onaylamak i\xE7in istemi kabul edin."
        }
      }
    },
    "zerion": {
      "qr_code": {
        "step1": {
          "title": "Zerion uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in Zerion'un ana ekran\u0131n\u0131za konumland\u0131rman\u0131z\u0131 \xF6neririz."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekleyin. Gizli ifadenizi asla ba\u015Fkalar\u0131yla payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Tarama d\xFC\u011Fmesine bas\u0131n",
          "description": "Taramadan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi belirecektir."
        }
      },
      "extension": {
        "step1": {
          "title": "Zerion eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Zerion'u g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedeklemeye emin olun. Gizli ifadenizi asla ba\u015Fkalar\u0131yla payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 ayarlad\u0131ktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      }
    },
    "rainbow": {
      "qr_code": {
        "step1": {
          "title": "Rainbow uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Rainbow'u ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya \u0130\xE7e Aktar",
          "description": "Telefonunuzdaki yedekleme \xF6zelli\u011Fimizi kullanarak c\xFCzdan\u0131n\u0131z\u0131 kolayca yedekleyebilirsiniz."
        },
        "step3": {
          "title": "Tarama d\xFC\u011Fmesine dokunun",
          "description": "Tarama yapt\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flaman\u0131z i\xE7in bir ba\u011Flant\u0131 istemi belirecektir."
        }
      }
    },
    "enkrypt": {
      "extension": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim sa\u011Flamak i\xE7in Enkrypt C\xFCzdan'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz.",
          "title": "Enkrypt C\xFCzdan eklentisini y\xFCkleyin"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedekledi\u011Finizden emin olun. Gizli ifadenizi hi\xE7 kimseyle payla\u015Fmay\u0131n.",
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n"
        },
        "step3": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      }
    },
    "frame": {
      "extension": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim sa\u011Flamak i\xE7in Frame'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz.",
          "title": "Frame ve e\u015Flik eden uzant\u0131y\u0131 y\xFCkleyin"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekledi\u011Finizden emin olun. Gizli ifadenizi asla ba\u015Fkas\u0131yla payla\u015Fmay\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 ayarlad\u0131ktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve uzant\u0131y\u0131 y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      }
    },
    "one_key": {
      "extension": {
        "step1": {
          "title": "OneKey Wallet uzant\u0131s\u0131n\u0131 y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in OneKey Wallet'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekledi\u011Finizden emin olun. Gizli ifadenizi kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      }
    },
    "paraswap": {
      "qr_code": {
        "step1": {
          "title": "ParaSwap uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in ParaSwap C\xFCzdan\u0131n\u0131z\u0131 ana ekran\u0131n\u0131za ekleyin."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "QR simgesine dokunun ve taray\u0131n",
          "description": "Ana ekran\u0131n\u0131zdaki QR simgesine dokunun, kodu taray\u0131n ve ba\u011Flanmay\u0131 onaylamak i\xE7in istemi kabul edin."
        }
      }
    },
    "phantom": {
      "extension": {
        "step1": {
          "title": "Phantom eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha kolay eri\u015Fim sa\u011Flamak i\xE7in Phantom'u g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekledi\u011Finizden emin olun. Gizli kurtarma ifadenizi kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 ayarlad\u0131ktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      }
    },
    "rabby": {
      "extension": {
        "step1": {
          "title": "Rabby eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Rabby'yi g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedekledi\u011Finizden emin olun. Gizli ifadenizi asla ba\u015Fkalar\u0131yla payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131daki d\xFC\u011Fmeye t\u0131klay\u0131n."
        }
      }
    },
    "ronin": {
      "qr_code": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Ronin C\xFCzdan\u0131n\u0131 ana ekran\u0131n\u0131za koymay\u0131 \xF6neririz.",
          "title": "Ronin C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "Taray\u0131c\u0131 d\xFC\u011Fmesine dokunun"
        }
      },
      "extension": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Ronin C\xFCzdan\u0131n\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz.",
          "title": "Ronin C\xFCzdan eklentisini y\xFCkleyin"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin.",
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin"
        }
      }
    },
    "ramper": {
      "extension": {
        "step1": {
          "title": "Ramper eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha kolay eri\u015Fim i\xE7in Ramper'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin."
        }
      }
    },
    "safeheron": {
      "extension": {
        "step1": {
          "title": "Core eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Safeheron'u g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedekledi\u011Finizden emin olun. Gizli ifadenizi hi\xE7 kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 ayarlad\u0131ktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      }
    },
    "taho": {
      "extension": {
        "step1": {
          "title": "Taho uzant\u0131s\u0131n\u0131 y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Taho'yu g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedekledi\u011Finizden emin olun. Gizli ifadenizi hi\xE7 kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 ayarlad\u0131ktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      }
    },
    "wigwam": {
      "extension": {
        "step1": {
          "title": "Wigwam eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Wigwam'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin."
        }
      }
    },
    "talisman": {
      "extension": {
        "step1": {
          "title": "Talisman eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Talisman'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "Ethereum C\xFCzdan\u0131 Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedekledi\u011Finizden emin olun. Kurtarma ifadenizi hi\xE7 kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 ayarlad\u0131ktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      }
    },
    "xdefi": {
      "extension": {
        "step1": {
          "title": "XDEFI C\xFCzdan eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in XDEFI Wallet'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun veya \u0130\xE7e Aktar\u0131n",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedekledi\u011Finizden emin olun. Gizli ifadenizi hi\xE7 kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 ayarlad\u0131ktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      }
    },
    "zeal": {
      "qr_code": {
        "step1": {
          "title": "Zeal uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Zeal C\xFCzdan\u0131 ana ekran\u0131n\u0131za ekleyin."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "QR simgesine dokunun ve taray\u0131n",
          "description": "Ana ekran\u0131n\u0131zdaki QR simgesine dokunun, kodu taray\u0131n ve ba\u011Flanmay\u0131 onaylamak i\xE7in istemi kabul edin."
        }
      },
      "extension": {
        "step1": {
          "title": "Zeal eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in Zeal'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin."
        }
      }
    },
    "safepal": {
      "extension": {
        "step1": {
          "title": "SafePal Wallet eklentisini y\xFCkleyin",
          "description": "Taray\u0131c\u0131n\u0131z\u0131n sa\u011F \xFCst k\xF6\u015Fesine t\u0131klay\u0131n ve kolay eri\u015Fim i\xE7in SafePal Wallet'\u0131 sabitleyin."
        },
        "step2": {
          "title": "Bir c\xFCzdan olu\u015Fturun veya i\xE7e aktar\u0131n",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "SafePal C\xFCzdan'\u0131 kurduktan sonra, taray\u0131c\u0131y\u0131 yenilemek ve eklentiyi y\xFCklemek i\xE7in a\u015Fa\u011F\u0131ya t\u0131klay\u0131n."
        }
      },
      "qr_code": {
        "step1": {
          "title": "SafePal C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "SafePal C\xFCzdan'\u0131 ana ekran\u0131n\u0131za koyun, c\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "Ayarlar'da WalletConnect'e dokunun",
          "description": "Yeni Ba\u011Flant\u0131'y\u0131 se\xE7in, ard\u0131ndan QR kodunu taray\u0131n ve ba\u011Flant\u0131y\u0131 onaylamak i\xE7in istemi onaylay\u0131n."
        }
      }
    },
    "desig": {
      "extension": {
        "step1": {
          "title": "Desig eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha kolay eri\u015Fim sa\u011Flamak i\xE7in Desig'i g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "Bir C\xFCzdan Olu\u015Fturun",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin."
        }
      }
    },
    "subwallet": {
      "extension": {
        "step1": {
          "title": "SubWallet eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in SubWallet'\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedekledi\u011Finizden emin olun. Kurtarma ifadenizi hi\xE7 kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin."
        }
      },
      "qr_code": {
        "step1": {
          "title": "SubWallet uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in SubWallet'\u0131 ana ekran\u0131n\u0131za koymenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131 d\xFC\u011Fmesine dokunun",
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir."
        }
      }
    },
    "clv": {
      "extension": {
        "step1": {
          "title": "CLV C\xFCzdan\u0131 eklentisini y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in CLV C\xFCzdan\u0131n\u0131 g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin."
        }
      },
      "qr_code": {
        "step1": {
          "title": "CLV C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in CLV C\xFCzdan\u0131n\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131 d\xFC\u011Fmesine dokunun",
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir."
        }
      }
    },
    "okto": {
      "qr_code": {
        "step1": {
          "title": "Okto uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "H\u0131zl\u0131 eri\u015Fim i\xE7in Okto'yu ana ekran\u0131n\u0131za ekleyin"
        },
        "step2": {
          "title": "MPC C\xFCzdan\u0131 olu\u015Fturun",
          "description": "Bir hesap olu\u015Fturun ve bir c\xFCzdan olu\u015Fturun"
        },
        "step3": {
          "title": "Ayarlar'da WalletConnect'e dokunun",
          "description": "Sa\u011F \xFCstteki Tarama QR simgesine dokunun ve ba\u011Flanmak i\xE7in istemi onaylay\u0131n."
        }
      }
    },
    "ledger": {
      "desktop": {
        "step1": {
          "title": "Ledger Live uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in Ledger Live'\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz."
        },
        "step2": {
          "title": "Ledger'\u0131n\u0131z\u0131 kurun",
          "description": "Yeni bir Ledger kurun veya mevcut birine ba\u011Flan\u0131n."
        },
        "step3": {
          "title": "Ba\u011Flan",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi belirecektir."
        }
      },
      "qr_code": {
        "step1": {
          "title": "Ledger Live uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in Ledger Live'\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz."
        },
        "step2": {
          "title": "Ledger'\u0131n\u0131z\u0131 kurun",
          "description": "Masa\xFCst\xFC uygulama ile senkronize olabilir veya Ledger'\u0131n\u0131z\u0131 ba\u011Flayabilirsiniz."
        },
        "step3": {
          "title": "Kodu taray\u0131n",
          "description": "WalletConnect'e dokunun ve ard\u0131ndan Taray\u0131c\u0131'ya ge\xE7in. Taramadan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi belirecektir."
        }
      }
    },
    "valora": {
      "qr_code": {
        "step1": {
          "title": "Valora uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in Valora'y\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz."
        },
        "step2": {
          "title": "Bir c\xFCzdan olu\u015Fturun veya i\xE7e aktar\u0131n",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131 d\xFC\u011Fmesine dokunun",
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir."
        }
      }
    },
    "gate": {
      "qr_code": {
        "step1": {
          "title": "Gate uygulamas\u0131n\u0131 a\xE7\u0131n",
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in Gate'i ana ekran\u0131n\u0131za konumland\u0131rman\u0131z\u0131 \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131 d\xFC\u011Fmesine dokunun",
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir."
        }
      },
      "extension": {
        "step1": {
          "title": "Gate uzant\u0131s\u0131n\u0131 y\xFCkleyin",
          "description": "C\xFCzdan\u0131n\u0131za daha kolay eri\u015Fim sa\u011Flamak i\xE7in Gate'i g\xF6rev \xE7ubu\u011Funuza sabitlemenizi \xF6neririz."
        },
        "step2": {
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 g\xFCvenli bir y\xF6ntem kullanarak yedekledi\u011Finizden emin olun. Gizli kurtarma ifadenizi kimseyle payla\u015Fmay\u0131n."
        },
        "step3": {
          "title": "Taray\u0131c\u0131n\u0131z\u0131 yenileyin",
          "description": "C\xFCzdan\u0131n\u0131z\u0131 kurduktan sonra, a\u015Fa\u011F\u0131ya t\u0131klay\u0131n ve taray\u0131c\u0131y\u0131 yenileyin ve eklentiyi y\xFCkleyin."
        }
      }
    },
    "xportal": {
      "qr_code": {
        "step1": {
          "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in xPortal'u ana ekran\u0131n\u0131za koyun.",
          "title": "xPortal uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "QR taray\u0131c\u0131 d\xFC\u011Fmesine dokunun"
        }
      }
    },
    "mew": {
      "qr_code": {
        "step1": {
          "description": "Daha h\u0131zl\u0131 eri\u015Fim i\xE7in MEW C\xFCzdan\u0131 ana ekran\u0131n\u0131za koyman\u0131z\u0131 \xF6neririz.",
          "title": "MEW C\xFCzdan uygulamas\u0131n\u0131 a\xE7\u0131n"
        },
        "step2": {
          "description": "C\xFCzdan\u0131n\u0131z\u0131 bulut yedekleme \xF6zelli\u011Fini kullanarak kolayca yedekleyebilirsiniz.",
          "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar"
        },
        "step3": {
          "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir.",
          "title": "Taray\u0131c\u0131 d\xFC\u011Fmesine dokunun"
        }
      }
    }
  },
  "zilpay": {
    "qr_code": {
      "step1": {
        "title": "ZilPay uygulamas\u0131n\u0131 a\xE7\u0131n",
        "description": "C\xFCzdan\u0131n\u0131za daha h\u0131zl\u0131 eri\u015Fim i\xE7in ZilPay'i ana ekran\u0131n\u0131za ekleyin."
      },
      "step2": {
        "title": "C\xFCzdan Olu\u015Ftur veya C\xFCzdan\u0131 \u0130\xE7e Aktar",
        "description": "Yeni bir c\xFCzdan olu\u015Fturun veya mevcut birini i\xE7e aktar\u0131n."
      },
      "step3": {
        "title": "Taray\u0131c\u0131 d\xFC\u011Fmesine dokunun",
        "description": "Tarad\u0131ktan sonra, c\xFCzdan\u0131n\u0131z\u0131 ba\u011Flamak i\xE7in bir ba\u011Flant\u0131 istemi g\xF6r\xFCnecektir."
      }
    }
  }
}
`;
;
}}),

};

//# sourceMappingURL=node_modules_%40rainbow-me_rainbowkit_dist_tr_TR-P7QAUUZU_9c313522.js.map