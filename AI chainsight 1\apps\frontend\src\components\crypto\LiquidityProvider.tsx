'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { ArrowUpDown, Loader2, Plus, Minus, Info } from 'lucide-react'
import { useAccount } from 'wagmi'
import axios from 'axios'
import toast from 'react-hot-toast'

interface LiquidityData {
  tokenA: string
  tokenB: string
  amountA: string
  amountB: string
  slippage: number
}

export function LiquidityProvider() {
  const { address, isConnected } = useAccount()
  const [liquidityData, setLiquidityData] = useState<LiquidityData>({
    tokenA: '',
    tokenB: '',
    amountA: '',
    amountB: '',
    slippage: 0.5
  })
  const [isAdding, setIsAdding] = useState(false)
  const [mode, setMode] = useState<'add' | 'remove'>('add')

  const popularTokens = [
    { symbol: 'ETH', address: '******************************************', name: 'Ethereum' },
    { symbol: 'USDC', address: '******************************************', name: 'USD Coin' },
    { symbol: 'USDT', address: '******************************************', name: 'Tether USD' },
    { symbol: 'WBTC', address: '******************************************', name: 'Wrapped Bitcoin' },
  ]

  const handleInputChange = (field: keyof LiquidityData, value: string | number) => {
    setLiquidityData(prev => ({ ...prev, [field]: value }))
  }

  const selectToken = (field: 'tokenA' | 'tokenB', token: typeof popularTokens[0]) => {
    setLiquidityData(prev => ({ ...prev, [field]: token.address }))
  }

  const addLiquidity = async () => {
    if (!isConnected) {
      toast.error('Please connect your wallet first')
      return
    }

    if (!liquidityData.tokenA || !liquidityData.tokenB) {
      toast.error('Please select both tokens')
      return
    }

    if (!liquidityData.amountA || !liquidityData.amountB) {
      toast.error('Please enter amounts for both tokens')
      return
    }

    setIsAdding(true)
    try {
      const response = await axios.post(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/api/crypto/add-liquidity`, {
        ...liquidityData,
        userAddress: address
      })

      toast.success('Liquidity added successfully!')
      console.log('Transaction:', response.data)
    } catch (error: any) {
      console.error('Liquidity error:', error)
      toast.error(error.response?.data?.detail || 'Failed to add liquidity')
    } finally {
      setIsAdding(false)
    }
  }

  const removeLiquidity = async () => {
    if (!isConnected) {
      toast.error('Please connect your wallet first')
      return
    }

    setIsAdding(true)
    try {
      const response = await axios.post(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/api/crypto/remove-liquidity`, {
        ...liquidityData,
        userAddress: address
      })

      toast.success('Liquidity removed successfully!')
      console.log('Transaction:', response.data)
    } catch (error: any) {
      console.error('Remove liquidity error:', error)
      toast.error(error.response?.data?.detail || 'Failed to remove liquidity')
    } finally {
      setIsAdding(false)
    }
  }

  return (
    <div className="luxury-card">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <ArrowUpDown className="w-6 h-6 text-luxury-gold" />
          <h3 className="text-xl font-semibold text-white">Liquidity Provider</h3>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => setMode('add')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              mode === 'add'
                ? 'bg-luxury-gold text-black'
                : 'bg-luxury-slate text-gray-400 hover:text-white'
            }`}
          >
            <Plus className="w-4 h-4 inline mr-1" />
            Add
          </button>
          <button
            onClick={() => setMode('remove')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              mode === 'remove'
                ? 'bg-luxury-gold text-black'
                : 'bg-luxury-slate text-gray-400 hover:text-white'
            }`}
          >
            <Minus className="w-4 h-4 inline mr-1" />
            Remove
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-6">
          {/* Token A Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Token A
            </label>
            <div className="space-y-2">
              <input
                type="text"
                value={liquidityData.tokenA}
                onChange={(e) => handleInputChange('tokenA', e.target.value)}
                placeholder="Token contract address"
                className="luxury-input w-full"
              />
              <div className="flex flex-wrap gap-2">
                {popularTokens.map((token) => (
                  <button
                    key={token.symbol}
                    onClick={() => selectToken('tokenA', token)}
                    className="px-3 py-1 bg-luxury-slate text-gray-300 rounded-md text-sm hover:bg-luxury-gold hover:text-black transition-colors"
                  >
                    {token.symbol}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Token B Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Token B
            </label>
            <div className="space-y-2">
              <input
                type="text"
                value={liquidityData.tokenB}
                onChange={(e) => handleInputChange('tokenB', e.target.value)}
                placeholder="Token contract address"
                className="luxury-input w-full"
              />
              <div className="flex flex-wrap gap-2">
                {popularTokens.map((token) => (
                  <button
                    key={token.symbol}
                    onClick={() => selectToken('tokenB', token)}
                    className="px-3 py-1 bg-luxury-slate text-gray-300 rounded-md text-sm hover:bg-luxury-gold hover:text-black transition-colors"
                  >
                    {token.symbol}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Amounts */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Amount A
              </label>
              <input
                type="number"
                value={liquidityData.amountA}
                onChange={(e) => handleInputChange('amountA', e.target.value)}
                placeholder="0.0"
                className="luxury-input w-full"
                step="0.000001"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Amount B
              </label>
              <input
                type="number"
                value={liquidityData.amountB}
                onChange={(e) => handleInputChange('amountB', e.target.value)}
                placeholder="0.0"
                className="luxury-input w-full"
                step="0.000001"
              />
            </div>
          </div>

          {/* Slippage */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Slippage Tolerance (%)
            </label>
            <div className="flex space-x-2">
              {[0.1, 0.5, 1.0, 3.0].map((value) => (
                <button
                  key={value}
                  onClick={() => handleInputChange('slippage', value)}
                  className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                    liquidityData.slippage === value
                      ? 'bg-luxury-gold text-black'
                      : 'bg-luxury-slate text-gray-400 hover:text-white'
                  }`}
                >
                  {value}%
                </button>
              ))}
              <input
                type="number"
                value={liquidityData.slippage}
                onChange={(e) => handleInputChange('slippage', parseFloat(e.target.value) || 0)}
                className="luxury-input flex-1"
                step="0.1"
                min="0"
                max="50"
              />
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {/* Pool Information */}
          <div className="bg-luxury-slate rounded-lg p-4">
            <h4 className="font-medium text-white mb-3 flex items-center">
              <Info className="w-4 h-4 mr-2" />
              Pool Information
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Pool Fee:</span>
                <span className="text-white">0.3%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Your Pool Share:</span>
                <span className="text-white">0.00%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Pool Liquidity:</span>
                <span className="text-white">$0.00</span>
              </div>
            </div>
          </div>

          {/* Price Impact */}
          <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
            <h4 className="font-medium text-blue-400 mb-2">Price Impact</h4>
            <p className="text-sm text-gray-300">
              Adding liquidity will not cause price impact as you're providing both tokens proportionally.
            </p>
          </div>

          {/* Action Button */}
          <button
            onClick={mode === 'add' ? addLiquidity : removeLiquidity}
            disabled={isAdding || !isConnected}
            className="luxury-button w-full disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isAdding ? (
              <div className="flex items-center justify-center space-x-2">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>{mode === 'add' ? 'Adding Liquidity...' : 'Removing Liquidity...'}</span>
              </div>
            ) : (
              `${mode === 'add' ? 'Add' : 'Remove'} Liquidity`
            )}
          </button>

          {!isConnected && (
            <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
              <p className="text-yellow-500 text-sm">
                Connect your wallet to manage liquidity
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
