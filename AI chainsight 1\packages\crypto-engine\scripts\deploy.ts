import { ethers } from "hardhat";
import { ChainsightToken } from "../typechain-types";

interface DeploymentParams {
  name: string;
  symbol: string;
  initialSupply: string;
  owner?: string;
}

async function deployToken(params: DeploymentParams): Promise<{
  contract: ChainsightToken;
  address: string;
  transactionHash: string;
}> {
  const [deployer] = await ethers.getSigners();
  
  console.log("Deploying ChainsightToken with account:", deployer.address);
  console.log("Account balance:", (await deployer.getBalance()).toString());

  // Convert initial supply to wei (assuming 18 decimals)
  const initialSupplyWei = ethers.parseEther(params.initialSupply);
  const ownerAddress = params.owner || deployer.address;

  // Deploy the contract
  const ChainsightTokenFactory = await ethers.getContractFactory("ChainsightToken");
  const chainsightToken = await ChainsightTokenFactory.deploy(
    params.name,
    params.symbol,
    initialSupply<PERSON>ei,
    ownerAddress
  );

  await chainsightToken.waitForDeployment();
  const contractAddress = await chainsightToken.getAddress();
  const deploymentTx = chainsightToken.deploymentTransaction();

  console.log("ChainsightToken deployed to:", contractAddress);
  console.log("Transaction hash:", deploymentTx?.hash);
  console.log("Owner:", ownerAddress);
  console.log("Initial supply:", params.initialSupply, params.symbol);

  return {
    contract: chainsightToken,
    address: contractAddress,
    transactionHash: deploymentTx?.hash || ""
  };
}

async function verifyDeployment(contractAddress: string) {
  const chainsightToken = await ethers.getContractAt("ChainsightToken", contractAddress);
  
  const tokenInfo = await chainsightToken.getTokenInfo();
  
  console.log("\n=== Token Verification ===");
  console.log("Name:", tokenInfo.tokenName);
  console.log("Symbol:", tokenInfo.tokenSymbol);
  console.log("Decimals:", tokenInfo.tokenDecimals.toString());
  console.log("Total Supply:", ethers.formatEther(tokenInfo.tokenTotalSupply));
  console.log("Max Supply:", ethers.formatEther(tokenInfo.tokenMaxSupply));
  console.log("Minting Fee:", ethers.formatEther(tokenInfo.currentMintingFee), "ETH");
  console.log("Is Paused:", tokenInfo.isPaused);
}

// Main deployment function
async function main() {
  try {
    // Default deployment parameters
    const defaultParams: DeploymentParams = {
      name: "Chainsight Token",
      symbol: "CST",
      initialSupply: "1000000" // 1 million tokens
    };

    // Deploy the token
    const deployment = await deployToken(defaultParams);
    
    // Verify the deployment
    await verifyDeployment(deployment.address);

    // Save deployment info
    const deploymentInfo = {
      network: (await ethers.provider.getNetwork()).name,
      contractAddress: deployment.address,
      transactionHash: deployment.transactionHash,
      deployer: (await ethers.getSigners())[0].address,
      timestamp: new Date().toISOString(),
      ...defaultParams
    };

    console.log("\n=== Deployment Complete ===");
    console.log(JSON.stringify(deploymentInfo, null, 2));

  } catch (error) {
    console.error("Deployment failed:", error);
    process.exitCode = 1;
  }
}

// Export for programmatic use
export { deployToken, verifyDeployment };

// Run if called directly
if (require.main === module) {
  main();
}
