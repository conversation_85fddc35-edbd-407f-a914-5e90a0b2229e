export interface MarketData {
  symbol: string;
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  market_cap?: number;
  circulating_supply?: number;
}

export interface Asset {
  symbol: string;
  name: string;
  type: 'stock' | 'crypto' | 'forex' | 'commodity' | 'bond';
  sector?: string;
  market_cap?: number;
  price: number;
  change_24h: number;
  volume_24h: number;
  metadata?: Record<string, any>;
}

export interface Portfolio {
  id: string;
  name: string;
  assets: PortfolioAsset[];
  total_value: number;
  cash_balance: number;
  created_at: Date;
  updated_at: Date;
}

export interface PortfolioAsset {
  symbol: string;
  quantity: number;
  average_cost: number;
  current_price: number;
  market_value: number;
  unrealized_pnl: number;
  weight: number;
}

export interface RiskMetrics {
  var_95: number; // Value at Risk 95%
  var_99: number; // Value at Risk 99%
  expected_shortfall: number;
  sharpe_ratio: number;
  sortino_ratio: number;
  max_drawdown: number;
  volatility: number;
  beta: number;
  alpha: number;
  correlation_matrix?: number[][];
}

export interface TechnicalIndicators {
  sma: Record<number, number>; // Simple Moving Averages
  ema: Record<number, number>; // Exponential Moving Averages
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  bollinger_bands: {
    upper: number;
    middle: number;
    lower: number;
    bandwidth: number;
  };
  stochastic: {
    k: number;
    d: number;
  };
  atr: number; // Average True Range
  adx: number; // Average Directional Index
  williams_r: number;
  cci: number; // Commodity Channel Index
}

export interface FundamentalData {
  symbol: string;
  market_cap: number;
  pe_ratio?: number;
  pb_ratio?: number;
  debt_to_equity?: number;
  roe?: number; // Return on Equity
  revenue_growth?: number;
  earnings_growth?: number;
  dividend_yield?: number;
  free_cash_flow?: number;
  book_value_per_share?: number;
  price_to_sales?: number;
}

export interface SentimentData {
  symbol: string;
  timestamp: number;
  sentiment_score: number; // -1 to 1
  confidence: number;
  sources: {
    news: number;
    social_media: number;
    analyst_reports: number;
    options_flow: number;
  };
  keywords: string[];
  volume_sentiment: number;
}

export interface AnalysisResult {
  symbol: string;
  timestamp: number;
  analysis_type: 'technical' | 'fundamental' | 'sentiment' | 'combined';
  score: number; // -100 to 100
  confidence: number;
  signals: Signal[];
  recommendations: Recommendation[];
  risk_assessment: RiskAssessment;
}

export interface Signal {
  type: 'buy' | 'sell' | 'hold';
  strength: number; // 0 to 1
  source: string;
  description: string;
  timestamp: number;
}

export interface Recommendation {
  action: 'strong_buy' | 'buy' | 'hold' | 'sell' | 'strong_sell';
  target_price?: number;
  stop_loss?: number;
  time_horizon: 'short' | 'medium' | 'long';
  reasoning: string;
  confidence: number;
}

export interface RiskAssessment {
  risk_level: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
  risk_score: number; // 0 to 100
  factors: string[];
  mitigation_strategies: string[];
}

export interface BacktestResult {
  strategy_name: string;
  start_date: Date;
  end_date: Date;
  initial_capital: number;
  final_capital: number;
  total_return: number;
  annualized_return: number;
  max_drawdown: number;
  sharpe_ratio: number;
  win_rate: number;
  profit_factor: number;
  trades: Trade[];
  equity_curve: EquityPoint[];
  metrics: BacktestMetrics;
}

export interface Trade {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  timestamp: Date;
  pnl?: number;
  commission?: number;
}

export interface EquityPoint {
  timestamp: Date;
  equity: number;
  drawdown: number;
}

export interface BacktestMetrics {
  total_trades: number;
  winning_trades: number;
  losing_trades: number;
  avg_win: number;
  avg_loss: number;
  largest_win: number;
  largest_loss: number;
  consecutive_wins: number;
  consecutive_losses: number;
  calmar_ratio: number;
  sortino_ratio: number;
}

export interface Alert {
  id: string;
  type: 'price' | 'volume' | 'technical' | 'news' | 'risk';
  symbol: string;
  condition: string;
  threshold: number;
  current_value: number;
  triggered_at: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  is_active: boolean;
}

export interface MarketCondition {
  overall_sentiment: 'bullish' | 'bearish' | 'neutral';
  volatility_regime: 'low' | 'medium' | 'high';
  trend_strength: number;
  market_phase: 'accumulation' | 'markup' | 'distribution' | 'markdown';
  fear_greed_index: number;
  correlation_breakdown: boolean;
}

export interface CorrelationMatrix {
  symbols: string[];
  matrix: number[][];
  timestamp: Date;
}

export interface OptimizationResult {
  weights: Record<string, number>;
  expected_return: number;
  expected_risk: number;
  sharpe_ratio: number;
  optimization_type: 'max_sharpe' | 'min_risk' | 'max_return' | 'risk_parity';
  constraints: OptimizationConstraints;
}

export interface OptimizationConstraints {
  max_weight?: number;
  min_weight?: number;
  sector_limits?: Record<string, number>;
  turnover_limit?: number;
  target_return?: number;
  target_risk?: number;
}
