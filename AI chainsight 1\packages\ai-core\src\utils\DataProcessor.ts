import { MarketData, TrainingData, ModelConfig, FeatureSet } from '../types';
import { TECHNICAL_INDICATORS } from '../constants';

export class DataProcessor {
  
  async processMarketData(
    marketData: MarketData[],
    includeTechnicalIndicators: boolean = true
  ): Promise<any> {
    try {
      console.log(`Processing ${marketData.length} market data points`);

      // Sort data by timestamp
      const sortedData = marketData.sort((a, b) => a.timestamp - b.timestamp);

      // Extract basic features
      const features = this.extractBasicFeatures(sortedData);

      // Add technical indicators if requested
      if (includeTechnicalIndicators) {
        const technicalFeatures = this.calculateTechnicalIndicators(sortedData);
        features.push(...technicalFeatures);
      }

      // Normalize features
      const normalizedFeatures = this.normalizeFeatures(features);

      return {
        features: normalizedFeatures,
        timestamps: sortedData.map(d => d.timestamp),
        symbols: sortedData.map(d => d.symbol),
        feature_names: this.getFeatureNames(includeTechnicalIndicators)
      };

    } catch (error) {
      console.error('Data processing failed:', error);
      throw new Error(`Failed to process market data: ${error.message}`);
    }
  }

  async prepareTrainingData(
    marketData: MarketData[],
    config: ModelConfig
  ): Promise<TrainingData> {
    try {
      console.log('Preparing training data...');

      // Process market data
      const processedData = await this.processMarketData(marketData, true);

      // Create sequences for time series prediction
      const sequences = this.createSequences(
        processedData.features,
        config.sequence_length,
        config.prediction_horizon
      );

      return {
        features: sequences.features,
        targets: sequences.targets,
        timestamps: processedData.timestamps,
        symbols: processedData.symbols
      };

    } catch (error) {
      console.error('Training data preparation failed:', error);
      throw error;
    }
  }

  splitData(data: TrainingData, trainRatio: number = 0.8): {
    trainData: TrainingData;
    valData: TrainingData;
  } {
    const splitIndex = Math.floor(data.features.length * trainRatio);

    return {
      trainData: {
        features: data.features.slice(0, splitIndex),
        targets: data.targets.slice(0, splitIndex),
        timestamps: data.timestamps.slice(0, splitIndex),
        symbols: data.symbols.slice(0, splitIndex)
      },
      valData: {
        features: data.features.slice(splitIndex),
        targets: data.targets.slice(splitIndex),
        timestamps: data.timestamps.slice(splitIndex),
        symbols: data.symbols.slice(splitIndex)
      }
    };
  }

  private extractBasicFeatures(marketData: MarketData[]): number[][] {
    return marketData.map(data => [
      data.open,
      data.high,
      data.low,
      data.close,
      data.volume,
      // Price changes
      data.high - data.low, // Daily range
      data.close - data.open, // Daily change
      // Relative features
      (data.close - data.open) / data.open, // Daily return
      data.volume / 1000000 // Volume in millions
    ]);
  }

  private calculateTechnicalIndicators(marketData: MarketData[]): number[][] {
    const indicators: number[][] = [];

    for (let i = 0; i < marketData.length; i++) {
      const indicatorRow: number[] = [];

      // Simple Moving Averages
      indicatorRow.push(this.calculateSMA(marketData, i, 5));
      indicatorRow.push(this.calculateSMA(marketData, i, 10));
      indicatorRow.push(this.calculateSMA(marketData, i, 20));
      indicatorRow.push(this.calculateSMA(marketData, i, 50));

      // Exponential Moving Averages
      indicatorRow.push(this.calculateEMA(marketData, i, 12));
      indicatorRow.push(this.calculateEMA(marketData, i, 26));

      // RSI
      indicatorRow.push(this.calculateRSI(marketData, i, 14));

      // MACD
      const macd = this.calculateMACD(marketData, i);
      indicatorRow.push(macd.macd);
      indicatorRow.push(macd.signal);
      indicatorRow.push(macd.histogram);

      // Bollinger Bands
      const bb = this.calculateBollingerBands(marketData, i, 20, 2);
      indicatorRow.push(bb.upper);
      indicatorRow.push(bb.middle);
      indicatorRow.push(bb.lower);
      indicatorRow.push(bb.bandwidth);

      // Volume indicators
      indicatorRow.push(this.calculateVolumeSMA(marketData, i, 20));

      indicators.push(indicatorRow);
    }

    return indicators;
  }

  private calculateSMA(data: MarketData[], index: number, period: number): number {
    if (index < period - 1) return data[index].close;

    let sum = 0;
    for (let i = index - period + 1; i <= index; i++) {
      sum += data[i].close;
    }
    return sum / period;
  }

  private calculateEMA(data: MarketData[], index: number, period: number): number {
    if (index === 0) return data[0].close;

    const multiplier = 2 / (period + 1);
    const prevEMA = index === 1 ? data[0].close : this.calculateEMA(data, index - 1, period);
    
    return (data[index].close * multiplier) + (prevEMA * (1 - multiplier));
  }

  private calculateRSI(data: MarketData[], index: number, period: number = 14): number {
    if (index < period) return 50; // Neutral RSI

    let gains = 0;
    let losses = 0;

    for (let i = index - period + 1; i <= index; i++) {
      const change = data[i].close - data[i - 1].close;
      if (change > 0) {
        gains += change;
      } else {
        losses += Math.abs(change);
      }
    }

    const avgGain = gains / period;
    const avgLoss = losses / period;

    if (avgLoss === 0) return 100;

    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }

  private calculateMACD(data: MarketData[], index: number): {
    macd: number;
    signal: number;
    histogram: number;
  } {
    const ema12 = this.calculateEMA(data, index, 12);
    const ema26 = this.calculateEMA(data, index, 26);
    const macd = ema12 - ema26;

    // Signal line (9-period EMA of MACD)
    const signal = index < 9 ? macd : this.calculateEMAOfMACD(data, index, 9);
    const histogram = macd - signal;

    return { macd, signal, histogram };
  }

  private calculateEMAOfMACD(data: MarketData[], index: number, period: number): number {
    // Simplified signal line calculation
    if (index < period) {
      const ema12 = this.calculateEMA(data, index, 12);
      const ema26 = this.calculateEMA(data, index, 26);
      return ema12 - ema26;
    }

    let sum = 0;
    for (let i = index - period + 1; i <= index; i++) {
      const ema12 = this.calculateEMA(data, i, 12);
      const ema26 = this.calculateEMA(data, i, 26);
      sum += (ema12 - ema26);
    }
    return sum / period;
  }

  private calculateBollingerBands(data: MarketData[], index: number, period: number, stdDev: number): {
    upper: number;
    middle: number;
    lower: number;
    bandwidth: number;
  } {
    const sma = this.calculateSMA(data, index, period);
    
    if (index < period - 1) {
      return { upper: sma, middle: sma, lower: sma, bandwidth: 0 };
    }

    // Calculate standard deviation
    let sumSquaredDiffs = 0;
    for (let i = index - period + 1; i <= index; i++) {
      const diff = data[i].close - sma;
      sumSquaredDiffs += diff * diff;
    }
    const standardDeviation = Math.sqrt(sumSquaredDiffs / period);

    const upper = sma + (standardDeviation * stdDev);
    const lower = sma - (standardDeviation * stdDev);
    const bandwidth = (upper - lower) / sma;

    return { upper, middle: sma, lower, bandwidth };
  }

  private calculateVolumeSMA(data: MarketData[], index: number, period: number): number {
    if (index < period - 1) return data[index].volume;

    let sum = 0;
    for (let i = index - period + 1; i <= index; i++) {
      sum += data[i].volume;
    }
    return sum / period;
  }

  private normalizeFeatures(features: number[][]): number[][] {
    if (features.length === 0) return features;

    const numFeatures = features[0].length;
    const normalized: number[][] = [];

    // Calculate min and max for each feature
    const mins = new Array(numFeatures).fill(Infinity);
    const maxs = new Array(numFeatures).fill(-Infinity);

    features.forEach(row => {
      row.forEach((value, index) => {
        if (value < mins[index]) mins[index] = value;
        if (value > maxs[index]) maxs[index] = value;
      });
    });

    // Normalize each feature to [0, 1]
    features.forEach(row => {
      const normalizedRow = row.map((value, index) => {
        const range = maxs[index] - mins[index];
        return range === 0 ? 0 : (value - mins[index]) / range;
      });
      normalized.push(normalizedRow);
    });

    return normalized;
  }

  private createSequences(
    features: number[][],
    sequenceLength: number,
    predictionHorizon: number
  ): { features: number[][][]; targets: number[][] } {
    const sequences: number[][][] = [];
    const targets: number[][] = [];

    for (let i = sequenceLength; i < features.length - predictionHorizon + 1; i++) {
      // Input sequence
      const sequence = features.slice(i - sequenceLength, i);
      sequences.push(sequence);

      // Target (next values to predict)
      const target = [];
      for (let j = 0; j < predictionHorizon; j++) {
        // Predict close price (assuming it's the 4th feature)
        target.push(features[i + j][3]);
      }
      targets.push(target);
    }

    return { features: sequences, targets };
  }

  private getFeatureNames(includeTechnicalIndicators: boolean): string[] {
    const basicFeatures = [
      'open', 'high', 'low', 'close', 'volume',
      'daily_range', 'daily_change', 'daily_return', 'volume_millions'
    ];

    if (!includeTechnicalIndicators) {
      return basicFeatures;
    }

    const technicalFeatures = [
      'sma_5', 'sma_10', 'sma_20', 'sma_50',
      'ema_12', 'ema_26',
      'rsi_14',
      'macd', 'macd_signal', 'macd_histogram',
      'bb_upper', 'bb_middle', 'bb_lower', 'bb_bandwidth',
      'volume_sma_20'
    ];

    return [...basicFeatures, ...technicalFeatures];
  }
}
