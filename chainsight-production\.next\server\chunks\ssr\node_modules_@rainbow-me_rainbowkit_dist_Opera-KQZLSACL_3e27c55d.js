module.exports = {

"[project]/node_modules/@rainbow-me/rainbowkit/dist/Opera-KQZLSACL.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Opera_default)
});
"use client";
// src/components/Icons/Opera.svg
var Opera_default = "data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2078%2078%22%3E%3ClinearGradient%20id%3D%22a%22%20x2%3D%221%22%20gradientTransform%3D%22matrix(0%20-54.944%20-54.944%200%2023.62%2079.474)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20offset%3D%220%22%20stop-color%3D%22%23ff1b2d%22%2F%3E%3Cstop%20offset%3D%22.3%22%20stop-color%3D%22%23ff1b2d%22%2F%3E%3Cstop%20offset%3D%22.614%22%20stop-color%3D%22%23ff1b2d%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23a70014%22%2F%3E%3C%2FlinearGradient%3E%3ClinearGradient%20id%3D%22b%22%20x2%3D%221%22%20gradientTransform%3D%22matrix(0%20-48.595%20-48.595%200%2037.854%2076.235)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20offset%3D%220%22%20stop-color%3D%22%239c0000%22%2F%3E%3Cstop%20offset%3D%22.7%22%20stop-color%3D%22%23ff4b4b%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23ff4b4b%22%2F%3E%3C%2FlinearGradient%3E%3Cpath%20fill%3D%22url(%23a)%22%20d%3D%22M28.346%2080.398C12.691%2080.398%200%2067.707%200%2052.052%200%2036.85%2011.968%2024.443%2026.996%2023.739a28.244%2028.244%200%200%201%2020.241%207.18c-3.322-2.203-7.207-3.47-11.359-3.47-6.75%200-12.796%203.348-16.862%208.629-3.134%203.7-5.164%209.169-5.302%2015.307v1.335c.138%206.137%202.168%2011.608%205.302%2015.307%204.066%205.28%2010.112%208.63%2016.862%208.63%204.152%200%208.038-1.269%2011.36-3.474a28.239%2028.239%200%200%201-18.785%207.215l-.108.001z%22%20transform%3D%22matrix(1.3333%200%200%20-1.3333%200%20107.2)%22%2F%3E%3Cpath%20fill%3D%22url(%23b)%22%20d%3D%22M19.016%2068.025c2.601%203.07%205.96%204.923%209.631%204.923%208.252%200%2014.941-9.356%2014.941-20.897s-6.69-20.897-14.941-20.897c-3.67%200-7.03%201.85-9.63%204.922%204.066-5.281%2010.11-8.63%2016.862-8.63%204.152%200%208.036%201.268%2011.359%203.472%205.802%205.19%209.455%2012.735%209.455%2021.133%200%208.397-3.653%2015.94-9.453%2021.13-3.324%202.206-7.209%203.473-11.361%203.473-6.75%200-12.796-3.348-16.862-8.63%22%20transform%3D%22matrix(1.3333%200%200%20-1.3333%200%20107.2)%22%2F%3E%3C%2Fsvg%3E";
;
}}),

};

//# sourceMappingURL=node_modules_%40rainbow-me_rainbowkit_dist_Opera-KQZLSACL_3e27c55d.js.map