{"name": "@chainsight/task-orchestrator", "version": "1.0.0", "description": "Task orchestration and plugin management system", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest"}, "dependencies": {"eventemitter3": "^5.0.0", "uuid": "^9.0.0", "lodash": "^4.17.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/uuid": "^9.0.0", "@types/lodash": "^4.14.0", "typescript": "^5.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}}