'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON>, Loader2, CheckCircle, AlertCircle, Copy } from 'lucide-react'
import { useAccount } from 'wagmi'
import axios from 'axios'
import toast from 'react-hot-toast'

interface TokenData {
  name: string
  symbol: string
  totalSupply: string
  decimals: number
  description: string
}

export function TokenCreator() {
  const { address, isConnected } = useAccount()
  const [tokenData, setTokenData] = useState<TokenData>({
    name: '',
    symbol: '',
    totalSupply: '',
    decimals: 18,
    description: ''
  })
  const [isDeploying, setIsDeploying] = useState(false)
  const [deployedToken, setDeployedToken] = useState<{
    address: string
    txHash: string
  } | null>(null)

  const handleInputChange = (field: keyof TokenData, value: string | number) => {
    setTokenData(prev => ({ ...prev, [field]: value }))
  }

  const validateForm = () => {
    if (!tokenData.name.trim()) {
      toast.error('Token name is required')
      return false
    }
    if (!tokenData.symbol.trim()) {
      toast.error('Token symbol is required')
      return false
    }
    if (!tokenData.totalSupply || parseFloat(tokenData.totalSupply) <= 0) {
      toast.error('Valid total supply is required')
      return false
    }
    if (!isConnected) {
      toast.error('Please connect your wallet first')
      return false
    }
    return true
  }

  const deployToken = async () => {
    if (!validateForm()) return

    setIsDeploying(true)
    try {
      const response = await axios.post(`${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/api/crypto/deploy-token`, {
        ...tokenData,
        deployer: address
      })

      setDeployedToken({
        address: response.data.contractAddress,
        txHash: response.data.transactionHash
      })

      toast.success('Token deployed successfully!')
    } catch (error: any) {
      console.error('Deployment error:', error)
      toast.error(error.response?.data?.detail || 'Failed to deploy token')
    } finally {
      setIsDeploying(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  if (deployedToken) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="luxury-card text-center"
      >
        <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircle className="w-8 h-8 text-white" />
        </div>
        
        <h3 className="text-2xl font-bold text-white mb-2">Token Deployed Successfully!</h3>
        <p className="text-gray-400 mb-8">Your ERC20 token has been deployed to the blockchain</p>
        
        <div className="space-y-4 mb-8">
          <div className="bg-luxury-slate rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Contract Address:</span>
              <button
                onClick={() => copyToClipboard(deployedToken.address)}
                className="flex items-center space-x-2 text-luxury-gold hover:text-yellow-400 transition-colors"
              >
                <span className="font-mono text-sm">{deployedToken.address.slice(0, 10)}...{deployedToken.address.slice(-8)}</span>
                <Copy className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          <div className="bg-luxury-slate rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Transaction Hash:</span>
              <button
                onClick={() => copyToClipboard(deployedToken.txHash)}
                className="flex items-center space-x-2 text-luxury-gold hover:text-yellow-400 transition-colors"
              >
                <span className="font-mono text-sm">{deployedToken.txHash.slice(0, 10)}...{deployedToken.txHash.slice(-8)}</span>
                <Copy className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
        
        <div className="flex space-x-4">
          <button
            onClick={() => {
              setDeployedToken(null)
              setTokenData({
                name: '',
                symbol: '',
                totalSupply: '',
                decimals: 18,
                description: ''
              })
            }}
            className="luxury-button-outline flex-1"
          >
            Deploy Another Token
          </button>
          <button
            onClick={() => window.open(`https://etherscan.io/address/${deployedToken.address}`, '_blank')}
            className="luxury-button flex-1"
          >
            View on Etherscan
          </button>
        </div>
      </motion.div>
    )
  }

  return (
    <div className="luxury-card">
      <div className="flex items-center space-x-3 mb-6">
        <Coins className="w-6 h-6 text-luxury-gold" />
        <h3 className="text-xl font-semibold text-white">Create ERC20 Token</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Token Name *
            </label>
            <input
              type="text"
              value={tokenData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="e.g., Chainsight Token"
              className="luxury-input w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Token Symbol *
            </label>
            <input
              type="text"
              value={tokenData.symbol}
              onChange={(e) => handleInputChange('symbol', e.target.value.toUpperCase())}
              placeholder="e.g., CST"
              className="luxury-input w-full"
              maxLength={10}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Total Supply *
            </label>
            <input
              type="number"
              value={tokenData.totalSupply}
              onChange={(e) => handleInputChange('totalSupply', e.target.value)}
              placeholder="e.g., 1000000"
              className="luxury-input w-full"
              min="1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Decimals
            </label>
            <select
              value={tokenData.decimals}
              onChange={(e) => handleInputChange('decimals', parseInt(e.target.value))}
              className="luxury-input w-full"
            >
              <option value={18}>18 (Standard)</option>
              <option value={8}>8</option>
              <option value={6}>6</option>
              <option value={0}>0</option>
            </select>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description
            </label>
            <textarea
              value={tokenData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe your token's purpose and utility..."
              className="luxury-textarea w-full h-32"
            />
          </div>

          <div className="bg-luxury-slate rounded-lg p-4">
            <h4 className="font-medium text-white mb-2">Token Preview</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Name:</span>
                <span className="text-white">{tokenData.name || 'Not set'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Symbol:</span>
                <span className="text-white">{tokenData.symbol || 'Not set'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Supply:</span>
                <span className="text-white">{tokenData.totalSupply || '0'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Decimals:</span>
                <span className="text-white">{tokenData.decimals}</span>
              </div>
            </div>
          </div>

          {!isConnected && (
            <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-yellow-500" />
                <span className="text-yellow-500 text-sm">
                  Connect your wallet to deploy tokens
                </span>
              </div>
            </div>
          )}

          <button
            onClick={deployToken}
            disabled={isDeploying || !isConnected}
            className="luxury-button w-full disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDeploying ? (
              <div className="flex items-center justify-center space-x-2">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>Deploying Token...</span>
              </div>
            ) : (
              'Deploy Token'
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
