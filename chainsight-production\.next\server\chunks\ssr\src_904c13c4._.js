module.exports = {

"[project]/src/fpga-core/interfaces/signal-types.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * FPGA Signal Type Definitions
 * Standardized signal types for inter-module communication
 */ __turbopack_context__.s({
    "createSignal": (()=>createSignal),
    "isValidSignalType": (()=>isValidSignalType)
});
function isValidSignalType(type) {
    const validTypes = [
        'crypto.analysis',
        'crypto.price',
        'crypto.portfolio',
        'finance.analysis',
        'finance.recommendation',
        'finance.risk',
        'legal.contract',
        'legal.compliance',
        'legal.advice',
        'design.generate',
        'design.analyze',
        'design.optimize',
        'face.detect',
        'face.recognize',
        'face.verify',
        'support.query',
        'support.response',
        'support.escalate',
        'web3.transaction',
        'web3.contract',
        'web3.wallet',
        'system.status',
        'system.error',
        'system.metric'
    ];
    return validTypes.includes(type);
}
function createSignal(type, data, source, priority = 'normal') {
    return {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type,
        data,
        timestamp: Date.now(),
        source,
        priority,
        metadata: {}
    };
}
}}),
"[project]/src/fpga-core/signal-routing/router.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * FPGA Signal Router - Central data routing system
 * Manages signal flow between processing units with minimal latency
 */ __turbopack_context__.s({
    "SignalRouter": (()=>SignalRouter),
    "globalRouter": (()=>globalRouter)
});
class SignalRouter {
    routes = new Map();
    processingUnits = new Map();
    signalQueue = [];
    metrics = {
        totalSignals: 0,
        routedSignals: 0,
        droppedSignals: 0,
        averageLatency: 0,
        throughput: 0
    };
    isProcessing = false;
    subscribers = new Map();
    constructor(){
        this.startProcessingLoop();
    }
    // Processing Unit Management
    registerProcessingUnit(pu) {
        this.processingUnits.set(pu.config.id, pu);
        console.log(`🔧 Registered PU: ${pu.config.name} (${pu.config.id})`);
    }
    unregisterProcessingUnit(id) {
        this.processingUnits.delete(id);
        console.log(`🔧 Unregistered PU: ${id}`);
    }
    // Route Management
    addRoute(signalType, config) {
        if (!this.routes.has(signalType)) {
            this.routes.set(signalType, []);
        }
        this.routes.get(signalType).push(config);
        console.log(`🛤️ Added route for ${signalType}`);
    }
    removeRoute(signalType, config) {
        const routes = this.routes.get(signalType);
        if (routes) {
            const index = routes.indexOf(config);
            if (index > -1) {
                routes.splice(index, 1);
            }
        }
    }
    // Signal Processing
    async routeSignal(signal) {
        const startTime = Date.now();
        this.metrics.totalSignals++;
        try {
            // Add to queue for processing
            this.signalQueue.push(signal);
            // Notify subscribers
            this.notifySubscribers(signal);
            // Update metrics
            const latency = Date.now() - startTime;
            this.updateLatencyMetrics(latency);
        } catch (error) {
            console.error('❌ Signal routing error:', error);
            this.metrics.droppedSignals++;
        }
    }
    async processSignalQueue() {
        if (this.isProcessing || this.signalQueue.length === 0) return;
        this.isProcessing = true;
        try {
            // Sort by priority
            this.signalQueue.sort((a, b)=>{
                const priorityOrder = {
                    critical: 4,
                    high: 3,
                    normal: 2,
                    low: 1
                };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            });
            // Process signals in batches
            const batchSize = 10;
            const batch = this.signalQueue.splice(0, batchSize);
            await Promise.all(batch.map((signal)=>this.processSignal(signal)));
        } finally{
            this.isProcessing = false;
        }
    }
    async processSignal(signal) {
        const routes = this.routes.get(signal.type) || [];
        for (const route of routes){
            try {
                // Check condition
                if (route.condition && !route.condition(signal)) continue;
                // Transform signal if needed
                let processedSignal = signal;
                if (route.transform) {
                    processedSignal = route.transform(signal);
                }
                // Route to target processing units
                await this.deliverSignal(processedSignal, route);
                this.metrics.routedSignals++;
            } catch (error) {
                console.error('❌ Route processing error:', error);
                this.metrics.droppedSignals++;
            }
        }
    }
    async deliverSignal(signal, route) {
        const targets = Array.isArray(route.to) ? route.to : [
            route.to
        ];
        for (const target of targets){
            const pu = this.processingUnits.get(target);
            if (pu && pu.canProcess(signal)) {
                try {
                    const result = await pu.process(signal);
                    if (result) {
                        // Route result signals
                        const results = Array.isArray(result) ? result : [
                            result
                        ];
                        for (const resultSignal of results){
                            await this.routeSignal(resultSignal);
                        }
                    }
                } catch (error) {
                    console.error(`❌ PU ${target} processing error:`, error);
                }
            }
        }
    }
    // Subscription Management
    subscribe(signalType, callback) {
        if (!this.subscribers.has(signalType)) {
            this.subscribers.set(signalType, new Set());
        }
        this.subscribers.get(signalType).add(callback);
        // Return unsubscribe function
        return ()=>{
            this.subscribers.get(signalType)?.delete(callback);
        };
    }
    notifySubscribers(signal) {
        const callbacks = this.subscribers.get(signal.type);
        if (callbacks) {
            callbacks.forEach((callback)=>{
                try {
                    callback(signal);
                } catch (error) {
                    console.error('❌ Subscriber callback error:', error);
                }
            });
        }
    }
    // Processing Loop
    startProcessingLoop() {
        setInterval(()=>{
            this.processSignalQueue();
            this.updateThroughputMetrics();
        }, 10); // 100Hz processing frequency
    }
    // Metrics
    updateLatencyMetrics(latency) {
        this.metrics.averageLatency = (this.metrics.averageLatency * (this.metrics.totalSignals - 1) + latency) / this.metrics.totalSignals;
    }
    updateThroughputMetrics() {
        // Calculate throughput over last second
        this.metrics.throughput = this.metrics.routedSignals; // Simplified
    }
    getMetrics() {
        return {
            ...this.metrics
        };
    }
    // Utility Methods
    getRegisteredPUs() {
        return Array.from(this.processingUnits.keys());
    }
    getRoutes() {
        return new Map(this.routes);
    }
    async shutdown() {
        // Stop all processing units
        for (const pu of this.processingUnits.values()){
            await pu.stop();
        }
        // Clear queues
        this.signalQueue = [];
        this.subscribers.clear();
        console.log('🔧 Signal Router shutdown complete');
    }
}
const globalRouter = new SignalRouter();
}}),
"[project]/src/fpga-core/interconnect/fabric.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * FPGA Interconnect Fabric - Communication layer for all processing units
 */ __turbopack_context__.s({
    "InterconnectFabric": (()=>InterconnectFabric),
    "globalFabric": (()=>globalFabric)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/fpga-core/interfaces/signal-types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$signal$2d$routing$2f$router$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/fpga-core/signal-routing/router.ts [app-ssr] (ecmascript)");
;
;
class InterconnectFabric {
    router;
    processingUnits = new Map();
    config;
    metrics = {
        registeredPUs: 0,
        activePUs: 0,
        totalSignals: 0,
        signalsPerSecond: 0,
        averageLatency: 0,
        errorRate: 0
    };
    healthCheckTimer;
    isInitialized = false;
    constructor(config = {}){
        this.config = {
            maxProcessingUnits: 20,
            signalBufferSize: 1000,
            healthCheckInterval: 5000,
            autoRecovery: true,
            ...config
        };
        this.router = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$signal$2d$routing$2f$router$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["globalRouter"];
        this.setupDefaultRoutes();
    }
    async initialize() {
        if (this.isInitialized) return;
        console.log('🔧 Initializing FPGA Interconnect Fabric...');
        // Start health monitoring
        this.startHealthMonitoring();
        // Setup error handling
        this.setupErrorHandling();
        this.isInitialized = true;
        console.log('✅ FPGA Interconnect Fabric initialized');
    }
    // Processing Unit Management
    async registerProcessingUnit(pu) {
        if (this.processingUnits.size >= this.config.maxProcessingUnits) {
            throw new Error('Maximum processing units limit reached');
        }
        try {
            // Initialize the processing unit
            await pu.initialize();
            await pu.start();
            // Register with router
            this.router.registerProcessingUnit(pu);
            this.processingUnits.set(pu.config.id, pu);
            // Update metrics
            this.metrics.registeredPUs = this.processingUnits.size;
            this.updateActivePUs();
            console.log(`✅ Registered PU: ${pu.config.name} (${pu.config.id})`);
            // Emit registration event
            await this.emitSignal((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('system.status', {
                component: pu.config.id,
                status: 'online'
            }, 'fabric', 'normal'));
        } catch (error) {
            console.error(`❌ Failed to register PU ${pu.config.id}:`, error);
            throw error;
        }
    }
    async unregisterProcessingUnit(id) {
        const pu = this.processingUnits.get(id);
        if (!pu) return;
        try {
            // Stop the processing unit
            await pu.stop();
            // Unregister from router
            this.router.unregisterProcessingUnit(id);
            this.processingUnits.delete(id);
            // Update metrics
            this.metrics.registeredPUs = this.processingUnits.size;
            this.updateActivePUs();
            console.log(`🔄 Unregistered PU: ${id}`);
        } catch (error) {
            console.error(`❌ Failed to unregister PU ${id}:`, error);
        }
    }
    // Signal Management
    async emitSignal(signal) {
        try {
            this.metrics.totalSignals++;
            await this.router.routeSignal(signal);
        } catch (error) {
            console.error('❌ Signal emission error:', error);
            this.metrics.errorRate = (this.metrics.errorRate + 1) / this.metrics.totalSignals;
        }
    }
    subscribeToSignals(signalType, callback) {
        return this.router.subscribe(signalType, callback);
    }
    // Health Monitoring
    startHealthMonitoring() {
        this.healthCheckTimer = setInterval(async ()=>{
            await this.performHealthCheck();
        }, this.config.healthCheckInterval);
    }
    async performHealthCheck() {
        const unhealthyPUs = [];
        for (const [id, pu] of this.processingUnits.entries()){
            try {
                const isHealthy = await pu.healthCheck();
                if (!isHealthy) {
                    unhealthyPUs.push(id);
                }
            } catch (error) {
                console.error(`❌ Health check failed for PU ${id}:`, error);
                unhealthyPUs.push(id);
            }
        }
        // Handle unhealthy PUs
        if (unhealthyPUs.length > 0 && this.config.autoRecovery) {
            await this.recoverUnhealthyPUs(unhealthyPUs);
        }
        this.updateActivePUs();
        this.updateMetrics();
    }
    async recoverUnhealthyPUs(unhealthyPUs) {
        for (const id of unhealthyPUs){
            const pu = this.processingUnits.get(id);
            if (!pu) continue;
            try {
                console.log(`🔄 Attempting to recover PU: ${id}`);
                await pu.reset();
                await pu.start();
                console.log(`✅ Recovered PU: ${id}`);
            } catch (error) {
                console.error(`❌ Failed to recover PU ${id}:`, error);
            // Consider removing the PU if recovery fails multiple times
            }
        }
    }
    // Default Signal Routes
    setupDefaultRoutes() {
        // Route crypto queries to crypto PU
        this.router.addRoute('crypto.analysis', {
            from: '*',
            to: 'crypto-pu'
        });
        this.router.addRoute('crypto.price', {
            from: '*',
            to: 'crypto-pu'
        });
        // Route support queries to support PU
        this.router.addRoute('support.query', {
            from: '*',
            to: 'support-pu'
        });
        // Route system status to all PUs for monitoring
        this.router.addRoute('system.status', {
            from: '*',
            to: [
                'crypto-pu',
                'support-pu'
            ],
            condition: (signal)=>signal.data.component === 'system'
        });
    }
    // Error Handling
    setupErrorHandling() {
        // Subscribe to error signals
        this.router.subscribe('system.error', (signal)=>{
            console.error('🚨 System error received:', signal.data);
            this.handleSystemError(signal);
        });
    }
    async handleSystemError(signal) {
        const errorData = signal.data;
        // Log error
        console.error(`🚨 System Error in ${errorData.component}:`, errorData.error);
        // Attempt recovery if auto-recovery is enabled
        if (this.config.autoRecovery && errorData.component) {
            const pu = this.processingUnits.get(errorData.component);
            if (pu) {
                try {
                    await pu.reset();
                    console.log(`🔄 Auto-recovered PU: ${errorData.component}`);
                } catch (error) {
                    console.error(`❌ Auto-recovery failed for ${errorData.component}:`, error);
                }
            }
        }
    }
    // Metrics & Monitoring
    updateActivePUs() {
        let activePUs = 0;
        for (const pu of this.processingUnits.values()){
            if (pu.state.status === 'idle' || pu.state.status === 'processing') {
                activePUs++;
            }
        }
        this.metrics.activePUs = activePUs;
    }
    updateMetrics() {
        const routerMetrics = this.router.getMetrics();
        this.metrics.signalsPerSecond = routerMetrics.throughput;
        this.metrics.averageLatency = routerMetrics.averageLatency;
        this.metrics.errorRate = routerMetrics.droppedSignals / Math.max(routerMetrics.totalSignals, 1);
    }
    getMetrics() {
        return {
            ...this.metrics
        };
    }
    getProcessingUnits() {
        return Array.from(this.processingUnits.values());
    }
    getProcessingUnit(id) {
        return this.processingUnits.get(id);
    }
    // Shutdown
    async shutdown() {
        console.log('🔄 Shutting down FPGA Interconnect Fabric...');
        // Stop health monitoring
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
        }
        // Stop all processing units
        for (const pu of this.processingUnits.values()){
            try {
                await pu.stop();
            } catch (error) {
                console.error('❌ Error stopping PU:', error);
            }
        }
        // Shutdown router
        await this.router.shutdown();
        this.isInitialized = false;
        console.log('✅ FPGA Interconnect Fabric shutdown complete');
    }
}
const globalFabric = new InterconnectFabric();
}}),
"[project]/src/fpga-core/interfaces/pu-interface.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * FPGA-Inspired Processing Unit Interface
 * Standardized interface for all AI modules
 */ __turbopack_context__.s({
    "BaseProcessingUnit": (()=>BaseProcessingUnit)
});
class BaseProcessingUnit {
    config;
    _state;
    constructor(config){
        this.config = config;
        this._state = {
            status: 'idle',
            load: 0,
            lastActivity: Date.now(),
            errorCount: 0,
            processedSignals: 0
        };
    }
    get state() {
        return {
            ...this._state
        };
    }
    async initialize() {
        this._state.status = 'idle';
    }
    async start() {
        this._state.status = 'idle';
    }
    async stop() {
        this._state.status = 'disabled';
    }
    async reset() {
        this._state = {
            status: 'idle',
            load: 0,
            lastActivity: Date.now(),
            errorCount: 0,
            processedSignals: 0
        };
    }
    setResourceLimits(limits) {
        Object.assign(this.config.resources, limits);
    }
    async healthCheck() {
        return this._state.status !== 'error';
    }
    getMetrics() {
        const now = Date.now();
        return {
            uptime: now - this._state.lastActivity,
            throughput: this._state.processedSignals / ((now - this._state.lastActivity) / 1000),
            latency: 0,
            errorRate: this._state.errorCount / Math.max(this._state.processedSignals, 1),
            resourceEfficiency: 100 - this._state.load
        };
    }
    updateState(updates) {
        this._state = {
            ...this._state,
            ...updates,
            lastActivity: Date.now()
        };
    }
}
}}),
"[project]/src/fpga-core/processing-units/crypto-pu/crypto-pu.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Crypto Processing Unit - FPGA-style AI module for cryptocurrency analysis
 */ __turbopack_context__.s({
    "CryptoProcessingUnit": (()=>CryptoProcessingUnit)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$pu$2d$interface$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/fpga-core/interfaces/pu-interface.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/fpga-core/interfaces/signal-types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-ssr] (ecmascript) <export OpenAI as default>");
;
;
;
class CryptoProcessingUnit extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$pu$2d$interface$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseProcessingUnit"] {
    openai;
    priceCache = new Map();
    analysisCache = new Map();
    constructor(){
        const config = {
            id: 'crypto-pu',
            name: 'Crypto Analysis Processing Unit',
            version: '1.0.0',
            capabilities: [
                'crypto.analysis',
                'crypto.price',
                'crypto.portfolio',
                'market.sentiment',
                'trading.signals'
            ],
            resources: {
                memory: 256,
                cpu: 30,
                gpu: 10 // % usage (for ML models)
            },
            dependencies: [
                'openai'
            ]
        };
        super(config);
        this.openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
            apiKey: ("TURBOPACK compile-time value", "********************************************************************************************************************************************************************"),
            dangerouslyAllowBrowser: true
        });
    }
    async initialize() {
        await super.initialize();
        console.log('🪙 Crypto PU initialized');
    }
    canProcess(signal) {
        return signal.type.startsWith('crypto.') || signal.type === 'system.status' || signal.type === 'support.query' && this.isCryptoRelated(signal.data);
    }
    async process(signal) {
        this.updateState({
            status: 'processing',
            load: 50
        });
        try {
            let result = null;
            switch(signal.type){
                case 'crypto.analysis':
                    result = await this.analyzeCrypto(signal);
                    break;
                case 'crypto.price':
                    result = await this.getPriceData(signal);
                    break;
                case 'crypto.portfolio':
                    result = await this.analyzePortfolio(signal);
                    break;
                case 'support.query':
                    if (this.isCryptoRelated(signal.data)) {
                        result = await this.handleCryptoQuery(signal);
                    }
                    break;
                default:
                    console.warn(`🪙 Crypto PU: Unsupported signal type ${signal.type}`);
            }
            this.updateState({
                status: 'idle',
                load: 0,
                processedSignals: this.state.processedSignals + 1
            });
            return result;
        } catch (error) {
            console.error('🪙 Crypto PU processing error:', error);
            this.updateState({
                status: 'error',
                load: 0,
                errorCount: this.state.errorCount + 1
            });
            return null;
        }
    }
    async analyzeCrypto(signal) {
        const data = signal.data;
        const cacheKey = `${data.symbol}-analysis`;
        // Check cache (5 minute expiry)
        const cached = this.analysisCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < 300000) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('crypto.analysis', {
                ...data,
                analysis: cached.analysis,
                confidence: 0.85
            }, this.config.id, 'normal');
        }
        try {
            const prompt = `Analyze ${data.symbol} cryptocurrency:
        Current Price: $${data.price || 'N/A'}
        Volume: ${data.volume || 'N/A'}
        Market Cap: $${data.marketCap || 'N/A'}
        
        Provide a concise technical analysis with:
        1. Price trend assessment
        2. Support/resistance levels
        3. Trading recommendation (buy/sell/hold)
        4. Risk assessment
        
        Keep response under 200 words.`;
            const response = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 300,
                temperature: 0.7
            });
            const analysis = response.choices[0]?.message?.content || 'Analysis unavailable';
            // Extract recommendation
            const recommendation = this.extractRecommendation(analysis);
            // Cache result
            this.analysisCache.set(cacheKey, {
                analysis,
                timestamp: Date.now()
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('crypto.analysis', {
                ...data,
                analysis,
                recommendation,
                confidence: 0.85
            }, this.config.id, 'normal');
        } catch (error) {
            console.error('🪙 Crypto analysis error:', error);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('crypto.analysis', {
                ...data,
                analysis: 'Analysis temporarily unavailable',
                recommendation: 'hold',
                confidence: 0.1
            }, this.config.id, 'low');
        }
    }
    async getPriceData(signal) {
        const data = signal.data;
        try {
            // Simulate price fetch (in real implementation, use actual API)
            const mockPrice = Math.random() * 50000 + 20000; // BTC-like price
            const mockVolume = Math.random() * 1000000000;
            const priceData = {
                ...data,
                price: mockPrice,
                volume: mockVolume,
                marketCap: mockPrice * 19000000 // Approximate BTC supply
            };
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('crypto.price', priceData, this.config.id, 'normal');
        } catch (error) {
            console.error('🪙 Price fetch error:', error);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('system.error', {
                component: this.config.id,
                error: {
                    code: 'PRICE_FETCH_ERROR',
                    message: 'Failed to fetch price data'
                }
            }, this.config.id, 'high');
        }
    }
    async analyzePortfolio(signal) {
        const data = signal.data; // Portfolio data
        try {
            const prompt = `Analyze this crypto portfolio and provide insights:
        ${JSON.stringify(data, null, 2)}
        
        Provide:
        1. Portfolio diversification assessment
        2. Risk analysis
        3. Rebalancing recommendations
        4. Overall performance outlook`;
            const response = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 400,
                temperature: 0.7
            });
            const analysis = response.choices[0]?.message?.content || 'Portfolio analysis unavailable';
            return [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('crypto.analysis', {
                    symbol: 'PORTFOLIO',
                    analysis,
                    recommendation: 'hold',
                    confidence: 0.8
                }, this.config.id, 'normal')
            ];
        } catch (error) {
            console.error('🪙 Portfolio analysis error:', error);
            return [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('system.error', {
                    component: this.config.id,
                    error: {
                        code: 'PORTFOLIO_ANALYSIS_ERROR',
                        message: 'Failed to analyze portfolio'
                    }
                }, this.config.id, 'high')
            ];
        }
    }
    async handleCryptoQuery(signal) {
        const query = signal.data.query;
        try {
            const response = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a cryptocurrency expert. Provide helpful, accurate information about crypto markets, trading, and blockchain technology.'
                    },
                    {
                        role: 'user',
                        content: query
                    }
                ],
                max_tokens: 300,
                temperature: 0.7
            });
            const answer = response.choices[0]?.message?.content || 'Unable to process crypto query';
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('support.response', {
                query,
                response: answer,
                sentiment: 'neutral',
                resolved: true
            }, this.config.id, 'normal');
        } catch (error) {
            console.error('🪙 Crypto query error:', error);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('support.response', {
                query,
                response: 'I apologize, but I cannot process your crypto query at the moment.',
                sentiment: 'neutral',
                resolved: false
            }, this.config.id, 'low');
        }
    }
    isCryptoRelated(data) {
        const query = data.query?.toLowerCase() || '';
        const cryptoKeywords = [
            'bitcoin',
            'btc',
            'ethereum',
            'eth',
            'crypto',
            'cryptocurrency',
            'blockchain',
            'trading',
            'price',
            'market',
            'portfolio',
            'wallet'
        ];
        return cryptoKeywords.some((keyword)=>query.includes(keyword));
    }
    extractRecommendation(analysis) {
        const lower = analysis.toLowerCase();
        if (lower.includes('buy') || lower.includes('bullish')) return 'buy';
        if (lower.includes('sell') || lower.includes('bearish')) return 'sell';
        return 'hold';
    }
    getResourceUsage() {
        return {
            memory: this.priceCache.size * 0.1 + this.analysisCache.size * 0.5,
            cpu: this.state.load,
            gpu: this.state.status === 'processing' ? 10 : 0
        };
    }
}
}}),
"[project]/src/fpga-core/processing-units/support-pu/support-pu.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Support Processing Unit - AI customer support module
 */ __turbopack_context__.s({
    "SupportProcessingUnit": (()=>SupportProcessingUnit)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$pu$2d$interface$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/fpga-core/interfaces/pu-interface.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/fpga-core/interfaces/signal-types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-ssr] (ecmascript) <export OpenAI as default>");
;
;
;
class SupportProcessingUnit extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$pu$2d$interface$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseProcessingUnit"] {
    openai;
    conversationHistory = new Map();
    constructor(){
        const config = {
            id: 'support-pu',
            name: 'Customer Support Processing Unit',
            version: '1.0.0',
            capabilities: [
                'support.query',
                'support.response',
                'support.escalate',
                'sentiment.analysis',
                'conversation.management'
            ],
            resources: {
                memory: 128,
                cpu: 25
            },
            dependencies: [
                'openai'
            ]
        };
        super(config);
        this.openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
            apiKey: ("TURBOPACK compile-time value", "********************************************************************************************************************************************************************"),
            dangerouslyAllowBrowser: true
        });
    }
    async initialize() {
        await super.initialize();
        console.log('🎧 Support PU initialized');
    }
    canProcess(signal) {
        return signal.type.startsWith('support.') || signal.type === 'system.status';
    }
    async process(signal) {
        this.updateState({
            status: 'processing',
            load: 40
        });
        try {
            let result = null;
            switch(signal.type){
                case 'support.query':
                    result = await this.handleQuery(signal);
                    break;
                case 'support.escalate':
                    result = await this.handleEscalation(signal);
                    break;
                default:
                    console.warn(`🎧 Support PU: Unsupported signal type ${signal.type}`);
            }
            this.updateState({
                status: 'idle',
                load: 0,
                processedSignals: this.state.processedSignals + 1
            });
            return result;
        } catch (error) {
            console.error('🎧 Support PU processing error:', error);
            this.updateState({
                status: 'error',
                load: 0,
                errorCount: this.state.errorCount + 1
            });
            return null;
        }
    }
    async handleQuery(signal) {
        const data = signal.data;
        const userId = data.user || 'anonymous';
        try {
            // Get conversation history
            const history = this.conversationHistory.get(userId) || [];
            // Analyze sentiment
            const sentiment = this.analyzeSentiment(data.query);
            // Generate response
            const messages = [
                {
                    role: 'system',
                    content: `You are Connectouch, a helpful AI assistant for the Chainsight platform. 
                   Provide friendly, professional support for crypto, finance, legal, design, and Web3 questions.
                   Keep responses concise and helpful. If you need to escalate, mention it clearly.`
                },
                ...history.slice(-6),
                {
                    role: 'user',
                    content: data.query
                }
            ];
            const response = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages,
                max_tokens: 300,
                temperature: 0.7
            });
            const answer = response.choices[0]?.message?.content || 'I apologize, but I cannot process your request at the moment.';
            // Update conversation history
            history.push({
                role: 'user',
                content: data.query
            }, {
                role: 'assistant',
                content: answer
            });
            this.conversationHistory.set(userId, history);
            // Determine if escalation is needed
            const needsEscalation = this.shouldEscalate(data.query, answer, sentiment);
            const responseData = {
                query: data.query,
                context: data.context,
                user: userId,
                priority: data.priority,
                response: answer,
                sentiment,
                resolved: !needsEscalation
            };
            const results = [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('support.response', responseData, this.config.id, 'normal')
            ];
            // Add escalation signal if needed
            if (needsEscalation) {
                results.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('support.escalate', {
                    ...responseData,
                    priority: 'high'
                }, this.config.id, 'high'));
            }
            return results.length === 1 ? results[0] : results;
        } catch (error) {
            console.error('🎧 Query handling error:', error);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('support.response', {
                query: data.query,
                user: userId,
                priority: data.priority,
                response: 'I apologize, but I\'m experiencing technical difficulties. Please try again later.',
                sentiment: 'neutral',
                resolved: false
            }, this.config.id, 'low');
        }
    }
    async handleEscalation(signal) {
        const data = signal.data;
        // Log escalation
        console.log(`🚨 Support escalation for user ${data.user}: ${data.query}`);
        // In a real system, this would notify human agents
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('system.status', {
            component: 'support-escalation',
            status: 'online',
            metrics: {
                cpu: 0,
                memory: 0,
                latency: 0,
                throughput: 1
            }
        }, this.config.id, 'high');
    }
    analyzeSentiment(text) {
        const positiveWords = [
            'good',
            'great',
            'excellent',
            'love',
            'amazing',
            'perfect',
            'thank'
        ];
        const negativeWords = [
            'bad',
            'terrible',
            'hate',
            'awful',
            'broken',
            'error',
            'problem',
            'issue'
        ];
        const words = text.toLowerCase().split(/\s+/);
        let score = 0;
        words.forEach((word)=>{
            if (positiveWords.includes(word)) score++;
            if (negativeWords.includes(word)) score--;
        });
        if (score > 0) return 'positive';
        if (score < 0) return 'negative';
        return 'neutral';
    }
    shouldEscalate(query, response, sentiment) {
        const escalationKeywords = [
            'urgent',
            'emergency',
            'critical',
            'bug',
            'broken',
            'not working',
            'refund',
            'cancel',
            'complaint',
            'manager',
            'supervisor'
        ];
        const queryLower = query.toLowerCase();
        const hasEscalationKeyword = escalationKeywords.some((keyword)=>queryLower.includes(keyword));
        const isNegativeSentiment = sentiment === 'negative';
        const responseIndicatesEscalation = response.toLowerCase().includes('escalate') || response.toLowerCase().includes('human agent');
        return hasEscalationKeyword || isNegativeSentiment || responseIndicatesEscalation;
    }
    getResourceUsage() {
        return {
            memory: this.conversationHistory.size * 0.5,
            cpu: this.state.load
        };
    }
    // Cleanup old conversations
    cleanupConversations() {
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours
        const now = Date.now();
        for (const [userId, history] of this.conversationHistory.entries()){
            if (history.length === 0) continue;
            const lastMessage = history[history.length - 1];
            if (now - (lastMessage.timestamp || 0) > maxAge) {
                this.conversationHistory.delete(userId);
            }
        }
    }
}
}}),
"[project]/src/fpga-core/chainsight-fpga.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Chainsight FPGA System - Main controller for the FPGA-inspired architecture
 */ __turbopack_context__.s({
    "ChainsightFPGA": (()=>ChainsightFPGA),
    "getChainsightFPGA": (()=>getChainsightFPGA),
    "initializeChainsight": (()=>initializeChainsight),
    "useChainsightFPGA": (()=>useChainsightFPGA)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interconnect$2f$fabric$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/fpga-core/interconnect/fabric.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$processing$2d$units$2f$crypto$2d$pu$2f$crypto$2d$pu$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/fpga-core/processing-units/crypto-pu/crypto-pu.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$processing$2d$units$2f$support$2d$pu$2f$support$2d$pu$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/fpga-core/processing-units/support-pu/support-pu.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/fpga-core/interfaces/signal-types.ts [app-ssr] (ecmascript)");
;
;
;
;
class ChainsightFPGA {
    fabric;
    config;
    isInitialized = false;
    eventListeners = new Map();
    constructor(config = {}){
        this.config = {
            enabledModules: [
                'crypto',
                'support',
                'finance',
                'legal',
                'design',
                'face',
                'web3'
            ],
            debugMode: ("TURBOPACK compile-time value", "development") === 'development',
            maxConcurrentSignals: 100,
            ...config
        };
        this.fabric = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interconnect$2f$fabric$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["globalFabric"];
    }
    async initialize() {
        if (this.isInitialized) return;
        console.log('🚀 Initializing Chainsight FPGA System...');
        try {
            // Initialize the interconnect fabric
            await this.fabric.initialize();
            // Register processing units
            await this.registerProcessingUnits();
            // Setup event subscriptions
            this.setupEventSubscriptions();
            this.isInitialized = true;
            console.log('✅ Chainsight FPGA System initialized successfully');
            // Emit system ready signal
            await this.emitSignal('system.status', {
                component: 'chainsight-fpga',
                status: 'online',
                metrics: {
                    cpu: 0,
                    memory: 0,
                    latency: 0,
                    throughput: 0
                }
            });
        } catch (error) {
            console.error('❌ Failed to initialize Chainsight FPGA:', error);
            throw error;
        }
    }
    async registerProcessingUnits() {
        const registrations = [];
        // Register Crypto PU
        if (this.config.enabledModules.includes('crypto')) {
            const cryptoPU = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$processing$2d$units$2f$crypto$2d$pu$2f$crypto$2d$pu$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CryptoProcessingUnit"]();
            registrations.push(this.fabric.registerProcessingUnit(cryptoPU));
        }
        // Register Support PU
        if (this.config.enabledModules.includes('support')) {
            const supportPU = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$processing$2d$units$2f$support$2d$pu$2f$support$2d$pu$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SupportProcessingUnit"]();
            registrations.push(this.fabric.registerProcessingUnit(supportPU));
        }
        // Wait for all registrations to complete
        await Promise.all(registrations);
        console.log(`✅ Registered ${registrations.length} processing units`);
    }
    setupEventSubscriptions() {
        // Subscribe to all signal types for event forwarding
        const signalTypes = [
            'crypto.analysis',
            'crypto.price',
            'crypto.portfolio',
            'support.query',
            'support.response',
            'support.escalate',
            'system.status',
            'system.error',
            'system.metric'
        ];
        signalTypes.forEach((signalType)=>{
            this.fabric.subscribeToSignals(signalType, (signal)=>{
                this.forwardEvent(signalType, signal);
            });
        });
    }
    // Public API Methods
    async analyzeCrypto(symbol, data) {
        const signal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('crypto.analysis', {
            symbol,
            ...data
        }, 'chainsight-api', 'normal');
        return this.emitAndWaitForResponse(signal, 'crypto.analysis');
    }
    async getCryptoPrice(symbol) {
        const signal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('crypto.price', {
            symbol
        }, 'chainsight-api', 'normal');
        return this.emitAndWaitForResponse(signal, 'crypto.price');
    }
    async askSupport(query, user, priority = 'normal') {
        const signal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])('support.query', {
            query,
            user,
            priority
        }, 'chainsight-api', priority);
        return this.emitAndWaitForResponse(signal, 'support.response');
    }
    async emitSignal(type, data, priority = 'normal') {
        const signal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$interfaces$2f$signal$2d$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignal"])(type, data, 'chainsight-api', priority);
        await this.fabric.emitSignal(signal);
    }
    async emitAndWaitForResponse(signal, expectedResponseType, timeout = 10000) {
        return new Promise((resolve, reject)=>{
            const timeoutId = setTimeout(()=>{
                reject(new Error(`Timeout waiting for ${expectedResponseType} response`));
            }, timeout);
            // Subscribe to response
            const unsubscribe = this.fabric.subscribeToSignals(expectedResponseType, (responseSignal)=>{
                clearTimeout(timeoutId);
                unsubscribe();
                resolve(responseSignal.data);
            });
            // Emit the signal
            this.fabric.emitSignal(signal).catch(reject);
        });
    }
    // Event System
    addEventListener(eventType, callback) {
        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, new Set());
        }
        this.eventListeners.get(eventType).add(callback);
        // Return unsubscribe function
        return ()=>{
            this.eventListeners.get(eventType)?.delete(callback);
        };
    }
    forwardEvent(eventType, signal) {
        const listeners = this.eventListeners.get(eventType);
        if (listeners) {
            listeners.forEach((callback)=>{
                try {
                    callback(signal.data);
                } catch (error) {
                    console.error(`❌ Event listener error for ${eventType}:`, error);
                }
            });
        }
    }
    // System Information
    getSystemMetrics() {
        return {
            fabric: this.fabric.getMetrics(),
            processingUnits: this.fabric.getProcessingUnits().map((pu)=>({
                    id: pu.config.id,
                    name: pu.config.name,
                    state: pu.state,
                    metrics: pu.getMetrics(),
                    resourceUsage: pu.getResourceUsage()
                }))
        };
    }
    getProcessingUnits() {
        return this.fabric.getProcessingUnits().map((pu)=>({
                id: pu.config.id,
                name: pu.config.name,
                capabilities: pu.config.capabilities,
                state: pu.state
            }));
    }
    isReady() {
        return this.isInitialized;
    }
    // Shutdown
    async shutdown() {
        console.log('🔄 Shutting down Chainsight FPGA System...');
        try {
            await this.fabric.shutdown();
            this.eventListeners.clear();
            this.isInitialized = false;
            console.log('✅ Chainsight FPGA System shutdown complete');
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
        }
    }
}
// Global instance
let globalChainsight = null;
function getChainsightFPGA(config) {
    if (!globalChainsight) {
        globalChainsight = new ChainsightFPGA(config);
    }
    return globalChainsight;
}
async function initializeChainsight(config) {
    const chainsight = getChainsightFPGA(config);
    await chainsight.initialize();
    return chainsight;
}
function useChainsightFPGA() {
    const chainsight = getChainsightFPGA();
    return {
        chainsight,
        isReady: chainsight.isReady(),
        analyzeCrypto: chainsight.analyzeCrypto.bind(chainsight),
        getCryptoPrice: chainsight.getCryptoPrice.bind(chainsight),
        askSupport: chainsight.askSupport.bind(chainsight),
        getSystemMetrics: chainsight.getSystemMetrics.bind(chainsight),
        getProcessingUnits: chainsight.getProcessingUnits.bind(chainsight),
        addEventListener: chainsight.addEventListener.bind(chainsight)
    };
}
}}),
"[project]/src/components/FPGAChainsight.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>FPGAChainsight)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$chainsight$2d$fpga$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/fpga-core/chainsight-fpga.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function FPGAChainsight() {
    const { chainsight, isReady, askSupport, analyzeCrypto, getCryptoPrice, getSystemMetrics } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$chainsight$2d$fpga$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useChainsightFPGA"])();
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [input, setInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [systemMetrics, setSystemMetrics] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [selectedModule, setSelectedModule] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('support');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Initialize Chainsight FPGA system
        const init = async ()=>{
            try {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$fpga$2d$core$2f$chainsight$2d$fpga$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initializeChainsight"])({
                    openaiApiKey: ("TURBOPACK compile-time value", "********************************************************************************************************************************************************************"),
                    debugMode: true
                });
                // Add welcome message
                setMessages([
                    {
                        id: '1',
                        type: 'assistant',
                        content: '🚀 Welcome to Chainsight FPGA! I\'m powered by a modular AI architecture. How can I help you today?',
                        timestamp: Date.now(),
                        module: 'system'
                    }
                ]);
                // Update metrics periodically
                const metricsInterval = setInterval(()=>{
                    setSystemMetrics(getSystemMetrics());
                }, 2000);
                return ()=>clearInterval(metricsInterval);
            } catch (error) {
                console.error('Failed to initialize Chainsight:', error);
            }
        };
        init();
    }, []);
    const handleSendMessage = async ()=>{
        if (!input.trim() || isLoading || !isReady) return;
        const userMessage = {
            id: Date.now().toString(),
            type: 'user',
            content: input,
            timestamp: Date.now()
        };
        setMessages((prev)=>[
                ...prev,
                userMessage
            ]);
        setInput('');
        setIsLoading(true);
        try {
            let response;
            let responseModule = selectedModule;
            // Route to appropriate module based on selection or content
            if (selectedModule === 'crypto' || input.toLowerCase().includes('crypto') || input.toLowerCase().includes('bitcoin')) {
                if (input.toLowerCase().includes('price')) {
                    response = await getCryptoPrice('BTC');
                    responseModule = 'crypto-price';
                } else {
                    response = await analyzeCrypto('BTC', {
                        query: input
                    });
                    responseModule = 'crypto-analysis';
                }
            } else {
                response = await askSupport(input, 'user', 'normal');
                responseModule = 'support';
            }
            const assistantMessage = {
                id: (Date.now() + 1).toString(),
                type: 'assistant',
                content: response.response || response.analysis || JSON.stringify(response, null, 2),
                timestamp: Date.now(),
                module: responseModule
            };
            setMessages((prev)=>[
                    ...prev,
                    assistantMessage
                ]);
        } catch (error) {
            console.error('Error processing message:', error);
            const errorMessage = {
                id: (Date.now() + 1).toString(),
                type: 'assistant',
                content: 'I apologize, but I encountered an error processing your request. Please try again.',
                timestamp: Date.now(),
                module: 'error'
            };
            setMessages((prev)=>[
                    ...prev,
                    errorMessage
                ]);
        } finally{
            setIsLoading(false);
        }
    };
    const handleKeyPress = (e)=>{
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };
    const getModuleColor = (module)=>{
        const colors = {
            'crypto': 'text-yellow-400',
            'crypto-price': 'text-green-400',
            'crypto-analysis': 'text-orange-400',
            'support': 'text-blue-400',
            'system': 'text-purple-400',
            'error': 'text-red-400'
        };
        return colors[module] || 'text-gray-400';
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-black/20 backdrop-blur-sm border-b border-purple-500/30 p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent",
                                    children: "Chainsight FPGA"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FPGAChainsight.tsx",
                                    lineNumber: 136,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-400 text-sm",
                                    children: [
                                        "Status: ",
                                        isReady ? '🟢 Online' : '🔴 Initializing...'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/FPGAChainsight.tsx",
                                    lineNumber: 139,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/FPGAChainsight.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-2",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                value: selectedModule,
                                onChange: (e)=>setSelectedModule(e.target.value),
                                className: "bg-gray-800 border border-purple-500/30 rounded px-3 py-1 text-white",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                        value: "support",
                                        children: "💬 Support"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                        lineNumber: 151,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                        value: "crypto",
                                        children: "🪙 Crypto"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                        lineNumber: 152,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                        value: "finance",
                                        children: "💰 Finance"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                        lineNumber: 153,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                        value: "legal",
                                        children: "⚖️ Legal"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                        lineNumber: 154,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                lineNumber: 146,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/FPGAChainsight.tsx",
                            lineNumber: 145,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/FPGAChainsight.tsx",
                    lineNumber: 134,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/FPGAChainsight.tsx",
                lineNumber: 133,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-6xl mx-auto p-4 grid grid-cols-1 lg:grid-cols-4 gap-4 h-[calc(100vh-100px)]",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "lg:col-span-3 bg-black/20 backdrop-blur-sm rounded-lg border border-purple-500/30 flex flex-col",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1 overflow-y-auto p-4 space-y-4",
                                children: [
                                    messages.map((message)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `max-w-[80%] p-3 rounded-lg ${message.type === 'user' ? 'bg-purple-600 text-white' : 'bg-gray-800 text-gray-100 border border-gray-700'}`,
                                                children: [
                                                    message.type === 'assistant' && message.module && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-xs ${getModuleColor(message.module)} mb-1`,
                                                        children: message.module.toUpperCase()
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                        lineNumber: 178,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "whitespace-pre-wrap",
                                                        children: message.content
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                        lineNumber: 182,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xs opacity-60 mt-1",
                                                        children: new Date(message.timestamp).toLocaleTimeString()
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                        lineNumber: 183,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                lineNumber: 170,
                                                columnNumber: 17
                                            }, this)
                                        }, message.id, false, {
                                            fileName: "[project]/src/components/FPGAChainsight.tsx",
                                            lineNumber: 166,
                                            columnNumber: 15
                                        }, this)),
                                    isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex justify-start",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-gray-800 text-gray-100 border border-gray-700 p-3 rounded-lg",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center space-x-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "animate-spin rounded-full h-4 w-4 border-b-2 border-purple-400"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                        lineNumber: 193,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: "Processing..."
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                        lineNumber: 194,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                lineNumber: 192,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/FPGAChainsight.tsx",
                                            lineNumber: 191,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                        lineNumber: 190,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                lineNumber: 164,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-4 border-t border-purple-500/30",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                            value: input,
                                            onChange: (e)=>setInput(e.target.value),
                                            onKeyPress: handleKeyPress,
                                            placeholder: `Ask ${selectedModule} module anything...`,
                                            className: "flex-1 bg-gray-800 border border-purple-500/30 rounded px-3 py-2 text-white placeholder-gray-400 resize-none",
                                            rows: 2,
                                            disabled: !isReady || isLoading
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/FPGAChainsight.tsx",
                                            lineNumber: 204,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: handleSendMessage,
                                            disabled: !isReady || isLoading || !input.trim(),
                                            className: "bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-4 py-2 rounded transition-colors",
                                            children: "Send"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/FPGAChainsight.tsx",
                                            lineNumber: 213,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/FPGAChainsight.tsx",
                                    lineNumber: 203,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                lineNumber: 202,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                        lineNumber: 162,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-black/20 backdrop-blur-sm rounded-lg border border-purple-500/30 p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-semibold text-white mb-4",
                                children: "System Metrics"
                            }, void 0, false, {
                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                lineNumber: 226,
                                columnNumber: 11
                            }, this),
                            systemMetrics ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4 text-sm",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-purple-400 font-medium mb-2",
                                                children: "Interconnect Fabric"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                lineNumber: 232,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-1 text-gray-300",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "Active PUs: ",
                                                            systemMetrics.fabric.activePUs,
                                                            "/",
                                                            systemMetrics.fabric.registeredPUs
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                        lineNumber: 234,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "Signals/sec: ",
                                                            systemMetrics.fabric.signalsPerSecond.toFixed(1)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                        lineNumber: 235,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "Avg Latency: ",
                                                            systemMetrics.fabric.averageLatency.toFixed(1),
                                                            "ms"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                        lineNumber: 236,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "Error Rate: ",
                                                            (systemMetrics.fabric.errorRate * 100).toFixed(1),
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                        lineNumber: 237,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                lineNumber: 233,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                        lineNumber: 231,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-blue-400 font-medium mb-2",
                                                children: "Processing Units"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                lineNumber: 243,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-2",
                                                children: systemMetrics.processingUnits.map((pu)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "bg-gray-800/50 p-2 rounded",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-white font-medium",
                                                                children: pu.name
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                                lineNumber: 247,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-xs text-gray-400",
                                                                children: [
                                                                    "Status: ",
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: pu.state.status === 'idle' ? 'text-green-400' : 'text-yellow-400',
                                                                        children: pu.state.status
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                                        lineNumber: 249,
                                                                        columnNumber: 33
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                                lineNumber: 248,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-xs text-gray-400",
                                                                children: [
                                                                    "Load: ",
                                                                    pu.state.load,
                                                                    "% | Processed: ",
                                                                    pu.state.processedSignals
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                                lineNumber: 253,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, pu.id, true, {
                                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                        lineNumber: 246,
                                                        columnNumber: 21
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                                lineNumber: 244,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                                        lineNumber: 242,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                lineNumber: 229,
                                columnNumber: 13
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-gray-400",
                                children: "Loading metrics..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/FPGAChainsight.tsx",
                                lineNumber: 262,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/FPGAChainsight.tsx",
                        lineNumber: 225,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/FPGAChainsight.tsx",
                lineNumber: 160,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/FPGAChainsight.tsx",
        lineNumber: 131,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/ClientOnly.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ClientOnly": (()=>ClientOnly)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
function ClientOnly({ children, fallback = null }) {
    const [hasMounted, setHasMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setHasMounted(true);
    }, []);
    if (!hasMounted) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: fallback
        }, void 0, false);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
}}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>HomePage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$FPGAChainsight$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/FPGAChainsight.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ClientOnly$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ClientOnly.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
function HomePage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ClientOnly$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ClientOnly"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$FPGAChainsight$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 11,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_904c13c4._.js.map