module.exports = {

"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_add_f158e7ec.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_all-wallets_885482e8.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom-circle.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-bottom-circle_69ae8121.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom-circle.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/app-store.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_app-store_29c5e3d6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/app-store.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_apple_9158157f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-bottom_9e31ab45.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-left.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-left_15455b2f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-left.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-right.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-right_cb66a0dd.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-right.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-top.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-top_3e640605.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-top.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/bank.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_bank_f79a765f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/bank.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/browser.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_browser_1ea0a1c9.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/browser.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/card.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_card_9f3e1099.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/card.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_checkmark_3fb95576.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark-bold.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_checkmark-bold_a96ee94b.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark-bold.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-bottom.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-bottom_92f6d070.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-bottom.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-left.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-left_6948e0d0.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-left.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-right.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-right_1e4103db.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-right.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-top.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-top_e551dea8.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-top.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chrome-store.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chrome-store_f05e2ccd.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chrome-store.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/clock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_clock_132112b6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/clock.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/close.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_close_fd91513e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/close.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/compass.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_compass_99c5944e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/compass.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/coinPlaceholder.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_coinPlaceholder_da8183ac.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/coinPlaceholder.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/copy.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_copy_078dedbd.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/copy.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_cursor_dc5ec722.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_cursor-transparent_08c58615.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/desktop.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_desktop_3a2a3a46.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/desktop.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/disconnect.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_disconnect_13e84801.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/disconnect.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/discord.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_discord_3e22e89f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/discord.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_etherscan_0c82c24d.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/extension.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_extension_00fc2bd4.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/extension.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/external-link.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_external-link_fd469e45.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/external-link.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/facebook.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_facebook_e43f5f50.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/facebook.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_farcaster_db714709.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/filters.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_filters_664aa42b.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/filters.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_github_81427df0.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/google.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_google_cd210b3f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/google.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/help-circle.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_help-circle_51c78f6d.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/help-circle.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/image.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_image_b3d39002.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/image.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/id.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_id_2540c926.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/id.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info-circle.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_info-circle_6a4031de.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info-circle.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/lightbulb.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_lightbulb_c547feeb.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/lightbulb.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mail.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_mail_f6a223b3.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mail.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mobile.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_mobile_8ca413e1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mobile.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/more.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_more_ce8bbc24.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/more.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/network-placeholder.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_network-placeholder_f13d362a.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/network-placeholder.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/nftPlaceholder.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_nftPlaceholder_b1beb960.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/nftPlaceholder.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/off.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_off_6046f8b6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/off.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_play-store_1bbd85b1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_plus_e4a4138b.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_qr-code_1adb3376.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/recycle-horizontal.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_recycle-horizontal_a938a0f4.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/recycle-horizontal.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/refresh.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_refresh_7e4cbf89.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/refresh.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/search.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_search_07c2a801.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/search.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/send.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_send_af8698f9.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/send.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontal.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontal_23a79a86.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontal.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalMedium.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalMedium_e3602620.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalMedium.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalBold_d1d91801.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalRoundedBold.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/8069e_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalRoundedBold_1cb36050.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalRoundedBold.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapVertical.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapVertical_f0320387.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapVertical.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_telegram_9d9d185c.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/three-dots.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_three-dots_25906122.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/three-dots.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_twitch_f8f3ef5f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_x_5850fe41.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitterIcon.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_twitterIcon_18e9734f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitterIcon.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_verify_c6a8abee.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify-filled.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_verify-filled_4e1c060f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify-filled.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_wallet_c699a9df.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_walletconnect_b37e5dde.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet-placeholder.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_wallet-placeholder_9900578b.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet-placeholder.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/warning-circle.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_warning-circle_6fc3a632.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/warning-circle.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_info_afb9b21c.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/exclamation-triangle.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_exclamation-triangle_3d6a0efb.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/exclamation-triangle.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_reown-logo_09a67e6d.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js [app-ssr] (ecmascript)");
    });
});
}}),

};