'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Wallet, Copy, ExternalLink, RefreshCw, Send, ArrowDownUp } from 'lucide-react'
import { useAccount, useBalance, useDisconnect } from 'wagmi'
import { useWeb3Modal } from '@web3modal/react'
import toast from 'react-hot-toast'

interface TokenBalance {
  symbol: string
  balance: string
  value: string
  address: string
}

export function WalletConnector() {
  const { address, isConnected } = useAccount()
  const { disconnect } = useDisconnect()
  const { open } = useWeb3Modal()
  const { data: ethBalance } = useBalance({ address })
  
  const [tokenBalances, setTokenBalances] = useState<TokenBalance[]>([
    { symbol: 'ETH', balance: '0.0', value: '$0.00', address: '******************************************' },
    { symbol: 'USDC', balance: '0.0', value: '$0.00', address: '******************************************' },
    { symbol: 'USDT', balance: '0.0', value: '$0.00', address: '******************************************' },
  ])
  const [isRefreshing, setIsRefreshing] = useState(false)

  useEffect(() => {
    if (ethBalance) {
      setTokenBalances(prev => prev.map(token => 
        token.symbol === 'ETH' 
          ? { ...token, balance: parseFloat(ethBalance.formatted).toFixed(4) }
          : token
      ))
    }
  }, [ethBalance])

  const copyAddress = () => {
    if (address) {
      navigator.clipboard.writeText(address)
      toast.success('Address copied to clipboard!')
    }
  }

  const refreshBalances = async () => {
    setIsRefreshing(true)
    // Simulate API call to refresh balances
    setTimeout(() => {
      setIsRefreshing(false)
      toast.success('Balances refreshed!')
    }, 2000)
  }

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`
  }

  if (!isConnected) {
    return (
      <div className="luxury-card text-center">
        <div className="w-16 h-16 bg-gradient-to-r from-luxury-gold to-yellow-400 rounded-full flex items-center justify-center mx-auto mb-6">
          <Wallet className="w-8 h-8 text-black" />
        </div>
        
        <h3 className="text-2xl font-bold text-white mb-4">Connect Your Wallet</h3>
        <p className="text-gray-400 mb-8 max-w-md mx-auto">
          Connect your Web3 wallet to access crypto features, deploy tokens, and manage liquidity pools.
        </p>
        
        <button
          onClick={() => open()}
          className="luxury-button"
        >
          Connect Wallet
        </button>
        
        <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
          {['MetaMask', 'WalletConnect', 'Coinbase', 'Trust Wallet'].map((wallet) => (
            <div key={wallet} className="bg-luxury-slate rounded-lg p-4 text-center">
              <div className="w-8 h-8 bg-luxury-gold rounded-full mx-auto mb-2" />
              <span className="text-sm text-gray-400">{wallet}</span>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Wallet Info Card */}
      <div className="luxury-card">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Wallet className="w-6 h-6 text-luxury-gold" />
            <h3 className="text-xl font-semibold text-white">Wallet Overview</h3>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={refreshBalances}
              disabled={isRefreshing}
              className="p-2 bg-luxury-slate rounded-lg hover:bg-luxury-gold hover:text-black transition-colors"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={() => disconnect()}
              className="luxury-button-outline text-sm"
            >
              Disconnect
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Wallet Address
              </label>
              <div className="flex items-center space-x-2">
                <div className="luxury-input flex-1 font-mono text-sm">
                  {formatAddress(address!)}
                </div>
                <button
                  onClick={copyAddress}
                  className="p-2 bg-luxury-slate rounded-lg hover:bg-luxury-gold hover:text-black transition-colors"
                >
                  <Copy className="w-4 h-4" />
                </button>
                <button
                  onClick={() => window.open(`https://etherscan.io/address/${address}`, '_blank')}
                  className="p-2 bg-luxury-slate rounded-lg hover:bg-luxury-gold hover:text-black transition-colors"
                >
                  <ExternalLink className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Network
              </label>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full" />
                <span className="text-white">Ethereum Mainnet</span>
              </div>
            </div>
          </div>

          <div className="bg-luxury-slate rounded-lg p-4">
            <h4 className="font-medium text-white mb-3">Portfolio Value</h4>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-white">$0.00</div>
              <div className="text-sm text-gray-400">24h Change: +0.00%</div>
            </div>
          </div>
        </div>
      </div>

      {/* Token Balances */}
      <div className="luxury-card">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white">Token Balances</h3>
          <div className="flex space-x-2">
            <button className="luxury-button-outline text-sm">
              <Send className="w-4 h-4 mr-1" />
              Send
            </button>
            <button className="luxury-button-outline text-sm">
              <ArrowDownUp className="w-4 h-4 mr-1" />
              Swap
            </button>
          </div>
        </div>

        <div className="space-y-3">
          {tokenBalances.map((token, index) => (
            <motion.div
              key={token.symbol}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex items-center justify-between p-4 bg-luxury-slate rounded-lg hover:bg-luxury-slate/80 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-luxury-gold to-yellow-400 rounded-full flex items-center justify-center">
                  <span className="text-black font-bold text-sm">{token.symbol}</span>
                </div>
                <div>
                  <div className="font-medium text-white">{token.symbol}</div>
                  <div className="text-sm text-gray-400">{token.value}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium text-white">{token.balance}</div>
                <div className="text-sm text-gray-400">{token.symbol}</div>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
          <p className="text-blue-400 text-sm">
            <strong>Note:</strong> Token balances are fetched in real-time from the blockchain. 
            Refresh to see the latest updates.
          </p>
        </div>
      </div>

      {/* Recent Transactions */}
      <div className="luxury-card">
        <h3 className="text-xl font-semibold text-white mb-6">Recent Transactions</h3>
        
        <div className="space-y-3">
          {[
            { type: 'Send', amount: '0.1 ETH', to: '0x1234...5678', status: 'Confirmed', time: '2 hours ago' },
            { type: 'Receive', amount: '100 USDC', from: '0x9876...4321', status: 'Confirmed', time: '1 day ago' },
            { type: 'Swap', amount: '0.05 ETH → 150 USDC', to: 'Uniswap', status: 'Confirmed', time: '3 days ago' },
          ].map((tx, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-luxury-slate rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  tx.type === 'Send' ? 'bg-red-500/20 text-red-400' :
                  tx.type === 'Receive' ? 'bg-green-500/20 text-green-400' :
                  'bg-blue-500/20 text-blue-400'
                }`}>
                  {tx.type === 'Send' ? '↗' : tx.type === 'Receive' ? '↙' : '↔'}
                </div>
                <div>
                  <div className="font-medium text-white">{tx.type}</div>
                  <div className="text-sm text-gray-400">{tx.amount}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-green-400">{tx.status}</div>
                <div className="text-sm text-gray-400">{tx.time}</div>
              </div>
            </div>
          ))}
        </div>

        <button className="w-full mt-4 luxury-button-outline">
          View All Transactions
        </button>
      </div>
    </div>
  )
}
