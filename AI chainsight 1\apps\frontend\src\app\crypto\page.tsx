'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Zap, Coins, TrendingUp, Wallet, Plus, ArrowUpDown } from 'lucide-react'
import { TokenCreator } from '@/components/crypto/TokenCreator'
import { LiquidityProvider } from '@/components/crypto/LiquidityProvider'
import { WalletConnector } from '@/components/crypto/WalletConnector'

export default function CryptoPage() {
  const [activeTab, setActiveTab] = useState('create')

  const tabs = [
    { id: 'create', label: 'Create Token', icon: Plus },
    { id: 'liquidity', label: 'Add Liquidity', icon: ArrowUpDown },
    { id: 'wallet', label: 'Wallet', icon: Wallet },
  ]

  return (
    <div className="min-h-screen pt-20 pb-12 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Zap className="w-8 h-8 text-luxury-gold" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-luxury-gold to-yellow-400 bg-clip-text text-transparent">
              Crypto Engine
            </h1>
          </div>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            Deploy ERC20 tokens, manage liquidity pools, and interact with DeFi protocols seamlessly
          </p>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
        >
          {[
            { icon: Coins, label: 'Tokens Created', value: '1,247', change: '+12%' },
            { icon: TrendingUp, label: 'Total Volume', value: '$2.4M', change: '+8.5%' },
            { icon: Wallet, label: 'Active Wallets', value: '892', change: '+15%' },
          ].map((stat, index) => (
            <div key={index} className="luxury-card">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-luxury-gold to-yellow-400 rounded-lg flex items-center justify-center">
                  <stat.icon className="w-6 h-6 text-black" />
                </div>
                <span className="text-green-500 text-sm font-medium">{stat.change}</span>
              </div>
              <div className="space-y-1">
                <p className="text-2xl font-bold text-white">{stat.value}</p>
                <p className="text-gray-400 text-sm">{stat.label}</p>
              </div>
            </div>
          ))}
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="flex space-x-1 bg-luxury-charcoal rounded-lg p-1 mb-8"
        >
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-md transition-all duration-300 flex-1 justify-center ${
                activeTab === tab.id
                  ? 'bg-luxury-gold text-black font-semibold'
                  : 'text-gray-400 hover:text-white hover:bg-luxury-slate/50'
              }`}
            >
              <tab.icon className="w-5 h-5" />
              <span>{tab.label}</span>
            </button>
          ))}
        </motion.div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4 }}
        >
          {activeTab === 'create' && <TokenCreator />}
          {activeTab === 'liquidity' && <LiquidityProvider />}
          {activeTab === 'wallet' && <WalletConnector />}
        </motion.div>
      </div>
    </div>
  )
}
