export interface FaceDetection {
  id: string;
  bounding_box: BoundingBox;
  confidence: number;
  landmarks: FaceLandmarks;
  quality_score: number;
  pose: FacePose;
  timestamp: Date;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface FaceLandmarks {
  points: LandmarkPoint[];
  contours: FaceContours;
  visibility_scores: number[];
}

export interface LandmarkPoint {
  x: number;
  y: number;
  z?: number;
  visibility?: number;
}

export interface FaceContours {
  face_oval: LandmarkPoint[];
  left_eyebrow: LandmarkPoint[];
  right_eyebrow: LandmarkPoint[];
  left_eye: LandmarkPoint[];
  right_eye: LandmarkPoint[];
  nose: LandmarkPoint[];
  lips: LandmarkPoint[];
  chin: LandmarkPoint[];
}

export interface FacePose {
  yaw: number;   // Head rotation left/right
  pitch: number; // Head rotation up/down
  roll: number;  // Head tilt left/right
}

export interface FaceEmbedding {
  id: string;
  vector: number[];
  model_version: string;
  extraction_timestamp: Date;
  quality_score: number;
}

export interface FaceMatch {
  person_id: string;
  confidence: number;
  distance: number;
  matched_embedding_id: string;
  verification_score: number;
}

export interface BiometricTemplate {
  id: string;
  person_id: string;
  embeddings: FaceEmbedding[];
  created_at: Date;
  updated_at: Date;
  quality_threshold: number;
  enrollment_images: number;
}

export interface EmotionResult {
  emotions: EmotionScore[];
  dominant_emotion: string;
  confidence: number;
  valence: number;    // Positive/negative emotion scale
  arousal: number;    // Intensity of emotion
  timestamp: Date;
}

export interface EmotionScore {
  emotion: string;
  score: number;
  confidence: number;
}

export interface DemographicResult {
  age: AgeEstimate;
  gender: GenderEstimate;
  ethnicity?: EthnicityEstimate;
  confidence: number;
  timestamp: Date;
}

export interface AgeEstimate {
  estimated_age: number;
  age_range: {
    min: number;
    max: number;
  };
  confidence: number;
}

export interface GenderEstimate {
  gender: 'male' | 'female' | 'unknown';
  confidence: number;
  probability_male: number;
  probability_female: number;
}

export interface EthnicityEstimate {
  ethnicity: string;
  confidence: number;
  probabilities: Record<string, number>;
}

export interface LivenessResult {
  is_live: boolean;
  confidence: number;
  liveness_score: number;
  checks: LivenessCheck[];
  timestamp: Date;
}

export interface LivenessCheck {
  type: 'blink' | 'head_movement' | 'texture_analysis' | 'depth_analysis';
  passed: boolean;
  score: number;
  details?: Record<string, any>;
}

export interface ScanResult {
  scan_id: string;
  detections: FaceDetection[];
  recognitions: FaceMatch[];
  analysis: FaceAnalysis;
  quality_assessment: QualityAssessment;
  processing_time: number;
  timestamp: Date;
  metadata: ScanMetadata;
}

export interface FaceAnalysis {
  emotions: EmotionResult;
  demographics: DemographicResult;
  liveness: LivenessResult;
  facial_attributes: FacialAttributes;
}

export interface FacialAttributes {
  glasses: boolean;
  sunglasses: boolean;
  mask: boolean;
  beard: boolean;
  mustache: boolean;
  smile: boolean;
  eyes_open: boolean;
  mouth_open: boolean;
  hair_color?: string;
  skin_tone?: string;
}

export interface QualityAssessment {
  overall_quality: number;
  sharpness: number;
  brightness: number;
  contrast: number;
  face_size: number;
  pose_quality: number;
  occlusion_score: number;
  is_suitable_for_recognition: boolean;
}

export interface ScanMetadata {
  image_width: number;
  image_height: number;
  image_format: string;
  model_versions: Record<string, string>;
  processing_pipeline: string[];
  device_info?: DeviceInfo;
}

export interface DeviceInfo {
  camera_id: string;
  resolution: string;
  fps: number;
  device_type: 'webcam' | 'mobile' | 'security_camera' | 'unknown';
}

export interface Person {
  id: string;
  name?: string;
  biometric_template: BiometricTemplate;
  enrollment_date: Date;
  last_seen?: Date;
  metadata: PersonMetadata;
}

export interface PersonMetadata {
  tags: string[];
  notes?: string;
  access_level?: string;
  department?: string;
  employee_id?: string;
  contact_info?: ContactInfo;
}

export interface ContactInfo {
  email?: string;
  phone?: string;
  address?: string;
}

export interface RecognitionConfig {
  confidence_threshold: number;
  max_distance_threshold: number;
  enable_liveness_check: boolean;
  enable_quality_check: boolean;
  min_face_size: number;
  max_pose_angle: number;
}

export interface EnrollmentConfig {
  min_images: number;
  max_images: number;
  quality_threshold: number;
  pose_variation_required: boolean;
  liveness_required: boolean;
  auto_improve: boolean;
}

export interface ScannerConfig {
  detection_model: string;
  recognition_model: string;
  emotion_model: string;
  age_gender_model: string;
  enable_gpu: boolean;
  batch_size: number;
  max_faces_per_image: number;
  enable_tracking: boolean;
}

export interface TrackingResult {
  track_id: string;
  person_id?: string;
  trajectory: TrajectoryPoint[];
  duration: number;
  confidence: number;
  last_update: Date;
}

export interface TrajectoryPoint {
  timestamp: Date;
  bounding_box: BoundingBox;
  confidence: number;
  landmarks?: FaceLandmarks;
}

export interface FaceSearchQuery {
  embedding?: number[];
  image?: Buffer | string;
  person_id?: string;
  confidence_threshold?: number;
  max_results?: number;
  filters?: SearchFilters;
}

export interface SearchFilters {
  age_range?: [number, number];
  gender?: 'male' | 'female';
  emotions?: string[];
  date_range?: [Date, Date];
  tags?: string[];
}

export interface SearchResult {
  matches: FaceMatch[];
  total_results: number;
  search_time: number;
  query_id: string;
}

export interface FaceCluster {
  id: string;
  representative_embedding: number[];
  member_count: number;
  members: string[]; // Person IDs
  created_at: Date;
  last_updated: Date;
  cluster_quality: number;
}

export interface AntiSpoofingResult {
  is_real: boolean;
  confidence: number;
  spoof_type?: 'photo' | 'video' | 'mask' | 'screen' | 'unknown';
  detection_methods: AntiSpoofingMethod[];
}

export interface AntiSpoofingMethod {
  method: string;
  score: number;
  passed: boolean;
  details?: Record<string, any>;
}

export interface FaceComparisonResult {
  similarity_score: number;
  is_same_person: boolean;
  confidence: number;
  distance: number;
  comparison_method: string;
  timestamp: Date;
}

export interface BatchProcessingResult {
  batch_id: string;
  total_images: number;
  processed_images: number;
  failed_images: number;
  results: ScanResult[];
  processing_time: number;
  errors: ProcessingError[];
}

export interface ProcessingError {
  image_id: string;
  error_type: string;
  error_message: string;
  timestamp: Date;
}
