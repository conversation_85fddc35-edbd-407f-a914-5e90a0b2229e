export interface MarketData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  symbol: string;
}

export interface PredictionResult {
  symbol: string;
  predictions: Array<{
    timestamp: number;
    predicted_price: number;
    confidence: number;
    lower_bound: number;
    upper_bound: number;
  }>;
  model_used: string;
  confidence: number;
  metadata: {
    training_data_points: number;
    features_used: string[];
    prediction_horizon: number;
    model_version: string;
  };
}

export interface ModelConfig {
  model_type: 'lstm' | 'transformer' | 'ensemble';
  sequence_length: number;
  prediction_horizon: number;
  features: string[];
  hyperparameters: {
    learning_rate: number;
    batch_size: number;
    epochs: number;
    dropout_rate: number;
    hidden_units?: number;
    num_layers?: number;
    attention_heads?: number;
  };
}

export interface TrainingData {
  features: number[][];
  targets: number[][];
  timestamps: number[];
  symbols: string[];
}

export interface PredictionOptions {
  model_type?: 'lstm' | 'transformer' | 'ensemble';
  confidence_interval?: number;
  include_technical_indicators?: boolean;
  include_sentiment_data?: boolean;
  custom_features?: string[];
}

export interface ModelMetrics {
  mse: number;
  mae: number;
  rmse: number;
  mape: number;
  r2_score: number;
  sharpe_ratio?: number;
  max_drawdown?: number;
  accuracy?: number;
}

export interface FeatureSet {
  price_features: number[];
  volume_features: number[];
  technical_indicators: number[];
  sentiment_features?: number[];
  macro_features?: number[];
}

export interface ModelState {
  model_id: string;
  model_type: string;
  is_trained: boolean;
  training_date: Date;
  performance_metrics: ModelMetrics;
  config: ModelConfig;
}

export interface PredictionRequest {
  symbol: string;
  timeframe: string;
  periods: number;
  options?: PredictionOptions;
}

export interface TrainingProgress {
  epoch: number;
  total_epochs: number;
  loss: number;
  val_loss: number;
  metrics: Partial<ModelMetrics>;
  estimated_time_remaining: number;
}
