{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.es.js", "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/constants/client.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/constants/history.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/constants/proposal.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/constants/session.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/constants/engine.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/constants/pendingRequest.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/constants/verify.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/constants/auth.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/controllers/engine.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/controllers/proposal.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/controllers/session.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/controllers/pendingRequest.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/controllers/authKey.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/controllers/authPairingTopic.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/controllers/authRequest.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/controllers/authStore.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/client.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit/node_modules/%40walletconnect/sign-client/src/index.ts"], "sourcesContent": ["import { SignClientTypes } from \"@walletconnect/types\";\n\nexport const SIGN_CLIENT_PROTOCOL = \"wc\";\nexport const SIGN_CLIENT_VERSION = 2;\nexport const SIGN_CLIENT_CONTEXT = \"client\";\n\nexport const SIGN_CLIENT_STORAGE_PREFIX = `${SIGN_CLIENT_PROTOCOL}@${SIGN_CLIENT_VERSION}:${SIGN_CLIENT_CONTEXT}:`;\n\nexport const SIGN_CLIENT_DEFAULT = {\n  name: SIGN_CLIENT_CONTEXT,\n  logger: \"error\",\n  controller: false,\n  relayUrl: \"wss://relay.walletconnect.org\",\n};\n\nexport const SIGN_CLIENT_EVENTS: Record<SignClientTypes.Event, SignClientTypes.Event> = {\n  session_proposal: \"session_proposal\",\n  session_update: \"session_update\",\n  session_extend: \"session_extend\",\n  session_ping: \"session_ping\",\n  session_delete: \"session_delete\",\n  session_expire: \"session_expire\",\n  session_request: \"session_request\",\n  session_request_sent: \"session_request_sent\",\n  session_event: \"session_event\",\n  proposal_expire: \"proposal_expire\",\n  session_authenticate: \"session_authenticate\",\n  session_request_expire: \"session_request_expire\",\n  session_connect: \"session_connect\",\n};\n\nexport const SIGN_CLIENT_STORAGE_OPTIONS = {\n  database: \":memory:\",\n};\n\nexport const WALLETCONNECT_DEEPLINK_CHOICE = \"WALLETCONNECT_DEEPLINK_CHOICE\";\n", "export const HISTORY_EVENTS = {\n  created: \"history_created\",\n  updated: \"history_updated\",\n  deleted: \"history_deleted\",\n  sync: \"history_sync\",\n};\n\nexport const HISTORY_CONTEXT = \"history\";\n\nexport const HISTORY_STORAGE_VERSION = \"0.3\";\n", "import { THIRTY_DAYS } from \"@walletconnect/time\";\n\nexport const PROPOSAL_CONTEXT = \"proposal\";\n\nexport const PROPOSAL_EXPIRY = THIRTY_DAYS;\n\nexport const PROPOSAL_EXPIRY_MESSAGE = \"Proposal expired\";\n", "import { SEVEN_DAYS } from \"@walletconnect/time\";\n\nexport const SESSION_CONTEXT = \"session\";\n\nexport const SESSION_EXPIRY = SEVEN_DAYS;\n", "import { FIVE_MINUTES, ONE_DAY, ONE_HOUR, SEVEN_DAYS } from \"@walletconnect/time\";\nimport { EngineTypes } from \"@walletconnect/types\";\n\nexport const ENGINE_CONTEXT = \"engine\";\n\nexport const ENGINE_RPC_OPTS: EngineTypes.RpcOptsMap = {\n  wc_sessionPropose: {\n    req: {\n      ttl: FIVE_MINUTES,\n      prompt: true,\n      tag: 1100,\n    },\n    res: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1101,\n    },\n    reject: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1120,\n    },\n    autoReject: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1121,\n    },\n  },\n  wc_sessionSettle: {\n    req: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1102,\n    },\n    res: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1103,\n    },\n  },\n  wc_sessionUpdate: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1104,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1105,\n    },\n  },\n  wc_sessionExtend: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1106,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1107,\n    },\n  },\n  wc_sessionRequest: {\n    req: {\n      ttl: FIVE_MINUTES,\n      prompt: true,\n      tag: 1108,\n    },\n    res: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1109,\n    },\n  },\n  wc_sessionEvent: {\n    req: {\n      ttl: FIVE_MINUTES,\n      prompt: true,\n      tag: 1110,\n    },\n    res: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1111,\n    },\n  },\n\n  wc_sessionDelete: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1112,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1113,\n    },\n  },\n  wc_sessionPing: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1114,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1115,\n    },\n  },\n  wc_sessionAuthenticate: {\n    req: {\n      ttl: ONE_HOUR,\n      prompt: true,\n      tag: 1116,\n    },\n    res: {\n      ttl: ONE_HOUR,\n      prompt: false,\n      tag: 1117,\n    },\n    reject: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1118,\n    },\n    autoReject: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1119,\n    },\n  },\n};\n\nexport const SESSION_REQUEST_EXPIRY_BOUNDARIES = {\n  min: FIVE_MINUTES,\n  max: SEVEN_DAYS,\n};\n\nexport const ENGINE_QUEUE_STATES: { idle: \"IDLE\"; active: \"ACTIVE\" } = {\n  idle: \"IDLE\",\n  active: \"ACTIVE\",\n};\n\nexport const TVF_METHODS = {\n  eth_sendTransaction: {\n    key: \"\",\n  },\n  eth_sendRawTransaction: {\n    key: \"\",\n  },\n  wallet_sendCalls: {\n    key: \"\",\n  },\n\n  solana_signTransaction: {\n    key: \"signature\",\n  },\n  solana_signAllTransactions: {\n    key: \"transactions\",\n  },\n  solana_signAndSendTransaction: {\n    key: \"signature\",\n  },\n};\n", "export const REQUEST_CONTEXT = \"request\";\n", "export const METHODS_TO_VERIFY = [\n  \"wc_sessionPropose\",\n  \"wc_sessionRequest\",\n  \"wc_authRequest\",\n  \"wc_sessionAuthenticate\",\n];\n", "export const AUTH_PROTOCOL = \"wc\";\nexport const AUTH_VERSION = 1.5;\nexport const AUTH_CONTEXT = \"auth\";\nexport const AUTH_KEYS_CONTEXT = \"authKeys\";\nexport const AUTH_PAIRING_TOPIC_CONTEXT = \"pairingTopics\";\nexport const AUTH_REQUEST_CONTEXT = \"requests\";\n\nexport const AUTH_STORAGE_PREFIX = `${AUTH_PROTOCOL}@${AUTH_VERSION}:${AUTH_CONTEXT}:`;\nexport const AUTH_PUBLIC_KEY_NAME = `${AUTH_STORAGE_PREFIX}:PUB_KEY`;\n", "/* eslint-disable no-console */\nimport {\n  EVENT_CLIENT_AUTHENTICATE_ERRORS,\n  EVENT_CLIENT_AUTHENTICATE_TRACES,\n  EVENT_CLIENT_PAIRING_ERRORS,\n  EVENT_CLIENT_PAIRING_TRACES,\n  EVENT_CLIENT_SESSION_ERRORS,\n  EVENT_CLIENT_SESSION_TRACES,\n  EXPIRER_EVENTS,\n  PAIRING_EVENTS,\n  RELAYER_DEFAULT_PROTOCOL,\n  RELAYER_EVENTS,\n  TRANSPORT_TYPES,\n  VERIFY_SERVER,\n} from \"@walletconnect/core\";\n\nimport {\n  formatJsonRpcError,\n  formatJsonRpcRequest,\n  formatJsonRpcResult,\n  payloadId,\n  isJsonRpcError,\n  isJsonRpcRequest,\n  isJsonRpcResponse,\n  isJsonRpcResult,\n  JsonRpcRequest,\n  ErrorResponse,\n  getBigIntRpcId,\n} from \"@walletconnect/jsonrpc-utils\";\nimport { FIVE_MINUTES, ONE_SECOND, toMiliseconds } from \"@walletconnect/time\";\nimport {\n  EnginePrivate,\n  EngineTypes,\n  ExpirerTypes,\n  IEngine,\n  IEngineEvents,\n  JsonRpcTypes,\n  PendingRequestTypes,\n  Verify,\n  CoreTypes,\n  ProposalTypes,\n  RelayerTypes,\n  SessionTypes,\n  PairingTypes,\n  AuthTypes,\n  EventClientTypes,\n} from \"@walletconnect/types\";\nimport {\n  calcExpiry,\n  createDelayedPromise,\n  engineEvent,\n  getInternalError,\n  getSdkError,\n  isConformingNamespaces,\n  isExpired,\n  isSessionCompatible,\n  isUndefined,\n  isValidController,\n  isValidErrorReason,\n  isValidEvent,\n  isValidId,\n  isValidNamespaces,\n  isValidNamespacesChainId,\n  isValidNamespacesEvent,\n  isValidNamespacesRequest,\n  isValidObject,\n  isValidParams,\n  isValidRelay,\n  isValidRelays,\n  isValidRequest,\n  isValidRequestExpiry,\n  hashMessage,\n  isValidRequiredNamespaces,\n  isValidResponse,\n  isValidString,\n  parseExpirerTarget,\n  TYPE_1,\n  TYPE_2,\n  handleDeeplinkRedirect,\n  MemoryStore,\n  getDeepLink,\n  hashKey,\n  getDidAddress,\n  formatMessage,\n  getMethodsFromRecap,\n  buildNamespacesFromAuth,\n  createEncodedRecap,\n  getChainsFromRecap,\n  mergeEncodedRecaps,\n  getRecapFromResources,\n  validateSignedCacao,\n  getNamespacedDidChainId,\n  parseChainId,\n  getLinkModeURL,\n  BASE64,\n  BASE64URL,\n  getSearchParamFromURL,\n  isReactNative,\n  isTestRun,\n  isValidArray,\n  extractSolanaTransactionId,\n  mergeRequiredAndOptionalNamespaces,\n} from \"@walletconnect/utils\";\nimport EventEmmiter from \"events\";\nimport {\n  ENGINE_CONTEXT,\n  ENGINE_RPC_OPTS,\n  PROPOSAL_EXPIRY_MESSAGE,\n  SESSION_EXPIRY,\n  SESSION_REQUEST_EXPIRY_BOUNDARIES,\n  METHODS_TO_VERIFY,\n  WALLETCONNECT_DEEPLINK_CHOICE,\n  ENGINE_QUEUE_STATES,\n  AUTH_PUBLIC_KEY_NAME,\n  TVF_METHODS,\n} from \"../constants\";\n\nexport class Engine extends IEngine {\n  public name = ENGINE_CONTEXT;\n\n  private events: IEngineEvents = new EventEmmiter();\n  private initialized = false;\n\n  /**\n   * Queue responsible for processing incoming requests such as session_update, session_event, session_ping etc\n   * It's needed when the client receives multiple requests at once from the mailbox immediately after initialization and to avoid attempting to process them all at the same time\n   */\n  private requestQueue: EngineTypes.EngineQueue<EngineTypes.EventCallback<JsonRpcRequest>> = {\n    state: ENGINE_QUEUE_STATES.idle,\n    queue: [],\n  };\n\n  /**\n   * Queue responsible for processing incoming session_request\n   * The queue emits the next request only after the previous one has been responded to\n   */\n  private sessionRequestQueue: EngineTypes.EngineQueue<PendingRequestTypes.Struct> = {\n    state: ENGINE_QUEUE_STATES.idle,\n    queue: [],\n  };\n\n  private requestQueueDelay = ONE_SECOND;\n  private expectedPairingMethodMap: Map<string, string[]> = new Map();\n  // Ephemeral (in-memory) map to store recently deleted items\n  private recentlyDeletedMap = new Map<\n    string | number,\n    \"pairing\" | \"session\" | \"proposal\" | \"request\"\n  >();\n\n  private recentlyDeletedLimit = 200;\n  private relayMessageCache: RelayerTypes.MessageEvent[] = [];\n  private pendingSessions: Map<\n    number,\n    {\n      sessionTopic: string;\n      pairingTopic: string;\n      proposalId: number;\n      publicKey: string;\n    }\n  > = new Map();\n\n  constructor(client: IEngine[\"client\"]) {\n    super(client);\n  }\n\n  public init: IEngine[\"init\"] = async () => {\n    if (!this.initialized) {\n      await this.cleanup();\n      this.registerRelayerEvents();\n      this.registerExpirerEvents();\n      this.registerPairingEvents();\n      await this.registerLinkModeListeners();\n      this.client.core.pairing.register({ methods: Object.keys(ENGINE_RPC_OPTS) });\n      this.initialized = true;\n      setTimeout(async () => {\n        await this.processPendingMessageEvents();\n\n        this.sessionRequestQueue.queue = this.getPendingSessionRequests();\n        this.processSessionRequestQueue();\n      }, toMiliseconds(this.requestQueueDelay));\n    }\n  };\n\n  private async processPendingMessageEvents() {\n    try {\n      const topics = this.client.session.keys;\n      const pendingMessages = this.client.core.relayer.messages.getWithoutAck(topics);\n      for (const [topic, messages] of Object.entries(pendingMessages)) {\n        for (const message of messages) {\n          try {\n            await this.onProviderMessageEvent({\n              topic,\n              message,\n              publishedAt: Date.now(),\n            });\n          } catch (error) {\n            this.client.logger.warn(\n              `Error processing pending message event for topic: ${topic}, message: ${message}`,\n            );\n          }\n        }\n      }\n    } catch (error) {\n      this.client.logger.warn(\"processPendingMessageEvents failed\", error);\n    }\n  }\n\n  // ---------- Public ------------------------------------------------ //\n\n  public connect: IEngine[\"connect\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    const connectParams = {\n      ...params,\n      requiredNamespaces: params.requiredNamespaces || {},\n      optionalNamespaces: params.optionalNamespaces || {},\n    };\n    await this.isValidConnect(connectParams);\n\n    // requiredNamespaces are deprecated, assign them to optionalNamespaces\n    connectParams.optionalNamespaces = mergeRequiredAndOptionalNamespaces(\n      connectParams.requiredNamespaces,\n      connectParams.optionalNamespaces,\n    );\n\n    connectParams.requiredNamespaces = {};\n\n    const {\n      pairingTopic,\n      requiredNamespaces,\n      optionalNamespaces,\n      sessionProperties,\n      scopedProperties,\n      relays,\n    } = connectParams;\n    let topic = pairingTopic;\n    let uri: string | undefined;\n    let active = false;\n    try {\n      if (topic) {\n        const pairing = this.client.core.pairing.pairings.get(topic);\n        this.client.logger.warn(\n          \"connect() with existing pairing topic is deprecated and will be removed in the next major release.\",\n        );\n        active = pairing.active;\n      }\n    } catch (error) {\n      this.client.logger.error(`connect() -> pairing.get(${topic}) failed`);\n      throw error;\n    }\n    if (!topic || !active) {\n      const { topic: newTopic, uri: newUri } = await this.client.core.pairing.create();\n      topic = newTopic;\n      uri = newUri;\n    }\n    // safety check to ensure pairing topic is available\n    if (!topic) {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `connect() pairing topic: ${topic}`);\n      throw new Error(message);\n    }\n\n    const publicKey = await this.client.core.crypto.generateKeyPair();\n\n    const expiry = ENGINE_RPC_OPTS.wc_sessionPropose.req.ttl || FIVE_MINUTES;\n    const expiryTimestamp = calcExpiry(expiry);\n    const proposal = {\n      requiredNamespaces,\n      optionalNamespaces,\n      relays: relays ?? [{ protocol: RELAYER_DEFAULT_PROTOCOL }],\n      proposer: {\n        publicKey,\n        metadata: this.client.metadata,\n      },\n      expiryTimestamp,\n      pairingTopic: topic,\n      ...(sessionProperties && { sessionProperties }),\n      ...(scopedProperties && { scopedProperties }),\n      id: payloadId(),\n    };\n    const sessionConnectTarget = engineEvent(\"session_connect\", proposal.id);\n\n    const {\n      reject,\n      resolve,\n      done: approval,\n    } = createDelayedPromise<SessionTypes.Struct>(expiry, PROPOSAL_EXPIRY_MESSAGE);\n\n    const proposalExpireHandler = ({ id }: { id: number }) => {\n      if (id === proposal.id) {\n        this.client.events.off(\"proposal_expire\", proposalExpireHandler);\n        this.pendingSessions.delete(proposal.id);\n        // emit the event to trigger reject, this approach automatically cleans up the .once listener below\n        this.events.emit(sessionConnectTarget, {\n          error: { message: PROPOSAL_EXPIRY_MESSAGE, code: 0 },\n        });\n      }\n    };\n\n    this.client.events.on(\"proposal_expire\", proposalExpireHandler);\n    this.events.once<\"session_connect\">(sessionConnectTarget, ({ error, session }) => {\n      this.client.events.off(\"proposal_expire\", proposalExpireHandler);\n      if (error) reject(error);\n      else if (session) {\n        resolve(session);\n      }\n    });\n\n    await this.sendRequest({\n      topic,\n      method: \"wc_sessionPropose\",\n      params: proposal,\n      throwOnFailedPublish: true,\n      clientRpcId: proposal.id,\n    });\n\n    await this.setProposal(proposal.id, proposal);\n    return { uri, approval };\n  };\n\n  public pair: IEngine[\"pair\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    try {\n      return await this.client.core.pairing.pair(params);\n    } catch (error) {\n      this.client.logger.error(\"pair() failed\");\n      throw error;\n    }\n  };\n\n  public approve: IEngine[\"approve\"] = async (params) => {\n    const configEvent = this.client.core.eventClient.createEvent({\n      properties: {\n        topic: params?.id?.toString(),\n        trace: [EVENT_CLIENT_SESSION_TRACES.session_approve_started],\n      },\n    });\n    try {\n      this.isInitialized();\n      await this.confirmOnlineStateOrThrow();\n    } catch (error) {\n      configEvent.setError(EVENT_CLIENT_SESSION_ERRORS.no_internet_connection);\n      throw error;\n    }\n    try {\n      await this.isValidProposalId(params?.id);\n    } catch (error) {\n      this.client.logger.error(`approve() -> proposal.get(${params?.id}) failed`);\n      configEvent.setError(EVENT_CLIENT_SESSION_ERRORS.proposal_not_found);\n      throw error;\n    }\n\n    try {\n      await this.isValidApprove(params);\n    } catch (error) {\n      this.client.logger.error(\"approve() -> isValidApprove() failed\");\n      configEvent.setError(\n        EVENT_CLIENT_SESSION_ERRORS.session_approve_namespace_validation_failure,\n      );\n      throw error;\n    }\n\n    const { id, relayProtocol, namespaces, sessionProperties, scopedProperties, sessionConfig } =\n      params;\n\n    const proposal = this.client.proposal.get(id);\n\n    this.client.core.eventClient.deleteEvent({ eventId: configEvent.eventId });\n\n    const { pairingTopic, proposer, requiredNamespaces, optionalNamespaces } = proposal;\n\n    let event = this.client.core.eventClient?.getEvent({\n      topic: pairingTopic,\n    }) as EventClientTypes.Event;\n    if (!event) {\n      event = this.client.core.eventClient?.createEvent({\n        type: EVENT_CLIENT_SESSION_TRACES.session_approve_started,\n        properties: {\n          topic: pairingTopic,\n          trace: [\n            EVENT_CLIENT_SESSION_TRACES.session_approve_started,\n            EVENT_CLIENT_SESSION_TRACES.session_namespaces_validation_success,\n          ],\n        },\n      });\n    }\n\n    const selfPublicKey = await this.client.core.crypto.generateKeyPair();\n    const peerPublicKey = proposer.publicKey;\n    const sessionTopic = await this.client.core.crypto.generateSharedKey(\n      selfPublicKey,\n      peerPublicKey,\n    );\n    const sessionSettle = {\n      relay: { protocol: relayProtocol ?? \"irn\" },\n      namespaces,\n      controller: { publicKey: selfPublicKey, metadata: this.client.metadata },\n      expiry: calcExpiry(SESSION_EXPIRY),\n      ...(sessionProperties && { sessionProperties }),\n      ...(scopedProperties && { scopedProperties }),\n      ...(sessionConfig && { sessionConfig }),\n    };\n    const transportType = TRANSPORT_TYPES.relay;\n    event.addTrace(EVENT_CLIENT_SESSION_TRACES.subscribing_session_topic);\n    try {\n      await this.client.core.relayer.subscribe(sessionTopic, { transportType });\n    } catch (error) {\n      event.setError(EVENT_CLIENT_SESSION_ERRORS.subscribe_session_topic_failure);\n      throw error;\n    }\n\n    event.addTrace(EVENT_CLIENT_SESSION_TRACES.subscribe_session_topic_success);\n\n    const session = {\n      ...sessionSettle,\n      topic: sessionTopic,\n      requiredNamespaces,\n      optionalNamespaces,\n      pairingTopic,\n      acknowledged: false,\n      self: sessionSettle.controller,\n      peer: {\n        publicKey: proposer.publicKey,\n        metadata: proposer.metadata,\n      },\n      controller: selfPublicKey,\n      transportType: TRANSPORT_TYPES.relay,\n    };\n    await this.client.session.set(sessionTopic, session);\n\n    event.addTrace(EVENT_CLIENT_SESSION_TRACES.store_session);\n\n    try {\n      event.addTrace(EVENT_CLIENT_SESSION_TRACES.publishing_session_settle);\n      await this.sendRequest({\n        topic: sessionTopic,\n        method: \"wc_sessionSettle\",\n        params: sessionSettle,\n        throwOnFailedPublish: true,\n      }).catch((error) => {\n        event?.setError(EVENT_CLIENT_SESSION_ERRORS.session_settle_publish_failure);\n        throw error;\n      });\n\n      event.addTrace(EVENT_CLIENT_SESSION_TRACES.session_settle_publish_success);\n\n      event.addTrace(EVENT_CLIENT_SESSION_TRACES.publishing_session_approve);\n      await this.sendResult<\"wc_sessionPropose\">({\n        id,\n        topic: pairingTopic,\n        result: {\n          relay: {\n            protocol: relayProtocol ?? \"irn\",\n          },\n          responderPublicKey: selfPublicKey,\n        },\n        throwOnFailedPublish: true,\n      }).catch((error) => {\n        event?.setError(EVENT_CLIENT_SESSION_ERRORS.session_approve_publish_failure);\n        throw error;\n      });\n\n      event.addTrace(EVENT_CLIENT_SESSION_TRACES.session_approve_publish_success);\n    } catch (error) {\n      this.client.logger.error(error);\n      // if the publish fails, delete the session and throw an error\n      this.client.session.delete(sessionTopic, getSdkError(\"USER_DISCONNECTED\"));\n      await this.client.core.relayer.unsubscribe(sessionTopic);\n      throw error;\n    }\n\n    this.client.core.eventClient.deleteEvent({ eventId: event.eventId });\n\n    await this.client.core.pairing.updateMetadata({\n      topic: pairingTopic,\n      metadata: proposer.metadata,\n    });\n    await this.client.proposal.delete(id, getSdkError(\"USER_DISCONNECTED\"));\n    await this.client.core.pairing.activate({ topic: pairingTopic });\n    await this.setExpiry(sessionTopic, calcExpiry(SESSION_EXPIRY));\n    return {\n      topic: sessionTopic,\n      acknowledged: () => Promise.resolve(this.client.session.get(sessionTopic)),\n    };\n  };\n\n  public reject: IEngine[\"reject\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    try {\n      await this.isValidReject(params);\n    } catch (error) {\n      this.client.logger.error(\"reject() -> isValidReject() failed\");\n      throw error;\n    }\n    const { id, reason } = params;\n    let pairingTopic;\n    try {\n      const proposal = this.client.proposal.get(id);\n      pairingTopic = proposal.pairingTopic;\n    } catch (error) {\n      this.client.logger.error(`reject() -> proposal.get(${id}) failed`);\n      throw error;\n    }\n\n    if (pairingTopic) {\n      await this.sendError({\n        id,\n        topic: pairingTopic,\n        error: reason,\n        rpcOpts: ENGINE_RPC_OPTS.wc_sessionPropose.reject,\n      });\n      await this.client.proposal.delete(id, getSdkError(\"USER_DISCONNECTED\"));\n    }\n  };\n\n  public update: IEngine[\"update\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    try {\n      await this.isValidUpdate(params);\n    } catch (error) {\n      this.client.logger.error(\"update() -> isValidUpdate() failed\");\n      throw error;\n    }\n    const { topic, namespaces } = params;\n\n    const { done: acknowledged, resolve, reject } = createDelayedPromise<void>();\n    const clientRpcId = payloadId();\n    const relayRpcId = getBigIntRpcId().toString() as any;\n\n    const oldNamespaces = this.client.session.get(topic).namespaces;\n    this.events.once(engineEvent(\"session_update\", clientRpcId), ({ error }: any) => {\n      if (error) reject(error);\n      else {\n        resolve();\n      }\n    });\n    // Update the session with the new namespaces, if the publish fails, revert to the old.\n    // This allows the client to use the updated session like emitting events\n    // without waiting for the peer to acknowledge\n    await this.client.session.update(topic, { namespaces });\n    await this.sendRequest({\n      topic,\n      method: \"wc_sessionUpdate\",\n      params: { namespaces },\n      throwOnFailedPublish: true,\n      clientRpcId,\n      relayRpcId,\n    }).catch((error) => {\n      this.client.logger.error(error);\n      this.client.session.update(topic, { namespaces: oldNamespaces });\n      reject(error);\n    });\n    return { acknowledged };\n  };\n\n  public extend: IEngine[\"extend\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    try {\n      await this.isValidExtend(params);\n    } catch (error) {\n      this.client.logger.error(\"extend() -> isValidExtend() failed\");\n      throw error;\n    }\n\n    const { topic } = params;\n    const clientRpcId = payloadId();\n    const { done: acknowledged, resolve, reject } = createDelayedPromise<void>();\n    this.events.once(engineEvent(\"session_extend\", clientRpcId), ({ error }: any) => {\n      if (error) reject(error);\n      else resolve();\n    });\n\n    await this.setExpiry(topic, calcExpiry(SESSION_EXPIRY));\n    this.sendRequest({\n      topic,\n      method: \"wc_sessionExtend\",\n      params: {},\n      clientRpcId,\n      throwOnFailedPublish: true,\n    }).catch((e) => {\n      reject(e);\n    });\n\n    return { acknowledged };\n  };\n\n  public request: IEngine[\"request\"] = async <T>(params: EngineTypes.RequestParams) => {\n    this.isInitialized();\n    try {\n      await this.isValidRequest(params);\n    } catch (error) {\n      this.client.logger.error(\"request() -> isValidRequest() failed\");\n      throw error;\n    }\n    const { chainId, request, topic, expiry = ENGINE_RPC_OPTS.wc_sessionRequest.req.ttl } = params;\n    const session = this.client.session.get(topic);\n\n    if (session?.transportType === TRANSPORT_TYPES.relay) {\n      await this.confirmOnlineStateOrThrow();\n    }\n    const clientRpcId = payloadId();\n    const relayRpcId = getBigIntRpcId().toString() as any;\n    const { done, resolve, reject } = createDelayedPromise<T>(\n      expiry,\n      \"Request expired. Please try again.\",\n    );\n    this.events.once<\"session_request\">(\n      engineEvent(\"session_request\", clientRpcId),\n      ({ error, result }) => {\n        if (error) reject(error);\n        else resolve(result);\n      },\n    );\n    const protocolMethod = \"wc_sessionRequest\";\n    const appLink = this.getAppLinkIfEnabled(session.peer.metadata, session.transportType);\n    if (appLink) {\n      await this.sendRequest({\n        clientRpcId,\n        relayRpcId,\n        topic,\n        method: protocolMethod,\n        params: {\n          request: {\n            ...request,\n            expiryTimestamp: calcExpiry(expiry),\n          },\n          chainId,\n        },\n        expiry,\n        throwOnFailedPublish: true,\n        appLink,\n      }).catch((error) => reject(error));\n\n      this.client.events.emit(\"session_request_sent\", {\n        topic,\n        request,\n        chainId,\n        id: clientRpcId,\n      });\n      const result = await done();\n      return result;\n    }\n\n    const protocolRequestParams: JsonRpcTypes.RequestParams[\"wc_sessionRequest\"] = {\n      request: {\n        ...request,\n        expiryTimestamp: calcExpiry(expiry),\n      },\n      chainId,\n    };\n    const shouldSetTVF = this.shouldSetTVF(protocolMethod, protocolRequestParams);\n\n    return await Promise.all([\n      new Promise<void>(async (resolve) => {\n        await this.sendRequest({\n          clientRpcId,\n          relayRpcId,\n          topic,\n          method: protocolMethod,\n          params: protocolRequestParams,\n          expiry,\n          throwOnFailedPublish: true,\n          ...(shouldSetTVF && {\n            tvf: this.getTVFParams(clientRpcId, protocolRequestParams),\n          }),\n        }).catch((error) => reject(error));\n        this.client.events.emit(\"session_request_sent\", {\n          topic,\n          request,\n          chainId,\n          id: clientRpcId,\n        });\n        resolve();\n      }),\n      new Promise<void>(async (resolve) => {\n        // only attempt to handle deeplinks if they are not explicitly disabled in the session config\n        if (!session.sessionConfig?.disableDeepLink) {\n          const wcDeepLink = (await getDeepLink(\n            this.client.core.storage,\n            WALLETCONNECT_DEEPLINK_CHOICE,\n          )) as string;\n          await handleDeeplinkRedirect({ id: clientRpcId, topic, wcDeepLink });\n        }\n        resolve();\n      }),\n      done(),\n    ]).then((result) => result[2]); // order is important here, we want to return the result of the `done` promise\n  };\n\n  public respond: IEngine[\"respond\"] = async (params) => {\n    this.isInitialized();\n    await this.isValidRespond(params);\n    const { topic, response } = params;\n    const { id } = response;\n    const session = this.client.session.get(topic);\n\n    if (session.transportType === TRANSPORT_TYPES.relay) {\n      await this.confirmOnlineStateOrThrow();\n    }\n\n    const appLink = this.getAppLinkIfEnabled(session.peer.metadata, session.transportType);\n    if (isJsonRpcResult(response)) {\n      await this.sendResult({\n        id,\n        topic,\n        result: response.result,\n        throwOnFailedPublish: true,\n        appLink,\n      });\n    } else if (isJsonRpcError(response)) {\n      await this.sendError({ id, topic, error: response.error, appLink });\n    }\n    this.cleanupAfterResponse(params);\n  };\n\n  public ping: IEngine[\"ping\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    try {\n      await this.isValidPing(params);\n    } catch (error) {\n      this.client.logger.error(\"ping() -> isValidPing() failed\");\n      throw error;\n    }\n    const { topic } = params;\n    if (this.client.session.keys.includes(topic)) {\n      const clientRpcId = payloadId();\n      const relayRpcId = getBigIntRpcId().toString() as any;\n      const { done, resolve, reject } = createDelayedPromise<void>();\n      this.events.once(engineEvent(\"session_ping\", clientRpcId), ({ error }: any) => {\n        if (error) reject(error);\n        else resolve();\n      });\n      await Promise.all([\n        this.sendRequest({\n          topic,\n          method: \"wc_sessionPing\",\n          params: {},\n          throwOnFailedPublish: true,\n          clientRpcId,\n          relayRpcId,\n        }),\n        done(),\n      ]);\n    } else if (this.client.core.pairing.pairings.keys.includes(topic)) {\n      this.client.logger.warn(\n        \"ping() on pairing topic is deprecated and will be removed in the next major release.\",\n      );\n      await this.client.core.pairing.ping({ topic });\n    }\n  };\n\n  public emit: IEngine[\"emit\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    await this.isValidEmit(params);\n    const { topic, event, chainId } = params;\n    const relayRpcId = getBigIntRpcId().toString() as any;\n    const clientRpcId = payloadId();\n    await this.sendRequest({\n      topic,\n      method: \"wc_sessionEvent\",\n      params: { event, chainId },\n      throwOnFailedPublish: true,\n      relayRpcId,\n      clientRpcId,\n    });\n  };\n\n  public disconnect: IEngine[\"disconnect\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    await this.isValidDisconnect(params);\n    const { topic } = params;\n    if (this.client.session.keys.includes(topic)) {\n      // await an ack to ensure the relay has received the disconnect request\n      await this.sendRequest({\n        topic,\n        method: \"wc_sessionDelete\",\n        params: getSdkError(\"USER_DISCONNECTED\"),\n        throwOnFailedPublish: true,\n      });\n      await this.deleteSession({ topic, emitEvent: false });\n    } else if (this.client.core.pairing.pairings.keys.includes(topic)) {\n      await this.client.core.pairing.disconnect({ topic });\n    } else {\n      const { message } = getInternalError(\n        \"MISMATCHED_TOPIC\",\n        `Session or pairing topic not found: ${topic}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  public find: IEngine[\"find\"] = (params) => {\n    this.isInitialized();\n    return this.client.session.getAll().filter((session) => isSessionCompatible(session, params));\n  };\n\n  public getPendingSessionRequests: IEngine[\"getPendingSessionRequests\"] = () => {\n    return this.client.pendingRequest.getAll();\n  };\n\n  // ---------- Auth ------------------------------------------------ //\n\n  public authenticate: IEngine[\"authenticate\"] = async (params, walletUniversalLink) => {\n    this.isInitialized();\n    this.isValidAuthenticate(params);\n\n    const isLinkMode =\n      walletUniversalLink &&\n      this.client.core.linkModeSupportedApps.includes(walletUniversalLink) &&\n      this.client.metadata.redirect?.linkMode;\n\n    const transportType: RelayerTypes.TransportType = isLinkMode\n      ? TRANSPORT_TYPES.link_mode\n      : TRANSPORT_TYPES.relay;\n\n    if (transportType === TRANSPORT_TYPES.relay) {\n      await this.confirmOnlineStateOrThrow();\n    }\n\n    const {\n      chains,\n      statement = \"\",\n      uri,\n      domain,\n      nonce,\n      type,\n      exp,\n      nbf,\n      methods = [],\n      expiry,\n    } = params;\n    // reassign resources to remove reference as the array is modified and might cause side effects\n    const resources = [...(params.resources || [])];\n\n    const { topic: pairingTopic, uri: connectionUri } = await this.client.core.pairing.create({\n      methods: [\"wc_sessionAuthenticate\"],\n      transportType,\n    });\n\n    this.client.logger.info({\n      message: \"Generated new pairing\",\n      pairing: { topic: pairingTopic, uri: connectionUri },\n    });\n\n    const publicKey = await this.client.core.crypto.generateKeyPair();\n    const responseTopic = hashKey(publicKey);\n\n    await Promise.all([\n      this.client.auth.authKeys.set(AUTH_PUBLIC_KEY_NAME, { responseTopic, publicKey }),\n      this.client.auth.pairingTopics.set(responseTopic, { topic: responseTopic, pairingTopic }),\n    ]);\n\n    // Subscribe to response topic\n    await this.client.core.relayer.subscribe(responseTopic, { transportType });\n\n    this.client.logger.info(`sending request to new pairing topic: ${pairingTopic}`);\n\n    if (methods.length > 0) {\n      const { namespace } = parseChainId(chains[0]);\n      let recap = createEncodedRecap(namespace, \"request\", methods);\n      const existingRecap = getRecapFromResources(resources);\n      if (existingRecap) {\n        // per Recaps spec, recap must occupy the last position in the resources array\n        // using .pop to remove the element given we already checked it's a recap and will replace it\n        const mergedRecap = mergeEncodedRecaps(recap, resources.pop() as string);\n        recap = mergedRecap;\n      }\n      resources.push(recap);\n    }\n\n    // Ensure the expiry is greater than the minimum required for the request - currently 1h\n    const authRequestExpiry =\n      expiry && expiry > ENGINE_RPC_OPTS.wc_sessionAuthenticate.req.ttl\n        ? expiry\n        : ENGINE_RPC_OPTS.wc_sessionAuthenticate.req.ttl;\n\n    const request: AuthTypes.SessionAuthenticateRequestParams = {\n      authPayload: {\n        type: type ?? \"caip122\",\n        chains,\n        statement,\n        aud: uri,\n        domain,\n        version: \"1\",\n        nonce,\n        iat: new Date().toISOString(),\n        exp,\n        nbf,\n        resources,\n      },\n      requester: { publicKey, metadata: this.client.metadata },\n      expiryTimestamp: calcExpiry(authRequestExpiry),\n    };\n\n    // ----- build namespaces for fallback session proposal ----- //\n    const namespaces = {\n      eip155: {\n        chains,\n        // request `personal_sign` method by default to allow for fallback siwe\n        methods: [...new Set([\"personal_sign\", ...methods])],\n        events: [\"chainChanged\", \"accountsChanged\"],\n      },\n    };\n\n    const proposal = {\n      requiredNamespaces: {},\n      optionalNamespaces: namespaces,\n      relays: [{ protocol: \"irn\" }],\n      pairingTopic,\n      proposer: {\n        publicKey,\n        metadata: this.client.metadata,\n      },\n      expiryTimestamp: calcExpiry(ENGINE_RPC_OPTS.wc_sessionPropose.req.ttl),\n      id: payloadId(),\n    };\n\n    const { done, resolve, reject } = createDelayedPromise(authRequestExpiry, \"Request expired\");\n\n    const authenticateId = payloadId();\n    const sessionConnectEventTarget = engineEvent(\"session_connect\", proposal.id);\n    const authenticateEventTarget = engineEvent(\"session_request\", authenticateId);\n\n    // handle fallback session proposal response\n    const onSessionConnect = async ({ error, session }: any) => {\n      // cleanup listener for authenticate response\n      this.events.off(authenticateEventTarget, onAuthenticate);\n      if (error) reject(error);\n      else if (session) {\n        resolve({\n          session,\n        });\n      }\n    };\n    // handle session authenticate response\n    const onAuthenticate = async (payload: any) => {\n      // delete this auth request on response\n      // we're using payload from the wallet to establish the session so we don't need to keep this around\n      await this.deletePendingAuthRequest(authenticateId, { message: \"fulfilled\", code: 0 });\n      if (payload.error) {\n        // wallets that do not support wc_sessionAuthenticate will return an error\n        // we should not reject the promise in this case as the fallback session proposal will be used\n        const error = getSdkError(\"WC_METHOD_UNSUPPORTED\", \"wc_sessionAuthenticate\");\n        if (payload.error.code === error.code) return;\n\n        // cleanup listener for fallback response\n        this.events.off(sessionConnectEventTarget, onSessionConnect);\n        return reject(payload.error.message);\n      }\n      // delete fallback proposal on successful authenticate as the proposal will not be responded to\n      await this.deleteProposal(proposal.id);\n      // cleanup listener for fallback response\n      this.events.off(sessionConnectEventTarget, onSessionConnect);\n\n      const {\n        cacaos,\n        responder,\n      }: {\n        cacaos: AuthTypes.SessionAuthenticateResponseParams[\"cacaos\"];\n        responder: AuthTypes.SessionAuthenticateResponseParams[\"responder\"];\n      } = payload.result;\n\n      const approvedMethods: string[] = [];\n      const approvedAccounts: string[] = [];\n      for (const cacao of cacaos) {\n        const isValid = await validateSignedCacao({ cacao, projectId: this.client.core.projectId });\n        if (!isValid) {\n          this.client.logger.error(cacao, \"Signature verification failed\");\n          reject(getSdkError(\"SESSION_SETTLEMENT_FAILED\", \"Signature verification failed\"));\n        }\n\n        const { p: payload } = cacao;\n        const recap = getRecapFromResources(payload.resources);\n\n        const approvedChains: string[] = [getNamespacedDidChainId(payload.iss) as string];\n        const parsedAddress = getDidAddress(payload.iss) as string;\n\n        if (recap) {\n          const methodsfromRecap = getMethodsFromRecap(recap);\n          const chainsFromRecap = getChainsFromRecap(recap);\n          approvedMethods.push(...methodsfromRecap);\n          approvedChains.push(...chainsFromRecap);\n        }\n\n        for (const chain of approvedChains) {\n          approvedAccounts.push(`${chain}:${parsedAddress}`);\n        }\n      }\n      const sessionTopic = await this.client.core.crypto.generateSharedKey(\n        publicKey,\n        responder.publicKey,\n      );\n\n      //create session object\n      let session: SessionTypes.Struct | undefined;\n\n      if (approvedMethods.length > 0) {\n        session = {\n          topic: sessionTopic,\n          acknowledged: true,\n          self: {\n            publicKey,\n            metadata: this.client.metadata,\n          },\n          peer: responder,\n          controller: responder.publicKey,\n          expiry: calcExpiry(SESSION_EXPIRY),\n          requiredNamespaces: {},\n          optionalNamespaces: {},\n          relay: { protocol: \"irn\" },\n          pairingTopic,\n          namespaces: buildNamespacesFromAuth(\n            [...new Set(approvedMethods)],\n            [...new Set(approvedAccounts)],\n          ),\n          transportType,\n        };\n\n        await this.client.core.relayer.subscribe(sessionTopic, { transportType });\n        await this.client.session.set(sessionTopic, session);\n        if (pairingTopic) {\n          await this.client.core.pairing.updateMetadata({\n            topic: pairingTopic,\n            metadata: responder.metadata,\n          });\n        }\n\n        session = this.client.session.get(sessionTopic);\n      }\n\n      if (\n        this.client.metadata.redirect?.linkMode &&\n        responder.metadata.redirect?.linkMode &&\n        responder.metadata.redirect?.universal &&\n        walletUniversalLink\n      ) {\n        // save wallet link in array of apps that support linkMode\n        this.client.core.addLinkModeSupportedApp(responder.metadata.redirect.universal);\n\n        this.client.session.update(sessionTopic, {\n          transportType: TRANSPORT_TYPES.link_mode,\n        });\n      }\n\n      resolve({\n        auths: cacaos,\n        session,\n      });\n    };\n\n    // subscribe to response events\n    this.events.once<\"session_connect\">(sessionConnectEventTarget, onSessionConnect);\n    this.events.once(authenticateEventTarget, onAuthenticate);\n\n    let linkModeURL;\n    try {\n      if (isLinkMode) {\n        const payload = formatJsonRpcRequest(\"wc_sessionAuthenticate\", request, authenticateId);\n        this.client.core.history.set(pairingTopic, payload);\n        const message = await this.client.core.crypto.encode(\"\", payload, {\n          type: TYPE_2,\n          encoding: BASE64URL,\n        });\n        linkModeURL = getLinkModeURL(walletUniversalLink, pairingTopic, message);\n      } else {\n        // send both (main & fallback) requests\n        await Promise.all([\n          this.sendRequest({\n            topic: pairingTopic,\n            method: \"wc_sessionAuthenticate\",\n            params: request,\n            expiry: params.expiry,\n            throwOnFailedPublish: true,\n            clientRpcId: authenticateId,\n          }),\n          this.sendRequest({\n            topic: pairingTopic,\n            method: \"wc_sessionPropose\",\n            params: proposal,\n            expiry: ENGINE_RPC_OPTS.wc_sessionPropose.req.ttl,\n            throwOnFailedPublish: true,\n            clientRpcId: proposal.id,\n          }),\n        ]);\n      }\n    } catch (error) {\n      // cleanup listeners on failed publish\n      this.events.off(sessionConnectEventTarget, onSessionConnect);\n      this.events.off(authenticateEventTarget, onAuthenticate);\n      throw error;\n    }\n\n    await this.setProposal(proposal.id, proposal);\n    await this.setAuthRequest(authenticateId, {\n      request: {\n        ...request,\n        verifyContext: {} as any,\n      },\n      pairingTopic,\n      transportType,\n    });\n\n    return {\n      uri: linkModeURL ?? connectionUri,\n      response: done,\n    } as EngineTypes.SessionAuthenticateResponsePromise;\n  };\n\n  public approveSessionAuthenticate: IEngine[\"approveSessionAuthenticate\"] = async (\n    sessionAuthenticateResponseParams,\n  ) => {\n    const { id, auths } = sessionAuthenticateResponseParams;\n\n    const event = this.client.core.eventClient.createEvent({\n      properties: {\n        topic: id.toString(),\n        trace: [EVENT_CLIENT_AUTHENTICATE_TRACES.authenticated_session_approve_started],\n      },\n    });\n\n    try {\n      this.isInitialized();\n    } catch (error) {\n      event.setError(EVENT_CLIENT_AUTHENTICATE_ERRORS.no_internet_connection);\n      throw error;\n    }\n\n    const pendingRequest = this.getPendingAuthRequest(id);\n\n    if (!pendingRequest) {\n      event.setError(\n        EVENT_CLIENT_AUTHENTICATE_ERRORS.authenticated_session_pending_request_not_found,\n      );\n      throw new Error(`Could not find pending auth request with id ${id}`);\n    }\n\n    const transportType = pendingRequest.transportType || TRANSPORT_TYPES.relay;\n    if (transportType === TRANSPORT_TYPES.relay) {\n      await this.confirmOnlineStateOrThrow();\n    }\n\n    const receiverPublicKey = pendingRequest.requester.publicKey;\n    const senderPublicKey = await this.client.core.crypto.generateKeyPair();\n    const responseTopic = hashKey(receiverPublicKey);\n\n    const encodeOpts = {\n      type: TYPE_1,\n      receiverPublicKey,\n      senderPublicKey,\n    };\n\n    const approvedMethods: string[] = [];\n    const approvedAccounts: string[] = [];\n    for (const cacao of auths) {\n      const isValid = await validateSignedCacao({ cacao, projectId: this.client.core.projectId });\n      if (!isValid) {\n        event.setError(EVENT_CLIENT_AUTHENTICATE_ERRORS.invalid_cacao);\n\n        const invalidErr = getSdkError(\n          \"SESSION_SETTLEMENT_FAILED\",\n          \"Signature verification failed\",\n        );\n\n        await this.sendError({\n          id,\n          topic: responseTopic,\n          error: invalidErr,\n          encodeOpts,\n        });\n\n        throw new Error(invalidErr.message);\n      }\n\n      event.addTrace(EVENT_CLIENT_AUTHENTICATE_TRACES.cacaos_verified);\n\n      const { p: payload } = cacao;\n      const recap = getRecapFromResources(payload.resources);\n\n      const approvedChains: string[] = [getNamespacedDidChainId(payload.iss) as string];\n\n      const parsedAddress = getDidAddress(payload.iss) as string;\n\n      if (recap) {\n        const methodsfromRecap = getMethodsFromRecap(recap);\n        const chainsFromRecap = getChainsFromRecap(recap);\n        approvedMethods.push(...methodsfromRecap);\n        approvedChains.push(...chainsFromRecap);\n      }\n      for (const chain of approvedChains) {\n        approvedAccounts.push(`${chain}:${parsedAddress}`);\n      }\n    }\n\n    const sessionTopic = await this.client.core.crypto.generateSharedKey(\n      senderPublicKey,\n      receiverPublicKey,\n    );\n\n    event.addTrace(EVENT_CLIENT_AUTHENTICATE_TRACES.create_authenticated_session_topic);\n\n    let session: SessionTypes.Struct | undefined;\n    if (approvedMethods?.length > 0) {\n      session = {\n        topic: sessionTopic,\n        acknowledged: true,\n        self: {\n          publicKey: senderPublicKey,\n          metadata: this.client.metadata,\n        },\n        peer: {\n          publicKey: receiverPublicKey,\n          metadata: pendingRequest.requester.metadata,\n        },\n        controller: receiverPublicKey,\n        expiry: calcExpiry(SESSION_EXPIRY),\n        authentication: auths,\n        requiredNamespaces: {},\n        optionalNamespaces: {},\n        relay: { protocol: \"irn\" },\n        pairingTopic: pendingRequest.pairingTopic,\n        namespaces: buildNamespacesFromAuth(\n          [...new Set(approvedMethods)],\n          [...new Set(approvedAccounts)],\n        ),\n        transportType,\n      };\n\n      event.addTrace(EVENT_CLIENT_AUTHENTICATE_TRACES.subscribing_authenticated_session_topic);\n\n      try {\n        await this.client.core.relayer.subscribe(sessionTopic, { transportType });\n      } catch (error) {\n        event.setError(\n          EVENT_CLIENT_AUTHENTICATE_ERRORS.subscribe_authenticated_session_topic_failure,\n        );\n        throw error;\n      }\n\n      event.addTrace(\n        EVENT_CLIENT_AUTHENTICATE_TRACES.subscribe_authenticated_session_topic_success,\n      );\n\n      await this.client.session.set(sessionTopic, session);\n\n      event.addTrace(EVENT_CLIENT_AUTHENTICATE_TRACES.store_authenticated_session);\n\n      await this.client.core.pairing.updateMetadata({\n        topic: pendingRequest.pairingTopic,\n        metadata: pendingRequest.requester.metadata,\n      });\n    }\n\n    event.addTrace(EVENT_CLIENT_AUTHENTICATE_TRACES.publishing_authenticated_session_approve);\n\n    try {\n      await this.sendResult<\"wc_sessionAuthenticate\">({\n        topic: responseTopic,\n        id,\n        result: {\n          cacaos: auths,\n          responder: {\n            publicKey: senderPublicKey,\n            metadata: this.client.metadata,\n          },\n        },\n        encodeOpts,\n        throwOnFailedPublish: true,\n        appLink: this.getAppLinkIfEnabled(pendingRequest.requester.metadata, transportType),\n      });\n    } catch (error) {\n      event.setError(\n        EVENT_CLIENT_AUTHENTICATE_ERRORS.authenticated_session_approve_publish_failure,\n      );\n      throw error;\n    }\n\n    await this.client.auth.requests.delete(id, { message: \"fulfilled\", code: 0 });\n    await this.client.core.pairing.activate({ topic: pendingRequest.pairingTopic });\n    this.client.core.eventClient.deleteEvent({ eventId: event.eventId });\n\n    return { session };\n  };\n\n  public rejectSessionAuthenticate: IEngine[\"rejectSessionAuthenticate\"] = async (params) => {\n    this.isInitialized();\n\n    const { id, reason } = params;\n\n    const pendingRequest = this.getPendingAuthRequest(id);\n\n    if (!pendingRequest) {\n      throw new Error(`Could not find pending auth request with id ${id}`);\n    }\n\n    if (pendingRequest.transportType === TRANSPORT_TYPES.relay) {\n      await this.confirmOnlineStateOrThrow();\n    }\n\n    const receiverPublicKey = pendingRequest.requester.publicKey;\n    const senderPublicKey = await this.client.core.crypto.generateKeyPair();\n    const responseTopic = hashKey(receiverPublicKey);\n\n    const encodeOpts = {\n      type: TYPE_1,\n      receiverPublicKey,\n      senderPublicKey,\n    };\n\n    await this.sendError({\n      id,\n      topic: responseTopic,\n      error: reason,\n      encodeOpts,\n      rpcOpts: ENGINE_RPC_OPTS.wc_sessionAuthenticate.reject,\n      appLink: this.getAppLinkIfEnabled(\n        pendingRequest.requester.metadata,\n        pendingRequest.transportType,\n      ),\n    });\n    await this.client.auth.requests.delete(id, { message: \"rejected\", code: 0 });\n    await this.client.proposal.delete(id, getSdkError(\"USER_DISCONNECTED\"));\n  };\n\n  public formatAuthMessage: IEngine[\"formatAuthMessage\"] = (params) => {\n    this.isInitialized();\n    const { request, iss } = params;\n    return formatMessage(request, iss);\n  };\n\n  public processRelayMessageCache: IEngine[\"processRelayMessageCache\"] = () => {\n    // process the relay messages cache in the next tick to allow event listeners to be registered by the implementing app\n    setTimeout(async () => {\n      if (this.relayMessageCache.length === 0) return;\n      while (this.relayMessageCache.length > 0) {\n        try {\n          const message = this.relayMessageCache.shift();\n          if (message) {\n            await this.onRelayMessage(message);\n          }\n        } catch (error) {\n          this.client.logger.error(error);\n        }\n      }\n    }, 50);\n  };\n\n  // ---------- Private Helpers --------------------------------------- //\n\n  private cleanupDuplicatePairings: EnginePrivate[\"cleanupDuplicatePairings\"] = async (\n    session: SessionTypes.Struct,\n  ) => {\n    // older SDK versions are missing the `pairingTopic` prop thus we need to check for it\n    if (!session.pairingTopic) return;\n\n    try {\n      const pairing = this.client.core.pairing.pairings.get(session.pairingTopic);\n      const allPairings = this.client.core.pairing.pairings.getAll();\n      const duplicates = allPairings.filter(\n        (p) =>\n          p.peerMetadata?.url &&\n          p.peerMetadata?.url === session.peer.metadata.url &&\n          p.topic &&\n          p.topic !== pairing.topic,\n      );\n      if (duplicates.length === 0) return;\n      this.client.logger.info(`Cleaning up ${duplicates.length} duplicate pairing(s)`);\n      await Promise.all(\n        duplicates.map((p) => this.client.core.pairing.disconnect({ topic: p.topic })),\n      );\n      this.client.logger.info(`Duplicate pairings clean up finished`);\n    } catch (error) {\n      this.client.logger.error(error);\n    }\n  };\n\n  private deleteSession: EnginePrivate[\"deleteSession\"] = async (params) => {\n    const { topic, expirerHasDeleted = false, emitEvent = true, id = 0 } = params;\n    const { self } = this.client.session.get(topic);\n    // Await the unsubscribe first to avoid deleting the symKey too early below.\n    await this.client.core.relayer.unsubscribe(topic);\n    await this.client.session.delete(topic, getSdkError(\"USER_DISCONNECTED\"));\n    this.addToRecentlyDeleted(topic, \"session\");\n    if (this.client.core.crypto.keychain.has(self.publicKey)) {\n      await this.client.core.crypto.deleteKeyPair(self.publicKey);\n    }\n    if (this.client.core.crypto.keychain.has(topic)) {\n      await this.client.core.crypto.deleteSymKey(topic);\n    }\n    if (!expirerHasDeleted) this.client.core.expirer.del(topic);\n    // remove any deeplinks from storage after the session is deleted\n    // to avoid navigating to incorrect deeplink later on\n    this.client.core.storage\n      .removeItem(WALLETCONNECT_DEEPLINK_CHOICE)\n      .catch((e) => this.client.logger.warn(e));\n    this.getPendingSessionRequests().forEach((r) => {\n      if (r.topic === topic) {\n        this.deletePendingSessionRequest(r.id, getSdkError(\"USER_DISCONNECTED\"));\n      }\n    });\n    // reset the queue state back to idle if a request for the deleted session is still in the queue\n    if (topic === this.sessionRequestQueue.queue[0]?.topic) {\n      this.sessionRequestQueue.state = ENGINE_QUEUE_STATES.idle;\n    }\n    if (emitEvent) this.client.events.emit(\"session_delete\", { id, topic });\n  };\n\n  private deleteProposal: EnginePrivate[\"deleteProposal\"] = async (id, expirerHasDeleted) => {\n    if (expirerHasDeleted) {\n      try {\n        const proposal = this.client.proposal.get(id);\n        const event = this.client.core.eventClient.getEvent({ topic: proposal.pairingTopic });\n        event?.setError(EVENT_CLIENT_SESSION_ERRORS.proposal_expired);\n      } catch (error) {}\n    }\n    await Promise.all([\n      this.client.proposal.delete(id, getSdkError(\"USER_DISCONNECTED\")),\n      expirerHasDeleted ? Promise.resolve() : this.client.core.expirer.del(id),\n    ]);\n    this.addToRecentlyDeleted(id, \"proposal\");\n  };\n\n  private deletePendingSessionRequest: EnginePrivate[\"deletePendingSessionRequest\"] = async (\n    id,\n    reason,\n    expirerHasDeleted = false,\n  ) => {\n    await Promise.all([\n      this.client.pendingRequest.delete(id, reason),\n      expirerHasDeleted ? Promise.resolve() : this.client.core.expirer.del(id),\n    ]);\n    this.addToRecentlyDeleted(id, \"request\");\n    this.sessionRequestQueue.queue = this.sessionRequestQueue.queue.filter((r) => r.id !== id);\n    if (expirerHasDeleted) {\n      this.sessionRequestQueue.state = ENGINE_QUEUE_STATES.idle;\n      this.client.events.emit(\"session_request_expire\", { id });\n    }\n  };\n\n  private deletePendingAuthRequest: EnginePrivate[\"deletePendingAuthRequest\"] = async (\n    id,\n    reason,\n    expirerHasDeleted = false,\n  ) => {\n    await Promise.all([\n      this.client.auth.requests.delete(id, reason),\n      expirerHasDeleted ? Promise.resolve() : this.client.core.expirer.del(id),\n    ]);\n  };\n\n  private setExpiry: EnginePrivate[\"setExpiry\"] = async (topic, expiry) => {\n    if (!this.client.session.keys.includes(topic)) return;\n    this.client.core.expirer.set(topic, expiry);\n    await this.client.session.update(topic, { expiry });\n  };\n\n  private setProposal: EnginePrivate[\"setProposal\"] = async (id, proposal) => {\n    this.client.core.expirer.set(id, calcExpiry(ENGINE_RPC_OPTS.wc_sessionPropose.req.ttl));\n    await this.client.proposal.set(id, proposal);\n  };\n\n  private setAuthRequest: EnginePrivate[\"setAuthRequest\"] = async (id, params) => {\n    const { request, pairingTopic, transportType = TRANSPORT_TYPES.relay } = params;\n    this.client.core.expirer.set(id, request.expiryTimestamp);\n    await this.client.auth.requests.set(id, {\n      authPayload: request.authPayload,\n      requester: request.requester,\n      expiryTimestamp: request.expiryTimestamp,\n      id,\n      pairingTopic,\n      verifyContext: request.verifyContext,\n      transportType,\n    });\n  };\n\n  private setPendingSessionRequest: EnginePrivate[\"setPendingSessionRequest\"] = async (\n    pendingRequest: PendingRequestTypes.Struct,\n  ) => {\n    const { id, topic, params, verifyContext } = pendingRequest;\n    const expiry =\n      params.request.expiryTimestamp || calcExpiry(ENGINE_RPC_OPTS.wc_sessionRequest.req.ttl);\n    this.client.core.expirer.set(id, expiry);\n    await this.client.pendingRequest.set(id, {\n      id,\n      topic,\n      params,\n      verifyContext,\n    });\n  };\n\n  private sendRequest: EnginePrivate[\"sendRequest\"] = async (args) => {\n    const {\n      topic,\n      method,\n      params,\n      expiry,\n      relayRpcId,\n      clientRpcId,\n      throwOnFailedPublish,\n      appLink,\n      tvf,\n    } = args;\n    const payload = formatJsonRpcRequest(method, params, clientRpcId);\n\n    let message: string;\n    const isLinkMode = !!appLink;\n\n    try {\n      const encoding = isLinkMode ? BASE64URL : BASE64;\n      message = await this.client.core.crypto.encode(topic, payload, { encoding });\n    } catch (error) {\n      await this.cleanup();\n      this.client.logger.error(`sendRequest() -> core.crypto.encode() for topic ${topic} failed`);\n      throw error;\n    }\n\n    let attestation: string | undefined;\n    if (METHODS_TO_VERIFY.includes(method)) {\n      const decryptedId = hashMessage(JSON.stringify(payload));\n      const id = hashMessage(message);\n      attestation = await this.client.core.verify.register({ id, decryptedId });\n    }\n    const opts = ENGINE_RPC_OPTS[method].req;\n    opts.attestation = attestation;\n    if (expiry) opts.ttl = expiry;\n    if (relayRpcId) opts.id = relayRpcId;\n    this.client.core.history.set(topic, payload);\n\n    if (isLinkMode) {\n      const redirectURL = getLinkModeURL(appLink, topic, message);\n      await (global as any).Linking.openURL(redirectURL, this.client.name);\n    } else {\n      const opts = ENGINE_RPC_OPTS[method].req;\n      if (expiry) opts.ttl = expiry;\n      if (relayRpcId) opts.id = relayRpcId;\n\n      opts.tvf = {\n        ...tvf,\n        correlationId: payload.id,\n      };\n\n      if (throwOnFailedPublish) {\n        opts.internal = {\n          ...opts.internal,\n          throwOnFailedPublish: true,\n        };\n        await this.client.core.relayer.publish(topic, message, opts);\n      } else {\n        this.client.core.relayer\n          .publish(topic, message, opts)\n          .catch((error) => this.client.logger.error(error));\n      }\n    }\n\n    return payload.id;\n  };\n\n  private sendResult: EnginePrivate[\"sendResult\"] = async (args) => {\n    const { id, topic, result, throwOnFailedPublish, encodeOpts, appLink } = args;\n    const payload = formatJsonRpcResult(id, result);\n    let message;\n    const isLinkMode = appLink && typeof (global as any)?.Linking !== \"undefined\";\n\n    try {\n      const encoding = isLinkMode ? BASE64URL : BASE64;\n      message = await this.client.core.crypto.encode(topic, payload, {\n        ...(encodeOpts || {}),\n        encoding,\n      });\n    } catch (error) {\n      // if encoding fails e.g. due to missing keychain, we want to cleanup all related data as its unusable\n      await this.cleanup();\n      this.client.logger.error(`sendResult() -> core.crypto.encode() for topic ${topic} failed`);\n      throw error;\n    }\n    let record;\n    let tvf;\n    try {\n      record = await this.client.core.history.get(topic, id);\n      const request = record.request;\n      try {\n        if (this.shouldSetTVF(request.method as JsonRpcTypes.WcMethod, request.params)) {\n          tvf = this.getTVFParams(id, request.params, result);\n        }\n      } catch (error) {\n        this.client.logger.warn(`sendResult() -> getTVFParams() failed`, error);\n      }\n    } catch (error) {\n      this.client.logger.error(`sendResult() -> history.get(${topic}, ${id}) failed`);\n      throw error;\n    }\n\n    if (isLinkMode) {\n      const redirectURL = getLinkModeURL(appLink, topic, message);\n      await (global as any).Linking.openURL(redirectURL, this.client.name);\n    } else {\n      const method = record.request.method as JsonRpcTypes.WcMethod;\n      const opts = ENGINE_RPC_OPTS[method].res;\n\n      opts.tvf = {\n        ...tvf,\n        correlationId: id,\n      };\n\n      if (throwOnFailedPublish) {\n        opts.internal = {\n          ...opts.internal,\n          throwOnFailedPublish: true,\n        };\n        await this.client.core.relayer.publish(topic, message, opts);\n      } else {\n        this.client.core.relayer\n          .publish(topic, message, opts)\n          .catch((error) => this.client.logger.error(error));\n      }\n    }\n\n    await this.client.core.history.resolve(payload);\n  };\n\n  private sendError: EnginePrivate[\"sendError\"] = async (params) => {\n    const { id, topic, error, encodeOpts, rpcOpts, appLink } = params;\n    const payload = formatJsonRpcError(id, error);\n    let message;\n    const isLinkMode = appLink && typeof (global as any)?.Linking !== \"undefined\";\n    try {\n      const encoding = isLinkMode ? BASE64URL : BASE64;\n      message = await this.client.core.crypto.encode(topic, payload, {\n        ...(encodeOpts || {}),\n        encoding,\n      });\n    } catch (error) {\n      await this.cleanup();\n      this.client.logger.error(`sendError() -> core.crypto.encode() for topic ${topic} failed`);\n      throw error;\n    }\n    let record;\n    try {\n      record = await this.client.core.history.get(topic, id);\n    } catch (error) {\n      this.client.logger.error(`sendError() -> history.get(${topic}, ${id}) failed`);\n      throw error;\n    }\n\n    if (isLinkMode) {\n      const redirectURL = getLinkModeURL(appLink, topic, message);\n      await (global as any).Linking.openURL(redirectURL, this.client.name);\n    } else {\n      const method = record.request.method as JsonRpcTypes.WcMethod;\n      const opts = rpcOpts || ENGINE_RPC_OPTS[method].res;\n      // await is intentionally omitted to speed up performance\n      this.client.core.relayer.publish(topic, message, opts);\n    }\n\n    await this.client.core.history.resolve(payload);\n  };\n\n  private cleanup: EnginePrivate[\"cleanup\"] = async () => {\n    const sessionTopics: string[] = [];\n    const proposalIds: number[] = [];\n    this.client.session.getAll().forEach((session) => {\n      let toCleanup = false;\n      if (isExpired(session.expiry)) toCleanup = true;\n      if (!this.client.core.crypto.keychain.has(session.topic)) toCleanup = true;\n      if (toCleanup) sessionTopics.push(session.topic);\n    });\n    this.client.proposal.getAll().forEach((proposal) => {\n      if (isExpired(proposal.expiryTimestamp)) proposalIds.push(proposal.id);\n    });\n    await Promise.all([\n      ...sessionTopics.map((topic) => this.deleteSession({ topic })),\n      ...proposalIds.map((id) => this.deleteProposal(id)),\n    ]);\n  };\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n\n  private async confirmOnlineStateOrThrow() {\n    await this.client.core.relayer.confirmOnlineStateOrThrow();\n  }\n\n  // ---------- Relay Events Router ----------------------------------- //\n\n  private registerRelayerEvents() {\n    this.client.core.relayer.on(RELAYER_EVENTS.message, (event: RelayerTypes.MessageEvent) => {\n      this.onProviderMessageEvent(event);\n    });\n  }\n\n  private onProviderMessageEvent = async (event: RelayerTypes.MessageEvent) => {\n    // capture any messages that arrive before the client is initialized so we can process them after initialization is complete\n    if (!this.initialized || this.relayMessageCache.length > 0) {\n      this.relayMessageCache.push(event);\n    } else {\n      await this.onRelayMessage(event);\n    }\n  };\n\n  private async onRelayMessage(event: RelayerTypes.MessageEvent) {\n    const { topic, message, attestation, transportType } = event;\n\n    // Retrieve the public key (if defined) to decrypt possible `auth_request` response\n    const { publicKey } = this.client.auth.authKeys.keys.includes(AUTH_PUBLIC_KEY_NAME)\n      ? this.client.auth.authKeys.get(AUTH_PUBLIC_KEY_NAME)\n      : ({ responseTopic: undefined, publicKey: undefined } as any);\n\n    try {\n      const payload = await this.client.core.crypto.decode(topic, message, {\n        receiverPublicKey: publicKey,\n        encoding: transportType === TRANSPORT_TYPES.link_mode ? BASE64URL : BASE64,\n      });\n\n      if (isJsonRpcRequest(payload)) {\n        this.client.core.history.set(topic, payload);\n        await this.onRelayEventRequest({\n          topic,\n          payload,\n          attestation,\n          transportType,\n          encryptedId: hashMessage(message),\n        });\n      } else if (isJsonRpcResponse(payload)) {\n        await this.client.core.history.resolve(payload);\n        await this.onRelayEventResponse({ topic, payload, transportType });\n        this.client.core.history.delete(topic, payload.id);\n      } else {\n        await this.onRelayEventUnknownPayload({ topic, payload, transportType });\n      }\n      await this.client.core.relayer.messages.ack(topic, message);\n    } catch (error) {\n      this.client.logger.error(error);\n    }\n  }\n\n  private onRelayEventRequest: EnginePrivate[\"onRelayEventRequest\"] = async (event) => {\n    this.requestQueue.queue.push(event);\n    await this.processRequestsQueue();\n  };\n\n  private processRequestsQueue = async () => {\n    if (this.requestQueue.state === ENGINE_QUEUE_STATES.active) {\n      this.client.logger.info(`Request queue already active, skipping...`);\n      return;\n    }\n\n    this.client.logger.info(\n      `Request queue starting with ${this.requestQueue.queue.length} requests`,\n    );\n\n    while (this.requestQueue.queue.length > 0) {\n      this.requestQueue.state = ENGINE_QUEUE_STATES.active;\n      const request = this.requestQueue.queue.shift();\n      if (!request) continue;\n\n      try {\n        await this.processRequest(request);\n      } catch (error) {\n        this.client.logger.warn(error);\n      }\n    }\n    this.requestQueue.state = ENGINE_QUEUE_STATES.idle;\n  };\n\n  private processRequest: EnginePrivate[\"onRelayEventRequest\"] = async (event) => {\n    const { topic, payload, attestation, transportType, encryptedId } = event;\n\n    const reqMethod = payload.method as JsonRpcTypes.WcMethod;\n\n    if (this.shouldIgnorePairingRequest({ topic, requestMethod: reqMethod })) {\n      return;\n    }\n\n    switch (reqMethod) {\n      case \"wc_sessionPropose\":\n        return await this.onSessionProposeRequest({ topic, payload, attestation, encryptedId });\n      case \"wc_sessionSettle\":\n        return await this.onSessionSettleRequest(topic, payload);\n      case \"wc_sessionUpdate\":\n        return await this.onSessionUpdateRequest(topic, payload);\n      case \"wc_sessionExtend\":\n        return await this.onSessionExtendRequest(topic, payload);\n      case \"wc_sessionPing\":\n        return await this.onSessionPingRequest(topic, payload);\n      case \"wc_sessionDelete\":\n        return await this.onSessionDeleteRequest(topic, payload);\n      case \"wc_sessionRequest\":\n        return await this.onSessionRequest({\n          topic,\n          payload,\n          attestation,\n          encryptedId,\n          transportType,\n        });\n      case \"wc_sessionEvent\":\n        return await this.onSessionEventRequest(topic, payload);\n      case \"wc_sessionAuthenticate\":\n        return await this.onSessionAuthenticateRequest({\n          topic,\n          payload,\n          attestation,\n          encryptedId,\n          transportType,\n        });\n      default:\n        return this.client.logger.info(`Unsupported request method ${reqMethod}`);\n    }\n  };\n\n  private onRelayEventResponse: EnginePrivate[\"onRelayEventResponse\"] = async (event) => {\n    const { topic, payload, transportType } = event;\n    const record = await this.client.core.history.get(topic, payload.id);\n    const resMethod = record.request.method as JsonRpcTypes.WcMethod;\n\n    switch (resMethod) {\n      case \"wc_sessionPropose\":\n        return this.onSessionProposeResponse(topic, payload, transportType);\n      case \"wc_sessionSettle\":\n        return this.onSessionSettleResponse(topic, payload);\n      case \"wc_sessionUpdate\":\n        return this.onSessionUpdateResponse(topic, payload);\n      case \"wc_sessionExtend\":\n        return this.onSessionExtendResponse(topic, payload);\n      case \"wc_sessionPing\":\n        return this.onSessionPingResponse(topic, payload);\n      case \"wc_sessionRequest\":\n        return this.onSessionRequestResponse(topic, payload);\n      case \"wc_sessionAuthenticate\":\n        return this.onSessionAuthenticateResponse(topic, payload);\n      default:\n        return this.client.logger.info(`Unsupported response method ${resMethod}`);\n    }\n  };\n\n  private onRelayEventUnknownPayload: EnginePrivate[\"onRelayEventUnknownPayload\"] = (event) => {\n    const { topic } = event;\n    const { message } = getInternalError(\n      \"MISSING_OR_INVALID\",\n      `Decoded payload on topic ${topic} is not identifiable as a JSON-RPC request or a response.`,\n    );\n    throw new Error(message);\n  };\n\n  private shouldIgnorePairingRequest: EnginePrivate[\"shouldIgnorePairingRequest\"] = (params) => {\n    const { topic, requestMethod } = params;\n    const expectedMethods = this.expectedPairingMethodMap.get(topic);\n    // check if the request method matches the expected method\n    if (!expectedMethods) return false;\n    if (expectedMethods.includes(requestMethod)) return false;\n\n    /**\n     * we want to make sure fallback session proposal is ignored only if there are subscribers\n     * for the `session_authenticate` event, otherwise this would result in no-op for the user\n     */\n    if (expectedMethods.includes(\"wc_sessionAuthenticate\")) {\n      if (this.client.events.listenerCount(\"session_authenticate\") > 0) {\n        return true;\n      }\n    }\n    return false;\n  };\n\n  // ---------- Relay Events Handlers --------------------------------- //\n\n  private onSessionProposeRequest: EnginePrivate[\"onSessionProposeRequest\"] = async (args) => {\n    const { topic, payload, attestation, encryptedId } = args;\n    const { params, id } = payload;\n    try {\n      const event = this.client.core.eventClient.getEvent({ topic });\n\n      if (this.client.events.listenerCount(\"session_proposal\") === 0) {\n        console.warn(\"No listener for session_proposal event\");\n        event?.setError(EVENT_CLIENT_PAIRING_ERRORS.proposal_listener_not_found);\n      }\n\n      this.isValidConnect({ ...payload.params });\n      const expiryTimestamp =\n        params.expiryTimestamp || calcExpiry(ENGINE_RPC_OPTS.wc_sessionPropose.req.ttl);\n      const proposal = { id, pairingTopic: topic, expiryTimestamp, ...params };\n      await this.setProposal(id, proposal);\n\n      const verifyContext = await this.getVerifyContext({\n        attestationId: attestation,\n        hash: hashMessage(JSON.stringify(payload)),\n        encryptedId,\n        metadata: proposal.proposer.metadata,\n      });\n\n      event?.addTrace(EVENT_CLIENT_PAIRING_TRACES.emit_session_proposal);\n\n      this.client.events.emit(\"session_proposal\", { id, params: proposal, verifyContext });\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n        rpcOpts: ENGINE_RPC_OPTS.wc_sessionPropose.autoReject,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionProposeResponse: EnginePrivate[\"onSessionProposeResponse\"] = async (\n    topic,\n    payload,\n    transportType,\n  ) => {\n    const { id } = payload;\n    if (isJsonRpcResult(payload)) {\n      const { result } = payload;\n      this.client.logger.trace({ type: \"method\", method: \"onSessionProposeResponse\", result });\n      const proposal = this.client.proposal.get(id);\n      this.client.logger.trace({ type: \"method\", method: \"onSessionProposeResponse\", proposal });\n      const selfPublicKey = proposal.proposer.publicKey;\n      this.client.logger.trace({\n        type: \"method\",\n        method: \"onSessionProposeResponse\",\n        selfPublicKey,\n      });\n      const peerPublicKey = result.responderPublicKey;\n      this.client.logger.trace({\n        type: \"method\",\n        method: \"onSessionProposeResponse\",\n        peerPublicKey,\n      });\n      const sessionTopic = await this.client.core.crypto.generateSharedKey(\n        selfPublicKey,\n        peerPublicKey,\n      );\n      this.pendingSessions.set(id, {\n        sessionTopic,\n        pairingTopic: topic,\n        proposalId: id,\n        publicKey: selfPublicKey,\n      });\n\n      const subscriptionId = await this.client.core.relayer.subscribe(sessionTopic, {\n        transportType,\n      });\n      this.client.logger.trace({\n        type: \"method\",\n        method: \"onSessionProposeResponse\",\n        subscriptionId,\n      });\n      await this.client.core.pairing.activate({ topic });\n    } else if (isJsonRpcError(payload)) {\n      await this.client.proposal.delete(id, getSdkError(\"USER_DISCONNECTED\"));\n      const target = engineEvent(\"session_connect\", id);\n      const listeners = this.events.listenerCount(target);\n      if (listeners === 0) {\n        throw new Error(`emitting ${target} without any listeners, 954`);\n      }\n      this.events.emit(target, { error: payload.error });\n    }\n  };\n\n  private onSessionSettleRequest: EnginePrivate[\"onSessionSettleRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id, params } = payload;\n    try {\n      this.isValidSessionSettleRequest(params);\n      const {\n        relay,\n        controller,\n        expiry,\n        namespaces,\n        sessionProperties,\n        scopedProperties,\n        sessionConfig,\n      } = payload.params;\n      const pendingSession = [...this.pendingSessions.values()].find(\n        (s) => s.sessionTopic === topic,\n      );\n\n      if (!pendingSession) {\n        return this.client.logger.error(`Pending session not found for topic ${topic}`);\n      }\n\n      const proposal = this.client.proposal.get(pendingSession.proposalId);\n\n      const session: SessionTypes.Struct = {\n        topic,\n        relay,\n        expiry,\n        namespaces,\n        acknowledged: true,\n        pairingTopic: pendingSession.pairingTopic,\n        requiredNamespaces: proposal.requiredNamespaces,\n        optionalNamespaces: proposal.optionalNamespaces,\n        controller: controller.publicKey,\n        self: {\n          publicKey: pendingSession.publicKey,\n          metadata: this.client.metadata,\n        },\n        peer: {\n          publicKey: controller.publicKey,\n          metadata: controller.metadata,\n        },\n        ...(sessionProperties && { sessionProperties }),\n        ...(scopedProperties && { scopedProperties }),\n        ...(sessionConfig && { sessionConfig }),\n        transportType: TRANSPORT_TYPES.relay,\n      };\n\n      await this.client.session.set(session.topic, session);\n      await this.setExpiry(session.topic, session.expiry);\n\n      await this.client.core.pairing.updateMetadata({\n        topic: pendingSession.pairingTopic,\n        metadata: session.peer.metadata,\n      });\n\n      this.client.events.emit(\"session_connect\", { session });\n      this.events.emit(engineEvent(\"session_connect\", pendingSession.proposalId), { session });\n\n      this.pendingSessions.delete(pendingSession.proposalId);\n      this.deleteProposal(pendingSession.proposalId, false);\n      this.cleanupDuplicatePairings(session);\n\n      await this.sendResult<\"wc_sessionSettle\">({\n        id: payload.id,\n        topic,\n        result: true,\n        throwOnFailedPublish: true,\n      });\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionSettleResponse: EnginePrivate[\"onSessionSettleResponse\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    if (isJsonRpcResult(payload)) {\n      await this.client.session.update(topic, { acknowledged: true });\n      this.events.emit(engineEvent(\"session_approve\", id), {});\n    } else if (isJsonRpcError(payload)) {\n      await this.client.session.delete(topic, getSdkError(\"USER_DISCONNECTED\"));\n      this.events.emit(engineEvent(\"session_approve\", id), { error: payload.error });\n    }\n  };\n\n  private onSessionUpdateRequest: EnginePrivate[\"onSessionUpdateRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { params, id } = payload;\n    try {\n      const memoryKey = `${topic}_session_update`;\n      // compare the current request id with the last processed session update\n      // we want to update only if the request is newer than the last processed one\n      const lastSessionUpdateId = MemoryStore.get<number>(memoryKey);\n\n      if (lastSessionUpdateId && this.isRequestOutOfSync(lastSessionUpdateId, id)) {\n        this.client.logger.warn(`Discarding out of sync request - ${id}`);\n        this.sendError({ id, topic, error: getSdkError(\"INVALID_UPDATE_REQUEST\") });\n        return;\n      }\n      this.isValidUpdate({ topic, ...params });\n      try {\n        MemoryStore.set(memoryKey, id);\n        await this.client.session.update(topic, { namespaces: params.namespaces });\n        await this.sendResult<\"wc_sessionUpdate\">({\n          id,\n          topic,\n          result: true,\n          throwOnFailedPublish: true,\n        });\n      } catch (e) {\n        MemoryStore.delete(memoryKey);\n        throw e;\n      }\n\n      this.client.events.emit(\"session_update\", { id, topic, params });\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  // compares the timestamp of the last processed request with the current request\n  // client <-> client rpc ID is timestamp + 3 random digits\n  private isRequestOutOfSync = (lastId: number, currentId: number) => {\n    return currentId.toString().slice(0, -3) < lastId.toString().slice(0, -3);\n  };\n\n  private onSessionUpdateResponse: EnginePrivate[\"onSessionUpdateResponse\"] = (_topic, payload) => {\n    const { id } = payload;\n    const target = engineEvent(\"session_update\", id);\n    const listeners = this.events.listenerCount(target);\n    if (listeners === 0) {\n      throw new Error(`emitting ${target} without any listeners`);\n    }\n    if (isJsonRpcResult(payload)) {\n      this.events.emit(engineEvent(\"session_update\", id), {});\n    } else if (isJsonRpcError(payload)) {\n      this.events.emit(engineEvent(\"session_update\", id), { error: payload.error });\n    }\n  };\n\n  private onSessionExtendRequest: EnginePrivate[\"onSessionExtendRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    try {\n      this.isValidExtend({ topic });\n      await this.setExpiry(topic, calcExpiry(SESSION_EXPIRY));\n      await this.sendResult<\"wc_sessionExtend\">({\n        id,\n        topic,\n        result: true,\n        throwOnFailedPublish: true,\n      });\n      this.client.events.emit(\"session_extend\", { id, topic });\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionExtendResponse: EnginePrivate[\"onSessionExtendResponse\"] = (_topic, payload) => {\n    const { id } = payload;\n    const target = engineEvent(\"session_extend\", id);\n    const listeners = this.events.listenerCount(target);\n    if (listeners === 0) {\n      throw new Error(`emitting ${target} without any listeners`);\n    }\n    if (isJsonRpcResult(payload)) {\n      this.events.emit(engineEvent(\"session_extend\", id), {});\n    } else if (isJsonRpcError(payload)) {\n      this.events.emit(engineEvent(\"session_extend\", id), { error: payload.error });\n    }\n  };\n\n  private onSessionPingRequest: EnginePrivate[\"onSessionPingRequest\"] = async (topic, payload) => {\n    const { id } = payload;\n    try {\n      this.isValidPing({ topic });\n      await this.sendResult<\"wc_sessionPing\">({\n        id,\n        topic,\n        result: true,\n        throwOnFailedPublish: true,\n      });\n      this.client.events.emit(\"session_ping\", { id, topic });\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionPingResponse: EnginePrivate[\"onSessionPingResponse\"] = (_topic, payload) => {\n    const { id } = payload;\n    const target = engineEvent(\"session_ping\", id);\n\n    // put at the end of the stack to avoid a race condition\n    // where session_ping listener is not yet initialized\n    setTimeout(() => {\n      const listeners = this.events.listenerCount(target);\n      if (listeners === 0) {\n        throw new Error(`emitting ${target} without any listeners 2176`);\n      }\n\n      if (isJsonRpcResult(payload)) {\n        this.events.emit(engineEvent(\"session_ping\", id), {});\n      } else if (isJsonRpcError(payload)) {\n        this.events.emit(engineEvent(\"session_ping\", id), { error: payload.error });\n      }\n    }, 500);\n  };\n\n  private onSessionDeleteRequest: EnginePrivate[\"onSessionDeleteRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    try {\n      this.isValidDisconnect({ topic, reason: payload.params });\n      Promise.all([\n        new Promise((resolve) => {\n          // RPC request needs to happen before deletion as it utalises session encryption\n          this.client.core.relayer.once(RELAYER_EVENTS.publish, async () => {\n            resolve(await this.deleteSession({ topic, id }));\n          });\n        }),\n        this.sendResult<\"wc_sessionDelete\">({\n          id,\n          topic,\n          result: true,\n          throwOnFailedPublish: true,\n        }),\n        this.cleanupPendingSentRequestsForTopic({ topic, error: getSdkError(\"USER_DISCONNECTED\") }),\n      ]).catch((err) => this.client.logger.error(err));\n    } catch (err: any) {\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionRequest: EnginePrivate[\"onSessionRequest\"] = async (args) => {\n    const { topic, payload, attestation, encryptedId, transportType } = args;\n    const { id, params } = payload;\n    try {\n      await this.isValidRequest({ topic, ...params });\n      const session = this.client.session.get(topic);\n      const verifyContext = await this.getVerifyContext({\n        attestationId: attestation,\n        hash: hashMessage(JSON.stringify(formatJsonRpcRequest(\"wc_sessionRequest\", params, id))),\n        encryptedId,\n        metadata: session.peer.metadata,\n        transportType,\n      });\n      const request = {\n        id,\n        topic,\n        params,\n        verifyContext,\n      };\n      await this.setPendingSessionRequest(request);\n\n      if (\n        transportType === TRANSPORT_TYPES.link_mode &&\n        session.peer.metadata.redirect?.universal\n      ) {\n        // save app as supported for link mode\n        this.client.core.addLinkModeSupportedApp(session.peer.metadata.redirect?.universal);\n      }\n\n      if (this.client.signConfig?.disableRequestQueue) {\n        this.emitSessionRequest(request);\n      } else {\n        this.addSessionRequestToSessionRequestQueue(request);\n        this.processSessionRequestQueue();\n      }\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionRequestResponse: EnginePrivate[\"onSessionRequestResponse\"] = (\n    _topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    const target = engineEvent(\"session_request\", id);\n    const listeners = this.events.listenerCount(target);\n    if (listeners === 0) {\n      throw new Error(`emitting ${target} without any listeners`);\n    }\n    if (isJsonRpcResult(payload)) {\n      this.events.emit(engineEvent(\"session_request\", id), { result: payload.result });\n    } else if (isJsonRpcError(payload)) {\n      this.events.emit(engineEvent(\"session_request\", id), { error: payload.error });\n    }\n  };\n\n  private onSessionEventRequest: EnginePrivate[\"onSessionEventRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id, params } = payload;\n    try {\n      // similar to session update, we want to discard out of sync requests\n      // additionally we have to check the event type as well e.g. chainChanged/accountsChanged\n      const memoryKey = `${topic}_session_event_${params.event.name}`;\n      // compare the current request id with the last processed session update\n      // we want to update only if the request is newer than the last processed one\n      const lastSessionUpdateId = MemoryStore.get<number>(memoryKey);\n      if (lastSessionUpdateId && this.isRequestOutOfSync(lastSessionUpdateId, id)) {\n        this.client.logger.info(`Discarding out of sync request - ${id}`);\n        return;\n      }\n\n      this.isValidEmit({ topic, ...params });\n      this.client.events.emit(\"session_event\", { id, topic, params });\n      MemoryStore.set(memoryKey, id);\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionAuthenticateResponse: EnginePrivate[\"onSessionAuthenticateResponse\"] = (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    this.client.logger.trace({\n      type: \"method\",\n      method: \"onSessionAuthenticateResponse\",\n      topic,\n      payload,\n    });\n    if (isJsonRpcResult(payload)) {\n      this.events.emit(engineEvent(\"session_request\", id), { result: payload.result });\n    } else if (isJsonRpcError(payload)) {\n      this.events.emit(engineEvent(\"session_request\", id), { error: payload.error });\n    }\n  };\n\n  private onSessionAuthenticateRequest: EnginePrivate[\"onSessionAuthenticateRequest\"] = async (\n    args,\n  ) => {\n    const { topic, payload, attestation, encryptedId, transportType } = args;\n    try {\n      const { requester, authPayload, expiryTimestamp } = payload.params;\n      const verifyContext = await this.getVerifyContext({\n        attestationId: attestation,\n        hash: hashMessage(JSON.stringify(payload)),\n        encryptedId,\n        metadata: requester.metadata,\n        transportType,\n      });\n      const pendingRequest = {\n        requester,\n        pairingTopic: topic,\n        id: payload.id,\n        authPayload,\n        verifyContext,\n        expiryTimestamp,\n      };\n      await this.setAuthRequest(payload.id, {\n        request: pendingRequest,\n        pairingTopic: topic,\n        transportType,\n      });\n\n      if (transportType === TRANSPORT_TYPES.link_mode && requester.metadata.redirect?.universal) {\n        // save app as supported for link mode\n        this.client.core.addLinkModeSupportedApp(requester.metadata.redirect.universal);\n      }\n\n      this.client.events.emit(\"session_authenticate\", {\n        topic,\n        params: payload.params,\n        id: payload.id,\n        verifyContext,\n      });\n    } catch (err: any) {\n      this.client.logger.error(err);\n\n      const receiverPublicKey = payload.params.requester.publicKey;\n      const senderPublicKey = await this.client.core.crypto.generateKeyPair();\n      const appLink = this.getAppLinkIfEnabled(payload.params.requester.metadata, transportType);\n\n      const encodeOpts = {\n        type: TYPE_1,\n        receiverPublicKey,\n        senderPublicKey,\n      };\n      await this.sendError({\n        id: payload.id,\n        topic,\n        error: err,\n        encodeOpts,\n        rpcOpts: ENGINE_RPC_OPTS.wc_sessionAuthenticate.autoReject,\n        appLink,\n      });\n    }\n  };\n\n  private addSessionRequestToSessionRequestQueue = (request: PendingRequestTypes.Struct) => {\n    this.sessionRequestQueue.queue.push(request);\n  };\n\n  private cleanupAfterResponse = (params: EngineTypes.RespondParams) => {\n    this.deletePendingSessionRequest(params.response.id, { message: \"fulfilled\", code: 0 });\n    // intentionally delay the emitting of the next pending request a bit\n    setTimeout(() => {\n      this.sessionRequestQueue.state = ENGINE_QUEUE_STATES.idle;\n      this.processSessionRequestQueue();\n    }, toMiliseconds(this.requestQueueDelay));\n  };\n\n  // Allows for cleanup on any sent pending requests if the peer disconnects the session before responding\n  private cleanupPendingSentRequestsForTopic = ({\n    topic,\n    error,\n  }: {\n    topic: string;\n    error: ErrorResponse;\n  }) => {\n    const pendingRequests = this.client.core.history.pending;\n    if (pendingRequests.length > 0) {\n      const forSession = pendingRequests.filter(\n        (r) => r.topic === topic && r.request.method === \"wc_sessionRequest\",\n      );\n      forSession.forEach((r) => {\n        const id = r.request.id;\n        const target = engineEvent(\"session_request\", id);\n        const listeners = this.events.listenerCount(target);\n        if (listeners === 0) {\n          throw new Error(`emitting ${target} without any listeners`);\n        }\n        // notify .request() handler of the rejection\n        this.events.emit(engineEvent(\"session_request\", r.request.id), {\n          error,\n        });\n      });\n    }\n  };\n\n  private processSessionRequestQueue = () => {\n    if (this.sessionRequestQueue.state === ENGINE_QUEUE_STATES.active) {\n      this.client.logger.info(\"session request queue is already active.\");\n      return;\n    }\n    // Select the first/oldest request in the array to ensure last-in-first-out (LIFO)\n    const request = this.sessionRequestQueue.queue[0];\n    if (!request) {\n      this.client.logger.info(\"session request queue is empty.\");\n      return;\n    }\n\n    try {\n      this.sessionRequestQueue.state = ENGINE_QUEUE_STATES.active;\n      this.emitSessionRequest(request);\n    } catch (error) {\n      this.client.logger.error(error);\n    }\n  };\n\n  private emitSessionRequest = (request: PendingRequestTypes.Struct) => {\n    this.client.events.emit(\"session_request\", request);\n  };\n\n  // ---------- Expirer Events ---------------------------------------- //\n\n  private registerExpirerEvents() {\n    this.client.core.expirer.on(EXPIRER_EVENTS.expired, async (event: ExpirerTypes.Expiration) => {\n      const { topic, id } = parseExpirerTarget(event.target);\n      if (id && this.client.pendingRequest.keys.includes(id)) {\n        return await this.deletePendingSessionRequest(id, getInternalError(\"EXPIRED\"), true);\n      }\n      if (id && this.client.auth.requests.keys.includes(id)) {\n        return await this.deletePendingAuthRequest(id, getInternalError(\"EXPIRED\"), true);\n      }\n\n      if (topic) {\n        if (this.client.session.keys.includes(topic)) {\n          await this.deleteSession({ topic, expirerHasDeleted: true });\n          this.client.events.emit(\"session_expire\", { topic });\n        }\n      } else if (id) {\n        await this.deleteProposal(id, true);\n        this.client.events.emit(\"proposal_expire\", { id });\n      }\n    });\n  }\n\n  // ---------- Pairing Events ---------------------------------------- //\n  private registerPairingEvents() {\n    this.client.core.pairing.events.on(PAIRING_EVENTS.create, (pairing: PairingTypes.Struct) =>\n      this.onPairingCreated(pairing),\n    );\n    this.client.core.pairing.events.on(PAIRING_EVENTS.delete, (pairing: PairingTypes.Struct) => {\n      this.addToRecentlyDeleted(pairing.topic, \"pairing\");\n    });\n  }\n\n  /**\n   * when a pairing is created, we check if there is a pending proposal for it.\n   * if there is, we send it to onSessionProposeRequest to be processed as if it was received from the relay.\n   * It allows QR/URI to be scanned multiple times without having to create new pairing.\n   */\n  private onPairingCreated = (pairing: PairingTypes.Struct) => {\n    if (pairing.methods) {\n      this.expectedPairingMethodMap.set(pairing.topic, pairing.methods);\n    }\n    if (pairing.active) return;\n    const proposals = this.client.proposal.getAll();\n    const proposal = proposals.find((p) => p.pairingTopic === pairing.topic);\n    if (!proposal) return;\n    this.onSessionProposeRequest({\n      topic: pairing.topic,\n      payload: formatJsonRpcRequest(\n        \"wc_sessionPropose\",\n        {\n          ...proposal,\n          requiredNamespaces: proposal.requiredNamespaces,\n          optionalNamespaces: proposal.optionalNamespaces,\n          relays: proposal.relays,\n          proposer: proposal.proposer,\n          sessionProperties: proposal.sessionProperties,\n          scopedProperties: proposal.scopedProperties,\n        },\n        proposal.id,\n      ),\n    });\n  };\n\n  // ---------- Validation Helpers ------------------------------------ //\n  private isValidPairingTopic(topic: any) {\n    if (!isValidString(topic, false)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `pairing topic should be a string: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    if (!this.client.core.pairing.pairings.keys.includes(topic)) {\n      const { message } = getInternalError(\n        \"NO_MATCHING_KEY\",\n        `pairing topic doesn't exist: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    if (isExpired(this.client.core.pairing.pairings.get(topic).expiry)) {\n      const { message } = getInternalError(\"EXPIRED\", `pairing topic: ${topic}`);\n      throw new Error(message);\n    }\n  }\n\n  private async isValidSessionTopic(topic: any) {\n    if (!isValidString(topic, false)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `session topic should be a string: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    // Store will throw custom message if topic was recently deleted\n    this.checkRecentlyDeleted(topic);\n    if (!this.client.session.keys.includes(topic)) {\n      const { message } = getInternalError(\n        \"NO_MATCHING_KEY\",\n        `session topic doesn't exist: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    if (isExpired(this.client.session.get(topic).expiry)) {\n      await this.deleteSession({ topic });\n      const { message } = getInternalError(\"EXPIRED\", `session topic: ${topic}`);\n      throw new Error(message);\n    }\n\n    if (!this.client.core.crypto.keychain.has(topic)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `session topic does not exist in keychain: ${topic}`,\n      );\n      await this.deleteSession({ topic });\n      throw new Error(message);\n    }\n  }\n\n  private async isValidSessionOrPairingTopic(topic: string) {\n    this.checkRecentlyDeleted(topic);\n    if (this.client.session.keys.includes(topic)) {\n      await this.isValidSessionTopic(topic);\n    } else if (this.client.core.pairing.pairings.keys.includes(topic)) {\n      this.isValidPairingTopic(topic);\n    } else if (!isValidString(topic, false)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `session or pairing topic should be a string: ${topic}`,\n      );\n      throw new Error(message);\n    } else {\n      const { message } = getInternalError(\n        \"NO_MATCHING_KEY\",\n        `session or pairing topic doesn't exist: ${topic}`,\n      );\n      throw new Error(message);\n    }\n  }\n\n  private async isValidProposalId(id: any) {\n    if (!isValidId(id)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `proposal id should be a number: ${id}`,\n      );\n      throw new Error(message);\n    }\n    if (!this.client.proposal.keys.includes(id)) {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `proposal id doesn't exist: ${id}`);\n      throw new Error(message);\n    }\n    if (isExpired(this.client.proposal.get(id).expiryTimestamp)) {\n      await this.deleteProposal(id);\n      const { message } = getInternalError(\"EXPIRED\", `proposal id: ${id}`);\n      throw new Error(message);\n    }\n  }\n\n  // ---------- Validation  ------------------------------------------- //\n\n  private isValidConnect: EnginePrivate[\"isValidConnect\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `connect() params: ${JSON.stringify(params)}`,\n      );\n      throw new Error(message);\n    }\n    const {\n      pairingTopic,\n      requiredNamespaces,\n      optionalNamespaces,\n      sessionProperties,\n      scopedProperties,\n      relays,\n    } = params;\n    if (!isUndefined(pairingTopic)) await this.isValidPairingTopic(pairingTopic);\n\n    if (!isValidRelays(relays, true)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `connect() relays: ${relays}`);\n      throw new Error(message);\n    }\n\n    // validate required namespaces only if they are defined\n    if (!isUndefined(requiredNamespaces) && isValidObject(requiredNamespaces) !== 0) {\n      const warning =\n        \"requiredNamespaces are deprecated and are automatically assigned to optionalNamespaces\";\n      // if logger level is one of the following, the logger.warn will not be shown, so we need to use console.warn\n      if ([\"fatal\", \"error\", \"silent\"].includes(this.client.logger.level)) {\n        console.warn(warning);\n      } else {\n        this.client.logger.warn(warning);\n      }\n      this.validateNamespaces(requiredNamespaces, \"requiredNamespaces\");\n    }\n\n    // validate optional namespaces only if they are defined\n    if (!isUndefined(optionalNamespaces) && isValidObject(optionalNamespaces) !== 0) {\n      this.validateNamespaces(optionalNamespaces, \"optionalNamespaces\");\n    }\n\n    // validate session properties only if they are defined\n    if (!isUndefined(sessionProperties)) {\n      this.validateSessionProps(sessionProperties, \"sessionProperties\");\n    }\n\n    if (!isUndefined(scopedProperties)) {\n      this.validateSessionProps(scopedProperties, \"scopedProperties\");\n\n      const requestedNamespaces = Object.keys(requiredNamespaces || {}).concat(\n        Object.keys(optionalNamespaces || {}),\n      );\n\n      const scopedNamespaces = Object.keys(scopedProperties);\n      const valid = scopedNamespaces.every((ns) => requestedNamespaces.includes(ns));\n      if (!valid) {\n        throw new Error(\n          `Scoped properties must be a subset of required/optional namespaces, received: ${JSON.stringify(\n            scopedProperties,\n          )}, required/optional namespaces: ${JSON.stringify(requestedNamespaces)}`,\n        );\n      }\n    }\n  };\n\n  private validateNamespaces = (\n    namespaces: ProposalTypes.RequiredNamespaces | ProposalTypes.OptionalNamespaces,\n    type: string,\n  ) => {\n    const validRequiredNamespacesError = isValidRequiredNamespaces(namespaces, \"connect()\", type);\n    if (validRequiredNamespacesError) throw new Error(validRequiredNamespacesError.message);\n  };\n\n  private isValidApprove: EnginePrivate[\"isValidApprove\"] = async (params) => {\n    if (!isValidParams(params))\n      throw new Error(\n        getInternalError(\"MISSING_OR_INVALID\", `approve() params: ${params}`).message,\n      );\n    const { id, namespaces, relayProtocol, sessionProperties, scopedProperties } = params;\n\n    this.checkRecentlyDeleted(id);\n    await this.isValidProposalId(id);\n    const proposal = this.client.proposal.get(id);\n    const validNamespacesError = isValidNamespaces(namespaces, \"approve()\");\n    if (validNamespacesError) throw new Error(validNamespacesError.message);\n    const conformingNamespacesError = isConformingNamespaces(\n      proposal.requiredNamespaces,\n      namespaces,\n      \"approve()\",\n    );\n    if (conformingNamespacesError) throw new Error(conformingNamespacesError.message);\n    if (!isValidString(relayProtocol, true)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `approve() relayProtocol: ${relayProtocol}`,\n      );\n      throw new Error(message);\n    }\n\n    if (!isUndefined(sessionProperties)) {\n      this.validateSessionProps(sessionProperties, \"sessionProperties\");\n    }\n\n    if (!isUndefined(scopedProperties)) {\n      this.validateSessionProps(scopedProperties, \"scopedProperties\");\n\n      const approvedNamespaces = new Set(Object.keys(namespaces));\n      const scopedNamespaces = Object.keys(scopedProperties);\n\n      // the approved scoped namespaces must be a subset of the approved namespaces\n      const valid = scopedNamespaces.every((ns) => approvedNamespaces.has(ns));\n      if (!valid) {\n        throw new Error(\n          `Scoped properties must be a subset of approved namespaces, received: ${JSON.stringify(\n            scopedProperties,\n          )}, approved namespaces: ${Array.from(approvedNamespaces).join(\", \")}`,\n        );\n      }\n    }\n  };\n\n  private isValidReject: EnginePrivate[\"isValidReject\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `reject() params: ${params}`);\n      throw new Error(message);\n    }\n    const { id, reason } = params;\n    this.checkRecentlyDeleted(id);\n    await this.isValidProposalId(id);\n    if (!isValidErrorReason(reason)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `reject() reason: ${JSON.stringify(reason)}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  private isValidSessionSettleRequest: EnginePrivate[\"isValidSessionSettleRequest\"] = (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `onSessionSettleRequest() params: ${params}`,\n      );\n      throw new Error(message);\n    }\n    const { relay, controller, namespaces, expiry } = params;\n    if (!isValidRelay(relay)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `onSessionSettleRequest() relay protocol should be a string`,\n      );\n      throw new Error(message);\n    }\n    const validControllerError = isValidController(controller, \"onSessionSettleRequest()\");\n    if (validControllerError) throw new Error(validControllerError.message);\n    const validNamespacesError = isValidNamespaces(namespaces, \"onSessionSettleRequest()\");\n    if (validNamespacesError) throw new Error(validNamespacesError.message);\n    if (isExpired(expiry)) {\n      const { message } = getInternalError(\"EXPIRED\", `onSessionSettleRequest()`);\n      throw new Error(message);\n    }\n  };\n\n  private isValidUpdate: EnginePrivate[\"isValidUpdate\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `update() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic, namespaces } = params;\n\n    this.checkRecentlyDeleted(topic);\n    await this.isValidSessionTopic(topic);\n    const session = this.client.session.get(topic);\n    const validNamespacesError = isValidNamespaces(namespaces, \"update()\");\n    if (validNamespacesError) throw new Error(validNamespacesError.message);\n    const conformingNamespacesError = isConformingNamespaces(\n      session.requiredNamespaces,\n      namespaces,\n      \"update()\",\n    );\n    if (conformingNamespacesError) throw new Error(conformingNamespacesError.message);\n    // TODO(ilja) - check if wallet\n  };\n\n  private isValidExtend: EnginePrivate[\"isValidExtend\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `extend() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic } = params;\n\n    this.checkRecentlyDeleted(topic);\n    await this.isValidSessionTopic(topic);\n  };\n\n  private isValidRequest: EnginePrivate[\"isValidRequest\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `request() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic, request, chainId, expiry } = params;\n    this.checkRecentlyDeleted(topic);\n    await this.isValidSessionTopic(topic);\n    const { namespaces } = this.client.session.get(topic);\n    if (!isValidNamespacesChainId(namespaces, chainId)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `request() chainId: ${chainId}`);\n      throw new Error(message);\n    }\n    if (!isValidRequest(request)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `request() ${JSON.stringify(request)}`,\n      );\n      throw new Error(message);\n    }\n    if (!isValidNamespacesRequest(namespaces, chainId, request.method)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `request() method: ${request.method}`,\n      );\n      throw new Error(message);\n    }\n    if (expiry && !isValidRequestExpiry(expiry, SESSION_REQUEST_EXPIRY_BOUNDARIES)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `request() expiry: ${expiry}. Expiry must be a number (in seconds) between ${SESSION_REQUEST_EXPIRY_BOUNDARIES.min} and ${SESSION_REQUEST_EXPIRY_BOUNDARIES.max}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  private isValidRespond: EnginePrivate[\"isValidRespond\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `respond() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic, response } = params;\n    try {\n      // if the session is already disconnected, we can't respond to the request so we need to delete it\n      await this.isValidSessionTopic(topic);\n    } catch (error) {\n      if (params?.response?.id) this.cleanupAfterResponse(params);\n      throw error;\n    }\n    if (!isValidResponse(response)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `respond() response: ${JSON.stringify(response)}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  private isValidPing: EnginePrivate[\"isValidPing\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `ping() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic } = params;\n    await this.isValidSessionOrPairingTopic(topic);\n  };\n\n  private isValidEmit: EnginePrivate[\"isValidEmit\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `emit() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic, event, chainId } = params;\n    await this.isValidSessionTopic(topic);\n    const { namespaces } = this.client.session.get(topic);\n    if (!isValidNamespacesChainId(namespaces, chainId)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `emit() chainId: ${chainId}`);\n      throw new Error(message);\n    }\n    if (!isValidEvent(event)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `emit() event: ${JSON.stringify(event)}`,\n      );\n      throw new Error(message);\n    }\n    if (!isValidNamespacesEvent(namespaces, chainId, event.name)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `emit() event: ${JSON.stringify(event)}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  private isValidDisconnect: EnginePrivate[\"isValidDisconnect\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `disconnect() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic } = params;\n    await this.isValidSessionOrPairingTopic(topic);\n  };\n\n  private isValidAuthenticate = (params: AuthTypes.SessionAuthenticateParams) => {\n    const { chains, uri, domain, nonce } = params;\n\n    // ----- validate params ----- //\n    if (!Array.isArray(chains) || chains.length === 0) {\n      throw new Error(\"chains is required and must be a non-empty array\");\n    }\n    if (!isValidString(uri, false)) {\n      throw new Error(\"uri is required parameter\");\n    }\n    if (!isValidString(domain, false)) {\n      throw new Error(\"domain is required parameter\");\n    }\n    if (!isValidString(nonce, false)) {\n      throw new Error(\"nonce is required parameter\");\n    }\n\n    // ----- reject multi namespaces ----- //\n    const uniqueNamespaces = [...new Set(chains.map((chain) => parseChainId(chain).namespace))];\n    if (uniqueNamespaces.length > 1) {\n      throw new Error(\n        \"Multi-namespace requests are not supported. Please request single namespace only.\",\n      );\n    }\n\n    const { namespace } = parseChainId(chains[0]);\n    if (namespace !== \"eip155\") {\n      throw new Error(\n        \"Only eip155 namespace is supported for authenticated sessions. Please use .connect() for non-eip155 chains.\",\n      );\n    }\n  };\n\n  private getVerifyContext = async (params: {\n    attestationId?: string;\n    hash?: string;\n    encryptedId?: string;\n    metadata: CoreTypes.Metadata;\n    transportType?: RelayerTypes.TransportType;\n  }) => {\n    const { attestationId, hash, encryptedId, metadata, transportType } = params;\n    const context: Verify.Context = {\n      verified: {\n        verifyUrl: metadata.verifyUrl || VERIFY_SERVER,\n        validation: \"UNKNOWN\",\n        origin: metadata.url || \"\",\n      },\n    };\n\n    try {\n      if (transportType === TRANSPORT_TYPES.link_mode) {\n        const applink = this.getAppLinkIfEnabled(metadata, transportType);\n        context.verified.validation =\n          applink && new URL(applink).origin === new URL(metadata.url).origin ? \"VALID\" : \"INVALID\";\n        return context;\n      }\n      const result = await this.client.core.verify.resolve({\n        attestationId,\n        hash,\n        encryptedId,\n        verifyUrl: metadata.verifyUrl,\n      });\n      if (result) {\n        context.verified.origin = result.origin;\n        context.verified.isScam = result.isScam;\n        context.verified.validation =\n          result.origin === new URL(metadata.url).origin ? \"VALID\" : \"INVALID\";\n      }\n    } catch (e) {\n      this.client.logger.warn(e);\n    }\n\n    this.client.logger.debug(`Verify context: ${JSON.stringify(context)}`);\n    return context;\n  };\n\n  private validateSessionProps = (properties: SessionTypes.ScopedProperties, type: string) => {\n    Object.values(properties).forEach((property, index) => {\n      if (property === null || property === undefined) {\n        const { message } = getInternalError(\n          \"MISSING_OR_INVALID\",\n          `${type} must contain an existing value for each key. Received: ${property} for key ${\n            Object.keys(properties)[index]\n          }`,\n        );\n        throw new Error(message);\n      }\n    });\n  };\n\n  private getPendingAuthRequest = (id: number) => {\n    const request = this.client.auth.requests.get(id);\n    return typeof request === \"object\" ? request : undefined;\n  };\n\n  private addToRecentlyDeleted = (\n    id: string | number,\n    type: \"pairing\" | \"session\" | \"proposal\" | \"request\",\n  ) => {\n    this.recentlyDeletedMap.set(id, type);\n    // remove first half of the map if it exceeds the limit\n    if (this.recentlyDeletedMap.size >= this.recentlyDeletedLimit) {\n      let i = 0;\n      const numItemsToDelete = this.recentlyDeletedLimit / 2;\n      for (const k of this.recentlyDeletedMap.keys()) {\n        if (i++ >= numItemsToDelete) {\n          break;\n        }\n        this.recentlyDeletedMap.delete(k);\n      }\n    }\n  };\n\n  private checkRecentlyDeleted = (id: string | number) => {\n    const deletedRecord = this.recentlyDeletedMap.get(id);\n    if (deletedRecord) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `Record was recently deleted - ${deletedRecord}: ${id}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  private isLinkModeEnabled = (\n    peerMetadata?: CoreTypes.Metadata,\n    transportType?: RelayerTypes.TransportType,\n  ): boolean => {\n    if (!peerMetadata || transportType !== TRANSPORT_TYPES.link_mode) return false;\n\n    return (\n      this.client.metadata?.redirect?.linkMode === true &&\n      this.client.metadata?.redirect?.universal !== undefined &&\n      this.client.metadata?.redirect?.universal !== \"\" &&\n      peerMetadata?.redirect?.universal !== undefined &&\n      peerMetadata?.redirect?.universal !== \"\" &&\n      peerMetadata?.redirect?.linkMode === true &&\n      this.client.core.linkModeSupportedApps.includes(peerMetadata.redirect.universal) &&\n      typeof (global as any)?.Linking !== \"undefined\"\n    );\n  };\n\n  private getAppLinkIfEnabled = (\n    peerMetadata?: CoreTypes.Metadata,\n    transportType?: RelayerTypes.TransportType,\n  ): string | undefined => {\n    return this.isLinkModeEnabled(peerMetadata, transportType)\n      ? peerMetadata?.redirect?.universal\n      : undefined;\n  };\n\n  private handleLinkModeMessage = ({ url }: { url: string }) => {\n    if (!url || !url.includes(\"wc_ev\") || !url.includes(\"topic\")) return;\n\n    const topic = getSearchParamFromURL(url, \"topic\") || \"\";\n    const message = decodeURIComponent(getSearchParamFromURL(url, \"wc_ev\") || \"\");\n\n    const sessionExists = this.client.session.keys.includes(topic);\n\n    if (sessionExists) {\n      this.client.session.update(topic, { transportType: TRANSPORT_TYPES.link_mode });\n    }\n\n    this.client.core.dispatchEnvelope({ topic, message, sessionExists });\n  };\n\n  private registerLinkModeListeners = async () => {\n    if (isTestRun() || (isReactNative() && this.client.metadata.redirect?.linkMode)) {\n      const linking = (global as any)?.Linking;\n      // global.Linking is set by react-native-compat\n      if (typeof linking !== \"undefined\") {\n        // set URL listener\n        linking.addEventListener(\"url\", this.handleLinkModeMessage, this.client.name);\n\n        // check for initial URL -> cold boots\n        const initialUrl = await linking.getInitialURL();\n        if (initialUrl) {\n          // wait to process the message to allow event listeners to be registered by the implementing app\n          setTimeout(() => {\n            this.handleLinkModeMessage({ url: initialUrl });\n          }, 50);\n        }\n      }\n    }\n  };\n\n  private shouldSetTVF = (\n    protocolMethod: JsonRpcTypes.WcMethod,\n    params: JsonRpcTypes.RequestParams[\"wc_sessionRequest\"],\n  ) => {\n    if (!params) return false;\n    if (protocolMethod !== \"wc_sessionRequest\") return false;\n    const { request } = params;\n    return Object.keys(TVF_METHODS).includes(request.method);\n  };\n\n  private getTVFParams = (\n    id: number,\n    params: JsonRpcTypes.RequestParams[\"wc_sessionRequest\"],\n    result?: any,\n  ) => {\n    try {\n      const requestMethod = params.request.method;\n      const txHashes = this.extractTxHashesFromResult(requestMethod, result);\n      const tvf: RelayerTypes.ITVF = {\n        correlationId: id,\n        rpcMethods: [requestMethod],\n        chainId: params.chainId,\n        ...(this.isValidContractData(params.request.params) && {\n          // initially only get contractAddresses from EVM txs\n          contractAddresses: [params.request.params?.[0]?.to],\n        }),\n        txHashes,\n      };\n      return tvf;\n    } catch (e) {\n      this.client.logger.warn(\"Error getting TVF params\", e);\n    }\n    return {};\n  };\n\n  private isValidContractData = (params: any) => {\n    if (!params) return false;\n    try {\n      const data = params?.data || params?.[0]?.data;\n\n      if (!data.startsWith(\"0x\")) return false;\n\n      const hexPart = data.slice(2);\n      if (!/^[0-9a-fA-F]*$/.test(hexPart)) return false;\n\n      return hexPart.length % 2 === 0;\n    } catch (e) {}\n    return false;\n  };\n\n  private extractTxHashesFromResult = (method: string, result: any): string[] => {\n    try {\n      const methodConfig = TVF_METHODS[method as keyof typeof TVF_METHODS];\n      // result = 0x...\n      if (typeof result === \"string\") {\n        return [result];\n      }\n\n      // result = { key: [0x...] } or { key: 0x... }\n      const hashes: string[] = result[methodConfig.key];\n\n      // result = { key: [0x...] }\n      if (isValidArray(hashes)) {\n        if (method === \"solana_signAllTransactions\") {\n          return hashes.map((hash) => extractSolanaTransactionId(hash));\n        }\n\n        return hashes;\n\n        // result = { key: 0x... }\n      } else if (typeof hashes === \"string\") {\n        return [hashes];\n      }\n    } catch (e) {\n      this.client.logger.warn(\"Error extracting tx hashes from result\", e);\n    }\n    return [];\n  };\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { ICore, ProposalTypes } from \"@walletconnect/types\";\n\nimport { SIGN_CLIENT_STORAGE_PREFIX, PROPOSAL_CONTEXT } from \"../constants\";\n\nexport class Proposal extends Store<number, ProposalTypes.Struct> {\n  constructor(\n    public core: I<PERSON>ore,\n    public logger: Logger,\n  ) {\n    super(core, logger, PROPOSAL_CONTEXT, SIGN_CLIENT_STORAGE_PREFIX);\n  }\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { ICore, SessionTypes } from \"@walletconnect/types\";\n\nimport { SIGN_CLIENT_STORAGE_PREFIX, SESSION_CONTEXT } from \"../constants\";\n\nexport class Session extends Store<string, SessionTypes.Struct> {\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    super(core, logger, SESSION_CONTEXT, SIGN_CLIENT_STORAGE_PREFIX);\n  }\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { ICore, PendingRequestTypes } from \"@walletconnect/types\";\nimport { REQUEST_CONTEXT, SIGN_CLIENT_STORAGE_PREFIX } from \"../constants\";\n\nexport class PendingRequest extends Store<number, PendingRequestTypes.Struct> {\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    super(\n      core,\n      logger,\n      REQUEST_CONTEXT,\n      SIGN_CLIENT_STORAGE_PREFIX,\n      (val: PendingRequestTypes.Struct) => val.id,\n    );\n  }\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { ICore } from \"@walletconnect/types\";\n\nimport { AUTH_KEYS_CONTEXT, AUTH_STORAGE_PREFIX, AUTH_PUBLIC_KEY_NAME } from \"../constants\";\n\nexport class Auth<PERSON><PERSON> extends Store<string, { responseTopic: string; publicKey: string }> {\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    super(core, logger, AUTH_KEYS_CONTEXT, AUTH_STORAGE_PREFIX, () => AUTH_PUBLIC_KEY_NAME);\n  }\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { ICore } from \"@walletconnect/types\";\n\nimport { AUTH_PAIRING_TOPIC_CONTEXT, AUTH_STORAGE_PREFIX } from \"../constants\";\n\nexport class AuthPairingTopic extends Store<string, { topic: string; pairingTopic: string }> {\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    super(core, logger, AUTH_PAIRING_TOPIC_CONTEXT, AUTH_STORAGE_PREFIX);\n  }\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { AuthTypes, ICore } from \"@walletconnect/types\";\n\nimport { AUTH_STORAGE_PREFIX, AUTH_REQUEST_CONTEXT } from \"../constants\";\n\nexport class AuthRequest extends Store<number, AuthTypes.PendingRequest> {\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    super(\n      core,\n      logger,\n      AUTH_REQUEST_CONTEXT,\n      AUTH_STORAGE_PREFIX,\n      (val: AuthTypes.PendingRequest) => val.id,\n    );\n  }\n}\n", "import { Logger } from \"@walletconnect/logger\";\nimport { <PERSON>Auth, ICore } from \"@walletconnect/types\";\nimport { AuthPairingTopic } from \"./authPairingTopic\";\nimport { AuthRequest } from \"./authRequest\";\nimport { AuthKey } from \"./authKey\";\n\nexport class AuthStore {\n  public authKeys: IAuth[\"authKeys\"];\n  public pairingTopics: IAuth[\"pairingTopics\"];\n  public requests: IAuth[\"requests\"];\n\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    this.authKeys = new AuthKey(this.core, this.logger);\n    this.pairingTopics = new AuthPairingTopic(this.core, this.logger);\n    this.requests = new AuthRequest(this.core, this.logger);\n  }\n\n  public async init() {\n    await this.authKeys.init();\n    await this.pairingTopics.init();\n    await this.requests.init();\n  }\n}\n", "import { Core } from \"@walletconnect/core\";\nimport {\n  generateChildLogger,\n  getDefaultLoggerOptions,\n  getLoggerContext,\n  pino,\n} from \"@walletconnect/logger\";\nimport { SignClientTypes, ISignClient, ISignClientEvents, EngineTypes } from \"@walletconnect/types\";\nimport { ONE_SECOND, toMiliseconds } from \"@walletconnect/time\";\nimport { populateAppMetadata } from \"@walletconnect/utils\";\nimport { EventEmitter } from \"events\";\nimport { SIGN_CLIENT_DEFAULT, SIGN_CLIENT_PROTOCOL, SIGN_CLIENT_VERSION } from \"./constants\";\nimport { AuthStore, Engine, PendingRequest, Proposal, Session } from \"./controllers\";\n\nexport class SignClient extends ISignClient {\n  public readonly protocol = SIGN_CLIENT_PROTOCOL;\n  public readonly version = SIGN_CLIENT_VERSION;\n  public readonly name: ISignClient[\"name\"] = SIGN_CLIENT_DEFAULT.name;\n  public readonly metadata: ISignClient[\"metadata\"];\n\n  public core: ISignClient[\"core\"];\n  public logger: ISignClient[\"logger\"];\n  public events: ISignClient[\"events\"] = new EventEmitter();\n  public engine: ISignClient[\"engine\"];\n  public session: ISignClient[\"session\"];\n  public proposal: ISignClient[\"proposal\"];\n  public pendingRequest: ISignClient[\"pendingRequest\"];\n  public auth: ISignClient[\"auth\"];\n  public signConfig?: ISignClient[\"signConfig\"];\n\n  static async init(opts?: SignClientTypes.Options) {\n    const client = new SignClient(opts);\n    await client.initialize();\n\n    return client;\n  }\n\n  constructor(opts?: SignClientTypes.Options) {\n    super(opts);\n\n    this.name = opts?.name || SIGN_CLIENT_DEFAULT.name;\n    this.metadata = populateAppMetadata(opts?.metadata);\n    this.signConfig = opts?.signConfig;\n\n    const logger =\n      typeof opts?.logger !== \"undefined\" && typeof opts?.logger !== \"string\"\n        ? opts.logger\n        : pino(getDefaultLoggerOptions({ level: opts?.logger || SIGN_CLIENT_DEFAULT.logger }));\n\n    this.core = opts?.core || new Core(opts);\n    this.logger = generateChildLogger(logger, this.name);\n    this.session = new Session(this.core, this.logger);\n    this.proposal = new Proposal(this.core, this.logger);\n    this.pendingRequest = new PendingRequest(this.core, this.logger);\n    this.engine = new Engine(this);\n    this.auth = new AuthStore(this.core, this.logger);\n  }\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  get pairing() {\n    return this.core.pairing.pairings;\n  }\n\n  // ---------- Events ----------------------------------------------- //\n\n  public on: ISignClientEvents[\"on\"] = (name, listener) => {\n    return this.events.on(name, listener);\n  };\n\n  public once: ISignClientEvents[\"once\"] = (name, listener) => {\n    return this.events.once(name, listener);\n  };\n\n  public off: ISignClientEvents[\"off\"] = (name, listener) => {\n    return this.events.off(name, listener);\n  };\n\n  public removeListener: ISignClientEvents[\"removeListener\"] = (name, listener) => {\n    return this.events.removeListener(name, listener);\n  };\n\n  public removeAllListeners: ISignClientEvents[\"removeAllListeners\"] = (name) => {\n    return this.events.removeAllListeners(name);\n  };\n\n  // ---------- Engine ----------------------------------------------- //\n\n  public connect: ISignClient[\"connect\"] = async (params) => {\n    try {\n      return await this.engine.connect(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public pair: ISignClient[\"pair\"] = async (params) => {\n    try {\n      return await this.engine.pair(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public approve: ISignClient[\"approve\"] = async (params) => {\n    try {\n      return await this.engine.approve(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public reject: ISignClient[\"reject\"] = async (params) => {\n    try {\n      return await this.engine.reject(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public update: ISignClient[\"update\"] = async (params) => {\n    try {\n      return await this.engine.update(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public extend: ISignClient[\"extend\"] = async (params) => {\n    try {\n      return await this.engine.extend(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public request: ISignClient[\"request\"] = async <T>(params: EngineTypes.RequestParams) => {\n    try {\n      return await this.engine.request<T>(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public respond: ISignClient[\"respond\"] = async (params) => {\n    try {\n      return await this.engine.respond(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public ping: ISignClient[\"ping\"] = async (params) => {\n    try {\n      return await this.engine.ping(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public emit: ISignClient[\"emit\"] = async (params) => {\n    try {\n      return await this.engine.emit(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public disconnect: ISignClient[\"disconnect\"] = async (params) => {\n    try {\n      return await this.engine.disconnect(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public find: ISignClient[\"find\"] = (params) => {\n    try {\n      return this.engine.find(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public getPendingSessionRequests: ISignClient[\"getPendingSessionRequests\"] = () => {\n    try {\n      return this.engine.getPendingSessionRequests();\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public authenticate: ISignClient[\"authenticate\"] = async (params, walletUniversalLink) => {\n    try {\n      return await this.engine.authenticate(params, walletUniversalLink);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public formatAuthMessage: ISignClient[\"formatAuthMessage\"] = (params) => {\n    try {\n      return this.engine.formatAuthMessage(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public approveSessionAuthenticate: ISignClient[\"approveSessionAuthenticate\"] = async (params) => {\n    try {\n      return await this.engine.approveSessionAuthenticate(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public rejectSessionAuthenticate: ISignClient[\"rejectSessionAuthenticate\"] = async (params) => {\n    try {\n      return await this.engine.rejectSessionAuthenticate(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async initialize() {\n    this.logger.trace(`Initialized`);\n    try {\n      await this.core.start();\n      await this.session.init();\n      await this.proposal.init();\n      await this.pendingRequest.init();\n      await this.auth.init();\n      await this.engine.init();\n      this.logger.info(`SignClient Initialization Success`);\n      setTimeout(() => {\n        this.engine.processRelayMessageCache();\n      }, toMiliseconds(ONE_SECOND));\n    } catch (error: any) {\n      this.logger.info(`SignClient Initialization Failure`);\n      this.logger.error(error.message);\n      throw error;\n    }\n  }\n}\n", "import { SignClient as Client } from \"./client\";\nimport { Session } from \"./controllers/session\";\nexport * from \"./constants\";\n\nexport const SessionStore = Session;\nexport const SignClient = Client;\nexport default Client;\n"], "names": ["THIRTY_DAYS", "SEVEN_DAYS", "FIVE_MINUTES", "ONE_DAY", "ONE_HOUR", "IEngine", "client", "__publicField", "ENGINE_CONTEXT", "EventEmmiter", "ENGINE_QUEUE_STATES", "ONE_SECOND", "ENGINE_RPC_OPTS", "toMiliseconds", "params", "connectParams", "__spreadProps", "__spreadValues", "mergeRequiredAndOptionalNamespaces", "pairingTopic", "requiredNamespaces", "optionalNamespaces", "sessionProperties", "scopedProperties", "relays", "topic", "uri", "active", "pairing", "error", "newTopic", "newUri", "message", "getInternalError", "public<PERSON>ey", "expiry", "expiryTimestamp", "calcExpiry", "proposal", "RELAYER_DEFAULT_PROTOCOL", "payloadId", "sessionConnectTarget", "engineEvent", "reject", "resolve", "approval", "createDelayedPromise", "PROPOSAL_EXPIRY_MESSAGE", "proposalExpireHandler", "id", "session", "_a", "_b", "_c", "configEvent", "EVENT_CLIENT_SESSION_TRACES", "EVENT_CLIENT_SESSION_ERRORS", "relayProtocol", "namespaces", "sessionConfig", "proposer", "event", "selfPublicKey", "peerPublic<PERSON>ey", "sessionTopic", "<PERSON><PERSON><PERSON><PERSON>", "SESSION_EXPIRY", "transportType", "TRANSPORT_TYPES", "getSdkError", "reason", "acknowledged", "clientRpcId", "relayRpcId", "getBigIntRpcId", "oldNamespaces", "e", "chainId", "request", "done", "result", "protocolMethod", "appLink", "protocolRequestParams", "shouldSetTVF", "wcDeepLink", "getDeepLink", "WALLETCONNECT_DEEPLINK_CHOICE", "handleDeeplinkRedirect", "response", "isJsonRpcResult", "isJsonRpcError", "isSessionCompatible", "walletUniversalLink", "isLinkMode", "chains", "statement", "domain", "nonce", "type", "exp", "nbf", "methods", "resources", "connectionUri", "responseTopic", "hash<PERSON><PERSON>", "AUTH_PUBLIC_KEY_NAME", "namespace", "parseChainId", "recap", "createEncodedRecap", "getRecapFromResources", "mergeEncodedRecaps", "authRequestExpiry", "authenticateId", "sessionConnectEventTarget", "authenticateEventTarget", "onSessionConnect", "onAuthenticate", "payload", "cacaos", "responder", "approvedMethods", "approvedAccounts", "cacao", "validateSignedCacao", "<PERSON><PERSON><PERSON><PERSON>", "getNamespacedDidChainId", "parsed<PERSON><PERSON><PERSON>", "get<PERSON>id<PERSON><PERSON><PERSON>", "methodsfromRecap", "getMethodsFromRecap", "chainsFromRecap", "getChainsFromRecap", "chain", "buildNamespacesFromAuth", "linkModeURL", "formatJsonRpcRequest", "TYPE_2", "BASE64URL", "getLinkModeURL", "sessionAuthenticateResponseParams", "auths", "EVENT_CLIENT_AUTHENTICATE_TRACES", "EVENT_CLIENT_AUTHENTICATE_ERRORS", "pendingRequest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeOpts", "TYPE_1", "invalidErr", "iss", "formatMessage", "duplicates", "p", "expirer<PERSON><PERSON><PERSON><PERSON><PERSON>", "emitEvent", "self", "r", "verifyContext", "args", "method", "throwOnFailedPublish", "tvf", "encoding", "BASE64", "attestation", "METHODS_TO_VERIFY", "decryptedId", "hashMessage", "opts", "redirectURL", "formatJsonRpcResult", "record", "rpcOpts", "formatJsonRpcError", "sessionTopics", "proposalIds", "toCleanup", "isExpired", "encryptedId", "req<PERSON><PERSON><PERSON>", "resMethod", "requestMethod", "expectedMethods", "EVENT_CLIENT_PAIRING_ERRORS", "EVENT_CLIENT_PAIRING_TRACES", "err", "subscriptionId", "target", "relay", "controller", "pendingSession", "s", "<PERSON><PERSON><PERSON>", "lastSessionUpdateId", "MemoryStore", "lastId", "currentId", "_topic", "RELAYER_EVENTS", "requester", "authPayload", "pendingRequests", "isValidParams", "isUndefined", "isValidRelays", "isValidObject", "warning", "requestedNamespaces", "ns", "validRequiredNamespacesError", "isValidRequiredNamespaces", "validNamespacesError", "isValidNamespaces", "conformingNamespacesError", "isConformingNamespaces", "isValidString", "approvedNamespaces", "isValidErrorReason", "is<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "validControllerError", "isValidController", "isValidNamespacesChainId", "isValidRequest", "isValidNamespacesRequest", "isValidRequestExpiry", "SESSION_REQUEST_EXPIRY_BOUNDARIES", "isValidResponse", "isValidEvent", "isValidNamespacesEvent", "attestationId", "hash", "metadata", "context", "VERIFY_SERVER", "applink", "properties", "property", "index", "i", "numItemsToDelete", "k", "deletedRecord", "peerMetadata", "_d", "_e", "_f", "_g", "_h", "_i", "url", "getSearchParamFromURL", "sessionExists", "isTestRun", "isReactNative", "linking", "initialUrl", "TVF_METHODS", "txHashes", "data", "hexPart", "methodConfig", "hashes", "isValidArray", "extractSolanaTransactionId", "topics", "pendingMessages", "messages", "isJsonRpcRequest", "isJsonRpcResponse", "EXPIRER_EVENTS", "parseExpirer<PERSON>arget", "PAIRING_EVENTS", "isValidId", "Store", "core", "logger", "PROPOSAL_CONTEXT", "SIGN_CLIENT_STORAGE_PREFIX", "SESSION_CONTEXT", "REQUEST_CONTEXT", "val", "AUTH_KEYS_CONTEXT", "AUTH_STORAGE_PREFIX", "AUTH_PAIRING_TOPIC_CONTEXT", "AUTH_REQUEST_CONTEXT", "o", "<PERSON><PERSON><PERSON><PERSON>", "AuthPairingTopic", "AuthRequest", "SignClient", "ISignClient", "SIGN_CLIENT_PROTOCOL", "SIGN_CLIENT_VERSION", "SIGN_CLIENT_DEFAULT", "EventEmitter", "name", "listener", "populateAppMetadata", "pino", "getDefaultLoggerOptions", "Core", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Session", "Proposal", "PendingRequest", "Engine", "AuthStore", "getLoggerContext", "Client"], "mappings": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "debugId": null}}, {"offset": {"line": 2368, "column": 0}, "map": {"version": 3, "file": "index.es.js", "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/constants/client.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/constants/history.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/constants/proposal.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/constants/session.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/constants/engine.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/constants/pendingRequest.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/constants/verify.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/constants/auth.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/controllers/engine.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/controllers/proposal.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/controllers/session.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/controllers/pendingRequest.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/controllers/authKey.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/controllers/authPairingTopic.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/controllers/authRequest.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/controllers/authStore.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/client.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/sign-client/src/index.ts"], "sourcesContent": ["import { SignClientTypes } from \"@walletconnect/types\";\n\nexport const SIGN_CLIENT_PROTOCOL = \"wc\";\nexport const SIGN_CLIENT_VERSION = 2;\nexport const SIGN_CLIENT_CONTEXT = \"client\";\n\nexport const SIGN_CLIENT_STORAGE_PREFIX = `${SIGN_CLIENT_PROTOCOL}@${SIGN_CLIENT_VERSION}:${SIGN_CLIENT_CONTEXT}:`;\n\nexport const SIGN_CLIENT_DEFAULT = {\n  name: SIGN_CLIENT_CONTEXT,\n  logger: \"error\",\n  controller: false,\n  relayUrl: \"wss://relay.walletconnect.org\",\n};\n\nexport const SIGN_CLIENT_EVENTS: Record<SignClientTypes.Event, SignClientTypes.Event> = {\n  session_proposal: \"session_proposal\",\n  session_update: \"session_update\",\n  session_extend: \"session_extend\",\n  session_ping: \"session_ping\",\n  session_delete: \"session_delete\",\n  session_expire: \"session_expire\",\n  session_request: \"session_request\",\n  session_request_sent: \"session_request_sent\",\n  session_event: \"session_event\",\n  proposal_expire: \"proposal_expire\",\n  session_authenticate: \"session_authenticate\",\n  session_request_expire: \"session_request_expire\",\n  session_connect: \"session_connect\",\n};\n\nexport const SIGN_CLIENT_STORAGE_OPTIONS = {\n  database: \":memory:\",\n};\n\nexport const WALLETCONNECT_DEEPLINK_CHOICE = \"WALLETCONNECT_DEEPLINK_CHOICE\";\n", "export const HISTORY_EVENTS = {\n  created: \"history_created\",\n  updated: \"history_updated\",\n  deleted: \"history_deleted\",\n  sync: \"history_sync\",\n};\n\nexport const HISTORY_CONTEXT = \"history\";\n\nexport const HISTORY_STORAGE_VERSION = \"0.3\";\n", "import { THIRTY_DAYS } from \"@walletconnect/time\";\n\nexport const PROPOSAL_CONTEXT = \"proposal\";\n\nexport const PROPOSAL_EXPIRY = THIRTY_DAYS;\n\nexport const PROPOSAL_EXPIRY_MESSAGE = \"Proposal expired\";\n", "import { SEVEN_DAYS } from \"@walletconnect/time\";\n\nexport const SESSION_CONTEXT = \"session\";\n\nexport const SESSION_EXPIRY = SEVEN_DAYS;\n", "import { FIVE_MINUTES, ONE_DAY, ONE_HOUR, SEVEN_DAYS } from \"@walletconnect/time\";\nimport { EngineTypes } from \"@walletconnect/types\";\n\nexport const ENGINE_CONTEXT = \"engine\";\n\nexport const ENGINE_RPC_OPTS: EngineTypes.RpcOptsMap = {\n  wc_sessionPropose: {\n    req: {\n      ttl: FIVE_MINUTES,\n      prompt: true,\n      tag: 1100,\n    },\n    res: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1101,\n    },\n    reject: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1120,\n    },\n    autoReject: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1121,\n    },\n  },\n  wc_sessionSettle: {\n    req: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1102,\n    },\n    res: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1103,\n    },\n  },\n  wc_sessionUpdate: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1104,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1105,\n    },\n  },\n  wc_sessionExtend: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1106,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1107,\n    },\n  },\n  wc_sessionRequest: {\n    req: {\n      ttl: FIVE_MINUTES,\n      prompt: true,\n      tag: 1108,\n    },\n    res: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1109,\n    },\n  },\n  wc_sessionEvent: {\n    req: {\n      ttl: FIVE_MINUTES,\n      prompt: true,\n      tag: 1110,\n    },\n    res: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1111,\n    },\n  },\n\n  wc_sessionDelete: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1112,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1113,\n    },\n  },\n  wc_sessionPing: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1114,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1115,\n    },\n  },\n  wc_sessionAuthenticate: {\n    req: {\n      ttl: ONE_HOUR,\n      prompt: true,\n      tag: 1116,\n    },\n    res: {\n      ttl: ONE_HOUR,\n      prompt: false,\n      tag: 1117,\n    },\n    reject: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1118,\n    },\n    autoReject: {\n      ttl: FIVE_MINUTES,\n      prompt: false,\n      tag: 1119,\n    },\n  },\n};\n\nexport const SESSION_REQUEST_EXPIRY_BOUNDARIES = {\n  min: FIVE_MINUTES,\n  max: SEVEN_DAYS,\n};\n\nexport const ENGINE_QUEUE_STATES: { idle: \"IDLE\"; active: \"ACTIVE\" } = {\n  idle: \"IDLE\",\n  active: \"ACTIVE\",\n};\n\nexport const TVF_METHODS = {\n  eth_sendTransaction: {\n    key: \"\",\n  },\n  eth_sendRawTransaction: {\n    key: \"\",\n  },\n  wallet_sendCalls: {\n    key: \"\",\n  },\n\n  solana_signTransaction: {\n    key: \"signature\",\n  },\n  solana_signAllTransactions: {\n    key: \"transactions\",\n  },\n  solana_signAndSendTransaction: {\n    key: \"signature\",\n  },\n};\n", "export const REQUEST_CONTEXT = \"request\";\n", "export const METHODS_TO_VERIFY = [\n  \"wc_sessionPropose\",\n  \"wc_sessionRequest\",\n  \"wc_authRequest\",\n  \"wc_sessionAuthenticate\",\n];\n", "export const AUTH_PROTOCOL = \"wc\";\nexport const AUTH_VERSION = 1.5;\nexport const AUTH_CONTEXT = \"auth\";\nexport const AUTH_KEYS_CONTEXT = \"authKeys\";\nexport const AUTH_PAIRING_TOPIC_CONTEXT = \"pairingTopics\";\nexport const AUTH_REQUEST_CONTEXT = \"requests\";\n\nexport const AUTH_STORAGE_PREFIX = `${AUTH_PROTOCOL}@${AUTH_VERSION}:${AUTH_CONTEXT}:`;\nexport const AUTH_PUBLIC_KEY_NAME = `${AUTH_STORAGE_PREFIX}:PUB_KEY`;\n", "/* eslint-disable no-console */\nimport {\n  EVENT_CLIENT_AUTHENTICATE_ERRORS,\n  EVENT_CLIENT_AUTHENTICATE_TRACES,\n  EVENT_CLIENT_PAIRING_ERRORS,\n  EVENT_CLIENT_PAIRING_TRACES,\n  EVENT_CLIENT_SESSION_ERRORS,\n  EVENT_CLIENT_SESSION_TRACES,\n  EXPIRER_EVENTS,\n  PAIRING_EVENTS,\n  RELAYER_DEFAULT_PROTOCOL,\n  RELAYER_EVENTS,\n  TRANSPORT_TYPES,\n  VERIFY_SERVER,\n} from \"@walletconnect/core\";\n\nimport {\n  formatJsonRpcError,\n  formatJsonRpcRequest,\n  formatJsonRpcResult,\n  payloadId,\n  isJsonRpcError,\n  isJsonRpcRequest,\n  isJsonRpcResponse,\n  isJsonRpcResult,\n  JsonRpcRequest,\n  ErrorResponse,\n  getBigIntRpcId,\n} from \"@walletconnect/jsonrpc-utils\";\nimport { FIVE_MINUTES, ONE_SECOND, toMiliseconds } from \"@walletconnect/time\";\nimport {\n  EnginePrivate,\n  EngineTypes,\n  ExpirerTypes,\n  IEngine,\n  IEngineEvents,\n  JsonRpcTypes,\n  PendingRequestTypes,\n  Verify,\n  CoreTypes,\n  ProposalTypes,\n  RelayerTypes,\n  SessionTypes,\n  PairingTypes,\n  AuthTypes,\n  EventClientTypes,\n} from \"@walletconnect/types\";\nimport {\n  calcExpiry,\n  createDelayedPromise,\n  engineEvent,\n  getInternalError,\n  getSdkError,\n  isConformingNamespaces,\n  isExpired,\n  isSessionCompatible,\n  isUndefined,\n  isValidController,\n  isValidErrorReason,\n  isValidEvent,\n  isValidId,\n  isValidNamespaces,\n  isValidNamespacesChainId,\n  isValidNamespacesEvent,\n  isValidNamespacesRequest,\n  isValidObject,\n  isValidParams,\n  isValidRelay,\n  isValidRelays,\n  isValidRequest,\n  isValidRequestExpiry,\n  hashMessage,\n  isValidRequiredNamespaces,\n  isValidResponse,\n  isValidString,\n  parseExpirerTarget,\n  TYPE_1,\n  TYPE_2,\n  handleDeeplinkRedirect,\n  MemoryStore,\n  getDeepLink,\n  hashKey,\n  getDidAddress,\n  formatMessage,\n  getMethodsFromRecap,\n  buildNamespacesFromAuth,\n  createEncodedRecap,\n  getChainsFromRecap,\n  mergeEncodedRecaps,\n  getRecapFromResources,\n  validateSignedCacao,\n  getNamespacedDidChainId,\n  parseChainId,\n  getLinkModeURL,\n  BASE64,\n  BASE64URL,\n  getSearchParamFromURL,\n  isReactNative,\n  isTestRun,\n  isValidArray,\n  extractSolanaTransactionId,\n  mergeRequiredAndOptionalNamespaces,\n} from \"@walletconnect/utils\";\nimport EventEmmiter from \"events\";\nimport {\n  ENGINE_CONTEXT,\n  ENGINE_RPC_OPTS,\n  PROPOSAL_EXPIRY_MESSAGE,\n  SESSION_EXPIRY,\n  SESSION_REQUEST_EXPIRY_BOUNDARIES,\n  METHODS_TO_VERIFY,\n  WALLETCONNECT_DEEPLINK_CHOICE,\n  ENGINE_QUEUE_STATES,\n  AUTH_PUBLIC_KEY_NAME,\n  TVF_METHODS,\n} from \"../constants\";\n\nexport class Engine extends IEngine {\n  public name = ENGINE_CONTEXT;\n\n  private events: IEngineEvents = new EventEmmiter();\n  private initialized = false;\n\n  /**\n   * Queue responsible for processing incoming requests such as session_update, session_event, session_ping etc\n   * It's needed when the client receives multiple requests at once from the mailbox immediately after initialization and to avoid attempting to process them all at the same time\n   */\n  private requestQueue: EngineTypes.EngineQueue<EngineTypes.EventCallback<JsonRpcRequest>> = {\n    state: ENGINE_QUEUE_STATES.idle,\n    queue: [],\n  };\n\n  /**\n   * Queue responsible for processing incoming session_request\n   * The queue emits the next request only after the previous one has been responded to\n   */\n  private sessionRequestQueue: EngineTypes.EngineQueue<PendingRequestTypes.Struct> = {\n    state: ENGINE_QUEUE_STATES.idle,\n    queue: [],\n  };\n\n  private requestQueueDelay = ONE_SECOND;\n  private expectedPairingMethodMap: Map<string, string[]> = new Map();\n  // Ephemeral (in-memory) map to store recently deleted items\n  private recentlyDeletedMap = new Map<\n    string | number,\n    \"pairing\" | \"session\" | \"proposal\" | \"request\"\n  >();\n\n  private recentlyDeletedLimit = 200;\n  private relayMessageCache: RelayerTypes.MessageEvent[] = [];\n  private pendingSessions: Map<\n    number,\n    {\n      sessionTopic: string;\n      pairingTopic: string;\n      proposalId: number;\n      publicKey: string;\n    }\n  > = new Map();\n\n  constructor(client: IEngine[\"client\"]) {\n    super(client);\n  }\n\n  public init: IEngine[\"init\"] = async () => {\n    if (!this.initialized) {\n      await this.cleanup();\n      this.registerRelayerEvents();\n      this.registerExpirerEvents();\n      this.registerPairingEvents();\n      await this.registerLinkModeListeners();\n      this.client.core.pairing.register({ methods: Object.keys(ENGINE_RPC_OPTS) });\n      this.initialized = true;\n      setTimeout(async () => {\n        await this.processPendingMessageEvents();\n\n        this.sessionRequestQueue.queue = this.getPendingSessionRequests();\n        this.processSessionRequestQueue();\n      }, toMiliseconds(this.requestQueueDelay));\n    }\n  };\n\n  private async processPendingMessageEvents() {\n    try {\n      const topics = this.client.session.keys;\n      const pendingMessages = this.client.core.relayer.messages.getWithoutAck(topics);\n      for (const [topic, messages] of Object.entries(pendingMessages)) {\n        for (const message of messages) {\n          try {\n            await this.onProviderMessageEvent({\n              topic,\n              message,\n              publishedAt: Date.now(),\n            });\n          } catch (error) {\n            this.client.logger.warn(\n              `Error processing pending message event for topic: ${topic}, message: ${message}`,\n            );\n          }\n        }\n      }\n    } catch (error) {\n      this.client.logger.warn(\"processPendingMessageEvents failed\", error);\n    }\n  }\n\n  // ---------- Public ------------------------------------------------ //\n\n  public connect: IEngine[\"connect\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    const connectParams = {\n      ...params,\n      requiredNamespaces: params.requiredNamespaces || {},\n      optionalNamespaces: params.optionalNamespaces || {},\n    };\n    await this.isValidConnect(connectParams);\n\n    // requiredNamespaces are deprecated, assign them to optionalNamespaces\n    connectParams.optionalNamespaces = mergeRequiredAndOptionalNamespaces(\n      connectParams.requiredNamespaces,\n      connectParams.optionalNamespaces,\n    );\n\n    connectParams.requiredNamespaces = {};\n\n    const {\n      pairingTopic,\n      requiredNamespaces,\n      optionalNamespaces,\n      sessionProperties,\n      scopedProperties,\n      relays,\n    } = connectParams;\n    let topic = pairingTopic;\n    let uri: string | undefined;\n    let active = false;\n    try {\n      if (topic) {\n        const pairing = this.client.core.pairing.pairings.get(topic);\n        this.client.logger.warn(\n          \"connect() with existing pairing topic is deprecated and will be removed in the next major release.\",\n        );\n        active = pairing.active;\n      }\n    } catch (error) {\n      this.client.logger.error(`connect() -> pairing.get(${topic}) failed`);\n      throw error;\n    }\n    if (!topic || !active) {\n      const { topic: newTopic, uri: newUri } = await this.client.core.pairing.create();\n      topic = newTopic;\n      uri = newUri;\n    }\n    // safety check to ensure pairing topic is available\n    if (!topic) {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `connect() pairing topic: ${topic}`);\n      throw new Error(message);\n    }\n\n    const publicKey = await this.client.core.crypto.generateKeyPair();\n\n    const expiry = ENGINE_RPC_OPTS.wc_sessionPropose.req.ttl || FIVE_MINUTES;\n    const expiryTimestamp = calcExpiry(expiry);\n    const proposal = {\n      requiredNamespaces,\n      optionalNamespaces,\n      relays: relays ?? [{ protocol: RELAYER_DEFAULT_PROTOCOL }],\n      proposer: {\n        publicKey,\n        metadata: this.client.metadata,\n      },\n      expiryTimestamp,\n      pairingTopic: topic,\n      ...(sessionProperties && { sessionProperties }),\n      ...(scopedProperties && { scopedProperties }),\n      id: payloadId(),\n    };\n    const sessionConnectTarget = engineEvent(\"session_connect\", proposal.id);\n\n    const {\n      reject,\n      resolve,\n      done: approval,\n    } = createDelayedPromise<SessionTypes.Struct>(expiry, PROPOSAL_EXPIRY_MESSAGE);\n\n    const proposalExpireHandler = ({ id }: { id: number }) => {\n      if (id === proposal.id) {\n        this.client.events.off(\"proposal_expire\", proposalExpireHandler);\n        this.pendingSessions.delete(proposal.id);\n        // emit the event to trigger reject, this approach automatically cleans up the .once listener below\n        this.events.emit(sessionConnectTarget, {\n          error: { message: PROPOSAL_EXPIRY_MESSAGE, code: 0 },\n        });\n      }\n    };\n\n    this.client.events.on(\"proposal_expire\", proposalExpireHandler);\n    this.events.once<\"session_connect\">(sessionConnectTarget, ({ error, session }) => {\n      this.client.events.off(\"proposal_expire\", proposalExpireHandler);\n      if (error) reject(error);\n      else if (session) {\n        resolve(session);\n      }\n    });\n\n    await this.sendRequest({\n      topic,\n      method: \"wc_sessionPropose\",\n      params: proposal,\n      throwOnFailedPublish: true,\n      clientRpcId: proposal.id,\n    });\n\n    await this.setProposal(proposal.id, proposal);\n    return { uri, approval };\n  };\n\n  public pair: IEngine[\"pair\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    try {\n      return await this.client.core.pairing.pair(params);\n    } catch (error) {\n      this.client.logger.error(\"pair() failed\");\n      throw error;\n    }\n  };\n\n  public approve: IEngine[\"approve\"] = async (params) => {\n    const configEvent = this.client.core.eventClient.createEvent({\n      properties: {\n        topic: params?.id?.toString(),\n        trace: [EVENT_CLIENT_SESSION_TRACES.session_approve_started],\n      },\n    });\n    try {\n      this.isInitialized();\n      await this.confirmOnlineStateOrThrow();\n    } catch (error) {\n      configEvent.setError(EVENT_CLIENT_SESSION_ERRORS.no_internet_connection);\n      throw error;\n    }\n    try {\n      await this.isValidProposalId(params?.id);\n    } catch (error) {\n      this.client.logger.error(`approve() -> proposal.get(${params?.id}) failed`);\n      configEvent.setError(EVENT_CLIENT_SESSION_ERRORS.proposal_not_found);\n      throw error;\n    }\n\n    try {\n      await this.isValidApprove(params);\n    } catch (error) {\n      this.client.logger.error(\"approve() -> isValidApprove() failed\");\n      configEvent.setError(\n        EVENT_CLIENT_SESSION_ERRORS.session_approve_namespace_validation_failure,\n      );\n      throw error;\n    }\n\n    const { id, relayProtocol, namespaces, sessionProperties, scopedProperties, sessionConfig } =\n      params;\n\n    const proposal = this.client.proposal.get(id);\n\n    this.client.core.eventClient.deleteEvent({ eventId: configEvent.eventId });\n\n    const { pairingTopic, proposer, requiredNamespaces, optionalNamespaces } = proposal;\n\n    let event = this.client.core.eventClient?.getEvent({\n      topic: pairingTopic,\n    }) as EventClientTypes.Event;\n    if (!event) {\n      event = this.client.core.eventClient?.createEvent({\n        type: EVENT_CLIENT_SESSION_TRACES.session_approve_started,\n        properties: {\n          topic: pairingTopic,\n          trace: [\n            EVENT_CLIENT_SESSION_TRACES.session_approve_started,\n            EVENT_CLIENT_SESSION_TRACES.session_namespaces_validation_success,\n          ],\n        },\n      });\n    }\n\n    const selfPublicKey = await this.client.core.crypto.generateKeyPair();\n    const peerPublicKey = proposer.publicKey;\n    const sessionTopic = await this.client.core.crypto.generateSharedKey(\n      selfPublicKey,\n      peerPublicKey,\n    );\n    const sessionSettle = {\n      relay: { protocol: relayProtocol ?? \"irn\" },\n      namespaces,\n      controller: { publicKey: selfPublicKey, metadata: this.client.metadata },\n      expiry: calcExpiry(SESSION_EXPIRY),\n      ...(sessionProperties && { sessionProperties }),\n      ...(scopedProperties && { scopedProperties }),\n      ...(sessionConfig && { sessionConfig }),\n    };\n    const transportType = TRANSPORT_TYPES.relay;\n    event.addTrace(EVENT_CLIENT_SESSION_TRACES.subscribing_session_topic);\n    try {\n      await this.client.core.relayer.subscribe(sessionTopic, { transportType });\n    } catch (error) {\n      event.setError(EVENT_CLIENT_SESSION_ERRORS.subscribe_session_topic_failure);\n      throw error;\n    }\n\n    event.addTrace(EVENT_CLIENT_SESSION_TRACES.subscribe_session_topic_success);\n\n    const session = {\n      ...sessionSettle,\n      topic: sessionTopic,\n      requiredNamespaces,\n      optionalNamespaces,\n      pairingTopic,\n      acknowledged: false,\n      self: sessionSettle.controller,\n      peer: {\n        publicKey: proposer.publicKey,\n        metadata: proposer.metadata,\n      },\n      controller: selfPublicKey,\n      transportType: TRANSPORT_TYPES.relay,\n    };\n    await this.client.session.set(sessionTopic, session);\n\n    event.addTrace(EVENT_CLIENT_SESSION_TRACES.store_session);\n\n    try {\n      event.addTrace(EVENT_CLIENT_SESSION_TRACES.publishing_session_settle);\n      await this.sendRequest({\n        topic: sessionTopic,\n        method: \"wc_sessionSettle\",\n        params: sessionSettle,\n        throwOnFailedPublish: true,\n      }).catch((error) => {\n        event?.setError(EVENT_CLIENT_SESSION_ERRORS.session_settle_publish_failure);\n        throw error;\n      });\n\n      event.addTrace(EVENT_CLIENT_SESSION_TRACES.session_settle_publish_success);\n\n      event.addTrace(EVENT_CLIENT_SESSION_TRACES.publishing_session_approve);\n      await this.sendResult<\"wc_sessionPropose\">({\n        id,\n        topic: pairingTopic,\n        result: {\n          relay: {\n            protocol: relayProtocol ?? \"irn\",\n          },\n          responderPublicKey: selfPublicKey,\n        },\n        throwOnFailedPublish: true,\n      }).catch((error) => {\n        event?.setError(EVENT_CLIENT_SESSION_ERRORS.session_approve_publish_failure);\n        throw error;\n      });\n\n      event.addTrace(EVENT_CLIENT_SESSION_TRACES.session_approve_publish_success);\n    } catch (error) {\n      this.client.logger.error(error);\n      // if the publish fails, delete the session and throw an error\n      this.client.session.delete(sessionTopic, getSdkError(\"USER_DISCONNECTED\"));\n      await this.client.core.relayer.unsubscribe(sessionTopic);\n      throw error;\n    }\n\n    this.client.core.eventClient.deleteEvent({ eventId: event.eventId });\n\n    await this.client.core.pairing.updateMetadata({\n      topic: pairingTopic,\n      metadata: proposer.metadata,\n    });\n    await this.client.proposal.delete(id, getSdkError(\"USER_DISCONNECTED\"));\n    await this.client.core.pairing.activate({ topic: pairingTopic });\n    await this.setExpiry(sessionTopic, calcExpiry(SESSION_EXPIRY));\n    return {\n      topic: sessionTopic,\n      acknowledged: () => Promise.resolve(this.client.session.get(sessionTopic)),\n    };\n  };\n\n  public reject: IEngine[\"reject\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    try {\n      await this.isValidReject(params);\n    } catch (error) {\n      this.client.logger.error(\"reject() -> isValidReject() failed\");\n      throw error;\n    }\n    const { id, reason } = params;\n    let pairingTopic;\n    try {\n      const proposal = this.client.proposal.get(id);\n      pairingTopic = proposal.pairingTopic;\n    } catch (error) {\n      this.client.logger.error(`reject() -> proposal.get(${id}) failed`);\n      throw error;\n    }\n\n    if (pairingTopic) {\n      await this.sendError({\n        id,\n        topic: pairingTopic,\n        error: reason,\n        rpcOpts: ENGINE_RPC_OPTS.wc_sessionPropose.reject,\n      });\n      await this.client.proposal.delete(id, getSdkError(\"USER_DISCONNECTED\"));\n    }\n  };\n\n  public update: IEngine[\"update\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    try {\n      await this.isValidUpdate(params);\n    } catch (error) {\n      this.client.logger.error(\"update() -> isValidUpdate() failed\");\n      throw error;\n    }\n    const { topic, namespaces } = params;\n\n    const { done: acknowledged, resolve, reject } = createDelayedPromise<void>();\n    const clientRpcId = payloadId();\n    const relayRpcId = getBigIntRpcId().toString() as any;\n\n    const oldNamespaces = this.client.session.get(topic).namespaces;\n    this.events.once(engineEvent(\"session_update\", clientRpcId), ({ error }: any) => {\n      if (error) reject(error);\n      else {\n        resolve();\n      }\n    });\n    // Update the session with the new namespaces, if the publish fails, revert to the old.\n    // This allows the client to use the updated session like emitting events\n    // without waiting for the peer to acknowledge\n    await this.client.session.update(topic, { namespaces });\n    await this.sendRequest({\n      topic,\n      method: \"wc_sessionUpdate\",\n      params: { namespaces },\n      throwOnFailedPublish: true,\n      clientRpcId,\n      relayRpcId,\n    }).catch((error) => {\n      this.client.logger.error(error);\n      this.client.session.update(topic, { namespaces: oldNamespaces });\n      reject(error);\n    });\n    return { acknowledged };\n  };\n\n  public extend: IEngine[\"extend\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    try {\n      await this.isValidExtend(params);\n    } catch (error) {\n      this.client.logger.error(\"extend() -> isValidExtend() failed\");\n      throw error;\n    }\n\n    const { topic } = params;\n    const clientRpcId = payloadId();\n    const { done: acknowledged, resolve, reject } = createDelayedPromise<void>();\n    this.events.once(engineEvent(\"session_extend\", clientRpcId), ({ error }: any) => {\n      if (error) reject(error);\n      else resolve();\n    });\n\n    await this.setExpiry(topic, calcExpiry(SESSION_EXPIRY));\n    this.sendRequest({\n      topic,\n      method: \"wc_sessionExtend\",\n      params: {},\n      clientRpcId,\n      throwOnFailedPublish: true,\n    }).catch((e) => {\n      reject(e);\n    });\n\n    return { acknowledged };\n  };\n\n  public request: IEngine[\"request\"] = async <T>(params: EngineTypes.RequestParams) => {\n    this.isInitialized();\n    try {\n      await this.isValidRequest(params);\n    } catch (error) {\n      this.client.logger.error(\"request() -> isValidRequest() failed\");\n      throw error;\n    }\n    const { chainId, request, topic, expiry = ENGINE_RPC_OPTS.wc_sessionRequest.req.ttl } = params;\n    const session = this.client.session.get(topic);\n\n    if (session?.transportType === TRANSPORT_TYPES.relay) {\n      await this.confirmOnlineStateOrThrow();\n    }\n    const clientRpcId = payloadId();\n    const relayRpcId = getBigIntRpcId().toString() as any;\n    const { done, resolve, reject } = createDelayedPromise<T>(\n      expiry,\n      \"Request expired. Please try again.\",\n    );\n    this.events.once<\"session_request\">(\n      engineEvent(\"session_request\", clientRpcId),\n      ({ error, result }) => {\n        if (error) reject(error);\n        else resolve(result);\n      },\n    );\n    const protocolMethod = \"wc_sessionRequest\";\n    const appLink = this.getAppLinkIfEnabled(session.peer.metadata, session.transportType);\n    if (appLink) {\n      await this.sendRequest({\n        clientRpcId,\n        relayRpcId,\n        topic,\n        method: protocolMethod,\n        params: {\n          request: {\n            ...request,\n            expiryTimestamp: calcExpiry(expiry),\n          },\n          chainId,\n        },\n        expiry,\n        throwOnFailedPublish: true,\n        appLink,\n      }).catch((error) => reject(error));\n\n      this.client.events.emit(\"session_request_sent\", {\n        topic,\n        request,\n        chainId,\n        id: clientRpcId,\n      });\n      const result = await done();\n      return result;\n    }\n\n    const protocolRequestParams: JsonRpcTypes.RequestParams[\"wc_sessionRequest\"] = {\n      request: {\n        ...request,\n        expiryTimestamp: calcExpiry(expiry),\n      },\n      chainId,\n    };\n    const shouldSetTVF = this.shouldSetTVF(protocolMethod, protocolRequestParams);\n\n    return await Promise.all([\n      new Promise<void>(async (resolve) => {\n        await this.sendRequest({\n          clientRpcId,\n          relayRpcId,\n          topic,\n          method: protocolMethod,\n          params: protocolRequestParams,\n          expiry,\n          throwOnFailedPublish: true,\n          ...(shouldSetTVF && {\n            tvf: this.getTVFParams(clientRpcId, protocolRequestParams),\n          }),\n        }).catch((error) => reject(error));\n        this.client.events.emit(\"session_request_sent\", {\n          topic,\n          request,\n          chainId,\n          id: clientRpcId,\n        });\n        resolve();\n      }),\n      new Promise<void>(async (resolve) => {\n        // only attempt to handle deeplinks if they are not explicitly disabled in the session config\n        if (!session.sessionConfig?.disableDeepLink) {\n          const wcDeepLink = (await getDeepLink(\n            this.client.core.storage,\n            WALLETCONNECT_DEEPLINK_CHOICE,\n          )) as string;\n          await handleDeeplinkRedirect({ id: clientRpcId, topic, wcDeepLink });\n        }\n        resolve();\n      }),\n      done(),\n    ]).then((result) => result[2]); // order is important here, we want to return the result of the `done` promise\n  };\n\n  public respond: IEngine[\"respond\"] = async (params) => {\n    this.isInitialized();\n    await this.isValidRespond(params);\n    const { topic, response } = params;\n    const { id } = response;\n    const session = this.client.session.get(topic);\n\n    if (session.transportType === TRANSPORT_TYPES.relay) {\n      await this.confirmOnlineStateOrThrow();\n    }\n\n    const appLink = this.getAppLinkIfEnabled(session.peer.metadata, session.transportType);\n    if (isJsonRpcResult(response)) {\n      await this.sendResult({\n        id,\n        topic,\n        result: response.result,\n        throwOnFailedPublish: true,\n        appLink,\n      });\n    } else if (isJsonRpcError(response)) {\n      await this.sendError({ id, topic, error: response.error, appLink });\n    }\n    this.cleanupAfterResponse(params);\n  };\n\n  public ping: IEngine[\"ping\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    try {\n      await this.isValidPing(params);\n    } catch (error) {\n      this.client.logger.error(\"ping() -> isValidPing() failed\");\n      throw error;\n    }\n    const { topic } = params;\n    if (this.client.session.keys.includes(topic)) {\n      const clientRpcId = payloadId();\n      const relayRpcId = getBigIntRpcId().toString() as any;\n      const { done, resolve, reject } = createDelayedPromise<void>();\n      this.events.once(engineEvent(\"session_ping\", clientRpcId), ({ error }: any) => {\n        if (error) reject(error);\n        else resolve();\n      });\n      await Promise.all([\n        this.sendRequest({\n          topic,\n          method: \"wc_sessionPing\",\n          params: {},\n          throwOnFailedPublish: true,\n          clientRpcId,\n          relayRpcId,\n        }),\n        done(),\n      ]);\n    } else if (this.client.core.pairing.pairings.keys.includes(topic)) {\n      this.client.logger.warn(\n        \"ping() on pairing topic is deprecated and will be removed in the next major release.\",\n      );\n      await this.client.core.pairing.ping({ topic });\n    }\n  };\n\n  public emit: IEngine[\"emit\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    await this.isValidEmit(params);\n    const { topic, event, chainId } = params;\n    const relayRpcId = getBigIntRpcId().toString() as any;\n    const clientRpcId = payloadId();\n    await this.sendRequest({\n      topic,\n      method: \"wc_sessionEvent\",\n      params: { event, chainId },\n      throwOnFailedPublish: true,\n      relayRpcId,\n      clientRpcId,\n    });\n  };\n\n  public disconnect: IEngine[\"disconnect\"] = async (params) => {\n    this.isInitialized();\n    await this.confirmOnlineStateOrThrow();\n    await this.isValidDisconnect(params);\n    const { topic } = params;\n    if (this.client.session.keys.includes(topic)) {\n      // await an ack to ensure the relay has received the disconnect request\n      await this.sendRequest({\n        topic,\n        method: \"wc_sessionDelete\",\n        params: getSdkError(\"USER_DISCONNECTED\"),\n        throwOnFailedPublish: true,\n      });\n      await this.deleteSession({ topic, emitEvent: false });\n    } else if (this.client.core.pairing.pairings.keys.includes(topic)) {\n      await this.client.core.pairing.disconnect({ topic });\n    } else {\n      const { message } = getInternalError(\n        \"MISMATCHED_TOPIC\",\n        `Session or pairing topic not found: ${topic}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  public find: IEngine[\"find\"] = (params) => {\n    this.isInitialized();\n    return this.client.session.getAll().filter((session) => isSessionCompatible(session, params));\n  };\n\n  public getPendingSessionRequests: IEngine[\"getPendingSessionRequests\"] = () => {\n    return this.client.pendingRequest.getAll();\n  };\n\n  // ---------- Auth ------------------------------------------------ //\n\n  public authenticate: IEngine[\"authenticate\"] = async (params, walletUniversalLink) => {\n    this.isInitialized();\n    this.isValidAuthenticate(params);\n\n    const isLinkMode =\n      walletUniversalLink &&\n      this.client.core.linkModeSupportedApps.includes(walletUniversalLink) &&\n      this.client.metadata.redirect?.linkMode;\n\n    const transportType: RelayerTypes.TransportType = isLinkMode\n      ? TRANSPORT_TYPES.link_mode\n      : TRANSPORT_TYPES.relay;\n\n    if (transportType === TRANSPORT_TYPES.relay) {\n      await this.confirmOnlineStateOrThrow();\n    }\n\n    const {\n      chains,\n      statement = \"\",\n      uri,\n      domain,\n      nonce,\n      type,\n      exp,\n      nbf,\n      methods = [],\n      expiry,\n    } = params;\n    // reassign resources to remove reference as the array is modified and might cause side effects\n    const resources = [...(params.resources || [])];\n\n    const { topic: pairingTopic, uri: connectionUri } = await this.client.core.pairing.create({\n      methods: [\"wc_sessionAuthenticate\"],\n      transportType,\n    });\n\n    this.client.logger.info({\n      message: \"Generated new pairing\",\n      pairing: { topic: pairingTopic, uri: connectionUri },\n    });\n\n    const publicKey = await this.client.core.crypto.generateKeyPair();\n    const responseTopic = hashKey(publicKey);\n\n    await Promise.all([\n      this.client.auth.authKeys.set(AUTH_PUBLIC_KEY_NAME, { responseTopic, publicKey }),\n      this.client.auth.pairingTopics.set(responseTopic, { topic: responseTopic, pairingTopic }),\n    ]);\n\n    // Subscribe to response topic\n    await this.client.core.relayer.subscribe(responseTopic, { transportType });\n\n    this.client.logger.info(`sending request to new pairing topic: ${pairingTopic}`);\n\n    if (methods.length > 0) {\n      const { namespace } = parseChainId(chains[0]);\n      let recap = createEncodedRecap(namespace, \"request\", methods);\n      const existingRecap = getRecapFromResources(resources);\n      if (existingRecap) {\n        // per Recaps spec, recap must occupy the last position in the resources array\n        // using .pop to remove the element given we already checked it's a recap and will replace it\n        const mergedRecap = mergeEncodedRecaps(recap, resources.pop() as string);\n        recap = mergedRecap;\n      }\n      resources.push(recap);\n    }\n\n    // Ensure the expiry is greater than the minimum required for the request - currently 1h\n    const authRequestExpiry =\n      expiry && expiry > ENGINE_RPC_OPTS.wc_sessionAuthenticate.req.ttl\n        ? expiry\n        : ENGINE_RPC_OPTS.wc_sessionAuthenticate.req.ttl;\n\n    const request: AuthTypes.SessionAuthenticateRequestParams = {\n      authPayload: {\n        type: type ?? \"caip122\",\n        chains,\n        statement,\n        aud: uri,\n        domain,\n        version: \"1\",\n        nonce,\n        iat: new Date().toISOString(),\n        exp,\n        nbf,\n        resources,\n      },\n      requester: { publicKey, metadata: this.client.metadata },\n      expiryTimestamp: calcExpiry(authRequestExpiry),\n    };\n\n    // ----- build namespaces for fallback session proposal ----- //\n    const namespaces = {\n      eip155: {\n        chains,\n        // request `personal_sign` method by default to allow for fallback siwe\n        methods: [...new Set([\"personal_sign\", ...methods])],\n        events: [\"chainChanged\", \"accountsChanged\"],\n      },\n    };\n\n    const proposal = {\n      requiredNamespaces: {},\n      optionalNamespaces: namespaces,\n      relays: [{ protocol: \"irn\" }],\n      pairingTopic,\n      proposer: {\n        publicKey,\n        metadata: this.client.metadata,\n      },\n      expiryTimestamp: calcExpiry(ENGINE_RPC_OPTS.wc_sessionPropose.req.ttl),\n      id: payloadId(),\n    };\n\n    const { done, resolve, reject } = createDelayedPromise(authRequestExpiry, \"Request expired\");\n\n    const authenticateId = payloadId();\n    const sessionConnectEventTarget = engineEvent(\"session_connect\", proposal.id);\n    const authenticateEventTarget = engineEvent(\"session_request\", authenticateId);\n\n    // handle fallback session proposal response\n    const onSessionConnect = async ({ error, session }: any) => {\n      // cleanup listener for authenticate response\n      this.events.off(authenticateEventTarget, onAuthenticate);\n      if (error) reject(error);\n      else if (session) {\n        resolve({\n          session,\n        });\n      }\n    };\n    // handle session authenticate response\n    const onAuthenticate = async (payload: any) => {\n      // delete this auth request on response\n      // we're using payload from the wallet to establish the session so we don't need to keep this around\n      await this.deletePendingAuthRequest(authenticateId, { message: \"fulfilled\", code: 0 });\n      if (payload.error) {\n        // wallets that do not support wc_sessionAuthenticate will return an error\n        // we should not reject the promise in this case as the fallback session proposal will be used\n        const error = getSdkError(\"WC_METHOD_UNSUPPORTED\", \"wc_sessionAuthenticate\");\n        if (payload.error.code === error.code) return;\n\n        // cleanup listener for fallback response\n        this.events.off(sessionConnectEventTarget, onSessionConnect);\n        return reject(payload.error.message);\n      }\n      // delete fallback proposal on successful authenticate as the proposal will not be responded to\n      await this.deleteProposal(proposal.id);\n      // cleanup listener for fallback response\n      this.events.off(sessionConnectEventTarget, onSessionConnect);\n\n      const {\n        cacaos,\n        responder,\n      }: {\n        cacaos: AuthTypes.SessionAuthenticateResponseParams[\"cacaos\"];\n        responder: AuthTypes.SessionAuthenticateResponseParams[\"responder\"];\n      } = payload.result;\n\n      const approvedMethods: string[] = [];\n      const approvedAccounts: string[] = [];\n      for (const cacao of cacaos) {\n        const isValid = await validateSignedCacao({ cacao, projectId: this.client.core.projectId });\n        if (!isValid) {\n          this.client.logger.error(cacao, \"Signature verification failed\");\n          reject(getSdkError(\"SESSION_SETTLEMENT_FAILED\", \"Signature verification failed\"));\n        }\n\n        const { p: payload } = cacao;\n        const recap = getRecapFromResources(payload.resources);\n\n        const approvedChains: string[] = [getNamespacedDidChainId(payload.iss) as string];\n        const parsedAddress = getDidAddress(payload.iss) as string;\n\n        if (recap) {\n          const methodsfromRecap = getMethodsFromRecap(recap);\n          const chainsFromRecap = getChainsFromRecap(recap);\n          approvedMethods.push(...methodsfromRecap);\n          approvedChains.push(...chainsFromRecap);\n        }\n\n        for (const chain of approvedChains) {\n          approvedAccounts.push(`${chain}:${parsedAddress}`);\n        }\n      }\n      const sessionTopic = await this.client.core.crypto.generateSharedKey(\n        publicKey,\n        responder.publicKey,\n      );\n\n      //create session object\n      let session: SessionTypes.Struct | undefined;\n\n      if (approvedMethods.length > 0) {\n        session = {\n          topic: sessionTopic,\n          acknowledged: true,\n          self: {\n            publicKey,\n            metadata: this.client.metadata,\n          },\n          peer: responder,\n          controller: responder.publicKey,\n          expiry: calcExpiry(SESSION_EXPIRY),\n          requiredNamespaces: {},\n          optionalNamespaces: {},\n          relay: { protocol: \"irn\" },\n          pairingTopic,\n          namespaces: buildNamespacesFromAuth(\n            [...new Set(approvedMethods)],\n            [...new Set(approvedAccounts)],\n          ),\n          transportType,\n        };\n\n        await this.client.core.relayer.subscribe(sessionTopic, { transportType });\n        await this.client.session.set(sessionTopic, session);\n        if (pairingTopic) {\n          await this.client.core.pairing.updateMetadata({\n            topic: pairingTopic,\n            metadata: responder.metadata,\n          });\n        }\n\n        session = this.client.session.get(sessionTopic);\n      }\n\n      if (\n        this.client.metadata.redirect?.linkMode &&\n        responder.metadata.redirect?.linkMode &&\n        responder.metadata.redirect?.universal &&\n        walletUniversalLink\n      ) {\n        // save wallet link in array of apps that support linkMode\n        this.client.core.addLinkModeSupportedApp(responder.metadata.redirect.universal);\n\n        this.client.session.update(sessionTopic, {\n          transportType: TRANSPORT_TYPES.link_mode,\n        });\n      }\n\n      resolve({\n        auths: cacaos,\n        session,\n      });\n    };\n\n    // subscribe to response events\n    this.events.once<\"session_connect\">(sessionConnectEventTarget, onSessionConnect);\n    this.events.once(authenticateEventTarget, onAuthenticate);\n\n    let linkModeURL;\n    try {\n      if (isLinkMode) {\n        const payload = formatJsonRpcRequest(\"wc_sessionAuthenticate\", request, authenticateId);\n        this.client.core.history.set(pairingTopic, payload);\n        const message = await this.client.core.crypto.encode(\"\", payload, {\n          type: TYPE_2,\n          encoding: BASE64URL,\n        });\n        linkModeURL = getLinkModeURL(walletUniversalLink, pairingTopic, message);\n      } else {\n        // send both (main & fallback) requests\n        await Promise.all([\n          this.sendRequest({\n            topic: pairingTopic,\n            method: \"wc_sessionAuthenticate\",\n            params: request,\n            expiry: params.expiry,\n            throwOnFailedPublish: true,\n            clientRpcId: authenticateId,\n          }),\n          this.sendRequest({\n            topic: pairingTopic,\n            method: \"wc_sessionPropose\",\n            params: proposal,\n            expiry: ENGINE_RPC_OPTS.wc_sessionPropose.req.ttl,\n            throwOnFailedPublish: true,\n            clientRpcId: proposal.id,\n          }),\n        ]);\n      }\n    } catch (error) {\n      // cleanup listeners on failed publish\n      this.events.off(sessionConnectEventTarget, onSessionConnect);\n      this.events.off(authenticateEventTarget, onAuthenticate);\n      throw error;\n    }\n\n    await this.setProposal(proposal.id, proposal);\n    await this.setAuthRequest(authenticateId, {\n      request: {\n        ...request,\n        verifyContext: {} as any,\n      },\n      pairingTopic,\n      transportType,\n    });\n\n    return {\n      uri: linkModeURL ?? connectionUri,\n      response: done,\n    } as EngineTypes.SessionAuthenticateResponsePromise;\n  };\n\n  public approveSessionAuthenticate: IEngine[\"approveSessionAuthenticate\"] = async (\n    sessionAuthenticateResponseParams,\n  ) => {\n    const { id, auths } = sessionAuthenticateResponseParams;\n\n    const event = this.client.core.eventClient.createEvent({\n      properties: {\n        topic: id.toString(),\n        trace: [EVENT_CLIENT_AUTHENTICATE_TRACES.authenticated_session_approve_started],\n      },\n    });\n\n    try {\n      this.isInitialized();\n    } catch (error) {\n      event.setError(EVENT_CLIENT_AUTHENTICATE_ERRORS.no_internet_connection);\n      throw error;\n    }\n\n    const pendingRequest = this.getPendingAuthRequest(id);\n\n    if (!pendingRequest) {\n      event.setError(\n        EVENT_CLIENT_AUTHENTICATE_ERRORS.authenticated_session_pending_request_not_found,\n      );\n      throw new Error(`Could not find pending auth request with id ${id}`);\n    }\n\n    const transportType = pendingRequest.transportType || TRANSPORT_TYPES.relay;\n    if (transportType === TRANSPORT_TYPES.relay) {\n      await this.confirmOnlineStateOrThrow();\n    }\n\n    const receiverPublicKey = pendingRequest.requester.publicKey;\n    const senderPublicKey = await this.client.core.crypto.generateKeyPair();\n    const responseTopic = hashKey(receiverPublicKey);\n\n    const encodeOpts = {\n      type: TYPE_1,\n      receiverPublicKey,\n      senderPublicKey,\n    };\n\n    const approvedMethods: string[] = [];\n    const approvedAccounts: string[] = [];\n    for (const cacao of auths) {\n      const isValid = await validateSignedCacao({ cacao, projectId: this.client.core.projectId });\n      if (!isValid) {\n        event.setError(EVENT_CLIENT_AUTHENTICATE_ERRORS.invalid_cacao);\n\n        const invalidErr = getSdkError(\n          \"SESSION_SETTLEMENT_FAILED\",\n          \"Signature verification failed\",\n        );\n\n        await this.sendError({\n          id,\n          topic: responseTopic,\n          error: invalidErr,\n          encodeOpts,\n        });\n\n        throw new Error(invalidErr.message);\n      }\n\n      event.addTrace(EVENT_CLIENT_AUTHENTICATE_TRACES.cacaos_verified);\n\n      const { p: payload } = cacao;\n      const recap = getRecapFromResources(payload.resources);\n\n      const approvedChains: string[] = [getNamespacedDidChainId(payload.iss) as string];\n\n      const parsedAddress = getDidAddress(payload.iss) as string;\n\n      if (recap) {\n        const methodsfromRecap = getMethodsFromRecap(recap);\n        const chainsFromRecap = getChainsFromRecap(recap);\n        approvedMethods.push(...methodsfromRecap);\n        approvedChains.push(...chainsFromRecap);\n      }\n      for (const chain of approvedChains) {\n        approvedAccounts.push(`${chain}:${parsedAddress}`);\n      }\n    }\n\n    const sessionTopic = await this.client.core.crypto.generateSharedKey(\n      senderPublicKey,\n      receiverPublicKey,\n    );\n\n    event.addTrace(EVENT_CLIENT_AUTHENTICATE_TRACES.create_authenticated_session_topic);\n\n    let session: SessionTypes.Struct | undefined;\n    if (approvedMethods?.length > 0) {\n      session = {\n        topic: sessionTopic,\n        acknowledged: true,\n        self: {\n          publicKey: senderPublicKey,\n          metadata: this.client.metadata,\n        },\n        peer: {\n          publicKey: receiverPublicKey,\n          metadata: pendingRequest.requester.metadata,\n        },\n        controller: receiverPublicKey,\n        expiry: calcExpiry(SESSION_EXPIRY),\n        authentication: auths,\n        requiredNamespaces: {},\n        optionalNamespaces: {},\n        relay: { protocol: \"irn\" },\n        pairingTopic: pendingRequest.pairingTopic,\n        namespaces: buildNamespacesFromAuth(\n          [...new Set(approvedMethods)],\n          [...new Set(approvedAccounts)],\n        ),\n        transportType,\n      };\n\n      event.addTrace(EVENT_CLIENT_AUTHENTICATE_TRACES.subscribing_authenticated_session_topic);\n\n      try {\n        await this.client.core.relayer.subscribe(sessionTopic, { transportType });\n      } catch (error) {\n        event.setError(\n          EVENT_CLIENT_AUTHENTICATE_ERRORS.subscribe_authenticated_session_topic_failure,\n        );\n        throw error;\n      }\n\n      event.addTrace(\n        EVENT_CLIENT_AUTHENTICATE_TRACES.subscribe_authenticated_session_topic_success,\n      );\n\n      await this.client.session.set(sessionTopic, session);\n\n      event.addTrace(EVENT_CLIENT_AUTHENTICATE_TRACES.store_authenticated_session);\n\n      await this.client.core.pairing.updateMetadata({\n        topic: pendingRequest.pairingTopic,\n        metadata: pendingRequest.requester.metadata,\n      });\n    }\n\n    event.addTrace(EVENT_CLIENT_AUTHENTICATE_TRACES.publishing_authenticated_session_approve);\n\n    try {\n      await this.sendResult<\"wc_sessionAuthenticate\">({\n        topic: responseTopic,\n        id,\n        result: {\n          cacaos: auths,\n          responder: {\n            publicKey: senderPublicKey,\n            metadata: this.client.metadata,\n          },\n        },\n        encodeOpts,\n        throwOnFailedPublish: true,\n        appLink: this.getAppLinkIfEnabled(pendingRequest.requester.metadata, transportType),\n      });\n    } catch (error) {\n      event.setError(\n        EVENT_CLIENT_AUTHENTICATE_ERRORS.authenticated_session_approve_publish_failure,\n      );\n      throw error;\n    }\n\n    await this.client.auth.requests.delete(id, { message: \"fulfilled\", code: 0 });\n    await this.client.core.pairing.activate({ topic: pendingRequest.pairingTopic });\n    this.client.core.eventClient.deleteEvent({ eventId: event.eventId });\n\n    return { session };\n  };\n\n  public rejectSessionAuthenticate: IEngine[\"rejectSessionAuthenticate\"] = async (params) => {\n    this.isInitialized();\n\n    const { id, reason } = params;\n\n    const pendingRequest = this.getPendingAuthRequest(id);\n\n    if (!pendingRequest) {\n      throw new Error(`Could not find pending auth request with id ${id}`);\n    }\n\n    if (pendingRequest.transportType === TRANSPORT_TYPES.relay) {\n      await this.confirmOnlineStateOrThrow();\n    }\n\n    const receiverPublicKey = pendingRequest.requester.publicKey;\n    const senderPublicKey = await this.client.core.crypto.generateKeyPair();\n    const responseTopic = hashKey(receiverPublicKey);\n\n    const encodeOpts = {\n      type: TYPE_1,\n      receiverPublicKey,\n      senderPublicKey,\n    };\n\n    await this.sendError({\n      id,\n      topic: responseTopic,\n      error: reason,\n      encodeOpts,\n      rpcOpts: ENGINE_RPC_OPTS.wc_sessionAuthenticate.reject,\n      appLink: this.getAppLinkIfEnabled(\n        pendingRequest.requester.metadata,\n        pendingRequest.transportType,\n      ),\n    });\n    await this.client.auth.requests.delete(id, { message: \"rejected\", code: 0 });\n    await this.client.proposal.delete(id, getSdkError(\"USER_DISCONNECTED\"));\n  };\n\n  public formatAuthMessage: IEngine[\"formatAuthMessage\"] = (params) => {\n    this.isInitialized();\n    const { request, iss } = params;\n    return formatMessage(request, iss);\n  };\n\n  public processRelayMessageCache: IEngine[\"processRelayMessageCache\"] = () => {\n    // process the relay messages cache in the next tick to allow event listeners to be registered by the implementing app\n    setTimeout(async () => {\n      if (this.relayMessageCache.length === 0) return;\n      while (this.relayMessageCache.length > 0) {\n        try {\n          const message = this.relayMessageCache.shift();\n          if (message) {\n            await this.onRelayMessage(message);\n          }\n        } catch (error) {\n          this.client.logger.error(error);\n        }\n      }\n    }, 50);\n  };\n\n  // ---------- Private Helpers --------------------------------------- //\n\n  private cleanupDuplicatePairings: EnginePrivate[\"cleanupDuplicatePairings\"] = async (\n    session: SessionTypes.Struct,\n  ) => {\n    // older SDK versions are missing the `pairingTopic` prop thus we need to check for it\n    if (!session.pairingTopic) return;\n\n    try {\n      const pairing = this.client.core.pairing.pairings.get(session.pairingTopic);\n      const allPairings = this.client.core.pairing.pairings.getAll();\n      const duplicates = allPairings.filter(\n        (p) =>\n          p.peerMetadata?.url &&\n          p.peerMetadata?.url === session.peer.metadata.url &&\n          p.topic &&\n          p.topic !== pairing.topic,\n      );\n      if (duplicates.length === 0) return;\n      this.client.logger.info(`Cleaning up ${duplicates.length} duplicate pairing(s)`);\n      await Promise.all(\n        duplicates.map((p) => this.client.core.pairing.disconnect({ topic: p.topic })),\n      );\n      this.client.logger.info(`Duplicate pairings clean up finished`);\n    } catch (error) {\n      this.client.logger.error(error);\n    }\n  };\n\n  private deleteSession: EnginePrivate[\"deleteSession\"] = async (params) => {\n    const { topic, expirerHasDeleted = false, emitEvent = true, id = 0 } = params;\n    const { self } = this.client.session.get(topic);\n    // Await the unsubscribe first to avoid deleting the symKey too early below.\n    await this.client.core.relayer.unsubscribe(topic);\n    await this.client.session.delete(topic, getSdkError(\"USER_DISCONNECTED\"));\n    this.addToRecentlyDeleted(topic, \"session\");\n    if (this.client.core.crypto.keychain.has(self.publicKey)) {\n      await this.client.core.crypto.deleteKeyPair(self.publicKey);\n    }\n    if (this.client.core.crypto.keychain.has(topic)) {\n      await this.client.core.crypto.deleteSymKey(topic);\n    }\n    if (!expirerHasDeleted) this.client.core.expirer.del(topic);\n    // remove any deeplinks from storage after the session is deleted\n    // to avoid navigating to incorrect deeplink later on\n    this.client.core.storage\n      .removeItem(WALLETCONNECT_DEEPLINK_CHOICE)\n      .catch((e) => this.client.logger.warn(e));\n    this.getPendingSessionRequests().forEach((r) => {\n      if (r.topic === topic) {\n        this.deletePendingSessionRequest(r.id, getSdkError(\"USER_DISCONNECTED\"));\n      }\n    });\n    // reset the queue state back to idle if a request for the deleted session is still in the queue\n    if (topic === this.sessionRequestQueue.queue[0]?.topic) {\n      this.sessionRequestQueue.state = ENGINE_QUEUE_STATES.idle;\n    }\n    if (emitEvent) this.client.events.emit(\"session_delete\", { id, topic });\n  };\n\n  private deleteProposal: EnginePrivate[\"deleteProposal\"] = async (id, expirerHasDeleted) => {\n    if (expirerHasDeleted) {\n      try {\n        const proposal = this.client.proposal.get(id);\n        const event = this.client.core.eventClient.getEvent({ topic: proposal.pairingTopic });\n        event?.setError(EVENT_CLIENT_SESSION_ERRORS.proposal_expired);\n      } catch (error) {}\n    }\n    await Promise.all([\n      this.client.proposal.delete(id, getSdkError(\"USER_DISCONNECTED\")),\n      expirerHasDeleted ? Promise.resolve() : this.client.core.expirer.del(id),\n    ]);\n    this.addToRecentlyDeleted(id, \"proposal\");\n  };\n\n  private deletePendingSessionRequest: EnginePrivate[\"deletePendingSessionRequest\"] = async (\n    id,\n    reason,\n    expirerHasDeleted = false,\n  ) => {\n    await Promise.all([\n      this.client.pendingRequest.delete(id, reason),\n      expirerHasDeleted ? Promise.resolve() : this.client.core.expirer.del(id),\n    ]);\n    this.addToRecentlyDeleted(id, \"request\");\n    this.sessionRequestQueue.queue = this.sessionRequestQueue.queue.filter((r) => r.id !== id);\n    if (expirerHasDeleted) {\n      this.sessionRequestQueue.state = ENGINE_QUEUE_STATES.idle;\n      this.client.events.emit(\"session_request_expire\", { id });\n    }\n  };\n\n  private deletePendingAuthRequest: EnginePrivate[\"deletePendingAuthRequest\"] = async (\n    id,\n    reason,\n    expirerHasDeleted = false,\n  ) => {\n    await Promise.all([\n      this.client.auth.requests.delete(id, reason),\n      expirerHasDeleted ? Promise.resolve() : this.client.core.expirer.del(id),\n    ]);\n  };\n\n  private setExpiry: EnginePrivate[\"setExpiry\"] = async (topic, expiry) => {\n    if (!this.client.session.keys.includes(topic)) return;\n    this.client.core.expirer.set(topic, expiry);\n    await this.client.session.update(topic, { expiry });\n  };\n\n  private setProposal: EnginePrivate[\"setProposal\"] = async (id, proposal) => {\n    this.client.core.expirer.set(id, calcExpiry(ENGINE_RPC_OPTS.wc_sessionPropose.req.ttl));\n    await this.client.proposal.set(id, proposal);\n  };\n\n  private setAuthRequest: EnginePrivate[\"setAuthRequest\"] = async (id, params) => {\n    const { request, pairingTopic, transportType = TRANSPORT_TYPES.relay } = params;\n    this.client.core.expirer.set(id, request.expiryTimestamp);\n    await this.client.auth.requests.set(id, {\n      authPayload: request.authPayload,\n      requester: request.requester,\n      expiryTimestamp: request.expiryTimestamp,\n      id,\n      pairingTopic,\n      verifyContext: request.verifyContext,\n      transportType,\n    });\n  };\n\n  private setPendingSessionRequest: EnginePrivate[\"setPendingSessionRequest\"] = async (\n    pendingRequest: PendingRequestTypes.Struct,\n  ) => {\n    const { id, topic, params, verifyContext } = pendingRequest;\n    const expiry =\n      params.request.expiryTimestamp || calcExpiry(ENGINE_RPC_OPTS.wc_sessionRequest.req.ttl);\n    this.client.core.expirer.set(id, expiry);\n    await this.client.pendingRequest.set(id, {\n      id,\n      topic,\n      params,\n      verifyContext,\n    });\n  };\n\n  private sendRequest: EnginePrivate[\"sendRequest\"] = async (args) => {\n    const {\n      topic,\n      method,\n      params,\n      expiry,\n      relayRpcId,\n      clientRpcId,\n      throwOnFailedPublish,\n      appLink,\n      tvf,\n    } = args;\n    const payload = formatJsonRpcRequest(method, params, clientRpcId);\n\n    let message: string;\n    const isLinkMode = !!appLink;\n\n    try {\n      const encoding = isLinkMode ? BASE64URL : BASE64;\n      message = await this.client.core.crypto.encode(topic, payload, { encoding });\n    } catch (error) {\n      await this.cleanup();\n      this.client.logger.error(`sendRequest() -> core.crypto.encode() for topic ${topic} failed`);\n      throw error;\n    }\n\n    let attestation: string | undefined;\n    if (METHODS_TO_VERIFY.includes(method)) {\n      const decryptedId = hashMessage(JSON.stringify(payload));\n      const id = hashMessage(message);\n      attestation = await this.client.core.verify.register({ id, decryptedId });\n    }\n    const opts = ENGINE_RPC_OPTS[method].req;\n    opts.attestation = attestation;\n    if (expiry) opts.ttl = expiry;\n    if (relayRpcId) opts.id = relayRpcId;\n    this.client.core.history.set(topic, payload);\n\n    if (isLinkMode) {\n      const redirectURL = getLinkModeURL(appLink, topic, message);\n      await (global as any).Linking.openURL(redirectURL, this.client.name);\n    } else {\n      const opts = ENGINE_RPC_OPTS[method].req;\n      if (expiry) opts.ttl = expiry;\n      if (relayRpcId) opts.id = relayRpcId;\n\n      opts.tvf = {\n        ...tvf,\n        correlationId: payload.id,\n      };\n\n      if (throwOnFailedPublish) {\n        opts.internal = {\n          ...opts.internal,\n          throwOnFailedPublish: true,\n        };\n        await this.client.core.relayer.publish(topic, message, opts);\n      } else {\n        this.client.core.relayer\n          .publish(topic, message, opts)\n          .catch((error) => this.client.logger.error(error));\n      }\n    }\n\n    return payload.id;\n  };\n\n  private sendResult: EnginePrivate[\"sendResult\"] = async (args) => {\n    const { id, topic, result, throwOnFailedPublish, encodeOpts, appLink } = args;\n    const payload = formatJsonRpcResult(id, result);\n    let message;\n    const isLinkMode = appLink && typeof (global as any)?.Linking !== \"undefined\";\n\n    try {\n      const encoding = isLinkMode ? BASE64URL : BASE64;\n      message = await this.client.core.crypto.encode(topic, payload, {\n        ...(encodeOpts || {}),\n        encoding,\n      });\n    } catch (error) {\n      // if encoding fails e.g. due to missing keychain, we want to cleanup all related data as its unusable\n      await this.cleanup();\n      this.client.logger.error(`sendResult() -> core.crypto.encode() for topic ${topic} failed`);\n      throw error;\n    }\n    let record;\n    let tvf;\n    try {\n      record = await this.client.core.history.get(topic, id);\n      const request = record.request;\n      try {\n        if (this.shouldSetTVF(request.method as JsonRpcTypes.WcMethod, request.params)) {\n          tvf = this.getTVFParams(id, request.params, result);\n        }\n      } catch (error) {\n        this.client.logger.warn(`sendResult() -> getTVFParams() failed`, error);\n      }\n    } catch (error) {\n      this.client.logger.error(`sendResult() -> history.get(${topic}, ${id}) failed`);\n      throw error;\n    }\n\n    if (isLinkMode) {\n      const redirectURL = getLinkModeURL(appLink, topic, message);\n      await (global as any).Linking.openURL(redirectURL, this.client.name);\n    } else {\n      const method = record.request.method as JsonRpcTypes.WcMethod;\n      const opts = ENGINE_RPC_OPTS[method].res;\n\n      opts.tvf = {\n        ...tvf,\n        correlationId: id,\n      };\n\n      if (throwOnFailedPublish) {\n        opts.internal = {\n          ...opts.internal,\n          throwOnFailedPublish: true,\n        };\n        await this.client.core.relayer.publish(topic, message, opts);\n      } else {\n        this.client.core.relayer\n          .publish(topic, message, opts)\n          .catch((error) => this.client.logger.error(error));\n      }\n    }\n\n    await this.client.core.history.resolve(payload);\n  };\n\n  private sendError: EnginePrivate[\"sendError\"] = async (params) => {\n    const { id, topic, error, encodeOpts, rpcOpts, appLink } = params;\n    const payload = formatJsonRpcError(id, error);\n    let message;\n    const isLinkMode = appLink && typeof (global as any)?.Linking !== \"undefined\";\n    try {\n      const encoding = isLinkMode ? BASE64URL : BASE64;\n      message = await this.client.core.crypto.encode(topic, payload, {\n        ...(encodeOpts || {}),\n        encoding,\n      });\n    } catch (error) {\n      await this.cleanup();\n      this.client.logger.error(`sendError() -> core.crypto.encode() for topic ${topic} failed`);\n      throw error;\n    }\n    let record;\n    try {\n      record = await this.client.core.history.get(topic, id);\n    } catch (error) {\n      this.client.logger.error(`sendError() -> history.get(${topic}, ${id}) failed`);\n      throw error;\n    }\n\n    if (isLinkMode) {\n      const redirectURL = getLinkModeURL(appLink, topic, message);\n      await (global as any).Linking.openURL(redirectURL, this.client.name);\n    } else {\n      const method = record.request.method as JsonRpcTypes.WcMethod;\n      const opts = rpcOpts || ENGINE_RPC_OPTS[method].res;\n      // await is intentionally omitted to speed up performance\n      this.client.core.relayer.publish(topic, message, opts);\n    }\n\n    await this.client.core.history.resolve(payload);\n  };\n\n  private cleanup: EnginePrivate[\"cleanup\"] = async () => {\n    const sessionTopics: string[] = [];\n    const proposalIds: number[] = [];\n    this.client.session.getAll().forEach((session) => {\n      let toCleanup = false;\n      if (isExpired(session.expiry)) toCleanup = true;\n      if (!this.client.core.crypto.keychain.has(session.topic)) toCleanup = true;\n      if (toCleanup) sessionTopics.push(session.topic);\n    });\n    this.client.proposal.getAll().forEach((proposal) => {\n      if (isExpired(proposal.expiryTimestamp)) proposalIds.push(proposal.id);\n    });\n    await Promise.all([\n      ...sessionTopics.map((topic) => this.deleteSession({ topic })),\n      ...proposalIds.map((id) => this.deleteProposal(id)),\n    ]);\n  };\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n\n  private async confirmOnlineStateOrThrow() {\n    await this.client.core.relayer.confirmOnlineStateOrThrow();\n  }\n\n  // ---------- Relay Events Router ----------------------------------- //\n\n  private registerRelayerEvents() {\n    this.client.core.relayer.on(RELAYER_EVENTS.message, (event: RelayerTypes.MessageEvent) => {\n      this.onProviderMessageEvent(event);\n    });\n  }\n\n  private onProviderMessageEvent = async (event: RelayerTypes.MessageEvent) => {\n    // capture any messages that arrive before the client is initialized so we can process them after initialization is complete\n    if (!this.initialized || this.relayMessageCache.length > 0) {\n      this.relayMessageCache.push(event);\n    } else {\n      await this.onRelayMessage(event);\n    }\n  };\n\n  private async onRelayMessage(event: RelayerTypes.MessageEvent) {\n    const { topic, message, attestation, transportType } = event;\n\n    // Retrieve the public key (if defined) to decrypt possible `auth_request` response\n    const { publicKey } = this.client.auth.authKeys.keys.includes(AUTH_PUBLIC_KEY_NAME)\n      ? this.client.auth.authKeys.get(AUTH_PUBLIC_KEY_NAME)\n      : ({ responseTopic: undefined, publicKey: undefined } as any);\n\n    try {\n      const payload = await this.client.core.crypto.decode(topic, message, {\n        receiverPublicKey: publicKey,\n        encoding: transportType === TRANSPORT_TYPES.link_mode ? BASE64URL : BASE64,\n      });\n\n      if (isJsonRpcRequest(payload)) {\n        this.client.core.history.set(topic, payload);\n        await this.onRelayEventRequest({\n          topic,\n          payload,\n          attestation,\n          transportType,\n          encryptedId: hashMessage(message),\n        });\n      } else if (isJsonRpcResponse(payload)) {\n        await this.client.core.history.resolve(payload);\n        await this.onRelayEventResponse({ topic, payload, transportType });\n        this.client.core.history.delete(topic, payload.id);\n      } else {\n        await this.onRelayEventUnknownPayload({ topic, payload, transportType });\n      }\n      await this.client.core.relayer.messages.ack(topic, message);\n    } catch (error) {\n      this.client.logger.error(error);\n    }\n  }\n\n  private onRelayEventRequest: EnginePrivate[\"onRelayEventRequest\"] = async (event) => {\n    this.requestQueue.queue.push(event);\n    await this.processRequestsQueue();\n  };\n\n  private processRequestsQueue = async () => {\n    if (this.requestQueue.state === ENGINE_QUEUE_STATES.active) {\n      this.client.logger.info(`Request queue already active, skipping...`);\n      return;\n    }\n\n    this.client.logger.info(\n      `Request queue starting with ${this.requestQueue.queue.length} requests`,\n    );\n\n    while (this.requestQueue.queue.length > 0) {\n      this.requestQueue.state = ENGINE_QUEUE_STATES.active;\n      const request = this.requestQueue.queue.shift();\n      if (!request) continue;\n\n      try {\n        await this.processRequest(request);\n      } catch (error) {\n        this.client.logger.warn(error);\n      }\n    }\n    this.requestQueue.state = ENGINE_QUEUE_STATES.idle;\n  };\n\n  private processRequest: EnginePrivate[\"onRelayEventRequest\"] = async (event) => {\n    const { topic, payload, attestation, transportType, encryptedId } = event;\n\n    const reqMethod = payload.method as JsonRpcTypes.WcMethod;\n\n    if (this.shouldIgnorePairingRequest({ topic, requestMethod: reqMethod })) {\n      return;\n    }\n\n    switch (reqMethod) {\n      case \"wc_sessionPropose\":\n        return await this.onSessionProposeRequest({ topic, payload, attestation, encryptedId });\n      case \"wc_sessionSettle\":\n        return await this.onSessionSettleRequest(topic, payload);\n      case \"wc_sessionUpdate\":\n        return await this.onSessionUpdateRequest(topic, payload);\n      case \"wc_sessionExtend\":\n        return await this.onSessionExtendRequest(topic, payload);\n      case \"wc_sessionPing\":\n        return await this.onSessionPingRequest(topic, payload);\n      case \"wc_sessionDelete\":\n        return await this.onSessionDeleteRequest(topic, payload);\n      case \"wc_sessionRequest\":\n        return await this.onSessionRequest({\n          topic,\n          payload,\n          attestation,\n          encryptedId,\n          transportType,\n        });\n      case \"wc_sessionEvent\":\n        return await this.onSessionEventRequest(topic, payload);\n      case \"wc_sessionAuthenticate\":\n        return await this.onSessionAuthenticateRequest({\n          topic,\n          payload,\n          attestation,\n          encryptedId,\n          transportType,\n        });\n      default:\n        return this.client.logger.info(`Unsupported request method ${reqMethod}`);\n    }\n  };\n\n  private onRelayEventResponse: EnginePrivate[\"onRelayEventResponse\"] = async (event) => {\n    const { topic, payload, transportType } = event;\n    const record = await this.client.core.history.get(topic, payload.id);\n    const resMethod = record.request.method as JsonRpcTypes.WcMethod;\n\n    switch (resMethod) {\n      case \"wc_sessionPropose\":\n        return this.onSessionProposeResponse(topic, payload, transportType);\n      case \"wc_sessionSettle\":\n        return this.onSessionSettleResponse(topic, payload);\n      case \"wc_sessionUpdate\":\n        return this.onSessionUpdateResponse(topic, payload);\n      case \"wc_sessionExtend\":\n        return this.onSessionExtendResponse(topic, payload);\n      case \"wc_sessionPing\":\n        return this.onSessionPingResponse(topic, payload);\n      case \"wc_sessionRequest\":\n        return this.onSessionRequestResponse(topic, payload);\n      case \"wc_sessionAuthenticate\":\n        return this.onSessionAuthenticateResponse(topic, payload);\n      default:\n        return this.client.logger.info(`Unsupported response method ${resMethod}`);\n    }\n  };\n\n  private onRelayEventUnknownPayload: EnginePrivate[\"onRelayEventUnknownPayload\"] = (event) => {\n    const { topic } = event;\n    const { message } = getInternalError(\n      \"MISSING_OR_INVALID\",\n      `Decoded payload on topic ${topic} is not identifiable as a JSON-RPC request or a response.`,\n    );\n    throw new Error(message);\n  };\n\n  private shouldIgnorePairingRequest: EnginePrivate[\"shouldIgnorePairingRequest\"] = (params) => {\n    const { topic, requestMethod } = params;\n    const expectedMethods = this.expectedPairingMethodMap.get(topic);\n    // check if the request method matches the expected method\n    if (!expectedMethods) return false;\n    if (expectedMethods.includes(requestMethod)) return false;\n\n    /**\n     * we want to make sure fallback session proposal is ignored only if there are subscribers\n     * for the `session_authenticate` event, otherwise this would result in no-op for the user\n     */\n    if (expectedMethods.includes(\"wc_sessionAuthenticate\")) {\n      if (this.client.events.listenerCount(\"session_authenticate\") > 0) {\n        return true;\n      }\n    }\n    return false;\n  };\n\n  // ---------- Relay Events Handlers --------------------------------- //\n\n  private onSessionProposeRequest: EnginePrivate[\"onSessionProposeRequest\"] = async (args) => {\n    const { topic, payload, attestation, encryptedId } = args;\n    const { params, id } = payload;\n    try {\n      const event = this.client.core.eventClient.getEvent({ topic });\n\n      if (this.client.events.listenerCount(\"session_proposal\") === 0) {\n        console.warn(\"No listener for session_proposal event\");\n        event?.setError(EVENT_CLIENT_PAIRING_ERRORS.proposal_listener_not_found);\n      }\n\n      this.isValidConnect({ ...payload.params });\n      const expiryTimestamp =\n        params.expiryTimestamp || calcExpiry(ENGINE_RPC_OPTS.wc_sessionPropose.req.ttl);\n      const proposal = { id, pairingTopic: topic, expiryTimestamp, ...params };\n      await this.setProposal(id, proposal);\n\n      const verifyContext = await this.getVerifyContext({\n        attestationId: attestation,\n        hash: hashMessage(JSON.stringify(payload)),\n        encryptedId,\n        metadata: proposal.proposer.metadata,\n      });\n\n      event?.addTrace(EVENT_CLIENT_PAIRING_TRACES.emit_session_proposal);\n\n      this.client.events.emit(\"session_proposal\", { id, params: proposal, verifyContext });\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n        rpcOpts: ENGINE_RPC_OPTS.wc_sessionPropose.autoReject,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionProposeResponse: EnginePrivate[\"onSessionProposeResponse\"] = async (\n    topic,\n    payload,\n    transportType,\n  ) => {\n    const { id } = payload;\n    if (isJsonRpcResult(payload)) {\n      const { result } = payload;\n      this.client.logger.trace({ type: \"method\", method: \"onSessionProposeResponse\", result });\n      const proposal = this.client.proposal.get(id);\n      this.client.logger.trace({ type: \"method\", method: \"onSessionProposeResponse\", proposal });\n      const selfPublicKey = proposal.proposer.publicKey;\n      this.client.logger.trace({\n        type: \"method\",\n        method: \"onSessionProposeResponse\",\n        selfPublicKey,\n      });\n      const peerPublicKey = result.responderPublicKey;\n      this.client.logger.trace({\n        type: \"method\",\n        method: \"onSessionProposeResponse\",\n        peerPublicKey,\n      });\n      const sessionTopic = await this.client.core.crypto.generateSharedKey(\n        selfPublicKey,\n        peerPublicKey,\n      );\n      this.pendingSessions.set(id, {\n        sessionTopic,\n        pairingTopic: topic,\n        proposalId: id,\n        publicKey: selfPublicKey,\n      });\n\n      const subscriptionId = await this.client.core.relayer.subscribe(sessionTopic, {\n        transportType,\n      });\n      this.client.logger.trace({\n        type: \"method\",\n        method: \"onSessionProposeResponse\",\n        subscriptionId,\n      });\n      await this.client.core.pairing.activate({ topic });\n    } else if (isJsonRpcError(payload)) {\n      await this.client.proposal.delete(id, getSdkError(\"USER_DISCONNECTED\"));\n      const target = engineEvent(\"session_connect\", id);\n      const listeners = this.events.listenerCount(target);\n      if (listeners === 0) {\n        throw new Error(`emitting ${target} without any listeners, 954`);\n      }\n      this.events.emit(target, { error: payload.error });\n    }\n  };\n\n  private onSessionSettleRequest: EnginePrivate[\"onSessionSettleRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id, params } = payload;\n    try {\n      this.isValidSessionSettleRequest(params);\n      const {\n        relay,\n        controller,\n        expiry,\n        namespaces,\n        sessionProperties,\n        scopedProperties,\n        sessionConfig,\n      } = payload.params;\n      const pendingSession = [...this.pendingSessions.values()].find(\n        (s) => s.sessionTopic === topic,\n      );\n\n      if (!pendingSession) {\n        return this.client.logger.error(`Pending session not found for topic ${topic}`);\n      }\n\n      const proposal = this.client.proposal.get(pendingSession.proposalId);\n\n      const session: SessionTypes.Struct = {\n        topic,\n        relay,\n        expiry,\n        namespaces,\n        acknowledged: true,\n        pairingTopic: pendingSession.pairingTopic,\n        requiredNamespaces: proposal.requiredNamespaces,\n        optionalNamespaces: proposal.optionalNamespaces,\n        controller: controller.publicKey,\n        self: {\n          publicKey: pendingSession.publicKey,\n          metadata: this.client.metadata,\n        },\n        peer: {\n          publicKey: controller.publicKey,\n          metadata: controller.metadata,\n        },\n        ...(sessionProperties && { sessionProperties }),\n        ...(scopedProperties && { scopedProperties }),\n        ...(sessionConfig && { sessionConfig }),\n        transportType: TRANSPORT_TYPES.relay,\n      };\n\n      await this.client.session.set(session.topic, session);\n      await this.setExpiry(session.topic, session.expiry);\n\n      await this.client.core.pairing.updateMetadata({\n        topic: pendingSession.pairingTopic,\n        metadata: session.peer.metadata,\n      });\n\n      this.client.events.emit(\"session_connect\", { session });\n      this.events.emit(engineEvent(\"session_connect\", pendingSession.proposalId), { session });\n\n      this.pendingSessions.delete(pendingSession.proposalId);\n      this.deleteProposal(pendingSession.proposalId, false);\n      this.cleanupDuplicatePairings(session);\n\n      await this.sendResult<\"wc_sessionSettle\">({\n        id: payload.id,\n        topic,\n        result: true,\n        throwOnFailedPublish: true,\n      });\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionSettleResponse: EnginePrivate[\"onSessionSettleResponse\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    if (isJsonRpcResult(payload)) {\n      await this.client.session.update(topic, { acknowledged: true });\n      this.events.emit(engineEvent(\"session_approve\", id), {});\n    } else if (isJsonRpcError(payload)) {\n      await this.client.session.delete(topic, getSdkError(\"USER_DISCONNECTED\"));\n      this.events.emit(engineEvent(\"session_approve\", id), { error: payload.error });\n    }\n  };\n\n  private onSessionUpdateRequest: EnginePrivate[\"onSessionUpdateRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { params, id } = payload;\n    try {\n      const memoryKey = `${topic}_session_update`;\n      // compare the current request id with the last processed session update\n      // we want to update only if the request is newer than the last processed one\n      const lastSessionUpdateId = MemoryStore.get<number>(memoryKey);\n\n      if (lastSessionUpdateId && this.isRequestOutOfSync(lastSessionUpdateId, id)) {\n        this.client.logger.warn(`Discarding out of sync request - ${id}`);\n        this.sendError({ id, topic, error: getSdkError(\"INVALID_UPDATE_REQUEST\") });\n        return;\n      }\n      this.isValidUpdate({ topic, ...params });\n      try {\n        MemoryStore.set(memoryKey, id);\n        await this.client.session.update(topic, { namespaces: params.namespaces });\n        await this.sendResult<\"wc_sessionUpdate\">({\n          id,\n          topic,\n          result: true,\n          throwOnFailedPublish: true,\n        });\n      } catch (e) {\n        MemoryStore.delete(memoryKey);\n        throw e;\n      }\n\n      this.client.events.emit(\"session_update\", { id, topic, params });\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  // compares the timestamp of the last processed request with the current request\n  // client <-> client rpc ID is timestamp + 3 random digits\n  private isRequestOutOfSync = (lastId: number, currentId: number) => {\n    return currentId.toString().slice(0, -3) < lastId.toString().slice(0, -3);\n  };\n\n  private onSessionUpdateResponse: EnginePrivate[\"onSessionUpdateResponse\"] = (_topic, payload) => {\n    const { id } = payload;\n    const target = engineEvent(\"session_update\", id);\n    const listeners = this.events.listenerCount(target);\n    if (listeners === 0) {\n      throw new Error(`emitting ${target} without any listeners`);\n    }\n    if (isJsonRpcResult(payload)) {\n      this.events.emit(engineEvent(\"session_update\", id), {});\n    } else if (isJsonRpcError(payload)) {\n      this.events.emit(engineEvent(\"session_update\", id), { error: payload.error });\n    }\n  };\n\n  private onSessionExtendRequest: EnginePrivate[\"onSessionExtendRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    try {\n      this.isValidExtend({ topic });\n      await this.setExpiry(topic, calcExpiry(SESSION_EXPIRY));\n      await this.sendResult<\"wc_sessionExtend\">({\n        id,\n        topic,\n        result: true,\n        throwOnFailedPublish: true,\n      });\n      this.client.events.emit(\"session_extend\", { id, topic });\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionExtendResponse: EnginePrivate[\"onSessionExtendResponse\"] = (_topic, payload) => {\n    const { id } = payload;\n    const target = engineEvent(\"session_extend\", id);\n    const listeners = this.events.listenerCount(target);\n    if (listeners === 0) {\n      throw new Error(`emitting ${target} without any listeners`);\n    }\n    if (isJsonRpcResult(payload)) {\n      this.events.emit(engineEvent(\"session_extend\", id), {});\n    } else if (isJsonRpcError(payload)) {\n      this.events.emit(engineEvent(\"session_extend\", id), { error: payload.error });\n    }\n  };\n\n  private onSessionPingRequest: EnginePrivate[\"onSessionPingRequest\"] = async (topic, payload) => {\n    const { id } = payload;\n    try {\n      this.isValidPing({ topic });\n      await this.sendResult<\"wc_sessionPing\">({\n        id,\n        topic,\n        result: true,\n        throwOnFailedPublish: true,\n      });\n      this.client.events.emit(\"session_ping\", { id, topic });\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionPingResponse: EnginePrivate[\"onSessionPingResponse\"] = (_topic, payload) => {\n    const { id } = payload;\n    const target = engineEvent(\"session_ping\", id);\n\n    // put at the end of the stack to avoid a race condition\n    // where session_ping listener is not yet initialized\n    setTimeout(() => {\n      const listeners = this.events.listenerCount(target);\n      if (listeners === 0) {\n        throw new Error(`emitting ${target} without any listeners 2176`);\n      }\n\n      if (isJsonRpcResult(payload)) {\n        this.events.emit(engineEvent(\"session_ping\", id), {});\n      } else if (isJsonRpcError(payload)) {\n        this.events.emit(engineEvent(\"session_ping\", id), { error: payload.error });\n      }\n    }, 500);\n  };\n\n  private onSessionDeleteRequest: EnginePrivate[\"onSessionDeleteRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    try {\n      this.isValidDisconnect({ topic, reason: payload.params });\n      Promise.all([\n        new Promise((resolve) => {\n          // RPC request needs to happen before deletion as it utalises session encryption\n          this.client.core.relayer.once(RELAYER_EVENTS.publish, async () => {\n            resolve(await this.deleteSession({ topic, id }));\n          });\n        }),\n        this.sendResult<\"wc_sessionDelete\">({\n          id,\n          topic,\n          result: true,\n          throwOnFailedPublish: true,\n        }),\n        this.cleanupPendingSentRequestsForTopic({ topic, error: getSdkError(\"USER_DISCONNECTED\") }),\n      ]).catch((err) => this.client.logger.error(err));\n    } catch (err: any) {\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionRequest: EnginePrivate[\"onSessionRequest\"] = async (args) => {\n    const { topic, payload, attestation, encryptedId, transportType } = args;\n    const { id, params } = payload;\n    try {\n      await this.isValidRequest({ topic, ...params });\n      const session = this.client.session.get(topic);\n      const verifyContext = await this.getVerifyContext({\n        attestationId: attestation,\n        hash: hashMessage(JSON.stringify(formatJsonRpcRequest(\"wc_sessionRequest\", params, id))),\n        encryptedId,\n        metadata: session.peer.metadata,\n        transportType,\n      });\n      const request = {\n        id,\n        topic,\n        params,\n        verifyContext,\n      };\n      await this.setPendingSessionRequest(request);\n\n      if (\n        transportType === TRANSPORT_TYPES.link_mode &&\n        session.peer.metadata.redirect?.universal\n      ) {\n        // save app as supported for link mode\n        this.client.core.addLinkModeSupportedApp(session.peer.metadata.redirect?.universal);\n      }\n\n      if (this.client.signConfig?.disableRequestQueue) {\n        this.emitSessionRequest(request);\n      } else {\n        this.addSessionRequestToSessionRequestQueue(request);\n        this.processSessionRequestQueue();\n      }\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionRequestResponse: EnginePrivate[\"onSessionRequestResponse\"] = (\n    _topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    const target = engineEvent(\"session_request\", id);\n    const listeners = this.events.listenerCount(target);\n    if (listeners === 0) {\n      throw new Error(`emitting ${target} without any listeners`);\n    }\n    if (isJsonRpcResult(payload)) {\n      this.events.emit(engineEvent(\"session_request\", id), { result: payload.result });\n    } else if (isJsonRpcError(payload)) {\n      this.events.emit(engineEvent(\"session_request\", id), { error: payload.error });\n    }\n  };\n\n  private onSessionEventRequest: EnginePrivate[\"onSessionEventRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id, params } = payload;\n    try {\n      // similar to session update, we want to discard out of sync requests\n      // additionally we have to check the event type as well e.g. chainChanged/accountsChanged\n      const memoryKey = `${topic}_session_event_${params.event.name}`;\n      // compare the current request id with the last processed session update\n      // we want to update only if the request is newer than the last processed one\n      const lastSessionUpdateId = MemoryStore.get<number>(memoryKey);\n      if (lastSessionUpdateId && this.isRequestOutOfSync(lastSessionUpdateId, id)) {\n        this.client.logger.info(`Discarding out of sync request - ${id}`);\n        return;\n      }\n\n      this.isValidEmit({ topic, ...params });\n      this.client.events.emit(\"session_event\", { id, topic, params });\n      MemoryStore.set(memoryKey, id);\n    } catch (err: any) {\n      await this.sendError({\n        id,\n        topic,\n        error: err,\n      });\n      this.client.logger.error(err);\n    }\n  };\n\n  private onSessionAuthenticateResponse: EnginePrivate[\"onSessionAuthenticateResponse\"] = (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    this.client.logger.trace({\n      type: \"method\",\n      method: \"onSessionAuthenticateResponse\",\n      topic,\n      payload,\n    });\n    if (isJsonRpcResult(payload)) {\n      this.events.emit(engineEvent(\"session_request\", id), { result: payload.result });\n    } else if (isJsonRpcError(payload)) {\n      this.events.emit(engineEvent(\"session_request\", id), { error: payload.error });\n    }\n  };\n\n  private onSessionAuthenticateRequest: EnginePrivate[\"onSessionAuthenticateRequest\"] = async (\n    args,\n  ) => {\n    const { topic, payload, attestation, encryptedId, transportType } = args;\n    try {\n      const { requester, authPayload, expiryTimestamp } = payload.params;\n      const verifyContext = await this.getVerifyContext({\n        attestationId: attestation,\n        hash: hashMessage(JSON.stringify(payload)),\n        encryptedId,\n        metadata: requester.metadata,\n        transportType,\n      });\n      const pendingRequest = {\n        requester,\n        pairingTopic: topic,\n        id: payload.id,\n        authPayload,\n        verifyContext,\n        expiryTimestamp,\n      };\n      await this.setAuthRequest(payload.id, {\n        request: pendingRequest,\n        pairingTopic: topic,\n        transportType,\n      });\n\n      if (transportType === TRANSPORT_TYPES.link_mode && requester.metadata.redirect?.universal) {\n        // save app as supported for link mode\n        this.client.core.addLinkModeSupportedApp(requester.metadata.redirect.universal);\n      }\n\n      this.client.events.emit(\"session_authenticate\", {\n        topic,\n        params: payload.params,\n        id: payload.id,\n        verifyContext,\n      });\n    } catch (err: any) {\n      this.client.logger.error(err);\n\n      const receiverPublicKey = payload.params.requester.publicKey;\n      const senderPublicKey = await this.client.core.crypto.generateKeyPair();\n      const appLink = this.getAppLinkIfEnabled(payload.params.requester.metadata, transportType);\n\n      const encodeOpts = {\n        type: TYPE_1,\n        receiverPublicKey,\n        senderPublicKey,\n      };\n      await this.sendError({\n        id: payload.id,\n        topic,\n        error: err,\n        encodeOpts,\n        rpcOpts: ENGINE_RPC_OPTS.wc_sessionAuthenticate.autoReject,\n        appLink,\n      });\n    }\n  };\n\n  private addSessionRequestToSessionRequestQueue = (request: PendingRequestTypes.Struct) => {\n    this.sessionRequestQueue.queue.push(request);\n  };\n\n  private cleanupAfterResponse = (params: EngineTypes.RespondParams) => {\n    this.deletePendingSessionRequest(params.response.id, { message: \"fulfilled\", code: 0 });\n    // intentionally delay the emitting of the next pending request a bit\n    setTimeout(() => {\n      this.sessionRequestQueue.state = ENGINE_QUEUE_STATES.idle;\n      this.processSessionRequestQueue();\n    }, toMiliseconds(this.requestQueueDelay));\n  };\n\n  // Allows for cleanup on any sent pending requests if the peer disconnects the session before responding\n  private cleanupPendingSentRequestsForTopic = ({\n    topic,\n    error,\n  }: {\n    topic: string;\n    error: ErrorResponse;\n  }) => {\n    const pendingRequests = this.client.core.history.pending;\n    if (pendingRequests.length > 0) {\n      const forSession = pendingRequests.filter(\n        (r) => r.topic === topic && r.request.method === \"wc_sessionRequest\",\n      );\n      forSession.forEach((r) => {\n        const id = r.request.id;\n        const target = engineEvent(\"session_request\", id);\n        const listeners = this.events.listenerCount(target);\n        if (listeners === 0) {\n          throw new Error(`emitting ${target} without any listeners`);\n        }\n        // notify .request() handler of the rejection\n        this.events.emit(engineEvent(\"session_request\", r.request.id), {\n          error,\n        });\n      });\n    }\n  };\n\n  private processSessionRequestQueue = () => {\n    if (this.sessionRequestQueue.state === ENGINE_QUEUE_STATES.active) {\n      this.client.logger.info(\"session request queue is already active.\");\n      return;\n    }\n    // Select the first/oldest request in the array to ensure last-in-first-out (LIFO)\n    const request = this.sessionRequestQueue.queue[0];\n    if (!request) {\n      this.client.logger.info(\"session request queue is empty.\");\n      return;\n    }\n\n    try {\n      this.sessionRequestQueue.state = ENGINE_QUEUE_STATES.active;\n      this.emitSessionRequest(request);\n    } catch (error) {\n      this.client.logger.error(error);\n    }\n  };\n\n  private emitSessionRequest = (request: PendingRequestTypes.Struct) => {\n    this.client.events.emit(\"session_request\", request);\n  };\n\n  // ---------- Expirer Events ---------------------------------------- //\n\n  private registerExpirerEvents() {\n    this.client.core.expirer.on(EXPIRER_EVENTS.expired, async (event: ExpirerTypes.Expiration) => {\n      const { topic, id } = parseExpirerTarget(event.target);\n      if (id && this.client.pendingRequest.keys.includes(id)) {\n        return await this.deletePendingSessionRequest(id, getInternalError(\"EXPIRED\"), true);\n      }\n      if (id && this.client.auth.requests.keys.includes(id)) {\n        return await this.deletePendingAuthRequest(id, getInternalError(\"EXPIRED\"), true);\n      }\n\n      if (topic) {\n        if (this.client.session.keys.includes(topic)) {\n          await this.deleteSession({ topic, expirerHasDeleted: true });\n          this.client.events.emit(\"session_expire\", { topic });\n        }\n      } else if (id) {\n        await this.deleteProposal(id, true);\n        this.client.events.emit(\"proposal_expire\", { id });\n      }\n    });\n  }\n\n  // ---------- Pairing Events ---------------------------------------- //\n  private registerPairingEvents() {\n    this.client.core.pairing.events.on(PAIRING_EVENTS.create, (pairing: PairingTypes.Struct) =>\n      this.onPairingCreated(pairing),\n    );\n    this.client.core.pairing.events.on(PAIRING_EVENTS.delete, (pairing: PairingTypes.Struct) => {\n      this.addToRecentlyDeleted(pairing.topic, \"pairing\");\n    });\n  }\n\n  /**\n   * when a pairing is created, we check if there is a pending proposal for it.\n   * if there is, we send it to onSessionProposeRequest to be processed as if it was received from the relay.\n   * It allows QR/URI to be scanned multiple times without having to create new pairing.\n   */\n  private onPairingCreated = (pairing: PairingTypes.Struct) => {\n    if (pairing.methods) {\n      this.expectedPairingMethodMap.set(pairing.topic, pairing.methods);\n    }\n    if (pairing.active) return;\n    const proposals = this.client.proposal.getAll();\n    const proposal = proposals.find((p) => p.pairingTopic === pairing.topic);\n    if (!proposal) return;\n    this.onSessionProposeRequest({\n      topic: pairing.topic,\n      payload: formatJsonRpcRequest(\n        \"wc_sessionPropose\",\n        {\n          ...proposal,\n          requiredNamespaces: proposal.requiredNamespaces,\n          optionalNamespaces: proposal.optionalNamespaces,\n          relays: proposal.relays,\n          proposer: proposal.proposer,\n          sessionProperties: proposal.sessionProperties,\n          scopedProperties: proposal.scopedProperties,\n        },\n        proposal.id,\n      ),\n    });\n  };\n\n  // ---------- Validation Helpers ------------------------------------ //\n  private isValidPairingTopic(topic: any) {\n    if (!isValidString(topic, false)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `pairing topic should be a string: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    if (!this.client.core.pairing.pairings.keys.includes(topic)) {\n      const { message } = getInternalError(\n        \"NO_MATCHING_KEY\",\n        `pairing topic doesn't exist: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    if (isExpired(this.client.core.pairing.pairings.get(topic).expiry)) {\n      const { message } = getInternalError(\"EXPIRED\", `pairing topic: ${topic}`);\n      throw new Error(message);\n    }\n  }\n\n  private async isValidSessionTopic(topic: any) {\n    if (!isValidString(topic, false)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `session topic should be a string: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    // Store will throw custom message if topic was recently deleted\n    this.checkRecentlyDeleted(topic);\n    if (!this.client.session.keys.includes(topic)) {\n      const { message } = getInternalError(\n        \"NO_MATCHING_KEY\",\n        `session topic doesn't exist: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    if (isExpired(this.client.session.get(topic).expiry)) {\n      await this.deleteSession({ topic });\n      const { message } = getInternalError(\"EXPIRED\", `session topic: ${topic}`);\n      throw new Error(message);\n    }\n\n    if (!this.client.core.crypto.keychain.has(topic)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `session topic does not exist in keychain: ${topic}`,\n      );\n      await this.deleteSession({ topic });\n      throw new Error(message);\n    }\n  }\n\n  private async isValidSessionOrPairingTopic(topic: string) {\n    this.checkRecentlyDeleted(topic);\n    if (this.client.session.keys.includes(topic)) {\n      await this.isValidSessionTopic(topic);\n    } else if (this.client.core.pairing.pairings.keys.includes(topic)) {\n      this.isValidPairingTopic(topic);\n    } else if (!isValidString(topic, false)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `session or pairing topic should be a string: ${topic}`,\n      );\n      throw new Error(message);\n    } else {\n      const { message } = getInternalError(\n        \"NO_MATCHING_KEY\",\n        `session or pairing topic doesn't exist: ${topic}`,\n      );\n      throw new Error(message);\n    }\n  }\n\n  private async isValidProposalId(id: any) {\n    if (!isValidId(id)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `proposal id should be a number: ${id}`,\n      );\n      throw new Error(message);\n    }\n    if (!this.client.proposal.keys.includes(id)) {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `proposal id doesn't exist: ${id}`);\n      throw new Error(message);\n    }\n    if (isExpired(this.client.proposal.get(id).expiryTimestamp)) {\n      await this.deleteProposal(id);\n      const { message } = getInternalError(\"EXPIRED\", `proposal id: ${id}`);\n      throw new Error(message);\n    }\n  }\n\n  // ---------- Validation  ------------------------------------------- //\n\n  private isValidConnect: EnginePrivate[\"isValidConnect\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `connect() params: ${JSON.stringify(params)}`,\n      );\n      throw new Error(message);\n    }\n    const {\n      pairingTopic,\n      requiredNamespaces,\n      optionalNamespaces,\n      sessionProperties,\n      scopedProperties,\n      relays,\n    } = params;\n    if (!isUndefined(pairingTopic)) await this.isValidPairingTopic(pairingTopic);\n\n    if (!isValidRelays(relays, true)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `connect() relays: ${relays}`);\n      throw new Error(message);\n    }\n\n    // validate required namespaces only if they are defined\n    if (!isUndefined(requiredNamespaces) && isValidObject(requiredNamespaces) !== 0) {\n      const warning =\n        \"requiredNamespaces are deprecated and are automatically assigned to optionalNamespaces\";\n      // if logger level is one of the following, the logger.warn will not be shown, so we need to use console.warn\n      if ([\"fatal\", \"error\", \"silent\"].includes(this.client.logger.level)) {\n        console.warn(warning);\n      } else {\n        this.client.logger.warn(warning);\n      }\n      this.validateNamespaces(requiredNamespaces, \"requiredNamespaces\");\n    }\n\n    // validate optional namespaces only if they are defined\n    if (!isUndefined(optionalNamespaces) && isValidObject(optionalNamespaces) !== 0) {\n      this.validateNamespaces(optionalNamespaces, \"optionalNamespaces\");\n    }\n\n    // validate session properties only if they are defined\n    if (!isUndefined(sessionProperties)) {\n      this.validateSessionProps(sessionProperties, \"sessionProperties\");\n    }\n\n    if (!isUndefined(scopedProperties)) {\n      this.validateSessionProps(scopedProperties, \"scopedProperties\");\n\n      const requestedNamespaces = Object.keys(requiredNamespaces || {}).concat(\n        Object.keys(optionalNamespaces || {}),\n      );\n\n      const scopedNamespaces = Object.keys(scopedProperties);\n      const valid = scopedNamespaces.every((ns) => requestedNamespaces.includes(ns));\n      if (!valid) {\n        throw new Error(\n          `Scoped properties must be a subset of required/optional namespaces, received: ${JSON.stringify(\n            scopedProperties,\n          )}, required/optional namespaces: ${JSON.stringify(requestedNamespaces)}`,\n        );\n      }\n    }\n  };\n\n  private validateNamespaces = (\n    namespaces: ProposalTypes.RequiredNamespaces | ProposalTypes.OptionalNamespaces,\n    type: string,\n  ) => {\n    const validRequiredNamespacesError = isValidRequiredNamespaces(namespaces, \"connect()\", type);\n    if (validRequiredNamespacesError) throw new Error(validRequiredNamespacesError.message);\n  };\n\n  private isValidApprove: EnginePrivate[\"isValidApprove\"] = async (params) => {\n    if (!isValidParams(params))\n      throw new Error(\n        getInternalError(\"MISSING_OR_INVALID\", `approve() params: ${params}`).message,\n      );\n    const { id, namespaces, relayProtocol, sessionProperties, scopedProperties } = params;\n\n    this.checkRecentlyDeleted(id);\n    await this.isValidProposalId(id);\n    const proposal = this.client.proposal.get(id);\n    const validNamespacesError = isValidNamespaces(namespaces, \"approve()\");\n    if (validNamespacesError) throw new Error(validNamespacesError.message);\n    const conformingNamespacesError = isConformingNamespaces(\n      proposal.requiredNamespaces,\n      namespaces,\n      \"approve()\",\n    );\n    if (conformingNamespacesError) throw new Error(conformingNamespacesError.message);\n    if (!isValidString(relayProtocol, true)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `approve() relayProtocol: ${relayProtocol}`,\n      );\n      throw new Error(message);\n    }\n\n    if (!isUndefined(sessionProperties)) {\n      this.validateSessionProps(sessionProperties, \"sessionProperties\");\n    }\n\n    if (!isUndefined(scopedProperties)) {\n      this.validateSessionProps(scopedProperties, \"scopedProperties\");\n\n      const approvedNamespaces = new Set(Object.keys(namespaces));\n      const scopedNamespaces = Object.keys(scopedProperties);\n\n      // the approved scoped namespaces must be a subset of the approved namespaces\n      const valid = scopedNamespaces.every((ns) => approvedNamespaces.has(ns));\n      if (!valid) {\n        throw new Error(\n          `Scoped properties must be a subset of approved namespaces, received: ${JSON.stringify(\n            scopedProperties,\n          )}, approved namespaces: ${Array.from(approvedNamespaces).join(\", \")}`,\n        );\n      }\n    }\n  };\n\n  private isValidReject: EnginePrivate[\"isValidReject\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `reject() params: ${params}`);\n      throw new Error(message);\n    }\n    const { id, reason } = params;\n    this.checkRecentlyDeleted(id);\n    await this.isValidProposalId(id);\n    if (!isValidErrorReason(reason)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `reject() reason: ${JSON.stringify(reason)}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  private isValidSessionSettleRequest: EnginePrivate[\"isValidSessionSettleRequest\"] = (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `onSessionSettleRequest() params: ${params}`,\n      );\n      throw new Error(message);\n    }\n    const { relay, controller, namespaces, expiry } = params;\n    if (!isValidRelay(relay)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `onSessionSettleRequest() relay protocol should be a string`,\n      );\n      throw new Error(message);\n    }\n    const validControllerError = isValidController(controller, \"onSessionSettleRequest()\");\n    if (validControllerError) throw new Error(validControllerError.message);\n    const validNamespacesError = isValidNamespaces(namespaces, \"onSessionSettleRequest()\");\n    if (validNamespacesError) throw new Error(validNamespacesError.message);\n    if (isExpired(expiry)) {\n      const { message } = getInternalError(\"EXPIRED\", `onSessionSettleRequest()`);\n      throw new Error(message);\n    }\n  };\n\n  private isValidUpdate: EnginePrivate[\"isValidUpdate\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `update() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic, namespaces } = params;\n\n    this.checkRecentlyDeleted(topic);\n    await this.isValidSessionTopic(topic);\n    const session = this.client.session.get(topic);\n    const validNamespacesError = isValidNamespaces(namespaces, \"update()\");\n    if (validNamespacesError) throw new Error(validNamespacesError.message);\n    const conformingNamespacesError = isConformingNamespaces(\n      session.requiredNamespaces,\n      namespaces,\n      \"update()\",\n    );\n    if (conformingNamespacesError) throw new Error(conformingNamespacesError.message);\n    // TODO(ilja) - check if wallet\n  };\n\n  private isValidExtend: EnginePrivate[\"isValidExtend\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `extend() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic } = params;\n\n    this.checkRecentlyDeleted(topic);\n    await this.isValidSessionTopic(topic);\n  };\n\n  private isValidRequest: EnginePrivate[\"isValidRequest\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `request() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic, request, chainId, expiry } = params;\n    this.checkRecentlyDeleted(topic);\n    await this.isValidSessionTopic(topic);\n    const { namespaces } = this.client.session.get(topic);\n    if (!isValidNamespacesChainId(namespaces, chainId)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `request() chainId: ${chainId}`);\n      throw new Error(message);\n    }\n    if (!isValidRequest(request)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `request() ${JSON.stringify(request)}`,\n      );\n      throw new Error(message);\n    }\n    if (!isValidNamespacesRequest(namespaces, chainId, request.method)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `request() method: ${request.method}`,\n      );\n      throw new Error(message);\n    }\n    if (expiry && !isValidRequestExpiry(expiry, SESSION_REQUEST_EXPIRY_BOUNDARIES)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `request() expiry: ${expiry}. Expiry must be a number (in seconds) between ${SESSION_REQUEST_EXPIRY_BOUNDARIES.min} and ${SESSION_REQUEST_EXPIRY_BOUNDARIES.max}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  private isValidRespond: EnginePrivate[\"isValidRespond\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `respond() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic, response } = params;\n    try {\n      // if the session is already disconnected, we can't respond to the request so we need to delete it\n      await this.isValidSessionTopic(topic);\n    } catch (error) {\n      if (params?.response?.id) this.cleanupAfterResponse(params);\n      throw error;\n    }\n    if (!isValidResponse(response)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `respond() response: ${JSON.stringify(response)}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  private isValidPing: EnginePrivate[\"isValidPing\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `ping() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic } = params;\n    await this.isValidSessionOrPairingTopic(topic);\n  };\n\n  private isValidEmit: EnginePrivate[\"isValidEmit\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `emit() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic, event, chainId } = params;\n    await this.isValidSessionTopic(topic);\n    const { namespaces } = this.client.session.get(topic);\n    if (!isValidNamespacesChainId(namespaces, chainId)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `emit() chainId: ${chainId}`);\n      throw new Error(message);\n    }\n    if (!isValidEvent(event)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `emit() event: ${JSON.stringify(event)}`,\n      );\n      throw new Error(message);\n    }\n    if (!isValidNamespacesEvent(namespaces, chainId, event.name)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `emit() event: ${JSON.stringify(event)}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  private isValidDisconnect: EnginePrivate[\"isValidDisconnect\"] = async (params) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `disconnect() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic } = params;\n    await this.isValidSessionOrPairingTopic(topic);\n  };\n\n  private isValidAuthenticate = (params: AuthTypes.SessionAuthenticateParams) => {\n    const { chains, uri, domain, nonce } = params;\n\n    // ----- validate params ----- //\n    if (!Array.isArray(chains) || chains.length === 0) {\n      throw new Error(\"chains is required and must be a non-empty array\");\n    }\n    if (!isValidString(uri, false)) {\n      throw new Error(\"uri is required parameter\");\n    }\n    if (!isValidString(domain, false)) {\n      throw new Error(\"domain is required parameter\");\n    }\n    if (!isValidString(nonce, false)) {\n      throw new Error(\"nonce is required parameter\");\n    }\n\n    // ----- reject multi namespaces ----- //\n    const uniqueNamespaces = [...new Set(chains.map((chain) => parseChainId(chain).namespace))];\n    if (uniqueNamespaces.length > 1) {\n      throw new Error(\n        \"Multi-namespace requests are not supported. Please request single namespace only.\",\n      );\n    }\n\n    const { namespace } = parseChainId(chains[0]);\n    if (namespace !== \"eip155\") {\n      throw new Error(\n        \"Only eip155 namespace is supported for authenticated sessions. Please use .connect() for non-eip155 chains.\",\n      );\n    }\n  };\n\n  private getVerifyContext = async (params: {\n    attestationId?: string;\n    hash?: string;\n    encryptedId?: string;\n    metadata: CoreTypes.Metadata;\n    transportType?: RelayerTypes.TransportType;\n  }) => {\n    const { attestationId, hash, encryptedId, metadata, transportType } = params;\n    const context: Verify.Context = {\n      verified: {\n        verifyUrl: metadata.verifyUrl || VERIFY_SERVER,\n        validation: \"UNKNOWN\",\n        origin: metadata.url || \"\",\n      },\n    };\n\n    try {\n      if (transportType === TRANSPORT_TYPES.link_mode) {\n        const applink = this.getAppLinkIfEnabled(metadata, transportType);\n        context.verified.validation =\n          applink && new URL(applink).origin === new URL(metadata.url).origin ? \"VALID\" : \"INVALID\";\n        return context;\n      }\n      const result = await this.client.core.verify.resolve({\n        attestationId,\n        hash,\n        encryptedId,\n        verifyUrl: metadata.verifyUrl,\n      });\n      if (result) {\n        context.verified.origin = result.origin;\n        context.verified.isScam = result.isScam;\n        context.verified.validation =\n          result.origin === new URL(metadata.url).origin ? \"VALID\" : \"INVALID\";\n      }\n    } catch (e) {\n      this.client.logger.warn(e);\n    }\n\n    this.client.logger.debug(`Verify context: ${JSON.stringify(context)}`);\n    return context;\n  };\n\n  private validateSessionProps = (properties: SessionTypes.ScopedProperties, type: string) => {\n    Object.values(properties).forEach((property, index) => {\n      if (property === null || property === undefined) {\n        const { message } = getInternalError(\n          \"MISSING_OR_INVALID\",\n          `${type} must contain an existing value for each key. Received: ${property} for key ${\n            Object.keys(properties)[index]\n          }`,\n        );\n        throw new Error(message);\n      }\n    });\n  };\n\n  private getPendingAuthRequest = (id: number) => {\n    const request = this.client.auth.requests.get(id);\n    return typeof request === \"object\" ? request : undefined;\n  };\n\n  private addToRecentlyDeleted = (\n    id: string | number,\n    type: \"pairing\" | \"session\" | \"proposal\" | \"request\",\n  ) => {\n    this.recentlyDeletedMap.set(id, type);\n    // remove first half of the map if it exceeds the limit\n    if (this.recentlyDeletedMap.size >= this.recentlyDeletedLimit) {\n      let i = 0;\n      const numItemsToDelete = this.recentlyDeletedLimit / 2;\n      for (const k of this.recentlyDeletedMap.keys()) {\n        if (i++ >= numItemsToDelete) {\n          break;\n        }\n        this.recentlyDeletedMap.delete(k);\n      }\n    }\n  };\n\n  private checkRecentlyDeleted = (id: string | number) => {\n    const deletedRecord = this.recentlyDeletedMap.get(id);\n    if (deletedRecord) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `Record was recently deleted - ${deletedRecord}: ${id}`,\n      );\n      throw new Error(message);\n    }\n  };\n\n  private isLinkModeEnabled = (\n    peerMetadata?: CoreTypes.Metadata,\n    transportType?: RelayerTypes.TransportType,\n  ): boolean => {\n    if (!peerMetadata || transportType !== TRANSPORT_TYPES.link_mode) return false;\n\n    return (\n      this.client.metadata?.redirect?.linkMode === true &&\n      this.client.metadata?.redirect?.universal !== undefined &&\n      this.client.metadata?.redirect?.universal !== \"\" &&\n      peerMetadata?.redirect?.universal !== undefined &&\n      peerMetadata?.redirect?.universal !== \"\" &&\n      peerMetadata?.redirect?.linkMode === true &&\n      this.client.core.linkModeSupportedApps.includes(peerMetadata.redirect.universal) &&\n      typeof (global as any)?.Linking !== \"undefined\"\n    );\n  };\n\n  private getAppLinkIfEnabled = (\n    peerMetadata?: CoreTypes.Metadata,\n    transportType?: RelayerTypes.TransportType,\n  ): string | undefined => {\n    return this.isLinkModeEnabled(peerMetadata, transportType)\n      ? peerMetadata?.redirect?.universal\n      : undefined;\n  };\n\n  private handleLinkModeMessage = ({ url }: { url: string }) => {\n    if (!url || !url.includes(\"wc_ev\") || !url.includes(\"topic\")) return;\n\n    const topic = getSearchParamFromURL(url, \"topic\") || \"\";\n    const message = decodeURIComponent(getSearchParamFromURL(url, \"wc_ev\") || \"\");\n\n    const sessionExists = this.client.session.keys.includes(topic);\n\n    if (sessionExists) {\n      this.client.session.update(topic, { transportType: TRANSPORT_TYPES.link_mode });\n    }\n\n    this.client.core.dispatchEnvelope({ topic, message, sessionExists });\n  };\n\n  private registerLinkModeListeners = async () => {\n    if (isTestRun() || (isReactNative() && this.client.metadata.redirect?.linkMode)) {\n      const linking = (global as any)?.Linking;\n      // global.Linking is set by react-native-compat\n      if (typeof linking !== \"undefined\") {\n        // set URL listener\n        linking.addEventListener(\"url\", this.handleLinkModeMessage, this.client.name);\n\n        // check for initial URL -> cold boots\n        const initialUrl = await linking.getInitialURL();\n        if (initialUrl) {\n          // wait to process the message to allow event listeners to be registered by the implementing app\n          setTimeout(() => {\n            this.handleLinkModeMessage({ url: initialUrl });\n          }, 50);\n        }\n      }\n    }\n  };\n\n  private shouldSetTVF = (\n    protocolMethod: JsonRpcTypes.WcMethod,\n    params: JsonRpcTypes.RequestParams[\"wc_sessionRequest\"],\n  ) => {\n    if (!params) return false;\n    if (protocolMethod !== \"wc_sessionRequest\") return false;\n    const { request } = params;\n    return Object.keys(TVF_METHODS).includes(request.method);\n  };\n\n  private getTVFParams = (\n    id: number,\n    params: JsonRpcTypes.RequestParams[\"wc_sessionRequest\"],\n    result?: any,\n  ) => {\n    try {\n      const requestMethod = params.request.method;\n      const txHashes = this.extractTxHashesFromResult(requestMethod, result);\n      const tvf: RelayerTypes.ITVF = {\n        correlationId: id,\n        rpcMethods: [requestMethod],\n        chainId: params.chainId,\n        ...(this.isValidContractData(params.request.params) && {\n          // initially only get contractAddresses from EVM txs\n          contractAddresses: [params.request.params?.[0]?.to],\n        }),\n        txHashes,\n      };\n      return tvf;\n    } catch (e) {\n      this.client.logger.warn(\"Error getting TVF params\", e);\n    }\n    return {};\n  };\n\n  private isValidContractData = (params: any) => {\n    if (!params) return false;\n    try {\n      const data = params?.data || params?.[0]?.data;\n\n      if (!data.startsWith(\"0x\")) return false;\n\n      const hexPart = data.slice(2);\n      if (!/^[0-9a-fA-F]*$/.test(hexPart)) return false;\n\n      return hexPart.length % 2 === 0;\n    } catch (e) {}\n    return false;\n  };\n\n  private extractTxHashesFromResult = (method: string, result: any): string[] => {\n    try {\n      const methodConfig = TVF_METHODS[method as keyof typeof TVF_METHODS];\n      // result = 0x...\n      if (typeof result === \"string\") {\n        return [result];\n      }\n\n      // result = { key: [0x...] } or { key: 0x... }\n      const hashes: string[] = result[methodConfig.key];\n\n      // result = { key: [0x...] }\n      if (isValidArray(hashes)) {\n        if (method === \"solana_signAllTransactions\") {\n          return hashes.map((hash) => extractSolanaTransactionId(hash));\n        }\n\n        return hashes;\n\n        // result = { key: 0x... }\n      } else if (typeof hashes === \"string\") {\n        return [hashes];\n      }\n    } catch (e) {\n      this.client.logger.warn(\"Error extracting tx hashes from result\", e);\n    }\n    return [];\n  };\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { ICore, ProposalTypes } from \"@walletconnect/types\";\n\nimport { SIGN_CLIENT_STORAGE_PREFIX, PROPOSAL_CONTEXT } from \"../constants\";\n\nexport class Proposal extends Store<number, ProposalTypes.Struct> {\n  constructor(\n    public core: I<PERSON>ore,\n    public logger: Logger,\n  ) {\n    super(core, logger, PROPOSAL_CONTEXT, SIGN_CLIENT_STORAGE_PREFIX);\n  }\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { ICore, SessionTypes } from \"@walletconnect/types\";\n\nimport { SIGN_CLIENT_STORAGE_PREFIX, SESSION_CONTEXT } from \"../constants\";\n\nexport class Session extends Store<string, SessionTypes.Struct> {\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    super(core, logger, SESSION_CONTEXT, SIGN_CLIENT_STORAGE_PREFIX);\n  }\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { ICore, PendingRequestTypes } from \"@walletconnect/types\";\nimport { REQUEST_CONTEXT, SIGN_CLIENT_STORAGE_PREFIX } from \"../constants\";\n\nexport class PendingRequest extends Store<number, PendingRequestTypes.Struct> {\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    super(\n      core,\n      logger,\n      REQUEST_CONTEXT,\n      SIGN_CLIENT_STORAGE_PREFIX,\n      (val: PendingRequestTypes.Struct) => val.id,\n    );\n  }\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { ICore } from \"@walletconnect/types\";\n\nimport { AUTH_KEYS_CONTEXT, AUTH_STORAGE_PREFIX, AUTH_PUBLIC_KEY_NAME } from \"../constants\";\n\nexport class Auth<PERSON><PERSON> extends Store<string, { responseTopic: string; publicKey: string }> {\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    super(core, logger, AUTH_KEYS_CONTEXT, AUTH_STORAGE_PREFIX, () => AUTH_PUBLIC_KEY_NAME);\n  }\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { ICore } from \"@walletconnect/types\";\n\nimport { AUTH_PAIRING_TOPIC_CONTEXT, AUTH_STORAGE_PREFIX } from \"../constants\";\n\nexport class AuthPairingTopic extends Store<string, { topic: string; pairingTopic: string }> {\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    super(core, logger, AUTH_PAIRING_TOPIC_CONTEXT, AUTH_STORAGE_PREFIX);\n  }\n}\n", "import { Store } from \"@walletconnect/core\";\nimport { Logger } from \"@walletconnect/logger\";\nimport { AuthTypes, ICore } from \"@walletconnect/types\";\n\nimport { AUTH_STORAGE_PREFIX, AUTH_REQUEST_CONTEXT } from \"../constants\";\n\nexport class AuthRequest extends Store<number, AuthTypes.PendingRequest> {\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    super(\n      core,\n      logger,\n      AUTH_REQUEST_CONTEXT,\n      AUTH_STORAGE_PREFIX,\n      (val: AuthTypes.PendingRequest) => val.id,\n    );\n  }\n}\n", "import { Logger } from \"@walletconnect/logger\";\nimport { <PERSON>Auth, ICore } from \"@walletconnect/types\";\nimport { AuthPairingTopic } from \"./authPairingTopic\";\nimport { AuthRequest } from \"./authRequest\";\nimport { AuthKey } from \"./authKey\";\n\nexport class AuthStore {\n  public authKeys: IAuth[\"authKeys\"];\n  public pairingTopics: IAuth[\"pairingTopics\"];\n  public requests: IAuth[\"requests\"];\n\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    this.authKeys = new AuthKey(this.core, this.logger);\n    this.pairingTopics = new AuthPairingTopic(this.core, this.logger);\n    this.requests = new AuthRequest(this.core, this.logger);\n  }\n\n  public async init() {\n    await this.authKeys.init();\n    await this.pairingTopics.init();\n    await this.requests.init();\n  }\n}\n", "import { Core } from \"@walletconnect/core\";\nimport {\n  generateChildLogger,\n  getDefaultLoggerOptions,\n  getLoggerContext,\n  pino,\n} from \"@walletconnect/logger\";\nimport { SignClientTypes, ISignClient, ISignClientEvents, EngineTypes } from \"@walletconnect/types\";\nimport { ONE_SECOND, toMiliseconds } from \"@walletconnect/time\";\nimport { populateAppMetadata } from \"@walletconnect/utils\";\nimport { EventEmitter } from \"events\";\nimport { SIGN_CLIENT_DEFAULT, SIGN_CLIENT_PROTOCOL, SIGN_CLIENT_VERSION } from \"./constants\";\nimport { AuthStore, Engine, PendingRequest, Proposal, Session } from \"./controllers\";\n\nexport class SignClient extends ISignClient {\n  public readonly protocol = SIGN_CLIENT_PROTOCOL;\n  public readonly version = SIGN_CLIENT_VERSION;\n  public readonly name: ISignClient[\"name\"] = SIGN_CLIENT_DEFAULT.name;\n  public readonly metadata: ISignClient[\"metadata\"];\n\n  public core: ISignClient[\"core\"];\n  public logger: ISignClient[\"logger\"];\n  public events: ISignClient[\"events\"] = new EventEmitter();\n  public engine: ISignClient[\"engine\"];\n  public session: ISignClient[\"session\"];\n  public proposal: ISignClient[\"proposal\"];\n  public pendingRequest: ISignClient[\"pendingRequest\"];\n  public auth: ISignClient[\"auth\"];\n  public signConfig?: ISignClient[\"signConfig\"];\n\n  static async init(opts?: SignClientTypes.Options) {\n    const client = new SignClient(opts);\n    await client.initialize();\n\n    return client;\n  }\n\n  constructor(opts?: SignClientTypes.Options) {\n    super(opts);\n\n    this.name = opts?.name || SIGN_CLIENT_DEFAULT.name;\n    this.metadata = populateAppMetadata(opts?.metadata);\n    this.signConfig = opts?.signConfig;\n\n    const logger =\n      typeof opts?.logger !== \"undefined\" && typeof opts?.logger !== \"string\"\n        ? opts.logger\n        : pino(getDefaultLoggerOptions({ level: opts?.logger || SIGN_CLIENT_DEFAULT.logger }));\n\n    this.core = opts?.core || new Core(opts);\n    this.logger = generateChildLogger(logger, this.name);\n    this.session = new Session(this.core, this.logger);\n    this.proposal = new Proposal(this.core, this.logger);\n    this.pendingRequest = new PendingRequest(this.core, this.logger);\n    this.engine = new Engine(this);\n    this.auth = new AuthStore(this.core, this.logger);\n  }\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  get pairing() {\n    return this.core.pairing.pairings;\n  }\n\n  // ---------- Events ----------------------------------------------- //\n\n  public on: ISignClientEvents[\"on\"] = (name, listener) => {\n    return this.events.on(name, listener);\n  };\n\n  public once: ISignClientEvents[\"once\"] = (name, listener) => {\n    return this.events.once(name, listener);\n  };\n\n  public off: ISignClientEvents[\"off\"] = (name, listener) => {\n    return this.events.off(name, listener);\n  };\n\n  public removeListener: ISignClientEvents[\"removeListener\"] = (name, listener) => {\n    return this.events.removeListener(name, listener);\n  };\n\n  public removeAllListeners: ISignClientEvents[\"removeAllListeners\"] = (name) => {\n    return this.events.removeAllListeners(name);\n  };\n\n  // ---------- Engine ----------------------------------------------- //\n\n  public connect: ISignClient[\"connect\"] = async (params) => {\n    try {\n      return await this.engine.connect(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public pair: ISignClient[\"pair\"] = async (params) => {\n    try {\n      return await this.engine.pair(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public approve: ISignClient[\"approve\"] = async (params) => {\n    try {\n      return await this.engine.approve(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public reject: ISignClient[\"reject\"] = async (params) => {\n    try {\n      return await this.engine.reject(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public update: ISignClient[\"update\"] = async (params) => {\n    try {\n      return await this.engine.update(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public extend: ISignClient[\"extend\"] = async (params) => {\n    try {\n      return await this.engine.extend(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public request: ISignClient[\"request\"] = async <T>(params: EngineTypes.RequestParams) => {\n    try {\n      return await this.engine.request<T>(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public respond: ISignClient[\"respond\"] = async (params) => {\n    try {\n      return await this.engine.respond(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public ping: ISignClient[\"ping\"] = async (params) => {\n    try {\n      return await this.engine.ping(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public emit: ISignClient[\"emit\"] = async (params) => {\n    try {\n      return await this.engine.emit(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public disconnect: ISignClient[\"disconnect\"] = async (params) => {\n    try {\n      return await this.engine.disconnect(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public find: ISignClient[\"find\"] = (params) => {\n    try {\n      return this.engine.find(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public getPendingSessionRequests: ISignClient[\"getPendingSessionRequests\"] = () => {\n    try {\n      return this.engine.getPendingSessionRequests();\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public authenticate: ISignClient[\"authenticate\"] = async (params, walletUniversalLink) => {\n    try {\n      return await this.engine.authenticate(params, walletUniversalLink);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public formatAuthMessage: ISignClient[\"formatAuthMessage\"] = (params) => {\n    try {\n      return this.engine.formatAuthMessage(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public approveSessionAuthenticate: ISignClient[\"approveSessionAuthenticate\"] = async (params) => {\n    try {\n      return await this.engine.approveSessionAuthenticate(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  public rejectSessionAuthenticate: ISignClient[\"rejectSessionAuthenticate\"] = async (params) => {\n    try {\n      return await this.engine.rejectSessionAuthenticate(params);\n    } catch (error: any) {\n      this.logger.error(error.message);\n      throw error;\n    }\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async initialize() {\n    this.logger.trace(`Initialized`);\n    try {\n      await this.core.start();\n      await this.session.init();\n      await this.proposal.init();\n      await this.pendingRequest.init();\n      await this.auth.init();\n      await this.engine.init();\n      this.logger.info(`SignClient Initialization Success`);\n      setTimeout(() => {\n        this.engine.processRelayMessageCache();\n      }, toMiliseconds(ONE_SECOND));\n    } catch (error: any) {\n      this.logger.info(`SignClient Initialization Failure`);\n      this.logger.error(error.message);\n      throw error;\n    }\n  }\n}\n", "import { SignClient as Client } from \"./client\";\nimport { Session } from \"./controllers/session\";\nexport * from \"./constants\";\n\nexport const SessionStore = Session;\nexport const SignClient = Client;\nexport default Client;\n"], "names": ["THIRTY_DAYS", "SEVEN_DAYS", "FIVE_MINUTES", "ONE_DAY", "ONE_HOUR", "IEngine", "client", "__publicField", "ENGINE_CONTEXT", "EventEmmiter", "ENGINE_QUEUE_STATES", "ONE_SECOND", "ENGINE_RPC_OPTS", "toMiliseconds", "params", "connectParams", "__spreadProps", "__spreadValues", "mergeRequiredAndOptionalNamespaces", "pairingTopic", "requiredNamespaces", "optionalNamespaces", "sessionProperties", "scopedProperties", "relays", "topic", "uri", "active", "pairing", "error", "newTopic", "newUri", "message", "getInternalError", "public<PERSON>ey", "expiry", "expiryTimestamp", "calcExpiry", "proposal", "RELAYER_DEFAULT_PROTOCOL", "payloadId", "sessionConnectTarget", "engineEvent", "reject", "resolve", "approval", "createDelayedPromise", "PROPOSAL_EXPIRY_MESSAGE", "proposalExpireHandler", "id", "session", "_a", "_b", "_c", "configEvent", "EVENT_CLIENT_SESSION_TRACES", "EVENT_CLIENT_SESSION_ERRORS", "relayProtocol", "namespaces", "sessionConfig", "proposer", "event", "selfPublicKey", "peerPublic<PERSON>ey", "sessionTopic", "<PERSON><PERSON><PERSON><PERSON>", "SESSION_EXPIRY", "transportType", "TRANSPORT_TYPES", "getSdkError", "reason", "acknowledged", "clientRpcId", "relayRpcId", "getBigIntRpcId", "oldNamespaces", "e", "chainId", "request", "done", "result", "protocolMethod", "appLink", "protocolRequestParams", "shouldSetTVF", "wcDeepLink", "getDeepLink", "WALLETCONNECT_DEEPLINK_CHOICE", "handleDeeplinkRedirect", "response", "isJsonRpcResult", "isJsonRpcError", "isSessionCompatible", "walletUniversalLink", "isLinkMode", "chains", "statement", "domain", "nonce", "type", "exp", "nbf", "methods", "resources", "connectionUri", "responseTopic", "hash<PERSON><PERSON>", "AUTH_PUBLIC_KEY_NAME", "namespace", "parseChainId", "recap", "createEncodedRecap", "getRecapFromResources", "mergeEncodedRecaps", "authRequestExpiry", "authenticateId", "sessionConnectEventTarget", "authenticateEventTarget", "onSessionConnect", "onAuthenticate", "payload", "cacaos", "responder", "approvedMethods", "approvedAccounts", "cacao", "validateSignedCacao", "<PERSON><PERSON><PERSON><PERSON>", "getNamespacedDidChainId", "parsed<PERSON><PERSON><PERSON>", "get<PERSON>id<PERSON><PERSON><PERSON>", "methodsfromRecap", "getMethodsFromRecap", "chainsFromRecap", "getChainsFromRecap", "chain", "buildNamespacesFromAuth", "linkModeURL", "formatJsonRpcRequest", "TYPE_2", "BASE64URL", "getLinkModeURL", "sessionAuthenticateResponseParams", "auths", "EVENT_CLIENT_AUTHENTICATE_TRACES", "EVENT_CLIENT_AUTHENTICATE_ERRORS", "pendingRequest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeOpts", "TYPE_1", "invalidErr", "iss", "formatMessage", "duplicates", "p", "expirer<PERSON><PERSON><PERSON><PERSON><PERSON>", "emitEvent", "self", "r", "verifyContext", "args", "method", "throwOnFailedPublish", "tvf", "encoding", "BASE64", "attestation", "METHODS_TO_VERIFY", "decryptedId", "hashMessage", "opts", "redirectURL", "formatJsonRpcResult", "record", "rpcOpts", "formatJsonRpcError", "sessionTopics", "proposalIds", "toCleanup", "isExpired", "encryptedId", "req<PERSON><PERSON><PERSON>", "resMethod", "requestMethod", "expectedMethods", "EVENT_CLIENT_PAIRING_ERRORS", "EVENT_CLIENT_PAIRING_TRACES", "err", "subscriptionId", "target", "relay", "controller", "pendingSession", "s", "<PERSON><PERSON><PERSON>", "lastSessionUpdateId", "MemoryStore", "lastId", "currentId", "_topic", "RELAYER_EVENTS", "requester", "authPayload", "pendingRequests", "isValidParams", "isUndefined", "isValidRelays", "isValidObject", "warning", "requestedNamespaces", "ns", "validRequiredNamespacesError", "isValidRequiredNamespaces", "validNamespacesError", "isValidNamespaces", "conformingNamespacesError", "isConformingNamespaces", "isValidString", "approvedNamespaces", "isValidErrorReason", "is<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "validControllerError", "isValidController", "isValidNamespacesChainId", "isValidRequest", "isValidNamespacesRequest", "isValidRequestExpiry", "SESSION_REQUEST_EXPIRY_BOUNDARIES", "isValidResponse", "isValidEvent", "isValidNamespacesEvent", "attestationId", "hash", "metadata", "context", "VERIFY_SERVER", "applink", "properties", "property", "index", "i", "numItemsToDelete", "k", "deletedRecord", "peerMetadata", "_d", "_e", "_f", "_g", "_h", "_i", "url", "getSearchParamFromURL", "sessionExists", "isTestRun", "isReactNative", "linking", "initialUrl", "TVF_METHODS", "txHashes", "data", "hexPart", "methodConfig", "hashes", "isValidArray", "extractSolanaTransactionId", "topics", "pendingMessages", "messages", "isJsonRpcRequest", "isJsonRpcResponse", "EXPIRER_EVENTS", "parseExpirer<PERSON>arget", "PAIRING_EVENTS", "isValidId", "Store", "core", "logger", "PROPOSAL_CONTEXT", "SIGN_CLIENT_STORAGE_PREFIX", "SESSION_CONTEXT", "REQUEST_CONTEXT", "val", "AUTH_KEYS_CONTEXT", "AUTH_STORAGE_PREFIX", "AUTH_PAIRING_TOPIC_CONTEXT", "AUTH_REQUEST_CONTEXT", "o", "<PERSON><PERSON><PERSON><PERSON>", "AuthPairingTopic", "AuthRequest", "SignClient", "ISignClient", "SIGN_CLIENT_PROTOCOL", "SIGN_CLIENT_VERSION", "SIGN_CLIENT_DEFAULT", "EventEmitter", "name", "listener", "populateAppMetadata", "pino", "getDefaultLoggerOptions", "Core", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Session", "Proposal", "PendingRequest", "Engine", "AuthStore", "getLoggerContext", "Client"], "mappings": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "debugId": null}}]}