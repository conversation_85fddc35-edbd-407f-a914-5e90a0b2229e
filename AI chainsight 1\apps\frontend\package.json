{"name": "@chainsight/frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .next"}, "dependencies": {"next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "framer-motion": "^10.16.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "lucide-react": "^0.292.0", "axios": "^1.6.0", "web3": "^4.2.0", "@web3modal/wagmi": "^3.5.0", "wagmi": "^1.4.0", "viem": "^1.19.0", "recharts": "^2.8.0", "react-webcam": "^7.1.0", "react-dropzone": "^14.2.0", "react-hot-toast": "^2.4.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}}