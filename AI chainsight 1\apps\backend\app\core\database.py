from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Boolean, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.sql import func
from typing import Generator
import os

from .config import settings

# Create SQLAlchemy engine
if settings.DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        settings.DATABASE_URL,
        connect_args={"check_same_thread": False},
        echo=False
    )
else:
    engine = create_engine(settings.DATABASE_URL, echo=False)

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class
Base = declarative_base()

# Database Models
class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class Task(Base):
    __tablename__ = "tasks"
    
    id = Column(String, primary_key=True, index=True)
    task_type = Column(String, nullable=False)
    status = Column(String, default="pending")
    payload = Column(Text)
    result = Column(Text)
    error = Column(Text)
    priority = Column(Integer, default=0)
    retries = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    user_id = Column(Integer, nullable=True)

class TokenDeployment(Base):
    __tablename__ = "token_deployments"
    
    id = Column(Integer, primary_key=True, index=True)
    contract_address = Column(String, unique=True, index=True)
    transaction_hash = Column(String, unique=True)
    token_name = Column(String, nullable=False)
    token_symbol = Column(String, nullable=False)
    total_supply = Column(String, nullable=False)
    decimals = Column(Integer, default=18)
    deployer_address = Column(String, nullable=False)
    network = Column(String, default="localhost")
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class ChatMessage(Base):
    __tablename__ = "chat_messages"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String, index=True)
    message = Column(Text, nullable=False)
    response = Column(Text, nullable=False)
    user_id = Column(Integer, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class FaceScanResult(Base):
    __tablename__ = "face_scan_results"
    
    id = Column(Integer, primary_key=True, index=True)
    file_path = Column(String, nullable=False)
    faces_detected = Column(Integer, default=0)
    confidence_scores = Column(Text)  # JSON string
    landmarks = Column(Text)  # JSON string
    emotions = Column(Text)  # JSON string
    user_id = Column(Integer, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class FinanceAnalysis(Base):
    __tablename__ = "finance_analysis"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String, nullable=False)
    analysis_type = Column(String, nullable=False)  # 'prediction', 'sentiment', 'technical'
    input_data = Column(Text)  # JSON string
    result = Column(Text)  # JSON string
    confidence = Column(Float)
    user_id = Column(Integer, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class DocumentAnalysis(Base):
    __tablename__ = "document_analysis"
    
    id = Column(Integer, primary_key=True, index=True)
    file_path = Column(String, nullable=False)
    document_type = Column(String, nullable=False)  # 'resume', 'contract', 'legal'
    extracted_text = Column(Text)
    analysis_result = Column(Text)  # JSON string
    confidence = Column(Float)
    user_id = Column(Integer, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

# Dependency to get database session
def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Database utility functions
def create_tables():
    """Create all database tables"""
    Base.metadata.create_all(bind=engine)

def drop_tables():
    """Drop all database tables"""
    Base.metadata.drop_all(bind=engine)

def reset_database():
    """Reset database by dropping and recreating all tables"""
    drop_tables()
    create_tables()

# Initialize database
def init_db():
    """Initialize database with default data"""
    create_tables()
    
    # Create default superuser if it doesn't exist
    db = SessionLocal()
    try:
        from .security import get_password_hash
        
        # Check if superuser exists
        superuser = db.query(User).filter(User.email == "<EMAIL>").first()
        if not superuser:
            superuser = User(
                email="<EMAIL>",
                username="admin",
                hashed_password=get_password_hash("admin123"),
                is_superuser=True,
                is_active=True
            )
            db.add(superuser)
            db.commit()
            print("Default superuser created: <EMAIL> / admin123")
    
    except Exception as e:
        print(f"Error creating default superuser: {e}")
    finally:
        db.close()

# Database health check
def check_db_health() -> bool:
    """Check if database is healthy"""
    try:
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        return True
    except Exception:
        return False
