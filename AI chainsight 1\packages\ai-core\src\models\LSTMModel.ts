import * as tf from '@tensorflow/tfjs-node';
import { ModelConfig, TrainingData, ModelMetrics } from '../types';

export class LSTMModel {
  private model: tf.Sequential | null = null;
  private isInitialized = false;
  private config: ModelConfig | null = null;

  async initialize(): Promise<void> {
    console.log('Initializing LSTM Model...');
    this.isInitialized = true;
  }

  async train(
    trainData: TrainingData,
    valData: TrainingData,
    config: ModelConfig
  ): Promise<void> {
    try {
      console.log('Training LSTM model...');
      this.config = config;

      // Build model architecture
      this.model = this.buildModel(config);

      // Prepare tensors
      const trainX = tf.tensor3d(trainData.features);
      const trainY = tf.tensor2d(trainData.targets);
      const valX = tf.tensor3d(valData.features);
      const valY = tf.tensor2d(valData.targets);

      // Compile model
      this.model.compile({
        optimizer: tf.train.adam(config.hyperparameters.learning_rate),
        loss: 'meanSquaredError',
        metrics: ['mae']
      });

      // Train model
      const history = await this.model.fit(trainX, trainY, {
        epochs: config.hyperparameters.epochs,
        batchSize: config.hyperparameters.batch_size,
        validationData: [valX, valY],
        verbose: 1,
        callbacks: {
          onEpochEnd: (epoch, logs) => {
            console.log(`Epoch ${epoch + 1}: loss=${logs?.loss?.toFixed(4)}, val_loss=${logs?.val_loss?.toFixed(4)}`);
          }
        }
      });

      // Clean up tensors
      trainX.dispose();
      trainY.dispose();
      valX.dispose();
      valY.dispose();

      console.log('LSTM model training completed');

    } catch (error) {
      console.error('LSTM training failed:', error);
      throw error;
    }
  }

  async predict(processedData: any): Promise<{
    predictions: any[];
    confidence: number;
  }> {
    if (!this.model) {
      throw new Error('Model not trained');
    }

    try {
      // Prepare input tensor
      const inputTensor = tf.tensor3d([processedData.features.slice(-60)]);
      
      // Make prediction
      const prediction = this.model.predict(inputTensor) as tf.Tensor;
      const predictionData = await prediction.data();

      // Generate time series predictions
      const predictions = [];
      const baseTimestamp = Date.now();
      
      for (let i = 0; i < predictionData.length; i++) {
        predictions.push({
          timestamp: baseTimestamp + (i + 1) * 24 * 60 * 60 * 1000, // Daily predictions
          predicted_price: predictionData[i],
          confidence: Math.random() * 0.3 + 0.7 // Simulate confidence 0.7-1.0
        });
      }

      // Clean up tensors
      inputTensor.dispose();
      prediction.dispose();

      return {
        predictions,
        confidence: 0.85
      };

    } catch (error) {
      console.error('LSTM prediction failed:', error);
      throw error;
    }
  }

  private buildModel(config: ModelConfig): tf.Sequential {
    const model = tf.sequential();

    // Input layer
    model.add(tf.layers.lstm({
      units: config.hyperparameters.hidden_units || 50,
      returnSequences: true,
      inputShape: [config.sequence_length, config.features.length],
      dropout: config.hyperparameters.dropout_rate
    }));

    // Additional LSTM layers
    for (let i = 1; i < (config.hyperparameters.num_layers || 2); i++) {
      model.add(tf.layers.lstm({
        units: config.hyperparameters.hidden_units || 50,
        returnSequences: i < (config.hyperparameters.num_layers || 2) - 1,
        dropout: config.hyperparameters.dropout_rate
      }));
    }

    // Dense layers
    model.add(tf.layers.dense({ units: 25, activation: 'relu' }));
    model.add(tf.layers.dropout({ rate: config.hyperparameters.dropout_rate }));
    model.add(tf.layers.dense({ units: config.prediction_horizon }));

    return model;
  }

  async save(path: string): Promise<void> {
    if (!this.model) {
      throw new Error('No model to save');
    }

    try {
      await this.model.save(`file://${path}`);
      console.log(`LSTM model saved to ${path}`);
    } catch (error) {
      console.error('Failed to save LSTM model:', error);
      throw error;
    }
  }

  async load(path: string): Promise<void> {
    try {
      this.model = await tf.loadLayersModel(`file://${path}/model.json`) as tf.Sequential;
      console.log(`LSTM model loaded from ${path}`);
    } catch (error) {
      console.error('Failed to load LSTM model:', error);
      throw error;
    }
  }

  dispose(): void {
    if (this.model) {
      this.model.dispose();
      this.model = null;
    }
  }

  getModel(): tf.Sequential | null {
    return this.model;
  }

  isModelTrained(): boolean {
    return this.model !== null;
  }
}
