(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@rainbow-me/rainbowkit/dist/base-OAXLRA4F.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>base_default)
});
"use client";
// src/components/RainbowKitProvider/chainIcons/base.svg
var base_default = "data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2228%22%20height%3D%2228%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cpath%20fill%3D%22%230052FF%22%20fill-rule%3D%22nonzero%22%20d%3D%22M14%2028a14%2014%200%201%200%200-28%2014%2014%200%200%200%200%2028Z%22%2F%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M13.967%2023.86c5.445%200%209.86-4.415%209.86-9.86%200-5.445-4.415-9.86-9.86-9.86-5.166%200-9.403%203.974-9.825%209.03h14.63v1.642H4.142c.413%205.065%204.654%209.047%209.826%209.047Z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E";
;
}}),
}]);

//# sourceMappingURL=node_modules_%40rainbow-me_rainbowkit_dist_base-OAXLRA4F_c1fbb2e8.js.map