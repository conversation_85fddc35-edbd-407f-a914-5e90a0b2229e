{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/stream.js"], "sourcesContent": ["'use strict';\n\nconst { Duplex } = require('stream');\n\n/**\n * Emits the `'close'` event on a stream.\n *\n * @param {Duplex} stream The stream.\n * @private\n */\nfunction emitClose(stream) {\n  stream.emit('close');\n}\n\n/**\n * The listener of the `'end'` event.\n *\n * @private\n */\nfunction duplexOnEnd() {\n  if (!this.destroyed && this._writableState.finished) {\n    this.destroy();\n  }\n}\n\n/**\n * The listener of the `'error'` event.\n *\n * @param {Error} err The error\n * @private\n */\nfunction duplexOnError(err) {\n  this.removeListener('error', duplexOnError);\n  this.destroy();\n  if (this.listenerCount('error') === 0) {\n    // Do not suppress the throwing behavior.\n    this.emit('error', err);\n  }\n}\n\n/**\n * Wraps a `WebSocket` in a duplex stream.\n *\n * @param {WebSocket} ws The `WebSocket` to wrap\n * @param {Object} [options] The options for the `Duplex` constructor\n * @return {Duplex} The duplex stream\n * @public\n */\nfunction createWebSocketStream(ws, options) {\n  let terminateOnD<PERSON>roy = true;\n\n  const duplex = new Duplex({\n    ...options,\n    autoDestroy: false,\n    emitClose: false,\n    objectMode: false,\n    writableObjectMode: false\n  });\n\n  ws.on('message', function message(msg, isBinary) {\n    const data =\n      !isBinary && duplex._readableState.objectMode ? msg.toString() : msg;\n\n    if (!duplex.push(data)) ws.pause();\n  });\n\n  ws.once('error', function error(err) {\n    if (duplex.destroyed) return;\n\n    // Prevent `ws.terminate()` from being called by `duplex._destroy()`.\n    //\n    // - If the `'error'` event is emitted before the `'open'` event, then\n    //   `ws.terminate()` is a noop as no socket is assigned.\n    // - Otherwise, the error is re-emitted by the listener of the `'error'`\n    //   event of the `Receiver` object. The listener already closes the\n    //   connection by calling `ws.close()`. This allows a close frame to be\n    //   sent to the other peer. If `ws.terminate()` is called right after this,\n    //   then the close frame might not be sent.\n    terminateOnDestroy = false;\n    duplex.destroy(err);\n  });\n\n  ws.once('close', function close() {\n    if (duplex.destroyed) return;\n\n    duplex.push(null);\n  });\n\n  duplex._destroy = function (err, callback) {\n    if (ws.readyState === ws.CLOSED) {\n      callback(err);\n      process.nextTick(emitClose, duplex);\n      return;\n    }\n\n    let called = false;\n\n    ws.once('error', function error(err) {\n      called = true;\n      callback(err);\n    });\n\n    ws.once('close', function close() {\n      if (!called) callback(err);\n      process.nextTick(emitClose, duplex);\n    });\n\n    if (terminateOnDestroy) ws.terminate();\n  };\n\n  duplex._final = function (callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once('open', function open() {\n        duplex._final(callback);\n      });\n      return;\n    }\n\n    // If the value of the `_socket` property is `null` it means that `ws` is a\n    // client websocket and the handshake failed. In fact, when this happens, a\n    // socket is never assigned to the websocket. Wait for the `'error'` event\n    // that will be emitted by the websocket.\n    if (ws._socket === null) return;\n\n    if (ws._socket._writableState.finished) {\n      callback();\n      if (duplex._readableState.endEmitted) duplex.destroy();\n    } else {\n      ws._socket.once('finish', function finish() {\n        // `duplex` is not destroyed here because the `'end'` event will be\n        // emitted on `duplex` after this `'finish'` event. The EOF signaling\n        // `null` chunk is, in fact, pushed when the websocket emits `'close'`.\n        callback();\n      });\n      ws.close();\n    }\n  };\n\n  duplex._read = function () {\n    if (ws.isPaused) ws.resume();\n  };\n\n  duplex._write = function (chunk, encoding, callback) {\n    if (ws.readyState === ws.CONNECTING) {\n      ws.once('open', function open() {\n        duplex._write(chunk, encoding, callback);\n      });\n      return;\n    }\n\n    ws.send(chunk, callback);\n  };\n\n  duplex.on('end', duplexOnEnd);\n  duplex.on('error', duplexOnError);\n  return duplex;\n}\n\nmodule.exports = createWebSocketStream;\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,EAAE,MAAM,EAAE;AAEhB;;;;;CAKC,GACD,SAAS,UAAU,MAAM;IACvB,OAAO,IAAI,CAAC;AACd;AAEA;;;;CAIC,GACD,SAAS;IACP,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;QACnD,IAAI,CAAC,OAAO;IACd;AACF;AAEA;;;;;CAKC,GACD,SAAS,cAAc,GAAG;IACxB,IAAI,CAAC,cAAc,CAAC,SAAS;IAC7B,IAAI,CAAC,OAAO;IACZ,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG;QACrC,yCAAyC;QACzC,IAAI,CAAC,IAAI,CAAC,SAAS;IACrB;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,sBAAsB,EAAE,EAAE,OAAO;IACxC,IAAI,qBAAqB;IAEzB,MAAM,SAAS,IAAI,OAAO;QACxB,GAAG,OAAO;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,oBAAoB;IACtB;IAEA,GAAG,EAAE,CAAC,WAAW,SAAS,QAAQ,GAAG,EAAE,QAAQ;QAC7C,MAAM,OACJ,CAAC,YAAY,OAAO,cAAc,CAAC,UAAU,GAAG,IAAI,QAAQ,KAAK;QAEnE,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,GAAG,KAAK;IAClC;IAEA,GAAG,IAAI,CAAC,SAAS,SAAS,MAAM,GAAG;QACjC,IAAI,OAAO,SAAS,EAAE;QAEtB,qEAAqE;QACrE,EAAE;QACF,sEAAsE;QACtE,yDAAyD;QACzD,wEAAwE;QACxE,oEAAoE;QACpE,wEAAwE;QACxE,4EAA4E;QAC5E,4CAA4C;QAC5C,qBAAqB;QACrB,OAAO,OAAO,CAAC;IACjB;IAEA,GAAG,IAAI,CAAC,SAAS,SAAS;QACxB,IAAI,OAAO,SAAS,EAAE;QAEtB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO,QAAQ,GAAG,SAAU,GAAG,EAAE,QAAQ;QACvC,IAAI,GAAG,UAAU,KAAK,GAAG,MAAM,EAAE;YAC/B,SAAS;YACT,QAAQ,QAAQ,CAAC,WAAW;YAC5B;QACF;QAEA,IAAI,SAAS;QAEb,GAAG,IAAI,CAAC,SAAS,SAAS,MAAM,GAAG;YACjC,SAAS;YACT,SAAS;QACX;QAEA,GAAG,IAAI,CAAC,SAAS,SAAS;YACxB,IAAI,CAAC,QAAQ,SAAS;YACtB,QAAQ,QAAQ,CAAC,WAAW;QAC9B;QAEA,IAAI,oBAAoB,GAAG,SAAS;IACtC;IAEA,OAAO,MAAM,GAAG,SAAU,QAAQ;QAChC,IAAI,GAAG,UAAU,KAAK,GAAG,UAAU,EAAE;YACnC,GAAG,IAAI,CAAC,QAAQ,SAAS;gBACvB,OAAO,MAAM,CAAC;YAChB;YACA;QACF;QAEA,2EAA2E;QAC3E,2EAA2E;QAC3E,0EAA0E;QAC1E,yCAAyC;QACzC,IAAI,GAAG,OAAO,KAAK,MAAM;QAEzB,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE;YACtC;YACA,IAAI,OAAO,cAAc,CAAC,UAAU,EAAE,OAAO,OAAO;QACtD,OAAO;YACL,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,SAAS;gBACjC,mEAAmE;gBACnE,qEAAqE;gBACrE,uEAAuE;gBACvE;YACF;YACA,GAAG,KAAK;QACV;IACF;IAEA,OAAO,KAAK,GAAG;QACb,IAAI,GAAG,QAAQ,EAAE,GAAG,MAAM;IAC5B;IAEA,OAAO,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ;QACjD,IAAI,GAAG,UAAU,KAAK,GAAG,UAAU,EAAE;YACnC,GAAG,IAAI,CAAC,QAAQ,SAAS;gBACvB,OAAO,MAAM,CAAC,OAAO,UAAU;YACjC;YACA;QACF;QAEA,GAAG,IAAI,CAAC,OAAO;IACjB;IAEA,OAAO,EAAE,CAAC,OAAO;IACjB,OAAO,EAAE,CAAC,SAAS;IACnB,OAAO;AACT;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/constants.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  BINARY_TYPES: ['nodebuffer', 'arraybuffer', 'fragments'],\n  EMPTY_BUFFER: Buffer.alloc(0),\n  GUID: '258EAFA5-E914-47DA-95CA-C5AB0DC85B11',\n  kForOnEventAttribute: Symbol('kIsForOnEventAttribute'),\n  kListener: Symbol('kListener'),\n  kStatusCode: Symbol('status-code'),\n  kWebSocket: Symbol('websocket'),\n  NOOP: () => {}\n};\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,GAAG;IACf,cAAc;QAAC;QAAc;QAAe;KAAY;IACxD,cAAc,OAAO,KAAK,CAAC;IAC3B,MAAM;IACN,sBAAsB,OAAO;IAC7B,WAAW,OAAO;IAClB,aAAa,OAAO;IACpB,YAAY,OAAO;IACnB,MAAM,KAAO;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/buffer-util.js"], "sourcesContent": ["'use strict';\n\nconst { EMPTY_BUFFER } = require('./constants');\n\nconst FastBuffer = Buffer[Symbol.species];\n\n/**\n * Merges an array of buffers into a new buffer.\n *\n * @param {<PERSON>uffer[]} list The array of buffers to concat\n * @param {Number} totalLength The total length of buffers in the list\n * @return {Buffer} The resulting buffer\n * @public\n */\nfunction concat(list, totalLength) {\n  if (list.length === 0) return EMPTY_BUFFER;\n  if (list.length === 1) return list[0];\n\n  const target = Buffer.allocUnsafe(totalLength);\n  let offset = 0;\n\n  for (let i = 0; i < list.length; i++) {\n    const buf = list[i];\n    target.set(buf, offset);\n    offset += buf.length;\n  }\n\n  if (offset < totalLength) {\n    return new FastBuffer(target.buffer, target.byteOffset, offset);\n  }\n\n  return target;\n}\n\n/**\n * Masks a buffer using the given mask.\n *\n * @param {Buffer} source The buffer to mask\n * @param {Buffer} mask The mask to use\n * @param {<PERSON>uffer} output The buffer where to store the result\n * @param {Number} offset The offset at which to start writing\n * @param {Number} length The number of bytes to mask.\n * @public\n */\nfunction _mask(source, mask, output, offset, length) {\n  for (let i = 0; i < length; i++) {\n    output[offset + i] = source[i] ^ mask[i & 3];\n  }\n}\n\n/**\n * Unmasks a buffer using the given mask.\n *\n * @param {Buffer} buffer The buffer to unmask\n * @param {Buffer} mask The mask to use\n * @public\n */\nfunction _unmask(buffer, mask) {\n  for (let i = 0; i < buffer.length; i++) {\n    buffer[i] ^= mask[i & 3];\n  }\n}\n\n/**\n * Converts a buffer to an `ArrayBuffer`.\n *\n * @param {Buffer} buf The buffer to convert\n * @return {ArrayBuffer} Converted buffer\n * @public\n */\nfunction toArrayBuffer(buf) {\n  if (buf.length === buf.buffer.byteLength) {\n    return buf.buffer;\n  }\n\n  return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.length);\n}\n\n/**\n * Converts `data` to a `Buffer`.\n *\n * @param {*} data The data to convert\n * @return {Buffer} The buffer\n * @throws {TypeError}\n * @public\n */\nfunction toBuffer(data) {\n  toBuffer.readOnly = true;\n\n  if (Buffer.isBuffer(data)) return data;\n\n  let buf;\n\n  if (data instanceof ArrayBuffer) {\n    buf = new FastBuffer(data);\n  } else if (ArrayBuffer.isView(data)) {\n    buf = new FastBuffer(data.buffer, data.byteOffset, data.byteLength);\n  } else {\n    buf = Buffer.from(data);\n    toBuffer.readOnly = false;\n  }\n\n  return buf;\n}\n\nmodule.exports = {\n  concat,\n  mask: _mask,\n  toArrayBuffer,\n  toBuffer,\n  unmask: _unmask\n};\n\n/* istanbul ignore else  */\nif (!process.env.WS_NO_BUFFER_UTIL) {\n  try {\n    const bufferUtil = require('bufferutil');\n\n    module.exports.mask = function (source, mask, output, offset, length) {\n      if (length < 48) _mask(source, mask, output, offset, length);\n      else bufferUtil.mask(source, mask, output, offset, length);\n    };\n\n    module.exports.unmask = function (buffer, mask) {\n      if (buffer.length < 32) _unmask(buffer, mask);\n      else bufferUtil.unmask(buffer, mask);\n    };\n  } catch (e) {\n    // Continue regardless of the error.\n  }\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,EAAE,YAAY,EAAE;AAEtB,MAAM,aAAa,MAAM,CAAC,OAAO,OAAO,CAAC;AAEzC;;;;;;;CAOC,GACD,SAAS,OAAO,IAAI,EAAE,WAAW;IAC/B,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;IAC9B,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO,IAAI,CAAC,EAAE;IAErC,MAAM,SAAS,OAAO,WAAW,CAAC;IAClC,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,OAAO,GAAG,CAAC,KAAK;QAChB,UAAU,IAAI,MAAM;IACtB;IAEA,IAAI,SAAS,aAAa;QACxB,OAAO,IAAI,WAAW,OAAO,MAAM,EAAE,OAAO,UAAU,EAAE;IAC1D;IAEA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,MAAM,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IACjD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,MAAM,CAAC,SAAS,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;IAC9C;AACF;AAEA;;;;;;CAMC,GACD,SAAS,QAAQ,MAAM,EAAE,IAAI;IAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;IAC1B;AACF;AAEA;;;;;;CAMC,GACD,SAAS,cAAc,GAAG;IACxB,IAAI,IAAI,MAAM,KAAK,IAAI,MAAM,CAAC,UAAU,EAAE;QACxC,OAAO,IAAI,MAAM;IACnB;IAEA,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,UAAU,EAAE,IAAI,UAAU,GAAG,IAAI,MAAM;AACrE;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,IAAI;IACpB,SAAS,QAAQ,GAAG;IAEpB,IAAI,OAAO,QAAQ,CAAC,OAAO,OAAO;IAElC,IAAI;IAEJ,IAAI,gBAAgB,aAAa;QAC/B,MAAM,IAAI,WAAW;IACvB,OAAO,IAAI,YAAY,MAAM,CAAC,OAAO;QACnC,MAAM,IAAI,WAAW,KAAK,MAAM,EAAE,KAAK,UAAU,EAAE,KAAK,UAAU;IACpE,OAAO;QACL,MAAM,OAAO,IAAI,CAAC;QAClB,SAAS,QAAQ,GAAG;IACtB;IAEA,OAAO;AACT;AAEA,OAAO,OAAO,GAAG;IACf;IACA,MAAM;IACN;IACA;IACA,QAAQ;AACV;AAEA,yBAAyB,GACzB,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAClC,IAAI;QACF,MAAM;QAEN,OAAO,OAAO,CAAC,IAAI,GAAG,SAAU,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;YAClE,IAAI,SAAS,IAAI,MAAM,QAAQ,MAAM,QAAQ,QAAQ;iBAChD,WAAW,IAAI,CAAC,QAAQ,MAAM,QAAQ,QAAQ;QACrD;QAEA,OAAO,OAAO,CAAC,MAAM,GAAG,SAAU,MAAM,EAAE,IAAI;YAC5C,IAAI,OAAO,MAAM,GAAG,IAAI,QAAQ,QAAQ;iBACnC,WAAW,MAAM,CAAC,QAAQ;QACjC;IACF,EAAE,OAAO,GAAG;IACV,oCAAoC;IACtC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/limiter.js"], "sourcesContent": ["'use strict';\n\nconst kDone = Symbol('kDone');\nconst kRun = Symbol('kRun');\n\n/**\n * A very simple job queue with adjustable concurrency. Adapted from\n * https://github.com/STRML/async-limiter\n */\nclass Limiter {\n  /**\n   * Creates a new `Limiter`.\n   *\n   * @param {Number} [concurrency=Infinity] The maximum number of jobs allowed\n   *     to run concurrently\n   */\n  constructor(concurrency) {\n    this[kDone] = () => {\n      this.pending--;\n      this[kRun]();\n    };\n    this.concurrency = concurrency || Infinity;\n    this.jobs = [];\n    this.pending = 0;\n  }\n\n  /**\n   * Adds a job to the queue.\n   *\n   * @param {Function} job The job to run\n   * @public\n   */\n  add(job) {\n    this.jobs.push(job);\n    this[kRun]();\n  }\n\n  /**\n   * Removes a job from the queue and runs it if possible.\n   *\n   * @private\n   */\n  [kRun]() {\n    if (this.pending === this.concurrency) return;\n\n    if (this.jobs.length) {\n      const job = this.jobs.shift();\n\n      this.pending++;\n      job(this[kDone]);\n    }\n  }\n}\n\nmodule.exports = Limiter;\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,QAAQ,OAAO;AACrB,MAAM,OAAO,OAAO;AAEpB;;;CAGC,GACD,MAAM;IACJ;;;;;GAKC,GACD,YAAY,WAAW,CAAE;QACvB,IAAI,CAAC,MAAM,GAAG;YACZ,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,KAAK;QACZ;QACA,IAAI,CAAC,WAAW,GAAG,eAAe;QAClC,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA;;;;;GAKC,GACD,IAAI,GAAG,EAAE;QACP,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,CAAC,KAAK;IACZ;IAEA;;;;GAIC,GACD,CAAC,KAAK,GAAG;QACP,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,WAAW,EAAE;QAEvC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACpB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK;YAE3B,IAAI,CAAC,OAAO;YACZ,IAAI,IAAI,CAAC,MAAM;QACjB;IACF;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/permessage-deflate.js"], "sourcesContent": ["'use strict';\n\nconst zlib = require('zlib');\n\nconst bufferUtil = require('./buffer-util');\nconst Limiter = require('./limiter');\nconst { kStatusCode } = require('./constants');\n\nconst FastBuffer = Buffer[Symbol.species];\nconst TRAILER = Buffer.from([0x00, 0x00, 0xff, 0xff]);\nconst kPerMessageDeflate = Symbol('permessage-deflate');\nconst kTotalLength = Symbol('total-length');\nconst kCallback = Symbol('callback');\nconst kBuffers = Symbol('buffers');\nconst kError = Symbol('error');\n\n//\n// We limit zlib concurrency, which prevents severe memory fragmentation\n// as documented in https://github.com/nodejs/node/issues/8871#issuecomment-250915913\n// and https://github.com/websockets/ws/issues/1202\n//\n// Intentionally global; it's the global thread pool that's an issue.\n//\nlet zlibLimiter;\n\n/**\n * permessage-deflate implementation.\n */\nclass PerMessageDeflate {\n  /**\n   * Creates a PerMessageDeflate instance.\n   *\n   * @param {Object} [options] Configuration options\n   * @param {(Boolean|Number)} [options.clientMaxWindowBits] Advertise support\n   *     for, or request, a custom client window size\n   * @param {Boolean} [options.clientNoContextTakeover=false] Advertise/\n   *     acknowledge disabling of client context takeover\n   * @param {Number} [options.concurrencyLimit=10] The number of concurrent\n   *     calls to zlib\n   * @param {(Boolean|Number)} [options.serverMaxWindowBits] Request/confirm the\n   *     use of a custom server window size\n   * @param {Boolean} [options.serverNoContextTakeover=false] Request/accept\n   *     disabling of server context takeover\n   * @param {Number} [options.threshold=1024] Size (in bytes) below which\n   *     messages should not be compressed if context takeover is disabled\n   * @param {Object} [options.zlibDeflateOptions] Options to pass to zlib on\n   *     deflate\n   * @param {Object} [options.zlibInflateOptions] Options to pass to zlib on\n   *     inflate\n   * @param {Boolean} [isServer=false] Create the instance in either server or\n   *     client mode\n   * @param {Number} [maxPayload=0] The maximum allowed message length\n   */\n  constructor(options, isServer, maxPayload) {\n    this._maxPayload = maxPayload | 0;\n    this._options = options || {};\n    this._threshold =\n      this._options.threshold !== undefined ? this._options.threshold : 1024;\n    this._isServer = !!isServer;\n    this._deflate = null;\n    this._inflate = null;\n\n    this.params = null;\n\n    if (!zlibLimiter) {\n      const concurrency =\n        this._options.concurrencyLimit !== undefined\n          ? this._options.concurrencyLimit\n          : 10;\n      zlibLimiter = new Limiter(concurrency);\n    }\n  }\n\n  /**\n   * @type {String}\n   */\n  static get extensionName() {\n    return 'permessage-deflate';\n  }\n\n  /**\n   * Create an extension negotiation offer.\n   *\n   * @return {Object} Extension parameters\n   * @public\n   */\n  offer() {\n    const params = {};\n\n    if (this._options.serverNoContextTakeover) {\n      params.server_no_context_takeover = true;\n    }\n    if (this._options.clientNoContextTakeover) {\n      params.client_no_context_takeover = true;\n    }\n    if (this._options.serverMaxWindowBits) {\n      params.server_max_window_bits = this._options.serverMaxWindowBits;\n    }\n    if (this._options.clientMaxWindowBits) {\n      params.client_max_window_bits = this._options.clientMaxWindowBits;\n    } else if (this._options.clientMaxWindowBits == null) {\n      params.client_max_window_bits = true;\n    }\n\n    return params;\n  }\n\n  /**\n   * Accept an extension negotiation offer/response.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Object} Accepted configuration\n   * @public\n   */\n  accept(configurations) {\n    configurations = this.normalizeParams(configurations);\n\n    this.params = this._isServer\n      ? this.acceptAsServer(configurations)\n      : this.acceptAsClient(configurations);\n\n    return this.params;\n  }\n\n  /**\n   * Releases all resources used by the extension.\n   *\n   * @public\n   */\n  cleanup() {\n    if (this._inflate) {\n      this._inflate.close();\n      this._inflate = null;\n    }\n\n    if (this._deflate) {\n      const callback = this._deflate[kCallback];\n\n      this._deflate.close();\n      this._deflate = null;\n\n      if (callback) {\n        callback(\n          new Error(\n            'The deflate stream was closed while data was being processed'\n          )\n        );\n      }\n    }\n  }\n\n  /**\n   *  Accept an extension negotiation offer.\n   *\n   * @param {Array} offers The extension negotiation offers\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsServer(offers) {\n    const opts = this._options;\n    const accepted = offers.find((params) => {\n      if (\n        (opts.serverNoContextTakeover === false &&\n          params.server_no_context_takeover) ||\n        (params.server_max_window_bits &&\n          (opts.serverMaxWindowBits === false ||\n            (typeof opts.serverMaxWindowBits === 'number' &&\n              opts.serverMaxWindowBits > params.server_max_window_bits))) ||\n        (typeof opts.clientMaxWindowBits === 'number' &&\n          !params.client_max_window_bits)\n      ) {\n        return false;\n      }\n\n      return true;\n    });\n\n    if (!accepted) {\n      throw new Error('None of the extension offers can be accepted');\n    }\n\n    if (opts.serverNoContextTakeover) {\n      accepted.server_no_context_takeover = true;\n    }\n    if (opts.clientNoContextTakeover) {\n      accepted.client_no_context_takeover = true;\n    }\n    if (typeof opts.serverMaxWindowBits === 'number') {\n      accepted.server_max_window_bits = opts.serverMaxWindowBits;\n    }\n    if (typeof opts.clientMaxWindowBits === 'number') {\n      accepted.client_max_window_bits = opts.clientMaxWindowBits;\n    } else if (\n      accepted.client_max_window_bits === true ||\n      opts.clientMaxWindowBits === false\n    ) {\n      delete accepted.client_max_window_bits;\n    }\n\n    return accepted;\n  }\n\n  /**\n   * Accept the extension negotiation response.\n   *\n   * @param {Array} response The extension negotiation response\n   * @return {Object} Accepted configuration\n   * @private\n   */\n  acceptAsClient(response) {\n    const params = response[0];\n\n    if (\n      this._options.clientNoContextTakeover === false &&\n      params.client_no_context_takeover\n    ) {\n      throw new Error('Unexpected parameter \"client_no_context_takeover\"');\n    }\n\n    if (!params.client_max_window_bits) {\n      if (typeof this._options.clientMaxWindowBits === 'number') {\n        params.client_max_window_bits = this._options.clientMaxWindowBits;\n      }\n    } else if (\n      this._options.clientMaxWindowBits === false ||\n      (typeof this._options.clientMaxWindowBits === 'number' &&\n        params.client_max_window_bits > this._options.clientMaxWindowBits)\n    ) {\n      throw new Error(\n        'Unexpected or invalid parameter \"client_max_window_bits\"'\n      );\n    }\n\n    return params;\n  }\n\n  /**\n   * Normalize parameters.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Array} The offers/response with normalized parameters\n   * @private\n   */\n  normalizeParams(configurations) {\n    configurations.forEach((params) => {\n      Object.keys(params).forEach((key) => {\n        let value = params[key];\n\n        if (value.length > 1) {\n          throw new Error(`Parameter \"${key}\" must have only a single value`);\n        }\n\n        value = value[0];\n\n        if (key === 'client_max_window_bits') {\n          if (value !== true) {\n            const num = +value;\n            if (!Number.isInteger(num) || num < 8 || num > 15) {\n              throw new TypeError(\n                `Invalid value for parameter \"${key}\": ${value}`\n              );\n            }\n            value = num;\n          } else if (!this._isServer) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else if (key === 'server_max_window_bits') {\n          const num = +value;\n          if (!Number.isInteger(num) || num < 8 || num > 15) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n          value = num;\n        } else if (\n          key === 'client_no_context_takeover' ||\n          key === 'server_no_context_takeover'\n        ) {\n          if (value !== true) {\n            throw new TypeError(\n              `Invalid value for parameter \"${key}\": ${value}`\n            );\n          }\n        } else {\n          throw new Error(`Unknown parameter \"${key}\"`);\n        }\n\n        params[key] = value;\n      });\n    });\n\n    return configurations;\n  }\n\n  /**\n   * Decompress data. Concurrency limited.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  decompress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._decompress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n\n  /**\n   * Compress data. Concurrency limited.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */\n  compress(data, fin, callback) {\n    zlibLimiter.add((done) => {\n      this._compress(data, fin, (err, result) => {\n        done();\n        callback(err, result);\n      });\n    });\n  }\n\n  /**\n   * Decompress data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _decompress(data, fin, callback) {\n    const endpoint = this._isServer ? 'client' : 'server';\n\n    if (!this._inflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits =\n        typeof this.params[key] !== 'number'\n          ? zlib.Z_DEFAULT_WINDOWBITS\n          : this.params[key];\n\n      this._inflate = zlib.createInflateRaw({\n        ...this._options.zlibInflateOptions,\n        windowBits\n      });\n      this._inflate[kPerMessageDeflate] = this;\n      this._inflate[kTotalLength] = 0;\n      this._inflate[kBuffers] = [];\n      this._inflate.on('error', inflateOnError);\n      this._inflate.on('data', inflateOnData);\n    }\n\n    this._inflate[kCallback] = callback;\n\n    this._inflate.write(data);\n    if (fin) this._inflate.write(TRAILER);\n\n    this._inflate.flush(() => {\n      const err = this._inflate[kError];\n\n      if (err) {\n        this._inflate.close();\n        this._inflate = null;\n        callback(err);\n        return;\n      }\n\n      const data = bufferUtil.concat(\n        this._inflate[kBuffers],\n        this._inflate[kTotalLength]\n      );\n\n      if (this._inflate._readableState.endEmitted) {\n        this._inflate.close();\n        this._inflate = null;\n      } else {\n        this._inflate[kTotalLength] = 0;\n        this._inflate[kBuffers] = [];\n\n        if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n          this._inflate.reset();\n        }\n      }\n\n      callback(null, data);\n    });\n  }\n\n  /**\n   * Compress data.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */\n  _compress(data, fin, callback) {\n    const endpoint = this._isServer ? 'server' : 'client';\n\n    if (!this._deflate) {\n      const key = `${endpoint}_max_window_bits`;\n      const windowBits =\n        typeof this.params[key] !== 'number'\n          ? zlib.Z_DEFAULT_WINDOWBITS\n          : this.params[key];\n\n      this._deflate = zlib.createDeflateRaw({\n        ...this._options.zlibDeflateOptions,\n        windowBits\n      });\n\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n\n      this._deflate.on('data', deflateOnData);\n    }\n\n    this._deflate[kCallback] = callback;\n\n    this._deflate.write(data);\n    this._deflate.flush(zlib.Z_SYNC_FLUSH, () => {\n      if (!this._deflate) {\n        //\n        // The deflate stream was closed while data was being processed.\n        //\n        return;\n      }\n\n      let data = bufferUtil.concat(\n        this._deflate[kBuffers],\n        this._deflate[kTotalLength]\n      );\n\n      if (fin) {\n        data = new FastBuffer(data.buffer, data.byteOffset, data.length - 4);\n      }\n\n      //\n      // Ensure that the callback will not be called again in\n      // `PerMessageDeflate#cleanup()`.\n      //\n      this._deflate[kCallback] = null;\n\n      this._deflate[kTotalLength] = 0;\n      this._deflate[kBuffers] = [];\n\n      if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n        this._deflate.reset();\n      }\n\n      callback(null, data);\n    });\n  }\n}\n\nmodule.exports = PerMessageDeflate;\n\n/**\n * The listener of the `zlib.DeflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction deflateOnData(chunk) {\n  this[kBuffers].push(chunk);\n  this[kTotalLength] += chunk.length;\n}\n\n/**\n * The listener of the `zlib.InflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction inflateOnData(chunk) {\n  this[kTotalLength] += chunk.length;\n\n  if (\n    this[kPerMessageDeflate]._maxPayload < 1 ||\n    this[kTotalLength] <= this[kPerMessageDeflate]._maxPayload\n  ) {\n    this[kBuffers].push(chunk);\n    return;\n  }\n\n  this[kError] = new RangeError('Max payload size exceeded');\n  this[kError].code = 'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH';\n  this[kError][kStatusCode] = 1009;\n  this.removeListener('data', inflateOnData);\n  this.reset();\n}\n\n/**\n * The listener of the `zlib.InflateRaw` stream `'error'` event.\n *\n * @param {Error} err The emitted error\n * @private\n */\nfunction inflateOnError(err) {\n  //\n  // There is no need to call `Zlib#close()` as the handle is automatically\n  // closed when an error is emitted.\n  //\n  this[kPerMessageDeflate]._inflate = null;\n  err[kStatusCode] = 1007;\n  this[kCallback](err);\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AAEN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,WAAW,EAAE;AAErB,MAAM,aAAa,MAAM,CAAC,OAAO,OAAO,CAAC;AACzC,MAAM,UAAU,OAAO,IAAI,CAAC;IAAC;IAAM;IAAM;IAAM;CAAK;AACpD,MAAM,qBAAqB,OAAO;AAClC,MAAM,eAAe,OAAO;AAC5B,MAAM,YAAY,OAAO;AACzB,MAAM,WAAW,OAAO;AACxB,MAAM,SAAS,OAAO;AAEtB,EAAE;AACF,wEAAwE;AACxE,qFAAqF;AACrF,mDAAmD;AACnD,EAAE;AACF,qEAAqE;AACrE,EAAE;AACF,IAAI;AAEJ;;CAEC,GACD,MAAM;IACJ;;;;;;;;;;;;;;;;;;;;;;;GAuBC,GACD,YAAY,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAE;QACzC,IAAI,CAAC,WAAW,GAAG,aAAa;QAChC,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;QAC5B,IAAI,CAAC,UAAU,GACb,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,YAAY,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG;QACpE,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,QAAQ,GAAG;QAEhB,IAAI,CAAC,MAAM,GAAG;QAEd,IAAI,CAAC,aAAa;YAChB,MAAM,cACJ,IAAI,CAAC,QAAQ,CAAC,gBAAgB,KAAK,YAC/B,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAC9B;YACN,cAAc,IAAI,QAAQ;QAC5B;IACF;IAEA;;GAEC,GACD,WAAW,gBAAgB;QACzB,OAAO;IACT;IAEA;;;;;GAKC,GACD,QAAQ;QACN,MAAM,SAAS,CAAC;QAEhB,IAAI,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE;YACzC,OAAO,0BAA0B,GAAG;QACtC;QACA,IAAI,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE;YACzC,OAAO,0BAA0B,GAAG;QACtC;QACA,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE;YACrC,OAAO,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB;QACnE;QACA,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE;YACrC,OAAO,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB;QACnE,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,IAAI,MAAM;YACpD,OAAO,sBAAsB,GAAG;QAClC;QAEA,OAAO;IACT;IAEA;;;;;;GAMC,GACD,OAAO,cAAc,EAAE;QACrB,iBAAiB,IAAI,CAAC,eAAe,CAAC;QAEtC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,GACxB,IAAI,CAAC,cAAc,CAAC,kBACpB,IAAI,CAAC,cAAc,CAAC;QAExB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA;;;;GAIC,GACD,UAAU;QACR,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,KAAK;YACnB,IAAI,CAAC,QAAQ,GAAG;QAClB;QAEA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,UAAU;YAEzC,IAAI,CAAC,QAAQ,CAAC,KAAK;YACnB,IAAI,CAAC,QAAQ,GAAG;YAEhB,IAAI,UAAU;gBACZ,SACE,IAAI,MACF;YAGN;QACF;IACF;IAEA;;;;;;GAMC,GACD,eAAe,MAAM,EAAE;QACrB,MAAM,OAAO,IAAI,CAAC,QAAQ;QAC1B,MAAM,WAAW,OAAO,IAAI,CAAC,CAAC;YAC5B,IACE,AAAC,KAAK,uBAAuB,KAAK,SAChC,OAAO,0BAA0B,IAClC,OAAO,sBAAsB,IAC5B,CAAC,KAAK,mBAAmB,KAAK,SAC3B,OAAO,KAAK,mBAAmB,KAAK,YACnC,KAAK,mBAAmB,GAAG,OAAO,sBAAsB,AAAC,KAC9D,OAAO,KAAK,mBAAmB,KAAK,YACnC,CAAC,OAAO,sBAAsB,EAChC;gBACA,OAAO;YACT;YAEA,OAAO;QACT;QAEA,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,uBAAuB,EAAE;YAChC,SAAS,0BAA0B,GAAG;QACxC;QACA,IAAI,KAAK,uBAAuB,EAAE;YAChC,SAAS,0BAA0B,GAAG;QACxC;QACA,IAAI,OAAO,KAAK,mBAAmB,KAAK,UAAU;YAChD,SAAS,sBAAsB,GAAG,KAAK,mBAAmB;QAC5D;QACA,IAAI,OAAO,KAAK,mBAAmB,KAAK,UAAU;YAChD,SAAS,sBAAsB,GAAG,KAAK,mBAAmB;QAC5D,OAAO,IACL,SAAS,sBAAsB,KAAK,QACpC,KAAK,mBAAmB,KAAK,OAC7B;YACA,OAAO,SAAS,sBAAsB;QACxC;QAEA,OAAO;IACT;IAEA;;;;;;GAMC,GACD,eAAe,QAAQ,EAAE;QACvB,MAAM,SAAS,QAAQ,CAAC,EAAE;QAE1B,IACE,IAAI,CAAC,QAAQ,CAAC,uBAAuB,KAAK,SAC1C,OAAO,0BAA0B,EACjC;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,OAAO,sBAAsB,EAAE;YAClC,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,KAAK,UAAU;gBACzD,OAAO,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB;YACnE;QACF,OAAO,IACL,IAAI,CAAC,QAAQ,CAAC,mBAAmB,KAAK,SACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,KAAK,YAC5C,OAAO,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EACnE;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;IACT;IAEA;;;;;;GAMC,GACD,gBAAgB,cAAc,EAAE;QAC9B,eAAe,OAAO,CAAC,CAAC;YACtB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAC;gBAC3B,IAAI,QAAQ,MAAM,CAAC,IAAI;gBAEvB,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,+BAA+B,CAAC;gBACpE;gBAEA,QAAQ,KAAK,CAAC,EAAE;gBAEhB,IAAI,QAAQ,0BAA0B;oBACpC,IAAI,UAAU,MAAM;wBAClB,MAAM,MAAM,CAAC;wBACb,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,MAAM,KAAK,MAAM,IAAI;4BACjD,MAAM,IAAI,UACR,CAAC,6BAA6B,EAAE,IAAI,GAAG,EAAE,OAAO;wBAEpD;wBACA,QAAQ;oBACV,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;wBAC1B,MAAM,IAAI,UACR,CAAC,6BAA6B,EAAE,IAAI,GAAG,EAAE,OAAO;oBAEpD;gBACF,OAAO,IAAI,QAAQ,0BAA0B;oBAC3C,MAAM,MAAM,CAAC;oBACb,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,MAAM,KAAK,MAAM,IAAI;wBACjD,MAAM,IAAI,UACR,CAAC,6BAA6B,EAAE,IAAI,GAAG,EAAE,OAAO;oBAEpD;oBACA,QAAQ;gBACV,OAAO,IACL,QAAQ,gCACR,QAAQ,8BACR;oBACA,IAAI,UAAU,MAAM;wBAClB,MAAM,IAAI,UACR,CAAC,6BAA6B,EAAE,IAAI,GAAG,EAAE,OAAO;oBAEpD;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;gBAC9C;gBAEA,MAAM,CAAC,IAAI,GAAG;YAChB;QACF;QAEA,OAAO;IACT;IAEA;;;;;;;GAOC,GACD,WAAW,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE;QAC9B,YAAY,GAAG,CAAC,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,KAAK;gBAChC;gBACA,SAAS,KAAK;YAChB;QACF;IACF;IAEA;;;;;;;GAOC,GACD,SAAS,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE;QAC5B,YAAY,GAAG,CAAC,CAAC;YACf,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,KAAK;gBAC9B;gBACA,SAAS,KAAK;YAChB;QACF;IACF;IAEA;;;;;;;GAOC,GACD,YAAY,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE;QAC/B,MAAM,WAAW,IAAI,CAAC,SAAS,GAAG,WAAW;QAE7C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,MAAM,GAAG,SAAS,gBAAgB,CAAC;YACzC,MAAM,aACJ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,WACxB,KAAK,oBAAoB,GACzB,IAAI,CAAC,MAAM,CAAC,IAAI;YAEtB,IAAI,CAAC,QAAQ,GAAG,KAAK,gBAAgB,CAAC;gBACpC,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB;gBACnC;YACF;YACA,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,IAAI;YACxC,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG;YAC9B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE;YAC5B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS;YAC1B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ;QAC3B;QAEA,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;QAE3B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QACpB,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QAE7B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YAClB,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO;YAEjC,IAAI,KAAK;gBACP,IAAI,CAAC,QAAQ,CAAC,KAAK;gBACnB,IAAI,CAAC,QAAQ,GAAG;gBAChB,SAAS;gBACT;YACF;YAEA,MAAM,OAAO,WAAW,MAAM,CAC5B,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,IAAI,CAAC,QAAQ,CAAC,aAAa;YAG7B,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,EAAE;gBAC3C,IAAI,CAAC,QAAQ,CAAC,KAAK;gBACnB,IAAI,CAAC,QAAQ,GAAG;YAClB,OAAO;gBACL,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG;gBAC9B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE;gBAE5B,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,oBAAoB,CAAC,CAAC,EAAE;oBACzD,IAAI,CAAC,QAAQ,CAAC,KAAK;gBACrB;YACF;YAEA,SAAS,MAAM;QACjB;IACF;IAEA;;;;;;;GAOC,GACD,UAAU,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE;QAC7B,MAAM,WAAW,IAAI,CAAC,SAAS,GAAG,WAAW;QAE7C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,MAAM,GAAG,SAAS,gBAAgB,CAAC;YACzC,MAAM,aACJ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,WACxB,KAAK,oBAAoB,GACzB,IAAI,CAAC,MAAM,CAAC,IAAI;YAEtB,IAAI,CAAC,QAAQ,GAAG,KAAK,gBAAgB,CAAC;gBACpC,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB;gBACnC;YACF;YAEA,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG;YAC9B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE;YAE5B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ;QAC3B;QAEA,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;QAE3B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QACpB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,YAAY,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,EAAE;gBACF,gEAAgE;gBAChE,EAAE;gBACF;YACF;YAEA,IAAI,OAAO,WAAW,MAAM,CAC1B,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,IAAI,CAAC,QAAQ,CAAC,aAAa;YAG7B,IAAI,KAAK;gBACP,OAAO,IAAI,WAAW,KAAK,MAAM,EAAE,KAAK,UAAU,EAAE,KAAK,MAAM,GAAG;YACpE;YAEA,EAAE;YACF,uDAAuD;YACvD,iCAAiC;YACjC,EAAE;YACF,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;YAE3B,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG;YAC9B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE;YAE5B,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,oBAAoB,CAAC,CAAC,EAAE;gBACzD,IAAI,CAAC,QAAQ,CAAC,KAAK;YACrB;YAEA,SAAS,MAAM;QACjB;IACF;AACF;AAEA,OAAO,OAAO,GAAG;AAEjB;;;;;CAKC,GACD,SAAS,cAAc,KAAK;IAC1B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IACpB,IAAI,CAAC,aAAa,IAAI,MAAM,MAAM;AACpC;AAEA;;;;;CAKC,GACD,SAAS,cAAc,KAAK;IAC1B,IAAI,CAAC,aAAa,IAAI,MAAM,MAAM;IAElC,IACE,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG,KACvC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAC1D;QACA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACpB;IACF;IAEA,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW;IAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;IACpB,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG;IAC5B,IAAI,CAAC,cAAc,CAAC,QAAQ;IAC5B,IAAI,CAAC,KAAK;AACZ;AAEA;;;;;CAKC,GACD,SAAS,eAAe,GAAG;IACzB,EAAE;IACF,yEAAyE;IACzE,mCAAmC;IACnC,EAAE;IACF,IAAI,CAAC,mBAAmB,CAAC,QAAQ,GAAG;IACpC,GAAG,CAAC,YAAY,GAAG;IACnB,IAAI,CAAC,UAAU,CAAC;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/validation.js"], "sourcesContent": ["'use strict';\n\nconst { isUtf8 } = require('buffer');\n\n//\n// Allowed token characters:\n//\n// '!', '#', '$', '%', '&', ''', '*', '+', '-',\n// '.', 0-9, A-Z, '^', '_', '`', a-z, '|', '~'\n//\n// tokenChars[32] === 0 // ' '\n// tokenChars[33] === 1 // '!'\n// tokenChars[34] === 0 // '\"'\n// ...\n//\n// prettier-ignore\nconst tokenChars = [\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // 0 - 15\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // 16 - 31\n  0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, // 32 - 47\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, // 48 - 63\n  0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 64 - 79\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, // 80 - 95\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 96 - 111\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0 // 112 - 127\n];\n\n/**\n * Checks if a status code is allowed in a close frame.\n *\n * @param {Number} code The status code\n * @return {Boolean} `true` if the status code is valid, else `false`\n * @public\n */\nfunction isValidStatusCode(code) {\n  return (\n    (code >= 1000 &&\n      code <= 1014 &&\n      code !== 1004 &&\n      code !== 1005 &&\n      code !== 1006) ||\n    (code >= 3000 && code <= 4999)\n  );\n}\n\n/**\n * Checks if a given buffer contains only correct UTF-8.\n * Ported from https://www.cl.cam.ac.uk/%7Emgk25/ucs/utf8_check.c by\n * Markus Kuhn.\n *\n * @param {Buffer} buf The buffer to check\n * @return {Boolean} `true` if `buf` contains only correct UTF-8, else `false`\n * @public\n */\nfunction _isValidUTF8(buf) {\n  const len = buf.length;\n  let i = 0;\n\n  while (i < len) {\n    if ((buf[i] & 0x80) === 0) {\n      // 0xxxxxxx\n      i++;\n    } else if ((buf[i] & 0xe0) === 0xc0) {\n      // 110xxxxx 10xxxxxx\n      if (\n        i + 1 === len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i] & 0xfe) === 0xc0 // Overlong\n      ) {\n        return false;\n      }\n\n      i += 2;\n    } else if ((buf[i] & 0xf0) === 0xe0) {\n      // 1110xxxx 10xxxxxx 10xxxxxx\n      if (\n        i + 2 >= len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i + 2] & 0xc0) !== 0x80 ||\n        (buf[i] === 0xe0 && (buf[i + 1] & 0xe0) === 0x80) || // Overlong\n        (buf[i] === 0xed && (buf[i + 1] & 0xe0) === 0xa0) // Surrogate (U+D800 - U+DFFF)\n      ) {\n        return false;\n      }\n\n      i += 3;\n    } else if ((buf[i] & 0xf8) === 0xf0) {\n      // 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx\n      if (\n        i + 3 >= len ||\n        (buf[i + 1] & 0xc0) !== 0x80 ||\n        (buf[i + 2] & 0xc0) !== 0x80 ||\n        (buf[i + 3] & 0xc0) !== 0x80 ||\n        (buf[i] === 0xf0 && (buf[i + 1] & 0xf0) === 0x80) || // Overlong\n        (buf[i] === 0xf4 && buf[i + 1] > 0x8f) ||\n        buf[i] > 0xf4 // > U+10FFFF\n      ) {\n        return false;\n      }\n\n      i += 4;\n    } else {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nmodule.exports = {\n  isValidStatusCode,\n  isValidUTF8: _isValidUTF8,\n  tokenChars\n};\n\nif (isUtf8) {\n  module.exports.isValidUTF8 = function (buf) {\n    return buf.length < 24 ? _isValidUTF8(buf) : isUtf8(buf);\n  };\n} /* istanbul ignore else  */ else if (!process.env.WS_NO_UTF_8_VALIDATE) {\n  try {\n    const isValidUTF8 = require('utf-8-validate');\n\n    module.exports.isValidUTF8 = function (buf) {\n      return buf.length < 32 ? _isValidUTF8(buf) : isValidUTF8(buf);\n    };\n  } catch (e) {\n    // Continue regardless of the error.\n  }\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,EAAE,MAAM,EAAE;AAEhB,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,+CAA+C;AAC/C,8CAA8C;AAC9C,EAAE;AACF,8BAA8B;AAC9B,8BAA8B;AAC9B,8BAA8B;AAC9B,MAAM;AACN,EAAE;AACF,kBAAkB;AAClB,MAAM,aAAa;IACjB;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,EAAE,YAAY;CAC5D;AAED;;;;;;CAMC,GACD,SAAS,kBAAkB,IAAI;IAC7B,OACE,AAAC,QAAQ,QACP,QAAQ,QACR,SAAS,QACT,SAAS,QACT,SAAS,QACV,QAAQ,QAAQ,QAAQ;AAE7B;AAEA;;;;;;;;CAQC,GACD,SAAS,aAAa,GAAG;IACvB,MAAM,MAAM,IAAI,MAAM;IACtB,IAAI,IAAI;IAER,MAAO,IAAI,IAAK;QACd,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,GAAG;YACzB,WAAW;YACX;QACF,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;YACnC,oBAAoB;YACpB,IACE,IAAI,MAAM,OACV,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACxB,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,KAAK,WAAW;cACpC;gBACA,OAAO;YACT;YAEA,KAAK;QACP,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;YACnC,6BAA6B;YAC7B,IACE,IAAI,KAAK,OACT,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACxB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACvB,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QAC3C,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,KAAM,8BAA8B;cAChF;gBACA,OAAO;YACT;YAEA,KAAK;QACP,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;YACnC,sCAAsC;YACtC,IACE,IAAI,KAAK,OACT,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACxB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACxB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QACvB,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,QAC3C,GAAG,CAAC,EAAE,KAAK,QAAQ,GAAG,CAAC,IAAI,EAAE,GAAG,QACjC,GAAG,CAAC,EAAE,GAAG,KAAK,aAAa;cAC3B;gBACA,OAAO;YACT;YAEA,KAAK;QACP,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,OAAO,OAAO,GAAG;IACf;IACA,aAAa;IACb;AACF;AAEA,IAAI,QAAQ;IACV,OAAO,OAAO,CAAC,WAAW,GAAG,SAAU,GAAG;QACxC,OAAO,IAAI,MAAM,GAAG,KAAK,aAAa,OAAO,OAAO;IACtD;AACF,OAAmC,IAAI,CAAC,QAAQ,GAAG,CAAC,oBAAoB,EAAE;IACxE,IAAI;QACF,MAAM;QAEN,OAAO,OAAO,CAAC,WAAW,GAAG,SAAU,GAAG;YACxC,OAAO,IAAI,MAAM,GAAG,KAAK,aAAa,OAAO,YAAY;QAC3D;IACF,EAAE,OAAO,GAAG;IACV,oCAAoC;IACtC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/receiver.js"], "sourcesContent": ["'use strict';\n\nconst { Writable } = require('stream');\n\nconst PerMessageDeflate = require('./permessage-deflate');\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  kStatusCode,\n  kWebSocket\n} = require('./constants');\nconst { concat, toArrayBuffer, unmask } = require('./buffer-util');\nconst { isValidStatusCode, isValidUTF8 } = require('./validation');\n\nconst FastBuffer = Buffer[Symbol.species];\n\nconst GET_INFO = 0;\nconst GET_PAYLOAD_LENGTH_16 = 1;\nconst GET_PAYLOAD_LENGTH_64 = 2;\nconst GET_MASK = 3;\nconst GET_DATA = 4;\nconst INFLATING = 5;\nconst DEFER_EVENT = 6;\n\n/**\n * HyBi Receiver implementation.\n *\n * @extends Writable\n */\nclass Receiver extends Writable {\n  /**\n   * Creates a Receiver instance.\n   *\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {String} [options.binaryType=nodebuffer] The type for binary data\n   * @param {Object} [options.extensions] An object containing the negotiated\n   *     extensions\n   * @param {Boolean} [options.isServer=false] Specifies whether to operate in\n   *     client or server mode\n   * @param {Number} [options.maxPayload=0] The maximum allowed message length\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   */\n  constructor(options = {}) {\n    super();\n\n    this._allowSynchronousEvents =\n      options.allowSynchronousEvents !== undefined\n        ? options.allowSynchronousEvents\n        : true;\n    this._binaryType = options.binaryType || BINARY_TYPES[0];\n    this._extensions = options.extensions || {};\n    this._isServer = !!options.isServer;\n    this._maxPayload = options.maxPayload | 0;\n    this._skipUTF8Validation = !!options.skipUTF8Validation;\n    this[kWebSocket] = undefined;\n\n    this._bufferedBytes = 0;\n    this._buffers = [];\n\n    this._compressed = false;\n    this._payloadLength = 0;\n    this._mask = undefined;\n    this._fragmented = 0;\n    this._masked = false;\n    this._fin = false;\n    this._opcode = 0;\n\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragments = [];\n\n    this._errored = false;\n    this._loop = false;\n    this._state = GET_INFO;\n  }\n\n  /**\n   * Implements `Writable.prototype._write()`.\n   *\n   * @param {Buffer} chunk The chunk of data to write\n   * @param {String} encoding The character encoding of `chunk`\n   * @param {Function} cb Callback\n   * @private\n   */\n  _write(chunk, encoding, cb) {\n    if (this._opcode === 0x08 && this._state == GET_INFO) return cb();\n\n    this._bufferedBytes += chunk.length;\n    this._buffers.push(chunk);\n    this.startLoop(cb);\n  }\n\n  /**\n   * Consumes `n` bytes from the buffered data.\n   *\n   * @param {Number} n The number of bytes to consume\n   * @return {Buffer} The consumed bytes\n   * @private\n   */\n  consume(n) {\n    this._bufferedBytes -= n;\n\n    if (n === this._buffers[0].length) return this._buffers.shift();\n\n    if (n < this._buffers[0].length) {\n      const buf = this._buffers[0];\n      this._buffers[0] = new FastBuffer(\n        buf.buffer,\n        buf.byteOffset + n,\n        buf.length - n\n      );\n\n      return new FastBuffer(buf.buffer, buf.byteOffset, n);\n    }\n\n    const dst = Buffer.allocUnsafe(n);\n\n    do {\n      const buf = this._buffers[0];\n      const offset = dst.length - n;\n\n      if (n >= buf.length) {\n        dst.set(this._buffers.shift(), offset);\n      } else {\n        dst.set(new Uint8Array(buf.buffer, buf.byteOffset, n), offset);\n        this._buffers[0] = new FastBuffer(\n          buf.buffer,\n          buf.byteOffset + n,\n          buf.length - n\n        );\n      }\n\n      n -= buf.length;\n    } while (n > 0);\n\n    return dst;\n  }\n\n  /**\n   * Starts the parsing loop.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  startLoop(cb) {\n    this._loop = true;\n\n    do {\n      switch (this._state) {\n        case GET_INFO:\n          this.getInfo(cb);\n          break;\n        case GET_PAYLOAD_LENGTH_16:\n          this.getPayloadLength16(cb);\n          break;\n        case GET_PAYLOAD_LENGTH_64:\n          this.getPayloadLength64(cb);\n          break;\n        case GET_MASK:\n          this.getMask();\n          break;\n        case GET_DATA:\n          this.getData(cb);\n          break;\n        case INFLATING:\n        case DEFER_EVENT:\n          this._loop = false;\n          return;\n      }\n    } while (this._loop);\n\n    if (!this._errored) cb();\n  }\n\n  /**\n   * Reads the first two bytes of a frame.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getInfo(cb) {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n\n    const buf = this.consume(2);\n\n    if ((buf[0] & 0x30) !== 0x00) {\n      const error = this.createError(\n        RangeError,\n        'RSV2 and RSV3 must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_RSV_2_3'\n      );\n\n      cb(error);\n      return;\n    }\n\n    const compressed = (buf[0] & 0x40) === 0x40;\n\n    if (compressed && !this._extensions[PerMessageDeflate.extensionName]) {\n      const error = this.createError(\n        RangeError,\n        'RSV1 must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_RSV_1'\n      );\n\n      cb(error);\n      return;\n    }\n\n    this._fin = (buf[0] & 0x80) === 0x80;\n    this._opcode = buf[0] & 0x0f;\n    this._payloadLength = buf[1] & 0x7f;\n\n    if (this._opcode === 0x00) {\n      if (compressed) {\n        const error = this.createError(\n          RangeError,\n          'RSV1 must be clear',\n          true,\n          1002,\n          'WS_ERR_UNEXPECTED_RSV_1'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (!this._fragmented) {\n        const error = this.createError(\n          RangeError,\n          'invalid opcode 0',\n          true,\n          1002,\n          'WS_ERR_INVALID_OPCODE'\n        );\n\n        cb(error);\n        return;\n      }\n\n      this._opcode = this._fragmented;\n    } else if (this._opcode === 0x01 || this._opcode === 0x02) {\n      if (this._fragmented) {\n        const error = this.createError(\n          RangeError,\n          `invalid opcode ${this._opcode}`,\n          true,\n          1002,\n          'WS_ERR_INVALID_OPCODE'\n        );\n\n        cb(error);\n        return;\n      }\n\n      this._compressed = compressed;\n    } else if (this._opcode > 0x07 && this._opcode < 0x0b) {\n      if (!this._fin) {\n        const error = this.createError(\n          RangeError,\n          'FIN must be set',\n          true,\n          1002,\n          'WS_ERR_EXPECTED_FIN'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (compressed) {\n        const error = this.createError(\n          RangeError,\n          'RSV1 must be clear',\n          true,\n          1002,\n          'WS_ERR_UNEXPECTED_RSV_1'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (\n        this._payloadLength > 0x7d ||\n        (this._opcode === 0x08 && this._payloadLength === 1)\n      ) {\n        const error = this.createError(\n          RangeError,\n          `invalid payload length ${this._payloadLength}`,\n          true,\n          1002,\n          'WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH'\n        );\n\n        cb(error);\n        return;\n      }\n    } else {\n      const error = this.createError(\n        RangeError,\n        `invalid opcode ${this._opcode}`,\n        true,\n        1002,\n        'WS_ERR_INVALID_OPCODE'\n      );\n\n      cb(error);\n      return;\n    }\n\n    if (!this._fin && !this._fragmented) this._fragmented = this._opcode;\n    this._masked = (buf[1] & 0x80) === 0x80;\n\n    if (this._isServer) {\n      if (!this._masked) {\n        const error = this.createError(\n          RangeError,\n          'MASK must be set',\n          true,\n          1002,\n          'WS_ERR_EXPECTED_MASK'\n        );\n\n        cb(error);\n        return;\n      }\n    } else if (this._masked) {\n      const error = this.createError(\n        RangeError,\n        'MASK must be clear',\n        true,\n        1002,\n        'WS_ERR_UNEXPECTED_MASK'\n      );\n\n      cb(error);\n      return;\n    }\n\n    if (this._payloadLength === 126) this._state = GET_PAYLOAD_LENGTH_16;\n    else if (this._payloadLength === 127) this._state = GET_PAYLOAD_LENGTH_64;\n    else this.haveLength(cb);\n  }\n\n  /**\n   * Gets extended payload length (7+16).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getPayloadLength16(cb) {\n    if (this._bufferedBytes < 2) {\n      this._loop = false;\n      return;\n    }\n\n    this._payloadLength = this.consume(2).readUInt16BE(0);\n    this.haveLength(cb);\n  }\n\n  /**\n   * Gets extended payload length (7+64).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getPayloadLength64(cb) {\n    if (this._bufferedBytes < 8) {\n      this._loop = false;\n      return;\n    }\n\n    const buf = this.consume(8);\n    const num = buf.readUInt32BE(0);\n\n    //\n    // The maximum safe integer in JavaScript is 2^53 - 1. An error is returned\n    // if payload length is greater than this number.\n    //\n    if (num > Math.pow(2, 53 - 32) - 1) {\n      const error = this.createError(\n        RangeError,\n        'Unsupported WebSocket frame: payload length > 2^53 - 1',\n        false,\n        1009,\n        'WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH'\n      );\n\n      cb(error);\n      return;\n    }\n\n    this._payloadLength = num * Math.pow(2, 32) + buf.readUInt32BE(4);\n    this.haveLength(cb);\n  }\n\n  /**\n   * Payload length has been read.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  haveLength(cb) {\n    if (this._payloadLength && this._opcode < 0x08) {\n      this._totalPayloadLength += this._payloadLength;\n      if (this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {\n        const error = this.createError(\n          RangeError,\n          'Max payload size exceeded',\n          false,\n          1009,\n          'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH'\n        );\n\n        cb(error);\n        return;\n      }\n    }\n\n    if (this._masked) this._state = GET_MASK;\n    else this._state = GET_DATA;\n  }\n\n  /**\n   * Reads mask bytes.\n   *\n   * @private\n   */\n  getMask() {\n    if (this._bufferedBytes < 4) {\n      this._loop = false;\n      return;\n    }\n\n    this._mask = this.consume(4);\n    this._state = GET_DATA;\n  }\n\n  /**\n   * Reads data bytes.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  getData(cb) {\n    let data = EMPTY_BUFFER;\n\n    if (this._payloadLength) {\n      if (this._bufferedBytes < this._payloadLength) {\n        this._loop = false;\n        return;\n      }\n\n      data = this.consume(this._payloadLength);\n\n      if (\n        this._masked &&\n        (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0\n      ) {\n        unmask(data, this._mask);\n      }\n    }\n\n    if (this._opcode > 0x07) {\n      this.controlMessage(data, cb);\n      return;\n    }\n\n    if (this._compressed) {\n      this._state = INFLATING;\n      this.decompress(data, cb);\n      return;\n    }\n\n    if (data.length) {\n      //\n      // This message is not compressed so its length is the sum of the payload\n      // length of all fragments.\n      //\n      this._messageLength = this._totalPayloadLength;\n      this._fragments.push(data);\n    }\n\n    this.dataMessage(cb);\n  }\n\n  /**\n   * Decompresses data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Function} cb Callback\n   * @private\n   */\n  decompress(data, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n\n    perMessageDeflate.decompress(data, this._fin, (err, buf) => {\n      if (err) return cb(err);\n\n      if (buf.length) {\n        this._messageLength += buf.length;\n        if (this._messageLength > this._maxPayload && this._maxPayload > 0) {\n          const error = this.createError(\n            RangeError,\n            'Max payload size exceeded',\n            false,\n            1009,\n            'WS_ERR_UNSUPPORTED_MESSAGE_LENGTH'\n          );\n\n          cb(error);\n          return;\n        }\n\n        this._fragments.push(buf);\n      }\n\n      this.dataMessage(cb);\n      if (this._state === GET_INFO) this.startLoop(cb);\n    });\n  }\n\n  /**\n   * Handles a data message.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */\n  dataMessage(cb) {\n    if (!this._fin) {\n      this._state = GET_INFO;\n      return;\n    }\n\n    const messageLength = this._messageLength;\n    const fragments = this._fragments;\n\n    this._totalPayloadLength = 0;\n    this._messageLength = 0;\n    this._fragmented = 0;\n    this._fragments = [];\n\n    if (this._opcode === 2) {\n      let data;\n\n      if (this._binaryType === 'nodebuffer') {\n        data = concat(fragments, messageLength);\n      } else if (this._binaryType === 'arraybuffer') {\n        data = toArrayBuffer(concat(fragments, messageLength));\n      } else {\n        data = fragments;\n      }\n\n      if (this._allowSynchronousEvents) {\n        this.emit('message', data, true);\n        this._state = GET_INFO;\n      } else {\n        this._state = DEFER_EVENT;\n        setImmediate(() => {\n          this.emit('message', data, true);\n          this._state = GET_INFO;\n          this.startLoop(cb);\n        });\n      }\n    } else {\n      const buf = concat(fragments, messageLength);\n\n      if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n        const error = this.createError(\n          Error,\n          'invalid UTF-8 sequence',\n          true,\n          1007,\n          'WS_ERR_INVALID_UTF8'\n        );\n\n        cb(error);\n        return;\n      }\n\n      if (this._state === INFLATING || this._allowSynchronousEvents) {\n        this.emit('message', buf, false);\n        this._state = GET_INFO;\n      } else {\n        this._state = DEFER_EVENT;\n        setImmediate(() => {\n          this.emit('message', buf, false);\n          this._state = GET_INFO;\n          this.startLoop(cb);\n        });\n      }\n    }\n  }\n\n  /**\n   * Handles a control message.\n   *\n   * @param {Buffer} data Data to handle\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */\n  controlMessage(data, cb) {\n    if (this._opcode === 0x08) {\n      if (data.length === 0) {\n        this._loop = false;\n        this.emit('conclude', 1005, EMPTY_BUFFER);\n        this.end();\n      } else {\n        const code = data.readUInt16BE(0);\n\n        if (!isValidStatusCode(code)) {\n          const error = this.createError(\n            RangeError,\n            `invalid status code ${code}`,\n            true,\n            1002,\n            'WS_ERR_INVALID_CLOSE_CODE'\n          );\n\n          cb(error);\n          return;\n        }\n\n        const buf = new FastBuffer(\n          data.buffer,\n          data.byteOffset + 2,\n          data.length - 2\n        );\n\n        if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n          const error = this.createError(\n            Error,\n            'invalid UTF-8 sequence',\n            true,\n            1007,\n            'WS_ERR_INVALID_UTF8'\n          );\n\n          cb(error);\n          return;\n        }\n\n        this._loop = false;\n        this.emit('conclude', code, buf);\n        this.end();\n      }\n\n      this._state = GET_INFO;\n      return;\n    }\n\n    if (this._allowSynchronousEvents) {\n      this.emit(this._opcode === 0x09 ? 'ping' : 'pong', data);\n      this._state = GET_INFO;\n    } else {\n      this._state = DEFER_EVENT;\n      setImmediate(() => {\n        this.emit(this._opcode === 0x09 ? 'ping' : 'pong', data);\n        this._state = GET_INFO;\n        this.startLoop(cb);\n      });\n    }\n  }\n\n  /**\n   * Builds an error object.\n   *\n   * @param {function(new:Error|RangeError)} ErrorCtor The error constructor\n   * @param {String} message The error message\n   * @param {Boolean} prefix Specifies whether or not to add a default prefix to\n   *     `message`\n   * @param {Number} statusCode The status code\n   * @param {String} errorCode The exposed error code\n   * @return {(Error|RangeError)} The error\n   * @private\n   */\n  createError(ErrorCtor, message, prefix, statusCode, errorCode) {\n    this._loop = false;\n    this._errored = true;\n\n    const err = new ErrorCtor(\n      prefix ? `Invalid WebSocket frame: ${message}` : message\n    );\n\n    Error.captureStackTrace(err, this.createError);\n    err.code = errorCode;\n    err[kStatusCode] = statusCode;\n    return err;\n  }\n}\n\nmodule.exports = Receiver;\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,EAAE,QAAQ,EAAE;AAElB,MAAM;AACN,MAAM,EACJ,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,UAAU,EACX;AACD,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE;AACvC,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE;AAExC,MAAM,aAAa,MAAM,CAAC,OAAO,OAAO,CAAC;AAEzC,MAAM,WAAW;AACjB,MAAM,wBAAwB;AAC9B,MAAM,wBAAwB;AAC9B,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,cAAc;AAEpB;;;;CAIC,GACD,MAAM,iBAAiB;IACrB;;;;;;;;;;;;;;;GAeC,GACD,YAAY,UAAU,CAAC,CAAC,CAAE;QACxB,KAAK;QAEL,IAAI,CAAC,uBAAuB,GAC1B,QAAQ,sBAAsB,KAAK,YAC/B,QAAQ,sBAAsB,GAC9B;QACN,IAAI,CAAC,WAAW,GAAG,QAAQ,UAAU,IAAI,YAAY,CAAC,EAAE;QACxD,IAAI,CAAC,WAAW,GAAG,QAAQ,UAAU,IAAI,CAAC;QAC1C,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,QAAQ;QACnC,IAAI,CAAC,WAAW,GAAG,QAAQ,UAAU,GAAG;QACxC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,QAAQ,kBAAkB;QACvD,IAAI,CAAC,WAAW,GAAG;QAEnB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,QAAQ,GAAG,EAAE;QAElB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QAEf,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE;QAEpB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;;;;;;GAOC,GACD,OAAO,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;QAC1B,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,MAAM,IAAI,UAAU,OAAO;QAE7D,IAAI,CAAC,cAAc,IAAI,MAAM,MAAM;QACnC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,SAAS,CAAC;IACjB;IAEA;;;;;;GAMC,GACD,QAAQ,CAAC,EAAE;QACT,IAAI,CAAC,cAAc,IAAI;QAEvB,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK;QAE7D,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE;YAC/B,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC5B,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,WACrB,IAAI,MAAM,EACV,IAAI,UAAU,GAAG,GACjB,IAAI,MAAM,GAAG;YAGf,OAAO,IAAI,WAAW,IAAI,MAAM,EAAE,IAAI,UAAU,EAAE;QACpD;QAEA,MAAM,MAAM,OAAO,WAAW,CAAC;QAE/B,GAAG;YACD,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC5B,MAAM,SAAS,IAAI,MAAM,GAAG;YAE5B,IAAI,KAAK,IAAI,MAAM,EAAE;gBACnB,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI;YACjC,OAAO;gBACL,IAAI,GAAG,CAAC,IAAI,WAAW,IAAI,MAAM,EAAE,IAAI,UAAU,EAAE,IAAI;gBACvD,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,WACrB,IAAI,MAAM,EACV,IAAI,UAAU,GAAG,GACjB,IAAI,MAAM,GAAG;YAEjB;YAEA,KAAK,IAAI,MAAM;QACjB,QAAS,IAAI,EAAG;QAEhB,OAAO;IACT;IAEA;;;;;GAKC,GACD,UAAU,EAAE,EAAE;QACZ,IAAI,CAAC,KAAK,GAAG;QAEb,GAAG;YACD,OAAQ,IAAI,CAAC,MAAM;gBACjB,KAAK;oBACH,IAAI,CAAC,OAAO,CAAC;oBACb;gBACF,KAAK;oBACH,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACF,KAAK;oBACH,IAAI,CAAC,kBAAkB,CAAC;oBACxB;gBACF,KAAK;oBACH,IAAI,CAAC,OAAO;oBACZ;gBACF,KAAK;oBACH,IAAI,CAAC,OAAO,CAAC;oBACb;gBACF,KAAK;gBACL,KAAK;oBACH,IAAI,CAAC,KAAK,GAAG;oBACb;YACJ;QACF,QAAS,IAAI,CAAC,KAAK,CAAE;QAErB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;IACtB;IAEA;;;;;GAKC,GACD,QAAQ,EAAE,EAAE;QACV,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG;YAC3B,IAAI,CAAC,KAAK,GAAG;YACb;QACF;QAEA,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC;QAEzB,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM;YAC5B,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,+BACA,MACA,MACA;YAGF,GAAG;YACH;QACF;QAEA,MAAM,aAAa,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM;QAEvC,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC,EAAE;YACpE,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,sBACA,MACA,MACA;YAGF,GAAG;YACH;QACF;QAEA,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM;QAChC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG;QACxB,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,EAAE,GAAG;QAE/B,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;YACzB,IAAI,YAAY;gBACd,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,sBACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,oBACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW;QACjC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,OAAO,KAAK,MAAM;YACzD,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,EAAE,EAChC,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IAAI,CAAC,WAAW,GAAG;QACrB,OAAO,IAAI,IAAI,CAAC,OAAO,GAAG,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM;YACrD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACd,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,mBACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IAAI,YAAY;gBACd,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,sBACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IACE,IAAI,CAAC,cAAc,GAAG,QACrB,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,cAAc,KAAK,GAClD;gBACA,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,CAAC,uBAAuB,EAAE,IAAI,CAAC,cAAc,EAAE,EAC/C,MACA,MACA;gBAGF,GAAG;gBACH;YACF;QACF,OAAO;YACL,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,EAAE,EAChC,MACA,MACA;YAGF,GAAG;YACH;QACF;QAEA,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO;QACpE,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,MAAM;QAEnC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,oBACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;QACF,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE;YACvB,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,sBACA,MACA,MACA;YAGF,GAAG;YACH;QACF;QAEA,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,IAAI,CAAC,MAAM,GAAG;aAC1C,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,IAAI,CAAC,MAAM,GAAG;aAC/C,IAAI,CAAC,UAAU,CAAC;IACvB;IAEA;;;;;GAKC,GACD,mBAAmB,EAAE,EAAE;QACrB,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG;YAC3B,IAAI,CAAC,KAAK,GAAG;YACb;QACF;QAEA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC;QACnD,IAAI,CAAC,UAAU,CAAC;IAClB;IAEA;;;;;GAKC,GACD,mBAAmB,EAAE,EAAE;QACrB,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG;YAC3B,IAAI,CAAC,KAAK,GAAG;YACb;QACF;QAEA,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC;QACzB,MAAM,MAAM,IAAI,YAAY,CAAC;QAE7B,EAAE;QACF,2EAA2E;QAC3E,iDAAiD;QACjD,EAAE;QACF,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,MAAM,GAAG;YAClC,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,0DACA,OACA,MACA;YAGF,GAAG;YACH;QACF;QAEA,IAAI,CAAC,cAAc,GAAG,MAAM,KAAK,GAAG,CAAC,GAAG,MAAM,IAAI,YAAY,CAAC;QAC/D,IAAI,CAAC,UAAU,CAAC;IAClB;IAEA;;;;;GAKC,GACD,WAAW,EAAE,EAAE;QACb,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM;YAC9C,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,cAAc;YAC/C,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG;gBACvE,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,6BACA,OACA,MACA;gBAGF,GAAG;gBACH;YACF;QACF;QAEA,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,GAAG;aAC3B,IAAI,CAAC,MAAM,GAAG;IACrB;IAEA;;;;GAIC,GACD,UAAU;QACR,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG;YAC3B,IAAI,CAAC,KAAK,GAAG;YACb;QACF;QAEA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;;;;GAKC,GACD,QAAQ,EAAE,EAAE;QACV,IAAI,OAAO;QAEX,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE;gBAC7C,IAAI,CAAC,KAAK,GAAG;gBACb;YACF;YAEA,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc;YAEvC,IACE,IAAI,CAAC,OAAO,IACZ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,GACpE;gBACA,OAAO,MAAM,IAAI,CAAC,KAAK;YACzB;QACF;QAEA,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM;YACvB,IAAI,CAAC,cAAc,CAAC,MAAM;YAC1B;QACF;QAEA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,UAAU,CAAC,MAAM;YACtB;QACF;QAEA,IAAI,KAAK,MAAM,EAAE;YACf,EAAE;YACF,yEAAyE;YACzE,2BAA2B;YAC3B,EAAE;YACF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB;YAC9C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACvB;QAEA,IAAI,CAAC,WAAW,CAAC;IACnB;IAEA;;;;;;GAMC,GACD,WAAW,IAAI,EAAE,EAAE,EAAE;QACnB,MAAM,oBAAoB,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC;QAE3E,kBAAkB,UAAU,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK;YAClD,IAAI,KAAK,OAAO,GAAG;YAEnB,IAAI,IAAI,MAAM,EAAE;gBACd,IAAI,CAAC,cAAc,IAAI,IAAI,MAAM;gBACjC,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG;oBAClE,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,6BACA,OACA,MACA;oBAGF,GAAG;oBACH;gBACF;gBAEA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACvB;YAEA,IAAI,CAAC,WAAW,CAAC;YACjB,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,SAAS,CAAC;QAC/C;IACF;IAEA;;;;;GAKC,GACD,YAAY,EAAE,EAAE;QACd,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,IAAI,CAAC,MAAM,GAAG;YACd;QACF;QAEA,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,YAAY,IAAI,CAAC,UAAU;QAEjC,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,GAAG,EAAE;QAEpB,IAAI,IAAI,CAAC,OAAO,KAAK,GAAG;YACtB,IAAI;YAEJ,IAAI,IAAI,CAAC,WAAW,KAAK,cAAc;gBACrC,OAAO,OAAO,WAAW;YAC3B,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,eAAe;gBAC7C,OAAO,cAAc,OAAO,WAAW;YACzC,OAAO;gBACL,OAAO;YACT;YAEA,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAChC,IAAI,CAAC,IAAI,CAAC,WAAW,MAAM;gBAC3B,IAAI,CAAC,MAAM,GAAG;YAChB,OAAO;gBACL,IAAI,CAAC,MAAM,GAAG;gBACd,aAAa;oBACX,IAAI,CAAC,IAAI,CAAC,WAAW,MAAM;oBAC3B,IAAI,CAAC,MAAM,GAAG;oBACd,IAAI,CAAC,SAAS,CAAC;gBACjB;YACF;QACF,OAAO;YACL,MAAM,MAAM,OAAO,WAAW;YAE9B,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,YAAY,MAAM;gBAClD,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,OACA,0BACA,MACA,MACA;gBAGF,GAAG;gBACH;YACF;YAEA,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,IAAI,CAAC,uBAAuB,EAAE;gBAC7D,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK;gBAC1B,IAAI,CAAC,MAAM,GAAG;YAChB,OAAO;gBACL,IAAI,CAAC,MAAM,GAAG;gBACd,aAAa;oBACX,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK;oBAC1B,IAAI,CAAC,MAAM,GAAG;oBACd,IAAI,CAAC,SAAS,CAAC;gBACjB;YACF;QACF;IACF;IAEA;;;;;;GAMC,GACD,eAAe,IAAI,EAAE,EAAE,EAAE;QACvB,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;YACzB,IAAI,KAAK,MAAM,KAAK,GAAG;gBACrB,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,IAAI,CAAC,YAAY,MAAM;gBAC5B,IAAI,CAAC,GAAG;YACV,OAAO;gBACL,MAAM,OAAO,KAAK,YAAY,CAAC;gBAE/B,IAAI,CAAC,kBAAkB,OAAO;oBAC5B,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,YACA,CAAC,oBAAoB,EAAE,MAAM,EAC7B,MACA,MACA;oBAGF,GAAG;oBACH;gBACF;gBAEA,MAAM,MAAM,IAAI,WACd,KAAK,MAAM,EACX,KAAK,UAAU,GAAG,GAClB,KAAK,MAAM,GAAG;gBAGhB,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,YAAY,MAAM;oBAClD,MAAM,QAAQ,IAAI,CAAC,WAAW,CAC5B,OACA,0BACA,MACA,MACA;oBAGF,GAAG;oBACH;gBACF;gBAEA,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,IAAI,CAAC,YAAY,MAAM;gBAC5B,IAAI,CAAC,GAAG;YACV;YAEA,IAAI,CAAC,MAAM,GAAG;YACd;QACF;QAEA,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,OAAO,SAAS,QAAQ;YACnD,IAAI,CAAC,MAAM,GAAG;QAChB,OAAO;YACL,IAAI,CAAC,MAAM,GAAG;YACd,aAAa;gBACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,OAAO,SAAS,QAAQ;gBACnD,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,SAAS,CAAC;YACjB;QACF;IACF;IAEA;;;;;;;;;;;GAWC,GACD,YAAY,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE;QAC7D,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;QAEhB,MAAM,MAAM,IAAI,UACd,SAAS,CAAC,yBAAyB,EAAE,SAAS,GAAG;QAGnD,MAAM,iBAAiB,CAAC,KAAK,IAAI,CAAC,WAAW;QAC7C,IAAI,IAAI,GAAG;QACX,GAAG,CAAC,YAAY,GAAG;QACnB,OAAO;IACT;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1390, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/sender.js"], "sourcesContent": ["/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex\" }] */\n\n'use strict';\n\nconst { Duplex } = require('stream');\nconst { randomFillSync } = require('crypto');\n\nconst PerMessageDeflate = require('./permessage-deflate');\nconst { EMPTY_BUFFER } = require('./constants');\nconst { isValidStatusCode } = require('./validation');\nconst { mask: applyMask, toBuffer } = require('./buffer-util');\n\nconst kByteLength = Symbol('kByteLength');\nconst maskBuffer = Buffer.alloc(4);\nconst RANDOM_POOL_SIZE = 8 * 1024;\nlet randomPool;\nlet randomPoolPointer = RANDOM_POOL_SIZE;\n\n/**\n * HyBi Sender implementation.\n */\nclass Sender {\n  /**\n   * Creates a Sender instance.\n   *\n   * @param {Duplex} socket The connection socket\n   * @param {Object} [extensions] An object containing the negotiated extensions\n   * @param {Function} [generateMask] The function used to generate the masking\n   *     key\n   */\n  constructor(socket, extensions, generateMask) {\n    this._extensions = extensions || {};\n\n    if (generateMask) {\n      this._generateMask = generateMask;\n      this._maskBuffer = Buffer.alloc(4);\n    }\n\n    this._socket = socket;\n\n    this._firstFragment = true;\n    this._compress = false;\n\n    this._bufferedBytes = 0;\n    this._deflating = false;\n    this._queue = [];\n  }\n\n  /**\n   * Frames a piece of data according to the HyBi WebSocket protocol.\n   *\n   * @param {(Buffer|String)} data The data to frame\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @return {(Buffer|String)[]} The framed data\n   * @public\n   */\n  static frame(data, options) {\n    let mask;\n    let merge = false;\n    let offset = 2;\n    let skipMasking = false;\n\n    if (options.mask) {\n      mask = options.maskBuffer || maskBuffer;\n\n      if (options.generateMask) {\n        options.generateMask(mask);\n      } else {\n        if (randomPoolPointer === RANDOM_POOL_SIZE) {\n          /* istanbul ignore else  */\n          if (randomPool === undefined) {\n            //\n            // This is lazily initialized because server-sent frames must not\n            // be masked so it may never be used.\n            //\n            randomPool = Buffer.alloc(RANDOM_POOL_SIZE);\n          }\n\n          randomFillSync(randomPool, 0, RANDOM_POOL_SIZE);\n          randomPoolPointer = 0;\n        }\n\n        mask[0] = randomPool[randomPoolPointer++];\n        mask[1] = randomPool[randomPoolPointer++];\n        mask[2] = randomPool[randomPoolPointer++];\n        mask[3] = randomPool[randomPoolPointer++];\n      }\n\n      skipMasking = (mask[0] | mask[1] | mask[2] | mask[3]) === 0;\n      offset = 6;\n    }\n\n    let dataLength;\n\n    if (typeof data === 'string') {\n      if (\n        (!options.mask || skipMasking) &&\n        options[kByteLength] !== undefined\n      ) {\n        dataLength = options[kByteLength];\n      } else {\n        data = Buffer.from(data);\n        dataLength = data.length;\n      }\n    } else {\n      dataLength = data.length;\n      merge = options.mask && options.readOnly && !skipMasking;\n    }\n\n    let payloadLength = dataLength;\n\n    if (dataLength >= 65536) {\n      offset += 8;\n      payloadLength = 127;\n    } else if (dataLength > 125) {\n      offset += 2;\n      payloadLength = 126;\n    }\n\n    const target = Buffer.allocUnsafe(merge ? dataLength + offset : offset);\n\n    target[0] = options.fin ? options.opcode | 0x80 : options.opcode;\n    if (options.rsv1) target[0] |= 0x40;\n\n    target[1] = payloadLength;\n\n    if (payloadLength === 126) {\n      target.writeUInt16BE(dataLength, 2);\n    } else if (payloadLength === 127) {\n      target[2] = target[3] = 0;\n      target.writeUIntBE(dataLength, 4, 6);\n    }\n\n    if (!options.mask) return [target, data];\n\n    target[1] |= 0x80;\n    target[offset - 4] = mask[0];\n    target[offset - 3] = mask[1];\n    target[offset - 2] = mask[2];\n    target[offset - 1] = mask[3];\n\n    if (skipMasking) return [target, data];\n\n    if (merge) {\n      applyMask(data, mask, target, offset, dataLength);\n      return [target];\n    }\n\n    applyMask(data, mask, data, 0, dataLength);\n    return [target, data];\n  }\n\n  /**\n   * Sends a close message to the other peer.\n   *\n   * @param {Number} [code] The status code component of the body\n   * @param {(String|Buffer)} [data] The message component of the body\n   * @param {Boolean} [mask=false] Specifies whether or not to mask the message\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  close(code, data, mask, cb) {\n    let buf;\n\n    if (code === undefined) {\n      buf = EMPTY_BUFFER;\n    } else if (typeof code !== 'number' || !isValidStatusCode(code)) {\n      throw new TypeError('First argument must be a valid error code number');\n    } else if (data === undefined || !data.length) {\n      buf = Buffer.allocUnsafe(2);\n      buf.writeUInt16BE(code, 0);\n    } else {\n      const length = Buffer.byteLength(data);\n\n      if (length > 123) {\n        throw new RangeError('The message must not be greater than 123 bytes');\n      }\n\n      buf = Buffer.allocUnsafe(2 + length);\n      buf.writeUInt16BE(code, 0);\n\n      if (typeof data === 'string') {\n        buf.write(data, 2);\n      } else {\n        buf.set(data, 2);\n      }\n    }\n\n    const options = {\n      [kByteLength]: buf.length,\n      fin: true,\n      generateMask: this._generateMask,\n      mask,\n      maskBuffer: this._maskBuffer,\n      opcode: 0x08,\n      readOnly: false,\n      rsv1: false\n    };\n\n    if (this._deflating) {\n      this.enqueue([this.dispatch, buf, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(buf, options), cb);\n    }\n  }\n\n  /**\n   * Sends a ping message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  ping(data, mask, cb) {\n    let byteLength;\n    let readOnly;\n\n    if (typeof data === 'string') {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer(data);\n      byteLength = data.length;\n      readOnly = toBuffer.readOnly;\n    }\n\n    if (byteLength > 125) {\n      throw new RangeError('The data size must not be greater than 125 bytes');\n    }\n\n    const options = {\n      [kByteLength]: byteLength,\n      fin: true,\n      generateMask: this._generateMask,\n      mask,\n      maskBuffer: this._maskBuffer,\n      opcode: 0x09,\n      readOnly,\n      rsv1: false\n    };\n\n    if (this._deflating) {\n      this.enqueue([this.dispatch, data, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(data, options), cb);\n    }\n  }\n\n  /**\n   * Sends a pong message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  pong(data, mask, cb) {\n    let byteLength;\n    let readOnly;\n\n    if (typeof data === 'string') {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer(data);\n      byteLength = data.length;\n      readOnly = toBuffer.readOnly;\n    }\n\n    if (byteLength > 125) {\n      throw new RangeError('The data size must not be greater than 125 bytes');\n    }\n\n    const options = {\n      [kByteLength]: byteLength,\n      fin: true,\n      generateMask: this._generateMask,\n      mask,\n      maskBuffer: this._maskBuffer,\n      opcode: 0x0a,\n      readOnly,\n      rsv1: false\n    };\n\n    if (this._deflating) {\n      this.enqueue([this.dispatch, data, false, options, cb]);\n    } else {\n      this.sendFrame(Sender.frame(data, options), cb);\n    }\n  }\n\n  /**\n   * Sends a data message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Object} options Options object\n   * @param {Boolean} [options.binary=false] Specifies whether `data` is binary\n   *     or text\n   * @param {Boolean} [options.compress=false] Specifies whether or not to\n   *     compress `data`\n   * @param {Boolean} [options.fin=false] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */\n  send(data, options, cb) {\n    const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n    let opcode = options.binary ? 2 : 1;\n    let rsv1 = options.compress;\n\n    let byteLength;\n    let readOnly;\n\n    if (typeof data === 'string') {\n      byteLength = Buffer.byteLength(data);\n      readOnly = false;\n    } else {\n      data = toBuffer(data);\n      byteLength = data.length;\n      readOnly = toBuffer.readOnly;\n    }\n\n    if (this._firstFragment) {\n      this._firstFragment = false;\n      if (\n        rsv1 &&\n        perMessageDeflate &&\n        perMessageDeflate.params[\n          perMessageDeflate._isServer\n            ? 'server_no_context_takeover'\n            : 'client_no_context_takeover'\n        ]\n      ) {\n        rsv1 = byteLength >= perMessageDeflate._threshold;\n      }\n      this._compress = rsv1;\n    } else {\n      rsv1 = false;\n      opcode = 0;\n    }\n\n    if (options.fin) this._firstFragment = true;\n\n    if (perMessageDeflate) {\n      const opts = {\n        [kByteLength]: byteLength,\n        fin: options.fin,\n        generateMask: this._generateMask,\n        mask: options.mask,\n        maskBuffer: this._maskBuffer,\n        opcode,\n        readOnly,\n        rsv1\n      };\n\n      if (this._deflating) {\n        this.enqueue([this.dispatch, data, this._compress, opts, cb]);\n      } else {\n        this.dispatch(data, this._compress, opts, cb);\n      }\n    } else {\n      this.sendFrame(\n        Sender.frame(data, {\n          [kByteLength]: byteLength,\n          fin: options.fin,\n          generateMask: this._generateMask,\n          mask: options.mask,\n          maskBuffer: this._maskBuffer,\n          opcode,\n          readOnly,\n          rsv1: false\n        }),\n        cb\n      );\n    }\n  }\n\n  /**\n   * Dispatches a message.\n   *\n   * @param {(Buffer|String)} data The message to send\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     `data`\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */\n  dispatch(data, compress, options, cb) {\n    if (!compress) {\n      this.sendFrame(Sender.frame(data, options), cb);\n      return;\n    }\n\n    const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n\n    this._bufferedBytes += options[kByteLength];\n    this._deflating = true;\n    perMessageDeflate.compress(data, options.fin, (_, buf) => {\n      if (this._socket.destroyed) {\n        const err = new Error(\n          'The socket was closed while data was being compressed'\n        );\n\n        if (typeof cb === 'function') cb(err);\n\n        for (let i = 0; i < this._queue.length; i++) {\n          const params = this._queue[i];\n          const callback = params[params.length - 1];\n\n          if (typeof callback === 'function') callback(err);\n        }\n\n        return;\n      }\n\n      this._bufferedBytes -= options[kByteLength];\n      this._deflating = false;\n      options.readOnly = false;\n      this.sendFrame(Sender.frame(buf, options), cb);\n      this.dequeue();\n    });\n  }\n\n  /**\n   * Executes queued send operations.\n   *\n   * @private\n   */\n  dequeue() {\n    while (!this._deflating && this._queue.length) {\n      const params = this._queue.shift();\n\n      this._bufferedBytes -= params[3][kByteLength];\n      Reflect.apply(params[0], this, params.slice(1));\n    }\n  }\n\n  /**\n   * Enqueues a send operation.\n   *\n   * @param {Array} params Send operation parameters.\n   * @private\n   */\n  enqueue(params) {\n    this._bufferedBytes += params[3][kByteLength];\n    this._queue.push(params);\n  }\n\n  /**\n   * Sends a frame.\n   *\n   * @param {Buffer[]} list The frame to send\n   * @param {Function} [cb] Callback\n   * @private\n   */\n  sendFrame(list, cb) {\n    if (list.length === 2) {\n      this._socket.cork();\n      this._socket.write(list[0]);\n      this._socket.write(list[1], cb);\n      this._socket.uncork();\n    } else {\n      this._socket.write(list[0], cb);\n    }\n  }\n}\n\nmodule.exports = Sender;\n"], "names": [], "mappings": "AAAA,wEAAwE,GAExE;AAEA,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM,EAAE,cAAc,EAAE;AAExB,MAAM;AACN,MAAM,EAAE,YAAY,EAAE;AACtB,MAAM,EAAE,iBAAiB,EAAE;AAC3B,MAAM,EAAE,MAAM,SAAS,EAAE,QAAQ,EAAE;AAEnC,MAAM,cAAc,OAAO;AAC3B,MAAM,aAAa,OAAO,KAAK,CAAC;AAChC,MAAM,mBAAmB,IAAI;AAC7B,IAAI;AACJ,IAAI,oBAAoB;AAExB;;CAEC,GACD,MAAM;IACJ;;;;;;;GAOC,GACD,YAAY,MAAM,EAAE,UAAU,EAAE,YAAY,CAAE;QAC5C,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC;QAElC,IAAI,cAAc;YAChB,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI,CAAC,WAAW,GAAG,OAAO,KAAK,CAAC;QAClC;QAEA,IAAI,CAAC,OAAO,GAAG;QAEf,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,SAAS,GAAG;QAEjB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,MAAM,GAAG,EAAE;IAClB;IAEA;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,OAAO,MAAM,IAAI,EAAE,OAAO,EAAE;QAC1B,IAAI;QACJ,IAAI,QAAQ;QACZ,IAAI,SAAS;QACb,IAAI,cAAc;QAElB,IAAI,QAAQ,IAAI,EAAE;YAChB,OAAO,QAAQ,UAAU,IAAI;YAE7B,IAAI,QAAQ,YAAY,EAAE;gBACxB,QAAQ,YAAY,CAAC;YACvB,OAAO;gBACL,IAAI,sBAAsB,kBAAkB;oBAC1C,yBAAyB,GACzB,IAAI,eAAe,WAAW;wBAC5B,EAAE;wBACF,iEAAiE;wBACjE,qCAAqC;wBACrC,EAAE;wBACF,aAAa,OAAO,KAAK,CAAC;oBAC5B;oBAEA,eAAe,YAAY,GAAG;oBAC9B,oBAAoB;gBACtB;gBAEA,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,oBAAoB;gBACzC,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,oBAAoB;gBACzC,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,oBAAoB;gBACzC,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,oBAAoB;YAC3C;YAEA,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,MAAM;YAC1D,SAAS;QACX;QAEA,IAAI;QAEJ,IAAI,OAAO,SAAS,UAAU;YAC5B,IACE,CAAC,CAAC,QAAQ,IAAI,IAAI,WAAW,KAC7B,OAAO,CAAC,YAAY,KAAK,WACzB;gBACA,aAAa,OAAO,CAAC,YAAY;YACnC,OAAO;gBACL,OAAO,OAAO,IAAI,CAAC;gBACnB,aAAa,KAAK,MAAM;YAC1B;QACF,OAAO;YACL,aAAa,KAAK,MAAM;YACxB,QAAQ,QAAQ,IAAI,IAAI,QAAQ,QAAQ,IAAI,CAAC;QAC/C;QAEA,IAAI,gBAAgB;QAEpB,IAAI,cAAc,OAAO;YACvB,UAAU;YACV,gBAAgB;QAClB,OAAO,IAAI,aAAa,KAAK;YAC3B,UAAU;YACV,gBAAgB;QAClB;QAEA,MAAM,SAAS,OAAO,WAAW,CAAC,QAAQ,aAAa,SAAS;QAEhE,MAAM,CAAC,EAAE,GAAG,QAAQ,GAAG,GAAG,QAAQ,MAAM,GAAG,OAAO,QAAQ,MAAM;QAChE,IAAI,QAAQ,IAAI,EAAE,MAAM,CAAC,EAAE,IAAI;QAE/B,MAAM,CAAC,EAAE,GAAG;QAEZ,IAAI,kBAAkB,KAAK;YACzB,OAAO,aAAa,CAAC,YAAY;QACnC,OAAO,IAAI,kBAAkB,KAAK;YAChC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG;YACxB,OAAO,WAAW,CAAC,YAAY,GAAG;QACpC;QAEA,IAAI,CAAC,QAAQ,IAAI,EAAE,OAAO;YAAC;YAAQ;SAAK;QAExC,MAAM,CAAC,EAAE,IAAI;QACb,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC5B,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC5B,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC5B,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAE5B,IAAI,aAAa,OAAO;YAAC;YAAQ;SAAK;QAEtC,IAAI,OAAO;YACT,UAAU,MAAM,MAAM,QAAQ,QAAQ;YACtC,OAAO;gBAAC;aAAO;QACjB;QAEA,UAAU,MAAM,MAAM,MAAM,GAAG;QAC/B,OAAO;YAAC;YAAQ;SAAK;IACvB;IAEA;;;;;;;;GAQC,GACD,MAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QAC1B,IAAI;QAEJ,IAAI,SAAS,WAAW;YACtB,MAAM;QACR,OAAO,IAAI,OAAO,SAAS,YAAY,CAAC,kBAAkB,OAAO;YAC/D,MAAM,IAAI,UAAU;QACtB,OAAO,IAAI,SAAS,aAAa,CAAC,KAAK,MAAM,EAAE;YAC7C,MAAM,OAAO,WAAW,CAAC;YACzB,IAAI,aAAa,CAAC,MAAM;QAC1B,OAAO;YACL,MAAM,SAAS,OAAO,UAAU,CAAC;YAEjC,IAAI,SAAS,KAAK;gBAChB,MAAM,IAAI,WAAW;YACvB;YAEA,MAAM,OAAO,WAAW,CAAC,IAAI;YAC7B,IAAI,aAAa,CAAC,MAAM;YAExB,IAAI,OAAO,SAAS,UAAU;gBAC5B,IAAI,KAAK,CAAC,MAAM;YAClB,OAAO;gBACL,IAAI,GAAG,CAAC,MAAM;YAChB;QACF;QAEA,MAAM,UAAU;YACd,CAAC,YAAY,EAAE,IAAI,MAAM;YACzB,KAAK;YACL,cAAc,IAAI,CAAC,aAAa;YAChC;YACA,YAAY,IAAI,CAAC,WAAW;YAC5B,QAAQ;YACR,UAAU;YACV,MAAM;QACR;QAEA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC;gBAAC,IAAI,CAAC,QAAQ;gBAAE;gBAAK;gBAAO;gBAAS;aAAG;QACvD,OAAO;YACL,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,CAAC,KAAK,UAAU;QAC7C;IACF;IAEA;;;;;;;GAOC,GACD,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QACnB,IAAI;QACJ,IAAI;QAEJ,IAAI,OAAO,SAAS,UAAU;YAC5B,aAAa,OAAO,UAAU,CAAC;YAC/B,WAAW;QACb,OAAO;YACL,OAAO,SAAS;YAChB,aAAa,KAAK,MAAM;YACxB,WAAW,SAAS,QAAQ;QAC9B;QAEA,IAAI,aAAa,KAAK;YACpB,MAAM,IAAI,WAAW;QACvB;QAEA,MAAM,UAAU;YACd,CAAC,YAAY,EAAE;YACf,KAAK;YACL,cAAc,IAAI,CAAC,aAAa;YAChC;YACA,YAAY,IAAI,CAAC,WAAW;YAC5B,QAAQ;YACR;YACA,MAAM;QACR;QAEA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC;gBAAC,IAAI,CAAC,QAAQ;gBAAE;gBAAM;gBAAO;gBAAS;aAAG;QACxD,OAAO;YACL,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,CAAC,MAAM,UAAU;QAC9C;IACF;IAEA;;;;;;;GAOC,GACD,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QACnB,IAAI;QACJ,IAAI;QAEJ,IAAI,OAAO,SAAS,UAAU;YAC5B,aAAa,OAAO,UAAU,CAAC;YAC/B,WAAW;QACb,OAAO;YACL,OAAO,SAAS;YAChB,aAAa,KAAK,MAAM;YACxB,WAAW,SAAS,QAAQ;QAC9B;QAEA,IAAI,aAAa,KAAK;YACpB,MAAM,IAAI,WAAW;QACvB;QAEA,MAAM,UAAU;YACd,CAAC,YAAY,EAAE;YACf,KAAK;YACL,cAAc,IAAI,CAAC,aAAa;YAChC;YACA,YAAY,IAAI,CAAC,WAAW;YAC5B,QAAQ;YACR;YACA,MAAM;QACR;QAEA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC;gBAAC,IAAI,CAAC,QAAQ;gBAAE;gBAAM;gBAAO;gBAAS;aAAG;QACxD,OAAO;YACL,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,CAAC,MAAM,UAAU;QAC9C;IACF;IAEA;;;;;;;;;;;;;;;GAeC,GACD,KAAK,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;QACtB,MAAM,oBAAoB,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC;QAC3E,IAAI,SAAS,QAAQ,MAAM,GAAG,IAAI;QAClC,IAAI,OAAO,QAAQ,QAAQ;QAE3B,IAAI;QACJ,IAAI;QAEJ,IAAI,OAAO,SAAS,UAAU;YAC5B,aAAa,OAAO,UAAU,CAAC;YAC/B,WAAW;QACb,OAAO;YACL,OAAO,SAAS;YAChB,aAAa,KAAK,MAAM;YACxB,WAAW,SAAS,QAAQ;QAC9B;QAEA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,GAAG;YACtB,IACE,QACA,qBACA,kBAAkB,MAAM,CACtB,kBAAkB,SAAS,GACvB,+BACA,6BACL,EACD;gBACA,OAAO,cAAc,kBAAkB,UAAU;YACnD;YACA,IAAI,CAAC,SAAS,GAAG;QACnB,OAAO;YACL,OAAO;YACP,SAAS;QACX;QAEA,IAAI,QAAQ,GAAG,EAAE,IAAI,CAAC,cAAc,GAAG;QAEvC,IAAI,mBAAmB;YACrB,MAAM,OAAO;gBACX,CAAC,YAAY,EAAE;gBACf,KAAK,QAAQ,GAAG;gBAChB,cAAc,IAAI,CAAC,aAAa;gBAChC,MAAM,QAAQ,IAAI;gBAClB,YAAY,IAAI,CAAC,WAAW;gBAC5B;gBACA;gBACA;YACF;YAEA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,OAAO,CAAC;oBAAC,IAAI,CAAC,QAAQ;oBAAE;oBAAM,IAAI,CAAC,SAAS;oBAAE;oBAAM;iBAAG;YAC9D,OAAO;gBACL,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,MAAM;YAC5C;QACF,OAAO;YACL,IAAI,CAAC,SAAS,CACZ,OAAO,KAAK,CAAC,MAAM;gBACjB,CAAC,YAAY,EAAE;gBACf,KAAK,QAAQ,GAAG;gBAChB,cAAc,IAAI,CAAC,aAAa;gBAChC,MAAM,QAAQ,IAAI;gBAClB,YAAY,IAAI,CAAC,WAAW;gBAC5B;gBACA;gBACA,MAAM;YACR,IACA;QAEJ;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;GAsBC,GACD,SAAS,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE;QACpC,IAAI,CAAC,UAAU;YACb,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,CAAC,MAAM,UAAU;YAC5C;QACF;QAEA,MAAM,oBAAoB,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC;QAE3E,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,YAAY;QAC3C,IAAI,CAAC,UAAU,GAAG;QAClB,kBAAkB,QAAQ,CAAC,MAAM,QAAQ,GAAG,EAAE,CAAC,GAAG;YAChD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gBAC1B,MAAM,MAAM,IAAI,MACd;gBAGF,IAAI,OAAO,OAAO,YAAY,GAAG;gBAEjC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAK;oBAC3C,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC7B,MAAM,WAAW,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;oBAE1C,IAAI,OAAO,aAAa,YAAY,SAAS;gBAC/C;gBAEA;YACF;YAEA,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,YAAY;YAC3C,IAAI,CAAC,UAAU,GAAG;YAClB,QAAQ,QAAQ,GAAG;YACnB,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,CAAC,KAAK,UAAU;YAC3C,IAAI,CAAC,OAAO;QACd;IACF;IAEA;;;;GAIC,GACD,UAAU;QACR,MAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAE;YAC7C,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK;YAEhC,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,EAAE,CAAC,YAAY;YAC7C,QAAQ,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,KAAK,CAAC;QAC9C;IACF;IAEA;;;;;GAKC,GACD,QAAQ,MAAM,EAAE;QACd,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,EAAE,CAAC,YAAY;QAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACnB;IAEA;;;;;;GAMC,GACD,UAAU,IAAI,EAAE,EAAE,EAAE;QAClB,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI;YACjB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;YAC5B,IAAI,CAAC,OAAO,CAAC,MAAM;QACrB,OAAO;YACL,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;QAC9B;IACF;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1833, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/event-target.js"], "sourcesContent": ["'use strict';\n\nconst { kForOnEventAttribute, kListener } = require('./constants');\n\nconst kCode = Symbol('kCode');\nconst kData = Symbol('kData');\nconst kError = Symbol('kError');\nconst kMessage = Symbol('kMessage');\nconst kReason = Symbol('kReason');\nconst kTarget = Symbol('kTarget');\nconst kType = Symbol('kType');\nconst kWasClean = Symbol('kWasClean');\n\n/**\n * Class representing an event.\n */\nclass Event {\n  /**\n   * Create a new `Event`.\n   *\n   * @param {String} type The name of the event\n   * @throws {TypeError} If the `type` argument is not specified\n   */\n  constructor(type) {\n    this[kTarget] = null;\n    this[kType] = type;\n  }\n\n  /**\n   * @type {*}\n   */\n  get target() {\n    return this[kTarget];\n  }\n\n  /**\n   * @type {String}\n   */\n  get type() {\n    return this[kType];\n  }\n}\n\nObject.defineProperty(Event.prototype, 'target', { enumerable: true });\nObject.defineProperty(Event.prototype, 'type', { enumerable: true });\n\n/**\n * Class representing a close event.\n *\n * @extends Event\n */\nclass CloseEvent extends Event {\n  /**\n   * Create a new `CloseEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {Number} [options.code=0] The status code explaining why the\n   *     connection was closed\n   * @param {String} [options.reason=''] A human-readable string explaining why\n   *     the connection was closed\n   * @param {Boolean} [options.wasClean=false] Indicates whether or not the\n   *     connection was cleanly closed\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kCode] = options.code === undefined ? 0 : options.code;\n    this[kReason] = options.reason === undefined ? '' : options.reason;\n    this[kWasClean] = options.wasClean === undefined ? false : options.wasClean;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get code() {\n    return this[kCode];\n  }\n\n  /**\n   * @type {String}\n   */\n  get reason() {\n    return this[kReason];\n  }\n\n  /**\n   * @type {Boolean}\n   */\n  get wasClean() {\n    return this[kWasClean];\n  }\n}\n\nObject.defineProperty(CloseEvent.prototype, 'code', { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, 'reason', { enumerable: true });\nObject.defineProperty(CloseEvent.prototype, 'wasClean', { enumerable: true });\n\n/**\n * Class representing an error event.\n *\n * @extends Event\n */\nclass ErrorEvent extends Event {\n  /**\n   * Create a new `ErrorEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.error=null] The error that generated this event\n   * @param {String} [options.message=''] The error message\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kError] = options.error === undefined ? null : options.error;\n    this[kMessage] = options.message === undefined ? '' : options.message;\n  }\n\n  /**\n   * @type {*}\n   */\n  get error() {\n    return this[kError];\n  }\n\n  /**\n   * @type {String}\n   */\n  get message() {\n    return this[kMessage];\n  }\n}\n\nObject.defineProperty(ErrorEvent.prototype, 'error', { enumerable: true });\nObject.defineProperty(ErrorEvent.prototype, 'message', { enumerable: true });\n\n/**\n * Class representing a message event.\n *\n * @extends Event\n */\nclass MessageEvent extends Event {\n  /**\n   * Create a new `MessageEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.data=null] The message content\n   */\n  constructor(type, options = {}) {\n    super(type);\n\n    this[kData] = options.data === undefined ? null : options.data;\n  }\n\n  /**\n   * @type {*}\n   */\n  get data() {\n    return this[kData];\n  }\n}\n\nObject.defineProperty(MessageEvent.prototype, 'data', { enumerable: true });\n\n/**\n * This provides methods for emulating the `EventTarget` interface. It's not\n * meant to be used directly.\n *\n * @mixin\n */\nconst EventTarget = {\n  /**\n   * Register an event listener.\n   *\n   * @param {String} type A string representing the event type to listen for\n   * @param {(Function|Object)} handler The listener to add\n   * @param {Object} [options] An options object specifies characteristics about\n   *     the event listener\n   * @param {Boolean} [options.once=false] A `Boolean` indicating that the\n   *     listener should be invoked at most once after being added. If `true`,\n   *     the listener would be automatically removed when invoked.\n   * @public\n   */\n  addEventListener(type, handler, options = {}) {\n    for (const listener of this.listeners(type)) {\n      if (\n        !options[kForOnEventAttribute] &&\n        listener[kListener] === handler &&\n        !listener[kForOnEventAttribute]\n      ) {\n        return;\n      }\n    }\n\n    let wrapper;\n\n    if (type === 'message') {\n      wrapper = function onMessage(data, isBinary) {\n        const event = new MessageEvent('message', {\n          data: isBinary ? data : data.toString()\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'close') {\n      wrapper = function onClose(code, message) {\n        const event = new CloseEvent('close', {\n          code,\n          reason: message.toString(),\n          wasClean: this._closeFrameReceived && this._closeFrameSent\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'error') {\n      wrapper = function onError(error) {\n        const event = new ErrorEvent('error', {\n          error,\n          message: error.message\n        });\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else if (type === 'open') {\n      wrapper = function onOpen() {\n        const event = new Event('open');\n\n        event[kTarget] = this;\n        callListener(handler, this, event);\n      };\n    } else {\n      return;\n    }\n\n    wrapper[kForOnEventAttribute] = !!options[kForOnEventAttribute];\n    wrapper[kListener] = handler;\n\n    if (options.once) {\n      this.once(type, wrapper);\n    } else {\n      this.on(type, wrapper);\n    }\n  },\n\n  /**\n   * Remove an event listener.\n   *\n   * @param {String} type A string representing the event type to remove\n   * @param {(Function|Object)} handler The listener to remove\n   * @public\n   */\n  removeEventListener(type, handler) {\n    for (const listener of this.listeners(type)) {\n      if (listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n        this.removeListener(type, listener);\n        break;\n      }\n    }\n  }\n};\n\nmodule.exports = {\n  CloseEvent,\n  ErrorEvent,\n  Event,\n  EventTarget,\n  MessageEvent\n};\n\n/**\n * Call an event listener\n *\n * @param {(Function|Object)} listener The listener to call\n * @param {*} thisArg The value to use as `this`` when calling the listener\n * @param {Event} event The event to pass to the listener\n * @private\n */\nfunction callListener(listener, thisArg, event) {\n  if (typeof listener === 'object' && listener.handleEvent) {\n    listener.handleEvent.call(listener, event);\n  } else {\n    listener.call(thisArg, event);\n  }\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,EAAE,oBAAoB,EAAE,SAAS,EAAE;AAEzC,MAAM,QAAQ,OAAO;AACrB,MAAM,QAAQ,OAAO;AACrB,MAAM,SAAS,OAAO;AACtB,MAAM,WAAW,OAAO;AACxB,MAAM,UAAU,OAAO;AACvB,MAAM,UAAU,OAAO;AACvB,MAAM,QAAQ,OAAO;AACrB,MAAM,YAAY,OAAO;AAEzB;;CAEC,GACD,MAAM;IACJ;;;;;GAKC,GACD,YAAY,IAAI,CAAE;QAChB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;GAEC,GACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA;;GAEC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM;IACpB;AACF;AAEA,OAAO,cAAc,CAAC,MAAM,SAAS,EAAE,UAAU;IAAE,YAAY;AAAK;AACpE,OAAO,cAAc,CAAC,MAAM,SAAS,EAAE,QAAQ;IAAE,YAAY;AAAK;AAElE;;;;CAIC,GACD,MAAM,mBAAmB;IACvB;;;;;;;;;;;;GAYC,GACD,YAAY,IAAI,EAAE,UAAU,CAAC,CAAC,CAAE;QAC9B,KAAK,CAAC;QAEN,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,KAAK,YAAY,IAAI,QAAQ,IAAI;QAC3D,IAAI,CAAC,QAAQ,GAAG,QAAQ,MAAM,KAAK,YAAY,KAAK,QAAQ,MAAM;QAClE,IAAI,CAAC,UAAU,GAAG,QAAQ,QAAQ,KAAK,YAAY,QAAQ,QAAQ,QAAQ;IAC7E;IAEA;;GAEC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA;;GAEC,GACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA;;GAEC,GACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,UAAU;IACxB;AACF;AAEA,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,QAAQ;IAAE,YAAY;AAAK;AACvE,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,UAAU;IAAE,YAAY;AAAK;AACzE,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,YAAY;IAAE,YAAY;AAAK;AAE3E;;;;CAIC,GACD,MAAM,mBAAmB;IACvB;;;;;;;;GAQC,GACD,YAAY,IAAI,EAAE,UAAU,CAAC,CAAC,CAAE;QAC9B,KAAK,CAAC;QAEN,IAAI,CAAC,OAAO,GAAG,QAAQ,KAAK,KAAK,YAAY,OAAO,QAAQ,KAAK;QACjE,IAAI,CAAC,SAAS,GAAG,QAAQ,OAAO,KAAK,YAAY,KAAK,QAAQ,OAAO;IACvE;IAEA;;GAEC,GACD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA;;GAEC,GACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS;IACvB;AACF;AAEA,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,SAAS;IAAE,YAAY;AAAK;AACxE,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,WAAW;IAAE,YAAY;AAAK;AAE1E;;;;CAIC,GACD,MAAM,qBAAqB;IACzB;;;;;;;GAOC,GACD,YAAY,IAAI,EAAE,UAAU,CAAC,CAAC,CAAE;QAC9B,KAAK,CAAC;QAEN,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,KAAK,YAAY,OAAO,QAAQ,IAAI;IAChE;IAEA;;GAEC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM;IACpB;AACF;AAEA,OAAO,cAAc,CAAC,aAAa,SAAS,EAAE,QAAQ;IAAE,YAAY;AAAK;AAEzE;;;;;CAKC,GACD,MAAM,cAAc;IAClB;;;;;;;;;;;GAWC,GACD,kBAAiB,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC1C,KAAK,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,MAAO;YAC3C,IACE,CAAC,OAAO,CAAC,qBAAqB,IAC9B,QAAQ,CAAC,UAAU,KAAK,WACxB,CAAC,QAAQ,CAAC,qBAAqB,EAC/B;gBACA;YACF;QACF;QAEA,IAAI;QAEJ,IAAI,SAAS,WAAW;YACtB,UAAU,SAAS,UAAU,IAAI,EAAE,QAAQ;gBACzC,MAAM,QAAQ,IAAI,aAAa,WAAW;oBACxC,MAAM,WAAW,OAAO,KAAK,QAAQ;gBACvC;gBAEA,KAAK,CAAC,QAAQ,GAAG,IAAI;gBACrB,aAAa,SAAS,IAAI,EAAE;YAC9B;QACF,OAAO,IAAI,SAAS,SAAS;YAC3B,UAAU,SAAS,QAAQ,IAAI,EAAE,OAAO;gBACtC,MAAM,QAAQ,IAAI,WAAW,SAAS;oBACpC;oBACA,QAAQ,QAAQ,QAAQ;oBACxB,UAAU,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,eAAe;gBAC5D;gBAEA,KAAK,CAAC,QAAQ,GAAG,IAAI;gBACrB,aAAa,SAAS,IAAI,EAAE;YAC9B;QACF,OAAO,IAAI,SAAS,SAAS;YAC3B,UAAU,SAAS,QAAQ,KAAK;gBAC9B,MAAM,QAAQ,IAAI,WAAW,SAAS;oBACpC;oBACA,SAAS,MAAM,OAAO;gBACxB;gBAEA,KAAK,CAAC,QAAQ,GAAG,IAAI;gBACrB,aAAa,SAAS,IAAI,EAAE;YAC9B;QACF,OAAO,IAAI,SAAS,QAAQ;YAC1B,UAAU,SAAS;gBACjB,MAAM,QAAQ,IAAI,MAAM;gBAExB,KAAK,CAAC,QAAQ,GAAG,IAAI;gBACrB,aAAa,SAAS,IAAI,EAAE;YAC9B;QACF,OAAO;YACL;QACF;QAEA,OAAO,CAAC,qBAAqB,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB;QAC/D,OAAO,CAAC,UAAU,GAAG;QAErB,IAAI,QAAQ,IAAI,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,MAAM;QAClB,OAAO;YACL,IAAI,CAAC,EAAE,CAAC,MAAM;QAChB;IACF;IAEA;;;;;;GAMC,GACD,qBAAoB,IAAI,EAAE,OAAO;QAC/B,KAAK,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,MAAO;YAC3C,IAAI,QAAQ,CAAC,UAAU,KAAK,WAAW,CAAC,QAAQ,CAAC,qBAAqB,EAAE;gBACtE,IAAI,CAAC,cAAc,CAAC,MAAM;gBAC1B;YACF;QACF;IACF;AACF;AAEA,OAAO,OAAO,GAAG;IACf;IACA;IACA;IACA;IACA;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,aAAa,QAAQ,EAAE,OAAO,EAAE,KAAK;IAC5C,IAAI,OAAO,aAAa,YAAY,SAAS,WAAW,EAAE;QACxD,SAAS,WAAW,CAAC,IAAI,CAAC,UAAU;IACtC,OAAO;QACL,SAAS,IAAI,CAAC,SAAS;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2089, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/extension.js"], "sourcesContent": ["'use strict';\n\nconst { tokenChars } = require('./validation');\n\n/**\n * Adds an offer to the map of extension offers or a parameter to the map of\n * parameters.\n *\n * @param {Object} dest The map of extension offers or parameters\n * @param {String} name The extension or parameter name\n * @param {(Object|Boolean|String)} elem The extension parameters or the\n *     parameter value\n * @private\n */\nfunction push(dest, name, elem) {\n  if (dest[name] === undefined) dest[name] = [elem];\n  else dest[name].push(elem);\n}\n\n/**\n * Parses the `Sec-WebSocket-Extensions` header into an object.\n *\n * @param {String} header The field value of the header\n * @return {Object} The parsed object\n * @public\n */\nfunction parse(header) {\n  const offers = Object.create(null);\n  let params = Object.create(null);\n  let mustUnescape = false;\n  let isEscaping = false;\n  let inQuotes = false;\n  let extensionName;\n  let paramName;\n  let start = -1;\n  let code = -1;\n  let end = -1;\n  let i = 0;\n\n  for (; i < header.length; i++) {\n    code = header.charCodeAt(i);\n\n    if (extensionName === undefined) {\n      if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (\n        i !== 0 &&\n        (code === 0x20 /* ' ' */ || code === 0x09) /* '\\t' */\n      ) {\n        if (end === -1 && start !== -1) end = i;\n      } else if (code === 0x3b /* ';' */ || code === 0x2c /* ',' */) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        const name = header.slice(start, end);\n        if (code === 0x2c) {\n          push(offers, name, params);\n          params = Object.create(null);\n        } else {\n          extensionName = name;\n        }\n\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else if (paramName === undefined) {\n      if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (code === 0x20 || code === 0x09) {\n        if (end === -1 && start !== -1) end = i;\n      } else if (code === 0x3b || code === 0x2c) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        push(params, header.slice(start, end), true);\n        if (code === 0x2c) {\n          push(offers, extensionName, params);\n          params = Object.create(null);\n          extensionName = undefined;\n        }\n\n        start = end = -1;\n      } else if (code === 0x3d /* '=' */ && start !== -1 && end === -1) {\n        paramName = header.slice(start, i);\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    } else {\n      //\n      // The value of a quoted-string after unescaping must conform to the\n      // token ABNF, so only token characters are valid.\n      // Ref: https://tools.ietf.org/html/rfc6455#section-9.1\n      //\n      if (isEscaping) {\n        if (tokenChars[code] !== 1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n        if (start === -1) start = i;\n        else if (!mustUnescape) mustUnescape = true;\n        isEscaping = false;\n      } else if (inQuotes) {\n        if (tokenChars[code] === 1) {\n          if (start === -1) start = i;\n        } else if (code === 0x22 /* '\"' */ && start !== -1) {\n          inQuotes = false;\n          end = i;\n        } else if (code === 0x5c /* '\\' */) {\n          isEscaping = true;\n        } else {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n      } else if (code === 0x22 && header.charCodeAt(i - 1) === 0x3d) {\n        inQuotes = true;\n      } else if (end === -1 && tokenChars[code] === 1) {\n        if (start === -1) start = i;\n      } else if (start !== -1 && (code === 0x20 || code === 0x09)) {\n        if (end === -1) end = i;\n      } else if (code === 0x3b || code === 0x2c) {\n        if (start === -1) {\n          throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n\n        if (end === -1) end = i;\n        let value = header.slice(start, end);\n        if (mustUnescape) {\n          value = value.replace(/\\\\/g, '');\n          mustUnescape = false;\n        }\n        push(params, paramName, value);\n        if (code === 0x2c) {\n          push(offers, extensionName, params);\n          params = Object.create(null);\n          extensionName = undefined;\n        }\n\n        paramName = undefined;\n        start = end = -1;\n      } else {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n    }\n  }\n\n  if (start === -1 || inQuotes || code === 0x20 || code === 0x09) {\n    throw new SyntaxError('Unexpected end of input');\n  }\n\n  if (end === -1) end = i;\n  const token = header.slice(start, end);\n  if (extensionName === undefined) {\n    push(offers, token, params);\n  } else {\n    if (paramName === undefined) {\n      push(params, token, true);\n    } else if (mustUnescape) {\n      push(params, paramName, token.replace(/\\\\/g, ''));\n    } else {\n      push(params, paramName, token);\n    }\n    push(offers, extensionName, params);\n  }\n\n  return offers;\n}\n\n/**\n * Builds the `Sec-WebSocket-Extensions` header field value.\n *\n * @param {Object} extensions The map of extensions and parameters to format\n * @return {String} A string representing the given object\n * @public\n */\nfunction format(extensions) {\n  return Object.keys(extensions)\n    .map((extension) => {\n      let configurations = extensions[extension];\n      if (!Array.isArray(configurations)) configurations = [configurations];\n      return configurations\n        .map((params) => {\n          return [extension]\n            .concat(\n              Object.keys(params).map((k) => {\n                let values = params[k];\n                if (!Array.isArray(values)) values = [values];\n                return values\n                  .map((v) => (v === true ? k : `${k}=${v}`))\n                  .join('; ');\n              })\n            )\n            .join('; ');\n        })\n        .join(', ');\n    })\n    .join(', ');\n}\n\nmodule.exports = { format, parse };\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,EAAE,UAAU,EAAE;AAEpB;;;;;;;;;CASC,GACD,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE,IAAI;IAC5B,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,IAAI,CAAC,KAAK,GAAG;QAAC;KAAK;SAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AACvB;AAEA;;;;;;CAMC,GACD,SAAS,MAAM,MAAM;IACnB,MAAM,SAAS,OAAO,MAAM,CAAC;IAC7B,IAAI,SAAS,OAAO,MAAM,CAAC;IAC3B,IAAI,eAAe;IACnB,IAAI,aAAa;IACjB,IAAI,WAAW;IACf,IAAI;IACJ,IAAI;IACJ,IAAI,QAAQ,CAAC;IACb,IAAI,OAAO,CAAC;IACZ,IAAI,MAAM,CAAC;IACX,IAAI,IAAI;IAER,MAAO,IAAI,OAAO,MAAM,EAAE,IAAK;QAC7B,OAAO,OAAO,UAAU,CAAC;QAEzB,IAAI,kBAAkB,WAAW;YAC/B,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,KAAK,KAAK,GAAG;gBACxC,IAAI,UAAU,CAAC,GAAG,QAAQ;YAC5B,OAAO,IACL,MAAM,KACN,CAAC,SAAS,KAAK,OAAO,OAAM,SAAS,IAAI,GACzC;gBACA,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,GAAG,MAAM;YACxC,OAAO,IAAI,SAAS,KAAK,OAAO,OAAM,SAAS,KAAK,OAAO,KAAI;gBAC7D,IAAI,UAAU,CAAC,GAAG;oBAChB,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;gBAC5D;gBAEA,IAAI,QAAQ,CAAC,GAAG,MAAM;gBACtB,MAAM,OAAO,OAAO,KAAK,CAAC,OAAO;gBACjC,IAAI,SAAS,MAAM;oBACjB,KAAK,QAAQ,MAAM;oBACnB,SAAS,OAAO,MAAM,CAAC;gBACzB,OAAO;oBACL,gBAAgB;gBAClB;gBAEA,QAAQ,MAAM,CAAC;YACjB,OAAO;gBACL,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;YAC5D;QACF,OAAO,IAAI,cAAc,WAAW;YAClC,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,KAAK,KAAK,GAAG;gBACxC,IAAI,UAAU,CAAC,GAAG,QAAQ;YAC5B,OAAO,IAAI,SAAS,QAAQ,SAAS,MAAM;gBACzC,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,GAAG,MAAM;YACxC,OAAO,IAAI,SAAS,QAAQ,SAAS,MAAM;gBACzC,IAAI,UAAU,CAAC,GAAG;oBAChB,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;gBAC5D;gBAEA,IAAI,QAAQ,CAAC,GAAG,MAAM;gBACtB,KAAK,QAAQ,OAAO,KAAK,CAAC,OAAO,MAAM;gBACvC,IAAI,SAAS,MAAM;oBACjB,KAAK,QAAQ,eAAe;oBAC5B,SAAS,OAAO,MAAM,CAAC;oBACvB,gBAAgB;gBAClB;gBAEA,QAAQ,MAAM,CAAC;YACjB,OAAO,IAAI,SAAS,KAAK,OAAO,OAAM,UAAU,CAAC,KAAK,QAAQ,CAAC,GAAG;gBAChE,YAAY,OAAO,KAAK,CAAC,OAAO;gBAChC,QAAQ,MAAM,CAAC;YACjB,OAAO;gBACL,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;YAC5D;QACF,OAAO;YACL,EAAE;YACF,oEAAoE;YACpE,kDAAkD;YAClD,uDAAuD;YACvD,EAAE;YACF,IAAI,YAAY;gBACd,IAAI,UAAU,CAAC,KAAK,KAAK,GAAG;oBAC1B,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;gBAC5D;gBACA,IAAI,UAAU,CAAC,GAAG,QAAQ;qBACrB,IAAI,CAAC,cAAc,eAAe;gBACvC,aAAa;YACf,OAAO,IAAI,UAAU;gBACnB,IAAI,UAAU,CAAC,KAAK,KAAK,GAAG;oBAC1B,IAAI,UAAU,CAAC,GAAG,QAAQ;gBAC5B,OAAO,IAAI,SAAS,KAAK,OAAO,OAAM,UAAU,CAAC,GAAG;oBAClD,WAAW;oBACX,MAAM;gBACR,OAAO,IAAI,SAAS,KAAK,OAAO,KAAI;oBAClC,aAAa;gBACf,OAAO;oBACL,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;gBAC5D;YACF,OAAO,IAAI,SAAS,QAAQ,OAAO,UAAU,CAAC,IAAI,OAAO,MAAM;gBAC7D,WAAW;YACb,OAAO,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,KAAK,KAAK,GAAG;gBAC/C,IAAI,UAAU,CAAC,GAAG,QAAQ;YAC5B,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,QAAQ,SAAS,IAAI,GAAG;gBAC3D,IAAI,QAAQ,CAAC,GAAG,MAAM;YACxB,OAAO,IAAI,SAAS,QAAQ,SAAS,MAAM;gBACzC,IAAI,UAAU,CAAC,GAAG;oBAChB,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;gBAC5D;gBAEA,IAAI,QAAQ,CAAC,GAAG,MAAM;gBACtB,IAAI,QAAQ,OAAO,KAAK,CAAC,OAAO;gBAChC,IAAI,cAAc;oBAChB,QAAQ,MAAM,OAAO,CAAC,OAAO;oBAC7B,eAAe;gBACjB;gBACA,KAAK,QAAQ,WAAW;gBACxB,IAAI,SAAS,MAAM;oBACjB,KAAK,QAAQ,eAAe;oBAC5B,SAAS,OAAO,MAAM,CAAC;oBACvB,gBAAgB;gBAClB;gBAEA,YAAY;gBACZ,QAAQ,MAAM,CAAC;YACjB,OAAO;gBACL,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;YAC5D;QACF;IACF;IAEA,IAAI,UAAU,CAAC,KAAK,YAAY,SAAS,QAAQ,SAAS,MAAM;QAC9D,MAAM,IAAI,YAAY;IACxB;IAEA,IAAI,QAAQ,CAAC,GAAG,MAAM;IACtB,MAAM,QAAQ,OAAO,KAAK,CAAC,OAAO;IAClC,IAAI,kBAAkB,WAAW;QAC/B,KAAK,QAAQ,OAAO;IACtB,OAAO;QACL,IAAI,cAAc,WAAW;YAC3B,KAAK,QAAQ,OAAO;QACtB,OAAO,IAAI,cAAc;YACvB,KAAK,QAAQ,WAAW,MAAM,OAAO,CAAC,OAAO;QAC/C,OAAO;YACL,KAAK,QAAQ,WAAW;QAC1B;QACA,KAAK,QAAQ,eAAe;IAC9B;IAEA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,OAAO,UAAU;IACxB,OAAO,OAAO,IAAI,CAAC,YAChB,GAAG,CAAC,CAAC;QACJ,IAAI,iBAAiB,UAAU,CAAC,UAAU;QAC1C,IAAI,CAAC,MAAM,OAAO,CAAC,iBAAiB,iBAAiB;YAAC;SAAe;QACrE,OAAO,eACJ,GAAG,CAAC,CAAC;YACJ,OAAO;gBAAC;aAAU,CACf,MAAM,CACL,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;gBACvB,IAAI,SAAS,MAAM,CAAC,EAAE;gBACtB,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,SAAS;oBAAC;iBAAO;gBAC7C,OAAO,OACJ,GAAG,CAAC,CAAC,IAAO,MAAM,OAAO,IAAI,GAAG,EAAE,CAAC,EAAE,GAAG,EACxC,IAAI,CAAC;YACV,IAED,IAAI,CAAC;QACV,GACC,IAAI,CAAC;IACV,GACC,IAAI,CAAC;AACV;AAEA,OAAO,OAAO,GAAG;IAAE;IAAQ;AAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2276, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/websocket.js"], "sourcesContent": ["/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex|Readable$\", \"caughtErrors\": \"none\" }] */\n\n'use strict';\n\nconst EventEmitter = require('events');\nconst https = require('https');\nconst http = require('http');\nconst net = require('net');\nconst tls = require('tls');\nconst { randomBytes, createHash } = require('crypto');\nconst { Duplex, Readable } = require('stream');\nconst { URL } = require('url');\n\nconst PerMessageDeflate = require('./permessage-deflate');\nconst Receiver = require('./receiver');\nconst Sender = require('./sender');\nconst {\n  BINARY_TYPES,\n  EMPTY_BUFFER,\n  GUID,\n  kForOnEventAttribute,\n  kListener,\n  kStatusCode,\n  kWebSocket,\n  NOOP\n} = require('./constants');\nconst {\n  EventTarget: { addEventListener, removeEventListener }\n} = require('./event-target');\nconst { format, parse } = require('./extension');\nconst { toBuffer } = require('./buffer-util');\n\nconst closeTimeout = 30 * 1000;\nconst kAborted = Symbol('kAborted');\nconst protocolVersions = [8, 13];\nconst readyStates = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];\nconst subprotocolRegex = /^[!#$%&'*+\\-.0-9A-Z^_`|a-z~]+$/;\n\n/**\n * Class representing a WebSocket.\n *\n * @extends EventEmitter\n */\nclass WebSocket extends EventEmitter {\n  /**\n   * Create a new `WebSocket`.\n   *\n   * @param {(String|URL)} address The URL to which to connect\n   * @param {(String|String[])} [protocols] The subprotocols\n   * @param {Object} [options] Connection options\n   */\n  constructor(address, protocols, options) {\n    super();\n\n    this._binaryType = BINARY_TYPES[0];\n    this._closeCode = 1006;\n    this._closeFrameReceived = false;\n    this._closeFrameSent = false;\n    this._closeMessage = EMPTY_BUFFER;\n    this._closeTimer = null;\n    this._extensions = {};\n    this._paused = false;\n    this._protocol = '';\n    this._readyState = WebSocket.CONNECTING;\n    this._receiver = null;\n    this._sender = null;\n    this._socket = null;\n\n    if (address !== null) {\n      this._bufferedAmount = 0;\n      this._isServer = false;\n      this._redirects = 0;\n\n      if (protocols === undefined) {\n        protocols = [];\n      } else if (!Array.isArray(protocols)) {\n        if (typeof protocols === 'object' && protocols !== null) {\n          options = protocols;\n          protocols = [];\n        } else {\n          protocols = [protocols];\n        }\n      }\n\n      initAsClient(this, address, protocols, options);\n    } else {\n      this._autoPong = options.autoPong;\n      this._isServer = true;\n    }\n  }\n\n  /**\n   * This deviates from the WHATWG interface since ws doesn't support the\n   * required default \"blob\" type (instead we define a custom \"nodebuffer\"\n   * type).\n   *\n   * @type {String}\n   */\n  get binaryType() {\n    return this._binaryType;\n  }\n\n  set binaryType(type) {\n    if (!BINARY_TYPES.includes(type)) return;\n\n    this._binaryType = type;\n\n    //\n    // Allow to change `binaryType` on the fly.\n    //\n    if (this._receiver) this._receiver._binaryType = type;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get bufferedAmount() {\n    if (!this._socket) return this._bufferedAmount;\n\n    return this._socket._writableState.length + this._sender._bufferedBytes;\n  }\n\n  /**\n   * @type {String}\n   */\n  get extensions() {\n    return Object.keys(this._extensions).join();\n  }\n\n  /**\n   * @type {Boolean}\n   */\n  get isPaused() {\n    return this._paused;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onclose() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onerror() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onopen() {\n    return null;\n  }\n\n  /**\n   * @type {Function}\n   */\n  /* istanbul ignore next */\n  get onmessage() {\n    return null;\n  }\n\n  /**\n   * @type {String}\n   */\n  get protocol() {\n    return this._protocol;\n  }\n\n  /**\n   * @type {Number}\n   */\n  get readyState() {\n    return this._readyState;\n  }\n\n  /**\n   * @type {String}\n   */\n  get url() {\n    return this._url;\n  }\n\n  /**\n   * Set up the socket and the internal resources.\n   *\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Object} options Options object\n   * @param {Boolean} [options.allowSynchronousEvents=false] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Number} [options.maxPayload=0] The maximum allowed message size\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @private\n   */\n  setSocket(socket, head, options) {\n    const receiver = new Receiver({\n      allowSynchronousEvents: options.allowSynchronousEvents,\n      binaryType: this.binaryType,\n      extensions: this._extensions,\n      isServer: this._isServer,\n      maxPayload: options.maxPayload,\n      skipUTF8Validation: options.skipUTF8Validation\n    });\n\n    this._sender = new Sender(socket, this._extensions, options.generateMask);\n    this._receiver = receiver;\n    this._socket = socket;\n\n    receiver[kWebSocket] = this;\n    socket[kWebSocket] = this;\n\n    receiver.on('conclude', receiverOnConclude);\n    receiver.on('drain', receiverOnDrain);\n    receiver.on('error', receiverOnError);\n    receiver.on('message', receiverOnMessage);\n    receiver.on('ping', receiverOnPing);\n    receiver.on('pong', receiverOnPong);\n\n    //\n    // These methods may not be available if `socket` is just a `Duplex`.\n    //\n    if (socket.setTimeout) socket.setTimeout(0);\n    if (socket.setNoDelay) socket.setNoDelay();\n\n    if (head.length > 0) socket.unshift(head);\n\n    socket.on('close', socketOnClose);\n    socket.on('data', socketOnData);\n    socket.on('end', socketOnEnd);\n    socket.on('error', socketOnError);\n\n    this._readyState = WebSocket.OPEN;\n    this.emit('open');\n  }\n\n  /**\n   * Emit the `'close'` event.\n   *\n   * @private\n   */\n  emitClose() {\n    if (!this._socket) {\n      this._readyState = WebSocket.CLOSED;\n      this.emit('close', this._closeCode, this._closeMessage);\n      return;\n    }\n\n    if (this._extensions[PerMessageDeflate.extensionName]) {\n      this._extensions[PerMessageDeflate.extensionName].cleanup();\n    }\n\n    this._receiver.removeAllListeners();\n    this._readyState = WebSocket.CLOSED;\n    this.emit('close', this._closeCode, this._closeMessage);\n  }\n\n  /**\n   * Start a closing handshake.\n   *\n   *          +----------+   +-----------+   +----------+\n   *     - - -|ws.close()|-->|close frame|-->|ws.close()|- - -\n   *    |     +----------+   +-----------+   +----------+     |\n   *          +----------+   +-----------+         |\n   * CLOSING  |ws.close()|<--|close frame|<--+-----+       CLOSING\n   *          +----------+   +-----------+   |\n   *    |           |                        |   +---+        |\n   *                +------------------------+-->|fin| - - - -\n   *    |         +---+                      |   +---+\n   *     - - - - -|fin|<---------------------+\n   *              +---+\n   *\n   * @param {Number} [code] Status code explaining why the connection is closing\n   * @param {(String|Buffer)} [data] The reason why the connection is\n   *     closing\n   * @public\n   */\n  close(code, data) {\n    if (this.readyState === WebSocket.CLOSED) return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = 'WebSocket was closed before the connection was established';\n      abortHandshake(this, this._req, msg);\n      return;\n    }\n\n    if (this.readyState === WebSocket.CLOSING) {\n      if (\n        this._closeFrameSent &&\n        (this._closeFrameReceived || this._receiver._writableState.errorEmitted)\n      ) {\n        this._socket.end();\n      }\n\n      return;\n    }\n\n    this._readyState = WebSocket.CLOSING;\n    this._sender.close(code, data, !this._isServer, (err) => {\n      //\n      // This error is handled by the `'error'` listener on the socket. We only\n      // want to know if the close frame has been sent here.\n      //\n      if (err) return;\n\n      this._closeFrameSent = true;\n\n      if (\n        this._closeFrameReceived ||\n        this._receiver._writableState.errorEmitted\n      ) {\n        this._socket.end();\n      }\n    });\n\n    //\n    // Specify a timeout for the closing handshake to complete.\n    //\n    this._closeTimer = setTimeout(\n      this._socket.destroy.bind(this._socket),\n      closeTimeout\n    );\n  }\n\n  /**\n   * Pause the socket.\n   *\n   * @public\n   */\n  pause() {\n    if (\n      this.readyState === WebSocket.CONNECTING ||\n      this.readyState === WebSocket.CLOSED\n    ) {\n      return;\n    }\n\n    this._paused = true;\n    this._socket.pause();\n  }\n\n  /**\n   * Send a ping.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the ping is sent\n   * @public\n   */\n  ping(data, mask, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof data === 'function') {\n      cb = data;\n      data = mask = undefined;\n    } else if (typeof mask === 'function') {\n      cb = mask;\n      mask = undefined;\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    if (mask === undefined) mask = !this._isServer;\n    this._sender.ping(data || EMPTY_BUFFER, mask, cb);\n  }\n\n  /**\n   * Send a pong.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the pong is sent\n   * @public\n   */\n  pong(data, mask, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof data === 'function') {\n      cb = data;\n      data = mask = undefined;\n    } else if (typeof mask === 'function') {\n      cb = mask;\n      mask = undefined;\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    if (mask === undefined) mask = !this._isServer;\n    this._sender.pong(data || EMPTY_BUFFER, mask, cb);\n  }\n\n  /**\n   * Resume the socket.\n   *\n   * @public\n   */\n  resume() {\n    if (\n      this.readyState === WebSocket.CONNECTING ||\n      this.readyState === WebSocket.CLOSED\n    ) {\n      return;\n    }\n\n    this._paused = false;\n    if (!this._receiver._writableState.needDrain) this._socket.resume();\n  }\n\n  /**\n   * Send a data message.\n   *\n   * @param {*} data The message to send\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.binary] Specifies whether `data` is binary or\n   *     text\n   * @param {Boolean} [options.compress] Specifies whether or not to compress\n   *     `data`\n   * @param {Boolean} [options.fin=true] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when data is written out\n   * @public\n   */\n  send(data, options, cb) {\n    if (this.readyState === WebSocket.CONNECTING) {\n      throw new Error('WebSocket is not open: readyState 0 (CONNECTING)');\n    }\n\n    if (typeof options === 'function') {\n      cb = options;\n      options = {};\n    }\n\n    if (typeof data === 'number') data = data.toString();\n\n    if (this.readyState !== WebSocket.OPEN) {\n      sendAfterClose(this, data, cb);\n      return;\n    }\n\n    const opts = {\n      binary: typeof data !== 'string',\n      mask: !this._isServer,\n      compress: true,\n      fin: true,\n      ...options\n    };\n\n    if (!this._extensions[PerMessageDeflate.extensionName]) {\n      opts.compress = false;\n    }\n\n    this._sender.send(data || EMPTY_BUFFER, opts, cb);\n  }\n\n  /**\n   * Forcibly close the connection.\n   *\n   * @public\n   */\n  terminate() {\n    if (this.readyState === WebSocket.CLOSED) return;\n    if (this.readyState === WebSocket.CONNECTING) {\n      const msg = 'WebSocket was closed before the connection was established';\n      abortHandshake(this, this._req, msg);\n      return;\n    }\n\n    if (this._socket) {\n      this._readyState = WebSocket.CLOSING;\n      this._socket.destroy();\n    }\n  }\n}\n\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CONNECTING', {\n  enumerable: true,\n  value: readyStates.indexOf('CONNECTING')\n});\n\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CONNECTING', {\n  enumerable: true,\n  value: readyStates.indexOf('CONNECTING')\n});\n\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'OPEN', {\n  enumerable: true,\n  value: readyStates.indexOf('OPEN')\n});\n\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'OPEN', {\n  enumerable: true,\n  value: readyStates.indexOf('OPEN')\n});\n\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CLOSING', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSING')\n});\n\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CLOSING', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSING')\n});\n\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket\n */\nObject.defineProperty(WebSocket, 'CLOSED', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSED')\n});\n\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket.prototype\n */\nObject.defineProperty(WebSocket.prototype, 'CLOSED', {\n  enumerable: true,\n  value: readyStates.indexOf('CLOSED')\n});\n\n[\n  'binaryType',\n  'bufferedAmount',\n  'extensions',\n  'isPaused',\n  'protocol',\n  'readyState',\n  'url'\n].forEach((property) => {\n  Object.defineProperty(WebSocket.prototype, property, { enumerable: true });\n});\n\n//\n// Add the `onopen`, `onerror`, `onclose`, and `onmessage` attributes.\n// See https://html.spec.whatwg.org/multipage/comms.html#the-websocket-interface\n//\n['open', 'error', 'close', 'message'].forEach((method) => {\n  Object.defineProperty(WebSocket.prototype, `on${method}`, {\n    enumerable: true,\n    get() {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) return listener[kListener];\n      }\n\n      return null;\n    },\n    set(handler) {\n      for (const listener of this.listeners(method)) {\n        if (listener[kForOnEventAttribute]) {\n          this.removeListener(method, listener);\n          break;\n        }\n      }\n\n      if (typeof handler !== 'function') return;\n\n      this.addEventListener(method, handler, {\n        [kForOnEventAttribute]: true\n      });\n    }\n  });\n});\n\nWebSocket.prototype.addEventListener = addEventListener;\nWebSocket.prototype.removeEventListener = removeEventListener;\n\nmodule.exports = WebSocket;\n\n/**\n * Initialize a WebSocket client.\n *\n * @param {WebSocket} websocket The client to initialize\n * @param {(String|URL)} address The URL to which to connect\n * @param {Array} protocols The subprotocols\n * @param {Object} [options] Connection options\n * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether any\n *     of the `'message'`, `'ping'`, and `'pong'` events can be emitted multiple\n *     times in the same tick\n * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n *     automatically send a pong in response to a ping\n * @param {Function} [options.finishRequest] A function which can be used to\n *     customize the headers of each http request before it is sent\n * @param {Boolean} [options.followRedirects=false] Whether or not to follow\n *     redirects\n * @param {Function} [options.generateMask] The function used to generate the\n *     masking key\n * @param {Number} [options.handshakeTimeout] Timeout in milliseconds for the\n *     handshake request\n * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n *     size\n * @param {Number} [options.maxRedirects=10] The maximum number of redirects\n *     allowed\n * @param {String} [options.origin] Value of the `Origin` or\n *     `Sec-WebSocket-Origin` header\n * @param {(Boolean|Object)} [options.perMessageDeflate=true] Enable/disable\n *     permessage-deflate\n * @param {Number} [options.protocolVersion=13] Value of the\n *     `Sec-WebSocket-Version` header\n * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n *     not to skip UTF-8 validation for text and close messages\n * @private\n */\nfunction initAsClient(websocket, address, protocols, options) {\n  const opts = {\n    allowSynchronousEvents: true,\n    autoPong: true,\n    protocolVersion: protocolVersions[1],\n    maxPayload: 100 * 1024 * 1024,\n    skipUTF8Validation: false,\n    perMessageDeflate: true,\n    followRedirects: false,\n    maxRedirects: 10,\n    ...options,\n    socketPath: undefined,\n    hostname: undefined,\n    protocol: undefined,\n    timeout: undefined,\n    method: 'GET',\n    host: undefined,\n    path: undefined,\n    port: undefined\n  };\n\n  websocket._autoPong = opts.autoPong;\n\n  if (!protocolVersions.includes(opts.protocolVersion)) {\n    throw new RangeError(\n      `Unsupported protocol version: ${opts.protocolVersion} ` +\n        `(supported versions: ${protocolVersions.join(', ')})`\n    );\n  }\n\n  let parsedUrl;\n\n  if (address instanceof URL) {\n    parsedUrl = address;\n  } else {\n    try {\n      parsedUrl = new URL(address);\n    } catch (e) {\n      throw new SyntaxError(`Invalid URL: ${address}`);\n    }\n  }\n\n  if (parsedUrl.protocol === 'http:') {\n    parsedUrl.protocol = 'ws:';\n  } else if (parsedUrl.protocol === 'https:') {\n    parsedUrl.protocol = 'wss:';\n  }\n\n  websocket._url = parsedUrl.href;\n\n  const isSecure = parsedUrl.protocol === 'wss:';\n  const isIpcUrl = parsedUrl.protocol === 'ws+unix:';\n  let invalidUrlMessage;\n\n  if (parsedUrl.protocol !== 'ws:' && !isSecure && !isIpcUrl) {\n    invalidUrlMessage =\n      'The URL\\'s protocol must be one of \"ws:\", \"wss:\", ' +\n      '\"http:\", \"https\", or \"ws+unix:\"';\n  } else if (isIpcUrl && !parsedUrl.pathname) {\n    invalidUrlMessage = \"The URL's pathname is empty\";\n  } else if (parsedUrl.hash) {\n    invalidUrlMessage = 'The URL contains a fragment identifier';\n  }\n\n  if (invalidUrlMessage) {\n    const err = new SyntaxError(invalidUrlMessage);\n\n    if (websocket._redirects === 0) {\n      throw err;\n    } else {\n      emitErrorAndClose(websocket, err);\n      return;\n    }\n  }\n\n  const defaultPort = isSecure ? 443 : 80;\n  const key = randomBytes(16).toString('base64');\n  const request = isSecure ? https.request : http.request;\n  const protocolSet = new Set();\n  let perMessageDeflate;\n\n  opts.createConnection =\n    opts.createConnection || (isSecure ? tlsConnect : netConnect);\n  opts.defaultPort = opts.defaultPort || defaultPort;\n  opts.port = parsedUrl.port || defaultPort;\n  opts.host = parsedUrl.hostname.startsWith('[')\n    ? parsedUrl.hostname.slice(1, -1)\n    : parsedUrl.hostname;\n  opts.headers = {\n    ...opts.headers,\n    'Sec-WebSocket-Version': opts.protocolVersion,\n    'Sec-WebSocket-Key': key,\n    Connection: 'Upgrade',\n    Upgrade: 'websocket'\n  };\n  opts.path = parsedUrl.pathname + parsedUrl.search;\n  opts.timeout = opts.handshakeTimeout;\n\n  if (opts.perMessageDeflate) {\n    perMessageDeflate = new PerMessageDeflate(\n      opts.perMessageDeflate !== true ? opts.perMessageDeflate : {},\n      false,\n      opts.maxPayload\n    );\n    opts.headers['Sec-WebSocket-Extensions'] = format({\n      [PerMessageDeflate.extensionName]: perMessageDeflate.offer()\n    });\n  }\n  if (protocols.length) {\n    for (const protocol of protocols) {\n      if (\n        typeof protocol !== 'string' ||\n        !subprotocolRegex.test(protocol) ||\n        protocolSet.has(protocol)\n      ) {\n        throw new SyntaxError(\n          'An invalid or duplicated subprotocol was specified'\n        );\n      }\n\n      protocolSet.add(protocol);\n    }\n\n    opts.headers['Sec-WebSocket-Protocol'] = protocols.join(',');\n  }\n  if (opts.origin) {\n    if (opts.protocolVersion < 13) {\n      opts.headers['Sec-WebSocket-Origin'] = opts.origin;\n    } else {\n      opts.headers.Origin = opts.origin;\n    }\n  }\n  if (parsedUrl.username || parsedUrl.password) {\n    opts.auth = `${parsedUrl.username}:${parsedUrl.password}`;\n  }\n\n  if (isIpcUrl) {\n    const parts = opts.path.split(':');\n\n    opts.socketPath = parts[0];\n    opts.path = parts[1];\n  }\n\n  let req;\n\n  if (opts.followRedirects) {\n    if (websocket._redirects === 0) {\n      websocket._originalIpc = isIpcUrl;\n      websocket._originalSecure = isSecure;\n      websocket._originalHostOrSocketPath = isIpcUrl\n        ? opts.socketPath\n        : parsedUrl.host;\n\n      const headers = options && options.headers;\n\n      //\n      // Shallow copy the user provided options so that headers can be changed\n      // without mutating the original object.\n      //\n      options = { ...options, headers: {} };\n\n      if (headers) {\n        for (const [key, value] of Object.entries(headers)) {\n          options.headers[key.toLowerCase()] = value;\n        }\n      }\n    } else if (websocket.listenerCount('redirect') === 0) {\n      const isSameHost = isIpcUrl\n        ? websocket._originalIpc\n          ? opts.socketPath === websocket._originalHostOrSocketPath\n          : false\n        : websocket._originalIpc\n          ? false\n          : parsedUrl.host === websocket._originalHostOrSocketPath;\n\n      if (!isSameHost || (websocket._originalSecure && !isSecure)) {\n        //\n        // Match curl 7.77.0 behavior and drop the following headers. These\n        // headers are also dropped when following a redirect to a subdomain.\n        //\n        delete opts.headers.authorization;\n        delete opts.headers.cookie;\n\n        if (!isSameHost) delete opts.headers.host;\n\n        opts.auth = undefined;\n      }\n    }\n\n    //\n    // Match curl 7.77.0 behavior and make the first `Authorization` header win.\n    // If the `Authorization` header is set, then there is nothing to do as it\n    // will take precedence.\n    //\n    if (opts.auth && !options.headers.authorization) {\n      options.headers.authorization =\n        'Basic ' + Buffer.from(opts.auth).toString('base64');\n    }\n\n    req = websocket._req = request(opts);\n\n    if (websocket._redirects) {\n      //\n      // Unlike what is done for the `'upgrade'` event, no early exit is\n      // triggered here if the user calls `websocket.close()` or\n      // `websocket.terminate()` from a listener of the `'redirect'` event. This\n      // is because the user can also call `request.destroy()` with an error\n      // before calling `websocket.close()` or `websocket.terminate()` and this\n      // would result in an error being emitted on the `request` object with no\n      // `'error'` event listeners attached.\n      //\n      websocket.emit('redirect', websocket.url, req);\n    }\n  } else {\n    req = websocket._req = request(opts);\n  }\n\n  if (opts.timeout) {\n    req.on('timeout', () => {\n      abortHandshake(websocket, req, 'Opening handshake has timed out');\n    });\n  }\n\n  req.on('error', (err) => {\n    if (req === null || req[kAborted]) return;\n\n    req = websocket._req = null;\n    emitErrorAndClose(websocket, err);\n  });\n\n  req.on('response', (res) => {\n    const location = res.headers.location;\n    const statusCode = res.statusCode;\n\n    if (\n      location &&\n      opts.followRedirects &&\n      statusCode >= 300 &&\n      statusCode < 400\n    ) {\n      if (++websocket._redirects > opts.maxRedirects) {\n        abortHandshake(websocket, req, 'Maximum redirects exceeded');\n        return;\n      }\n\n      req.abort();\n\n      let addr;\n\n      try {\n        addr = new URL(location, address);\n      } catch (e) {\n        const err = new SyntaxError(`Invalid URL: ${location}`);\n        emitErrorAndClose(websocket, err);\n        return;\n      }\n\n      initAsClient(websocket, addr, protocols, options);\n    } else if (!websocket.emit('unexpected-response', req, res)) {\n      abortHandshake(\n        websocket,\n        req,\n        `Unexpected server response: ${res.statusCode}`\n      );\n    }\n  });\n\n  req.on('upgrade', (res, socket, head) => {\n    websocket.emit('upgrade', res);\n\n    //\n    // The user may have closed the connection from a listener of the\n    // `'upgrade'` event.\n    //\n    if (websocket.readyState !== WebSocket.CONNECTING) return;\n\n    req = websocket._req = null;\n\n    const upgrade = res.headers.upgrade;\n\n    if (upgrade === undefined || upgrade.toLowerCase() !== 'websocket') {\n      abortHandshake(websocket, socket, 'Invalid Upgrade header');\n      return;\n    }\n\n    const digest = createHash('sha1')\n      .update(key + GUID)\n      .digest('base64');\n\n    if (res.headers['sec-websocket-accept'] !== digest) {\n      abortHandshake(websocket, socket, 'Invalid Sec-WebSocket-Accept header');\n      return;\n    }\n\n    const serverProt = res.headers['sec-websocket-protocol'];\n    let protError;\n\n    if (serverProt !== undefined) {\n      if (!protocolSet.size) {\n        protError = 'Server sent a subprotocol but none was requested';\n      } else if (!protocolSet.has(serverProt)) {\n        protError = 'Server sent an invalid subprotocol';\n      }\n    } else if (protocolSet.size) {\n      protError = 'Server sent no subprotocol';\n    }\n\n    if (protError) {\n      abortHandshake(websocket, socket, protError);\n      return;\n    }\n\n    if (serverProt) websocket._protocol = serverProt;\n\n    const secWebSocketExtensions = res.headers['sec-websocket-extensions'];\n\n    if (secWebSocketExtensions !== undefined) {\n      if (!perMessageDeflate) {\n        const message =\n          'Server sent a Sec-WebSocket-Extensions header but no extension ' +\n          'was requested';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      let extensions;\n\n      try {\n        extensions = parse(secWebSocketExtensions);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Extensions header';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      const extensionNames = Object.keys(extensions);\n\n      if (\n        extensionNames.length !== 1 ||\n        extensionNames[0] !== PerMessageDeflate.extensionName\n      ) {\n        const message = 'Server indicated an extension that was not requested';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      try {\n        perMessageDeflate.accept(extensions[PerMessageDeflate.extensionName]);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Extensions header';\n        abortHandshake(websocket, socket, message);\n        return;\n      }\n\n      websocket._extensions[PerMessageDeflate.extensionName] =\n        perMessageDeflate;\n    }\n\n    websocket.setSocket(socket, head, {\n      allowSynchronousEvents: opts.allowSynchronousEvents,\n      generateMask: opts.generateMask,\n      maxPayload: opts.maxPayload,\n      skipUTF8Validation: opts.skipUTF8Validation\n    });\n  });\n\n  if (opts.finishRequest) {\n    opts.finishRequest(req, websocket);\n  } else {\n    req.end();\n  }\n}\n\n/**\n * Emit the `'error'` and `'close'` events.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {Error} The error to emit\n * @private\n */\nfunction emitErrorAndClose(websocket, err) {\n  websocket._readyState = WebSocket.CLOSING;\n  websocket.emit('error', err);\n  websocket.emitClose();\n}\n\n/**\n * Create a `net.Socket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {net.Socket} The newly created socket used to start the connection\n * @private\n */\nfunction netConnect(options) {\n  options.path = options.socketPath;\n  return net.connect(options);\n}\n\n/**\n * Create a `tls.TLSSocket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {tls.TLSSocket} The newly created socket used to start the connection\n * @private\n */\nfunction tlsConnect(options) {\n  options.path = undefined;\n\n  if (!options.servername && options.servername !== '') {\n    options.servername = net.isIP(options.host) ? '' : options.host;\n  }\n\n  return tls.connect(options);\n}\n\n/**\n * Abort the handshake and emit an error.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {(http.ClientRequest|net.Socket|tls.Socket)} stream The request to\n *     abort or the socket to destroy\n * @param {String} message The error message\n * @private\n */\nfunction abortHandshake(websocket, stream, message) {\n  websocket._readyState = WebSocket.CLOSING;\n\n  const err = new Error(message);\n  Error.captureStackTrace(err, abortHandshake);\n\n  if (stream.setHeader) {\n    stream[kAborted] = true;\n    stream.abort();\n\n    if (stream.socket && !stream.socket.destroyed) {\n      //\n      // On Node.js >= 14.3.0 `request.abort()` does not destroy the socket if\n      // called after the request completed. See\n      // https://github.com/websockets/ws/issues/1869.\n      //\n      stream.socket.destroy();\n    }\n\n    process.nextTick(emitErrorAndClose, websocket, err);\n  } else {\n    stream.destroy(err);\n    stream.once('error', websocket.emit.bind(websocket, 'error'));\n    stream.once('close', websocket.emitClose.bind(websocket));\n  }\n}\n\n/**\n * Handle cases where the `ping()`, `pong()`, or `send()` methods are called\n * when the `readyState` attribute is `CLOSING` or `CLOSED`.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {*} [data] The data to send\n * @param {Function} [cb] Callback\n * @private\n */\nfunction sendAfterClose(websocket, data, cb) {\n  if (data) {\n    const length = toBuffer(data).length;\n\n    //\n    // The `_bufferedAmount` property is used only when the peer is a client and\n    // the opening handshake fails. Under these circumstances, in fact, the\n    // `setSocket()` method is not called, so the `_socket` and `_sender`\n    // properties are set to `null`.\n    //\n    if (websocket._socket) websocket._sender._bufferedBytes += length;\n    else websocket._bufferedAmount += length;\n  }\n\n  if (cb) {\n    const err = new Error(\n      `WebSocket is not open: readyState ${websocket.readyState} ` +\n        `(${readyStates[websocket.readyState]})`\n    );\n    process.nextTick(cb, err);\n  }\n}\n\n/**\n * The listener of the `Receiver` `'conclude'` event.\n *\n * @param {Number} code The status code\n * @param {Buffer} reason The reason for closing\n * @private\n */\nfunction receiverOnConclude(code, reason) {\n  const websocket = this[kWebSocket];\n\n  websocket._closeFrameReceived = true;\n  websocket._closeMessage = reason;\n  websocket._closeCode = code;\n\n  if (websocket._socket[kWebSocket] === undefined) return;\n\n  websocket._socket.removeListener('data', socketOnData);\n  process.nextTick(resume, websocket._socket);\n\n  if (code === 1005) websocket.close();\n  else websocket.close(code, reason);\n}\n\n/**\n * The listener of the `Receiver` `'drain'` event.\n *\n * @private\n */\nfunction receiverOnDrain() {\n  const websocket = this[kWebSocket];\n\n  if (!websocket.isPaused) websocket._socket.resume();\n}\n\n/**\n * The listener of the `Receiver` `'error'` event.\n *\n * @param {(RangeError|Error)} err The emitted error\n * @private\n */\nfunction receiverOnError(err) {\n  const websocket = this[kWebSocket];\n\n  if (websocket._socket[kWebSocket] !== undefined) {\n    websocket._socket.removeListener('data', socketOnData);\n\n    //\n    // On Node.js < 14.0.0 the `'error'` event is emitted synchronously. See\n    // https://github.com/websockets/ws/issues/1940.\n    //\n    process.nextTick(resume, websocket._socket);\n\n    websocket.close(err[kStatusCode]);\n  }\n\n  websocket.emit('error', err);\n}\n\n/**\n * The listener of the `Receiver` `'finish'` event.\n *\n * @private\n */\nfunction receiverOnFinish() {\n  this[kWebSocket].emitClose();\n}\n\n/**\n * The listener of the `Receiver` `'message'` event.\n *\n * @param {Buffer|ArrayBuffer|Buffer[])} data The message\n * @param {Boolean} isBinary Specifies whether the message is binary or not\n * @private\n */\nfunction receiverOnMessage(data, isBinary) {\n  this[kWebSocket].emit('message', data, isBinary);\n}\n\n/**\n * The listener of the `Receiver` `'ping'` event.\n *\n * @param {Buffer} data The data included in the ping frame\n * @private\n */\nfunction receiverOnPing(data) {\n  const websocket = this[kWebSocket];\n\n  if (websocket._autoPong) websocket.pong(data, !this._isServer, NOOP);\n  websocket.emit('ping', data);\n}\n\n/**\n * The listener of the `Receiver` `'pong'` event.\n *\n * @param {Buffer} data The data included in the pong frame\n * @private\n */\nfunction receiverOnPong(data) {\n  this[kWebSocket].emit('pong', data);\n}\n\n/**\n * Resume a readable stream\n *\n * @param {Readable} stream The readable stream\n * @private\n */\nfunction resume(stream) {\n  stream.resume();\n}\n\n/**\n * The listener of the socket `'close'` event.\n *\n * @private\n */\nfunction socketOnClose() {\n  const websocket = this[kWebSocket];\n\n  this.removeListener('close', socketOnClose);\n  this.removeListener('data', socketOnData);\n  this.removeListener('end', socketOnEnd);\n\n  websocket._readyState = WebSocket.CLOSING;\n\n  let chunk;\n\n  //\n  // The close frame might not have been received or the `'end'` event emitted,\n  // for example, if the socket was destroyed due to an error. Ensure that the\n  // `receiver` stream is closed after writing any remaining buffered data to\n  // it. If the readable side of the socket is in flowing mode then there is no\n  // buffered data as everything has been already written and `readable.read()`\n  // will return `null`. If instead, the socket is paused, any possible buffered\n  // data will be read as a single chunk.\n  //\n  if (\n    !this._readableState.endEmitted &&\n    !websocket._closeFrameReceived &&\n    !websocket._receiver._writableState.errorEmitted &&\n    (chunk = websocket._socket.read()) !== null\n  ) {\n    websocket._receiver.write(chunk);\n  }\n\n  websocket._receiver.end();\n\n  this[kWebSocket] = undefined;\n\n  clearTimeout(websocket._closeTimer);\n\n  if (\n    websocket._receiver._writableState.finished ||\n    websocket._receiver._writableState.errorEmitted\n  ) {\n    websocket.emitClose();\n  } else {\n    websocket._receiver.on('error', receiverOnFinish);\n    websocket._receiver.on('finish', receiverOnFinish);\n  }\n}\n\n/**\n * The listener of the socket `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */\nfunction socketOnData(chunk) {\n  if (!this[kWebSocket]._receiver.write(chunk)) {\n    this.pause();\n  }\n}\n\n/**\n * The listener of the socket `'end'` event.\n *\n * @private\n */\nfunction socketOnEnd() {\n  const websocket = this[kWebSocket];\n\n  websocket._readyState = WebSocket.CLOSING;\n  websocket._receiver.end();\n  this.end();\n}\n\n/**\n * The listener of the socket `'error'` event.\n *\n * @private\n */\nfunction socketOnError() {\n  const websocket = this[kWebSocket];\n\n  this.removeListener('error', socketOnError);\n  this.on('error', NOOP);\n\n  if (websocket) {\n    websocket._readyState = WebSocket.CLOSING;\n    this.destroy();\n  }\n}\n"], "names": [], "mappings": "AAAA,0GAA0G,GAE1G;AAEA,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE;AACjC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;AAC1B,MAAM,EAAE,GAAG,EAAE;AAEb,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EACJ,YAAY,EACZ,YAAY,EACZ,IAAI,EACJ,oBAAoB,EACpB,SAAS,EACT,WAAW,EACX,UAAU,EACV,IAAI,EACL;AACD,MAAM,EACJ,aAAa,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,EACvD;AACD,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE;AACvB,MAAM,EAAE,QAAQ,EAAE;AAElB,MAAM,eAAe,KAAK;AAC1B,MAAM,WAAW,OAAO;AACxB,MAAM,mBAAmB;IAAC;IAAG;CAAG;AAChC,MAAM,cAAc;IAAC;IAAc;IAAQ;IAAW;CAAS;AAC/D,MAAM,mBAAmB;AAEzB;;;;CAIC,GACD,MAAM,kBAAkB;IACtB;;;;;;GAMC,GACD,YAAY,OAAO,EAAE,SAAS,EAAE,OAAO,CAAE;QACvC,KAAK;QAEL,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC,EAAE;QAClC,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG,UAAU,UAAU;QACvC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QAEf,IAAI,YAAY,MAAM;YACpB,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,UAAU,GAAG;YAElB,IAAI,cAAc,WAAW;gBAC3B,YAAY,EAAE;YAChB,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,YAAY;gBACpC,IAAI,OAAO,cAAc,YAAY,cAAc,MAAM;oBACvD,UAAU;oBACV,YAAY,EAAE;gBAChB,OAAO;oBACL,YAAY;wBAAC;qBAAU;gBACzB;YACF;YAEA,aAAa,IAAI,EAAE,SAAS,WAAW;QACzC,OAAO;YACL,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ;YACjC,IAAI,CAAC,SAAS,GAAG;QACnB;IACF;IAEA;;;;;;GAMC,GACD,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,IAAI,WAAW,IAAI,EAAE;QACnB,IAAI,CAAC,aAAa,QAAQ,CAAC,OAAO;QAElC,IAAI,CAAC,WAAW,GAAG;QAEnB,EAAE;QACF,2CAA2C;QAC3C,EAAE;QACF,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG;IACnD;IAEA;;GAEC,GACD,IAAI,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,eAAe;QAE9C,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc;IACzE;IAEA;;GAEC,GACD,IAAI,aAAa;QACf,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI;IAC3C;IAEA;;GAEC,GACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA;;GAEC,GACD,wBAAwB,GACxB,IAAI,UAAU;QACZ,OAAO;IACT;IAEA;;GAEC,GACD,wBAAwB,GACxB,IAAI,UAAU;QACZ,OAAO;IACT;IAEA;;GAEC,GACD,wBAAwB,GACxB,IAAI,SAAS;QACX,OAAO;IACT;IAEA;;GAEC,GACD,wBAAwB,GACxB,IAAI,YAAY;QACd,OAAO;IACT;IAEA;;GAEC,GACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA;;GAEC,GACD,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;GAEC,GACD,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,IAAI;IAClB;IAEA;;;;;;;;;;;;;;;GAeC,GACD,UAAU,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE;QAC/B,MAAM,WAAW,IAAI,SAAS;YAC5B,wBAAwB,QAAQ,sBAAsB;YACtD,YAAY,IAAI,CAAC,UAAU;YAC3B,YAAY,IAAI,CAAC,WAAW;YAC5B,UAAU,IAAI,CAAC,SAAS;YACxB,YAAY,QAAQ,UAAU;YAC9B,oBAAoB,QAAQ,kBAAkB;QAChD;QAEA,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,QAAQ,IAAI,CAAC,WAAW,EAAE,QAAQ,YAAY;QACxE,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QAEf,QAAQ,CAAC,WAAW,GAAG,IAAI;QAC3B,MAAM,CAAC,WAAW,GAAG,IAAI;QAEzB,SAAS,EAAE,CAAC,YAAY;QACxB,SAAS,EAAE,CAAC,SAAS;QACrB,SAAS,EAAE,CAAC,SAAS;QACrB,SAAS,EAAE,CAAC,WAAW;QACvB,SAAS,EAAE,CAAC,QAAQ;QACpB,SAAS,EAAE,CAAC,QAAQ;QAEpB,EAAE;QACF,qEAAqE;QACrE,EAAE;QACF,IAAI,OAAO,UAAU,EAAE,OAAO,UAAU,CAAC;QACzC,IAAI,OAAO,UAAU,EAAE,OAAO,UAAU;QAExC,IAAI,KAAK,MAAM,GAAG,GAAG,OAAO,OAAO,CAAC;QAEpC,OAAO,EAAE,CAAC,SAAS;QACnB,OAAO,EAAE,CAAC,QAAQ;QAClB,OAAO,EAAE,CAAC,OAAO;QACjB,OAAO,EAAE,CAAC,SAAS;QAEnB,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI;QACjC,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA;;;;GAIC,GACD,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,WAAW,GAAG,UAAU,MAAM;YACnC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa;YACtD;QACF;QAEA,IAAI,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC,EAAE;YACrD,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC,CAAC,OAAO;QAC3D;QAEA,IAAI,CAAC,SAAS,CAAC,kBAAkB;QACjC,IAAI,CAAC,WAAW,GAAG,UAAU,MAAM;QACnC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa;IACxD;IAEA;;;;;;;;;;;;;;;;;;;GAmBC,GACD,MAAM,IAAI,EAAE,IAAI,EAAE;QAChB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,MAAM,EAAE;QAC1C,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,EAAE;YAC5C,MAAM,MAAM;YACZ,eAAe,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;YAChC;QACF;QAEA,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,OAAO,EAAE;YACzC,IACE,IAAI,CAAC,eAAe,IACpB,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,YAAY,GACvE;gBACA,IAAI,CAAC,OAAO,CAAC,GAAG;YAClB;YAEA;QACF;QAEA,IAAI,CAAC,WAAW,GAAG,UAAU,OAAO;QACpC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAC/C,EAAE;YACF,yEAAyE;YACzE,sDAAsD;YACtD,EAAE;YACF,IAAI,KAAK;YAET,IAAI,CAAC,eAAe,GAAG;YAEvB,IACE,IAAI,CAAC,mBAAmB,IACxB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,YAAY,EAC1C;gBACA,IAAI,CAAC,OAAO,CAAC,GAAG;YAClB;QACF;QAEA,EAAE;QACF,2DAA2D;QAC3D,EAAE;QACF,IAAI,CAAC,WAAW,GAAG,WACjB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GACtC;IAEJ;IAEA;;;;GAIC,GACD,QAAQ;QACN,IACE,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,IACxC,IAAI,CAAC,UAAU,KAAK,UAAU,MAAM,EACpC;YACA;QACF;QAEA,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,CAAC,KAAK;IACpB;IAEA;;;;;;;GAOC,GACD,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QACnB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,EAAE;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,OAAO,SAAS,YAAY;YAC9B,KAAK;YACL,OAAO,OAAO;QAChB,OAAO,IAAI,OAAO,SAAS,YAAY;YACrC,KAAK;YACL,OAAO;QACT;QAEA,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,QAAQ;QAElD,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YACtC,eAAe,IAAI,EAAE,MAAM;YAC3B;QACF;QAEA,IAAI,SAAS,WAAW,OAAO,CAAC,IAAI,CAAC,SAAS;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,cAAc,MAAM;IAChD;IAEA;;;;;;;GAOC,GACD,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QACnB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,EAAE;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,OAAO,SAAS,YAAY;YAC9B,KAAK;YACL,OAAO,OAAO;QAChB,OAAO,IAAI,OAAO,SAAS,YAAY;YACrC,KAAK;YACL,OAAO;QACT;QAEA,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,QAAQ;QAElD,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YACtC,eAAe,IAAI,EAAE,MAAM;YAC3B;QACF;QAEA,IAAI,SAAS,WAAW,OAAO,CAAC,IAAI,CAAC,SAAS;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,cAAc,MAAM;IAChD;IAEA;;;;GAIC,GACD,SAAS;QACP,IACE,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,IACxC,IAAI,CAAC,UAAU,KAAK,UAAU,MAAM,EACpC;YACA;QACF;QAEA,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;IACnE;IAEA;;;;;;;;;;;;;;GAcC,GACD,KAAK,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;QACtB,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,EAAE;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,OAAO,YAAY,YAAY;YACjC,KAAK;YACL,UAAU,CAAC;QACb;QAEA,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,QAAQ;QAElD,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YACtC,eAAe,IAAI,EAAE,MAAM;YAC3B;QACF;QAEA,MAAM,OAAO;YACX,QAAQ,OAAO,SAAS;YACxB,MAAM,CAAC,IAAI,CAAC,SAAS;YACrB,UAAU;YACV,KAAK;YACL,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,aAAa,CAAC,EAAE;YACtD,KAAK,QAAQ,GAAG;QAClB;QAEA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,cAAc,MAAM;IAChD;IAEA;;;;GAIC,GACD,YAAY;QACV,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,MAAM,EAAE;QAC1C,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,UAAU,EAAE;YAC5C,MAAM,MAAM;YACZ,eAAe,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;YAChC;QACF;QAEA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,WAAW,GAAG,UAAU,OAAO;YACpC,IAAI,CAAC,OAAO,CAAC,OAAO;QACtB;IACF;AACF;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,WAAW,cAAc;IAC7C,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,cAAc;IACvD,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,WAAW,QAAQ;IACvC,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,QAAQ;IACjD,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,WAAW,WAAW;IAC1C,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,WAAW;IACpD,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,WAAW,UAAU;IACzC,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;;;CAGC,GACD,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,UAAU;IACnD,YAAY;IACZ,OAAO,YAAY,OAAO,CAAC;AAC7B;AAEA;IACE;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAC,OAAO,CAAC,CAAC;IACT,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,UAAU;QAAE,YAAY;IAAK;AAC1E;AAEA,EAAE;AACF,sEAAsE;AACtE,gFAAgF;AAChF,EAAE;AACF;IAAC;IAAQ;IAAS;IAAS;CAAU,CAAC,OAAO,CAAC,CAAC;IAC7C,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE;QACxD,YAAY;QACZ;YACE,KAAK,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,QAAS;gBAC7C,IAAI,QAAQ,CAAC,qBAAqB,EAAE,OAAO,QAAQ,CAAC,UAAU;YAChE;YAEA,OAAO;QACT;QACA,KAAI,OAAO;YACT,KAAK,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,QAAS;gBAC7C,IAAI,QAAQ,CAAC,qBAAqB,EAAE;oBAClC,IAAI,CAAC,cAAc,CAAC,QAAQ;oBAC5B;gBACF;YACF;YAEA,IAAI,OAAO,YAAY,YAAY;YAEnC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,SAAS;gBACrC,CAAC,qBAAqB,EAAE;YAC1B;QACF;IACF;AACF;AAEA,UAAU,SAAS,CAAC,gBAAgB,GAAG;AACvC,UAAU,SAAS,CAAC,mBAAmB,GAAG;AAE1C,OAAO,OAAO,GAAG;AAEjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiCC,GACD,SAAS,aAAa,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO;IAC1D,MAAM,OAAO;QACX,wBAAwB;QACxB,UAAU;QACV,iBAAiB,gBAAgB,CAAC,EAAE;QACpC,YAAY,MAAM,OAAO;QACzB,oBAAoB;QACpB,mBAAmB;QACnB,iBAAiB;QACjB,cAAc;QACd,GAAG,OAAO;QACV,YAAY;QACZ,UAAU;QACV,UAAU;QACV,SAAS;QACT,QAAQ;QACR,MAAM;QACN,MAAM;QACN,MAAM;IACR;IAEA,UAAU,SAAS,GAAG,KAAK,QAAQ;IAEnC,IAAI,CAAC,iBAAiB,QAAQ,CAAC,KAAK,eAAe,GAAG;QACpD,MAAM,IAAI,WACR,CAAC,8BAA8B,EAAE,KAAK,eAAe,CAAC,CAAC,CAAC,GACtD,CAAC,qBAAqB,EAAE,iBAAiB,IAAI,CAAC,MAAM,CAAC,CAAC;IAE5D;IAEA,IAAI;IAEJ,IAAI,mBAAmB,KAAK;QAC1B,YAAY;IACd,OAAO;QACL,IAAI;YACF,YAAY,IAAI,IAAI;QACtB,EAAE,OAAO,GAAG;YACV,MAAM,IAAI,YAAY,CAAC,aAAa,EAAE,SAAS;QACjD;IACF;IAEA,IAAI,UAAU,QAAQ,KAAK,SAAS;QAClC,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,UAAU,QAAQ,KAAK,UAAU;QAC1C,UAAU,QAAQ,GAAG;IACvB;IAEA,UAAU,IAAI,GAAG,UAAU,IAAI;IAE/B,MAAM,WAAW,UAAU,QAAQ,KAAK;IACxC,MAAM,WAAW,UAAU,QAAQ,KAAK;IACxC,IAAI;IAEJ,IAAI,UAAU,QAAQ,KAAK,SAAS,CAAC,YAAY,CAAC,UAAU;QAC1D,oBACE,uDACA;IACJ,OAAO,IAAI,YAAY,CAAC,UAAU,QAAQ,EAAE;QAC1C,oBAAoB;IACtB,OAAO,IAAI,UAAU,IAAI,EAAE;QACzB,oBAAoB;IACtB;IAEA,IAAI,mBAAmB;QACrB,MAAM,MAAM,IAAI,YAAY;QAE5B,IAAI,UAAU,UAAU,KAAK,GAAG;YAC9B,MAAM;QACR,OAAO;YACL,kBAAkB,WAAW;YAC7B;QACF;IACF;IAEA,MAAM,cAAc,WAAW,MAAM;IACrC,MAAM,MAAM,YAAY,IAAI,QAAQ,CAAC;IACrC,MAAM,UAAU,WAAW,MAAM,OAAO,GAAG,KAAK,OAAO;IACvD,MAAM,cAAc,IAAI;IACxB,IAAI;IAEJ,KAAK,gBAAgB,GACnB,KAAK,gBAAgB,IAAI,CAAC,WAAW,aAAa,UAAU;IAC9D,KAAK,WAAW,GAAG,KAAK,WAAW,IAAI;IACvC,KAAK,IAAI,GAAG,UAAU,IAAI,IAAI;IAC9B,KAAK,IAAI,GAAG,UAAU,QAAQ,CAAC,UAAU,CAAC,OACtC,UAAU,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,KAC7B,UAAU,QAAQ;IACtB,KAAK,OAAO,GAAG;QACb,GAAG,KAAK,OAAO;QACf,yBAAyB,KAAK,eAAe;QAC7C,qBAAqB;QACrB,YAAY;QACZ,SAAS;IACX;IACA,KAAK,IAAI,GAAG,UAAU,QAAQ,GAAG,UAAU,MAAM;IACjD,KAAK,OAAO,GAAG,KAAK,gBAAgB;IAEpC,IAAI,KAAK,iBAAiB,EAAE;QAC1B,oBAAoB,IAAI,kBACtB,KAAK,iBAAiB,KAAK,OAAO,KAAK,iBAAiB,GAAG,CAAC,GAC5D,OACA,KAAK,UAAU;QAEjB,KAAK,OAAO,CAAC,2BAA2B,GAAG,OAAO;YAChD,CAAC,kBAAkB,aAAa,CAAC,EAAE,kBAAkB,KAAK;QAC5D;IACF;IACA,IAAI,UAAU,MAAM,EAAE;QACpB,KAAK,MAAM,YAAY,UAAW;YAChC,IACE,OAAO,aAAa,YACpB,CAAC,iBAAiB,IAAI,CAAC,aACvB,YAAY,GAAG,CAAC,WAChB;gBACA,MAAM,IAAI,YACR;YAEJ;YAEA,YAAY,GAAG,CAAC;QAClB;QAEA,KAAK,OAAO,CAAC,yBAAyB,GAAG,UAAU,IAAI,CAAC;IAC1D;IACA,IAAI,KAAK,MAAM,EAAE;QACf,IAAI,KAAK,eAAe,GAAG,IAAI;YAC7B,KAAK,OAAO,CAAC,uBAAuB,GAAG,KAAK,MAAM;QACpD,OAAO;YACL,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK,MAAM;QACnC;IACF;IACA,IAAI,UAAU,QAAQ,IAAI,UAAU,QAAQ,EAAE;QAC5C,KAAK,IAAI,GAAG,GAAG,UAAU,QAAQ,CAAC,CAAC,EAAE,UAAU,QAAQ,EAAE;IAC3D;IAEA,IAAI,UAAU;QACZ,MAAM,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC;QAE9B,KAAK,UAAU,GAAG,KAAK,CAAC,EAAE;QAC1B,KAAK,IAAI,GAAG,KAAK,CAAC,EAAE;IACtB;IAEA,IAAI;IAEJ,IAAI,KAAK,eAAe,EAAE;QACxB,IAAI,UAAU,UAAU,KAAK,GAAG;YAC9B,UAAU,YAAY,GAAG;YACzB,UAAU,eAAe,GAAG;YAC5B,UAAU,yBAAyB,GAAG,WAClC,KAAK,UAAU,GACf,UAAU,IAAI;YAElB,MAAM,UAAU,WAAW,QAAQ,OAAO;YAE1C,EAAE;YACF,wEAAwE;YACxE,wCAAwC;YACxC,EAAE;YACF,UAAU;gBAAE,GAAG,OAAO;gBAAE,SAAS,CAAC;YAAE;YAEpC,IAAI,SAAS;gBACX,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,SAAU;oBAClD,QAAQ,OAAO,CAAC,IAAI,WAAW,GAAG,GAAG;gBACvC;YACF;QACF,OAAO,IAAI,UAAU,aAAa,CAAC,gBAAgB,GAAG;YACpD,MAAM,aAAa,WACf,UAAU,YAAY,GACpB,KAAK,UAAU,KAAK,UAAU,yBAAyB,GACvD,QACF,UAAU,YAAY,GACpB,QACA,UAAU,IAAI,KAAK,UAAU,yBAAyB;YAE5D,IAAI,CAAC,cAAe,UAAU,eAAe,IAAI,CAAC,UAAW;gBAC3D,EAAE;gBACF,mEAAmE;gBACnE,qEAAqE;gBACrE,EAAE;gBACF,OAAO,KAAK,OAAO,CAAC,aAAa;gBACjC,OAAO,KAAK,OAAO,CAAC,MAAM;gBAE1B,IAAI,CAAC,YAAY,OAAO,KAAK,OAAO,CAAC,IAAI;gBAEzC,KAAK,IAAI,GAAG;YACd;QACF;QAEA,EAAE;QACF,4EAA4E;QAC5E,0EAA0E;QAC1E,wBAAwB;QACxB,EAAE;QACF,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,OAAO,CAAC,aAAa,EAAE;YAC/C,QAAQ,OAAO,CAAC,aAAa,GAC3B,WAAW,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,QAAQ,CAAC;QAC/C;QAEA,MAAM,UAAU,IAAI,GAAG,QAAQ;QAE/B,IAAI,UAAU,UAAU,EAAE;YACxB,EAAE;YACF,kEAAkE;YAClE,0DAA0D;YAC1D,0EAA0E;YAC1E,sEAAsE;YACtE,yEAAyE;YACzE,yEAAyE;YACzE,sCAAsC;YACtC,EAAE;YACF,UAAU,IAAI,CAAC,YAAY,UAAU,GAAG,EAAE;QAC5C;IACF,OAAO;QACL,MAAM,UAAU,IAAI,GAAG,QAAQ;IACjC;IAEA,IAAI,KAAK,OAAO,EAAE;QAChB,IAAI,EAAE,CAAC,WAAW;YAChB,eAAe,WAAW,KAAK;QACjC;IACF;IAEA,IAAI,EAAE,CAAC,SAAS,CAAC;QACf,IAAI,QAAQ,QAAQ,GAAG,CAAC,SAAS,EAAE;QAEnC,MAAM,UAAU,IAAI,GAAG;QACvB,kBAAkB,WAAW;IAC/B;IAEA,IAAI,EAAE,CAAC,YAAY,CAAC;QAClB,MAAM,WAAW,IAAI,OAAO,CAAC,QAAQ;QACrC,MAAM,aAAa,IAAI,UAAU;QAEjC,IACE,YACA,KAAK,eAAe,IACpB,cAAc,OACd,aAAa,KACb;YACA,IAAI,EAAE,UAAU,UAAU,GAAG,KAAK,YAAY,EAAE;gBAC9C,eAAe,WAAW,KAAK;gBAC/B;YACF;YAEA,IAAI,KAAK;YAET,IAAI;YAEJ,IAAI;gBACF,OAAO,IAAI,IAAI,UAAU;YAC3B,EAAE,OAAO,GAAG;gBACV,MAAM,MAAM,IAAI,YAAY,CAAC,aAAa,EAAE,UAAU;gBACtD,kBAAkB,WAAW;gBAC7B;YACF;YAEA,aAAa,WAAW,MAAM,WAAW;QAC3C,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,uBAAuB,KAAK,MAAM;YAC3D,eACE,WACA,KACA,CAAC,4BAA4B,EAAE,IAAI,UAAU,EAAE;QAEnD;IACF;IAEA,IAAI,EAAE,CAAC,WAAW,CAAC,KAAK,QAAQ;QAC9B,UAAU,IAAI,CAAC,WAAW;QAE1B,EAAE;QACF,iEAAiE;QACjE,qBAAqB;QACrB,EAAE;QACF,IAAI,UAAU,UAAU,KAAK,UAAU,UAAU,EAAE;QAEnD,MAAM,UAAU,IAAI,GAAG;QAEvB,MAAM,UAAU,IAAI,OAAO,CAAC,OAAO;QAEnC,IAAI,YAAY,aAAa,QAAQ,WAAW,OAAO,aAAa;YAClE,eAAe,WAAW,QAAQ;YAClC;QACF;QAEA,MAAM,SAAS,WAAW,QACvB,MAAM,CAAC,MAAM,MACb,MAAM,CAAC;QAEV,IAAI,IAAI,OAAO,CAAC,uBAAuB,KAAK,QAAQ;YAClD,eAAe,WAAW,QAAQ;YAClC;QACF;QAEA,MAAM,aAAa,IAAI,OAAO,CAAC,yBAAyB;QACxD,IAAI;QAEJ,IAAI,eAAe,WAAW;YAC5B,IAAI,CAAC,YAAY,IAAI,EAAE;gBACrB,YAAY;YACd,OAAO,IAAI,CAAC,YAAY,GAAG,CAAC,aAAa;gBACvC,YAAY;YACd;QACF,OAAO,IAAI,YAAY,IAAI,EAAE;YAC3B,YAAY;QACd;QAEA,IAAI,WAAW;YACb,eAAe,WAAW,QAAQ;YAClC;QACF;QAEA,IAAI,YAAY,UAAU,SAAS,GAAG;QAEtC,MAAM,yBAAyB,IAAI,OAAO,CAAC,2BAA2B;QAEtE,IAAI,2BAA2B,WAAW;YACxC,IAAI,CAAC,mBAAmB;gBACtB,MAAM,UACJ,oEACA;gBACF,eAAe,WAAW,QAAQ;gBAClC;YACF;YAEA,IAAI;YAEJ,IAAI;gBACF,aAAa,MAAM;YACrB,EAAE,OAAO,KAAK;gBACZ,MAAM,UAAU;gBAChB,eAAe,WAAW,QAAQ;gBAClC;YACF;YAEA,MAAM,iBAAiB,OAAO,IAAI,CAAC;YAEnC,IACE,eAAe,MAAM,KAAK,KAC1B,cAAc,CAAC,EAAE,KAAK,kBAAkB,aAAa,EACrD;gBACA,MAAM,UAAU;gBAChB,eAAe,WAAW,QAAQ;gBAClC;YACF;YAEA,IAAI;gBACF,kBAAkB,MAAM,CAAC,UAAU,CAAC,kBAAkB,aAAa,CAAC;YACtE,EAAE,OAAO,KAAK;gBACZ,MAAM,UAAU;gBAChB,eAAe,WAAW,QAAQ;gBAClC;YACF;YAEA,UAAU,WAAW,CAAC,kBAAkB,aAAa,CAAC,GACpD;QACJ;QAEA,UAAU,SAAS,CAAC,QAAQ,MAAM;YAChC,wBAAwB,KAAK,sBAAsB;YACnD,cAAc,KAAK,YAAY;YAC/B,YAAY,KAAK,UAAU;YAC3B,oBAAoB,KAAK,kBAAkB;QAC7C;IACF;IAEA,IAAI,KAAK,aAAa,EAAE;QACtB,KAAK,aAAa,CAAC,KAAK;IAC1B,OAAO;QACL,IAAI,GAAG;IACT;AACF;AAEA;;;;;;CAMC,GACD,SAAS,kBAAkB,SAAS,EAAE,GAAG;IACvC,UAAU,WAAW,GAAG,UAAU,OAAO;IACzC,UAAU,IAAI,CAAC,SAAS;IACxB,UAAU,SAAS;AACrB;AAEA;;;;;;CAMC,GACD,SAAS,WAAW,OAAO;IACzB,QAAQ,IAAI,GAAG,QAAQ,UAAU;IACjC,OAAO,IAAI,OAAO,CAAC;AACrB;AAEA;;;;;;CAMC,GACD,SAAS,WAAW,OAAO;IACzB,QAAQ,IAAI,GAAG;IAEf,IAAI,CAAC,QAAQ,UAAU,IAAI,QAAQ,UAAU,KAAK,IAAI;QACpD,QAAQ,UAAU,GAAG,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,QAAQ,IAAI;IACjE;IAEA,OAAO,IAAI,OAAO,CAAC;AACrB;AAEA;;;;;;;;CAQC,GACD,SAAS,eAAe,SAAS,EAAE,MAAM,EAAE,OAAO;IAChD,UAAU,WAAW,GAAG,UAAU,OAAO;IAEzC,MAAM,MAAM,IAAI,MAAM;IACtB,MAAM,iBAAiB,CAAC,KAAK;IAE7B,IAAI,OAAO,SAAS,EAAE;QACpB,MAAM,CAAC,SAAS,GAAG;QACnB,OAAO,KAAK;QAEZ,IAAI,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,SAAS,EAAE;YAC7C,EAAE;YACF,wEAAwE;YACxE,0CAA0C;YAC1C,gDAAgD;YAChD,EAAE;YACF,OAAO,MAAM,CAAC,OAAO;QACvB;QAEA,QAAQ,QAAQ,CAAC,mBAAmB,WAAW;IACjD,OAAO;QACL,OAAO,OAAO,CAAC;QACf,OAAO,IAAI,CAAC,SAAS,UAAU,IAAI,CAAC,IAAI,CAAC,WAAW;QACpD,OAAO,IAAI,CAAC,SAAS,UAAU,SAAS,CAAC,IAAI,CAAC;IAChD;AACF;AAEA;;;;;;;;CAQC,GACD,SAAS,eAAe,SAAS,EAAE,IAAI,EAAE,EAAE;IACzC,IAAI,MAAM;QACR,MAAM,SAAS,SAAS,MAAM,MAAM;QAEpC,EAAE;QACF,4EAA4E;QAC5E,uEAAuE;QACvE,qEAAqE;QACrE,gCAAgC;QAChC,EAAE;QACF,IAAI,UAAU,OAAO,EAAE,UAAU,OAAO,CAAC,cAAc,IAAI;aACtD,UAAU,eAAe,IAAI;IACpC;IAEA,IAAI,IAAI;QACN,MAAM,MAAM,IAAI,MACd,CAAC,kCAAkC,EAAE,UAAU,UAAU,CAAC,CAAC,CAAC,GAC1D,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,UAAU,CAAC,CAAC,CAAC,CAAC;QAE5C,QAAQ,QAAQ,CAAC,IAAI;IACvB;AACF;AAEA;;;;;;CAMC,GACD,SAAS,mBAAmB,IAAI,EAAE,MAAM;IACtC,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,UAAU,mBAAmB,GAAG;IAChC,UAAU,aAAa,GAAG;IAC1B,UAAU,UAAU,GAAG;IAEvB,IAAI,UAAU,OAAO,CAAC,WAAW,KAAK,WAAW;IAEjD,UAAU,OAAO,CAAC,cAAc,CAAC,QAAQ;IACzC,QAAQ,QAAQ,CAAC,QAAQ,UAAU,OAAO;IAE1C,IAAI,SAAS,MAAM,UAAU,KAAK;SAC7B,UAAU,KAAK,CAAC,MAAM;AAC7B;AAEA;;;;CAIC,GACD,SAAS;IACP,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,IAAI,CAAC,UAAU,QAAQ,EAAE,UAAU,OAAO,CAAC,MAAM;AACnD;AAEA;;;;;CAKC,GACD,SAAS,gBAAgB,GAAG;IAC1B,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,IAAI,UAAU,OAAO,CAAC,WAAW,KAAK,WAAW;QAC/C,UAAU,OAAO,CAAC,cAAc,CAAC,QAAQ;QAEzC,EAAE;QACF,wEAAwE;QACxE,gDAAgD;QAChD,EAAE;QACF,QAAQ,QAAQ,CAAC,QAAQ,UAAU,OAAO;QAE1C,UAAU,KAAK,CAAC,GAAG,CAAC,YAAY;IAClC;IAEA,UAAU,IAAI,CAAC,SAAS;AAC1B;AAEA;;;;CAIC,GACD,SAAS;IACP,IAAI,CAAC,WAAW,CAAC,SAAS;AAC5B;AAEA;;;;;;CAMC,GACD,SAAS,kBAAkB,IAAI,EAAE,QAAQ;IACvC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,MAAM;AACzC;AAEA;;;;;CAKC,GACD,SAAS,eAAe,IAAI;IAC1B,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,IAAI,UAAU,SAAS,EAAE,UAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE;IAC/D,UAAU,IAAI,CAAC,QAAQ;AACzB;AAEA;;;;;CAKC,GACD,SAAS,eAAe,IAAI;IAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ;AAChC;AAEA;;;;;CAKC,GACD,SAAS,OAAO,MAAM;IACpB,OAAO,MAAM;AACf;AAEA;;;;CAIC,GACD,SAAS;IACP,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,IAAI,CAAC,cAAc,CAAC,SAAS;IAC7B,IAAI,CAAC,cAAc,CAAC,QAAQ;IAC5B,IAAI,CAAC,cAAc,CAAC,OAAO;IAE3B,UAAU,WAAW,GAAG,UAAU,OAAO;IAEzC,IAAI;IAEJ,EAAE;IACF,6EAA6E;IAC7E,4EAA4E;IAC5E,2EAA2E;IAC3E,6EAA6E;IAC7E,6EAA6E;IAC7E,8EAA8E;IAC9E,uCAAuC;IACvC,EAAE;IACF,IACE,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,IAC/B,CAAC,UAAU,mBAAmB,IAC9B,CAAC,UAAU,SAAS,CAAC,cAAc,CAAC,YAAY,IAChD,CAAC,QAAQ,UAAU,OAAO,CAAC,IAAI,EAAE,MAAM,MACvC;QACA,UAAU,SAAS,CAAC,KAAK,CAAC;IAC5B;IAEA,UAAU,SAAS,CAAC,GAAG;IAEvB,IAAI,CAAC,WAAW,GAAG;IAEnB,aAAa,UAAU,WAAW;IAElC,IACE,UAAU,SAAS,CAAC,cAAc,CAAC,QAAQ,IAC3C,UAAU,SAAS,CAAC,cAAc,CAAC,YAAY,EAC/C;QACA,UAAU,SAAS;IACrB,OAAO;QACL,UAAU,SAAS,CAAC,EAAE,CAAC,SAAS;QAChC,UAAU,SAAS,CAAC,EAAE,CAAC,UAAU;IACnC;AACF;AAEA;;;;;CAKC,GACD,SAAS,aAAa,KAAK;IACzB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ;QAC5C,IAAI,CAAC,KAAK;IACZ;AACF;AAEA;;;;CAIC,GACD,SAAS;IACP,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,UAAU,WAAW,GAAG,UAAU,OAAO;IACzC,UAAU,SAAS,CAAC,GAAG;IACvB,IAAI,CAAC,GAAG;AACV;AAEA;;;;CAIC,GACD,SAAS;IACP,MAAM,YAAY,IAAI,CAAC,WAAW;IAElC,IAAI,CAAC,cAAc,CAAC,SAAS;IAC7B,IAAI,CAAC,EAAE,CAAC,SAAS;IAEjB,IAAI,WAAW;QACb,UAAU,WAAW,GAAG,UAAU,OAAO;QACzC,IAAI,CAAC,OAAO;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3320, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/subprotocol.js"], "sourcesContent": ["'use strict';\n\nconst { tokenChars } = require('./validation');\n\n/**\n * Parses the `Sec-WebSocket-Protocol` header into a set of subprotocol names.\n *\n * @param {String} header The field value of the header\n * @return {Set} The subprotocol names\n * @public\n */\nfunction parse(header) {\n  const protocols = new Set();\n  let start = -1;\n  let end = -1;\n  let i = 0;\n\n  for (i; i < header.length; i++) {\n    const code = header.charCodeAt(i);\n\n    if (end === -1 && tokenChars[code] === 1) {\n      if (start === -1) start = i;\n    } else if (\n      i !== 0 &&\n      (code === 0x20 /* ' ' */ || code === 0x09) /* '\\t' */\n    ) {\n      if (end === -1 && start !== -1) end = i;\n    } else if (code === 0x2c /* ',' */) {\n      if (start === -1) {\n        throw new SyntaxError(`Unexpected character at index ${i}`);\n      }\n\n      if (end === -1) end = i;\n\n      const protocol = header.slice(start, end);\n\n      if (protocols.has(protocol)) {\n        throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n      }\n\n      protocols.add(protocol);\n      start = end = -1;\n    } else {\n      throw new SyntaxError(`Unexpected character at index ${i}`);\n    }\n  }\n\n  if (start === -1 || end !== -1) {\n    throw new SyntaxError('Unexpected end of input');\n  }\n\n  const protocol = header.slice(start, i);\n\n  if (protocols.has(protocol)) {\n    throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n  }\n\n  protocols.add(protocol);\n  return protocols;\n}\n\nmodule.exports = { parse };\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,EAAE,UAAU,EAAE;AAEpB;;;;;;CAMC,GACD,SAAS,MAAM,MAAM;IACnB,MAAM,YAAY,IAAI;IACtB,IAAI,QAAQ,CAAC;IACb,IAAI,MAAM,CAAC;IACX,IAAI,IAAI;IAER,IAAK,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QAC9B,MAAM,OAAO,OAAO,UAAU,CAAC;QAE/B,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,KAAK,KAAK,GAAG;YACxC,IAAI,UAAU,CAAC,GAAG,QAAQ;QAC5B,OAAO,IACL,MAAM,KACN,CAAC,SAAS,KAAK,OAAO,OAAM,SAAS,IAAI,GACzC;YACA,IAAI,QAAQ,CAAC,KAAK,UAAU,CAAC,GAAG,MAAM;QACxC,OAAO,IAAI,SAAS,KAAK,OAAO,KAAI;YAClC,IAAI,UAAU,CAAC,GAAG;gBAChB,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;YAC5D;YAEA,IAAI,QAAQ,CAAC,GAAG,MAAM;YAEtB,MAAM,WAAW,OAAO,KAAK,CAAC,OAAO;YAErC,IAAI,UAAU,GAAG,CAAC,WAAW;gBAC3B,MAAM,IAAI,YAAY,CAAC,KAAK,EAAE,SAAS,2BAA2B,CAAC;YACrE;YAEA,UAAU,GAAG,CAAC;YACd,QAAQ,MAAM,CAAC;QACjB,OAAO;YACL,MAAM,IAAI,YAAY,CAAC,8BAA8B,EAAE,GAAG;QAC5D;IACF;IAEA,IAAI,UAAU,CAAC,KAAK,QAAQ,CAAC,GAAG;QAC9B,MAAM,IAAI,YAAY;IACxB;IAEA,MAAM,WAAW,OAAO,KAAK,CAAC,OAAO;IAErC,IAAI,UAAU,GAAG,CAAC,WAAW;QAC3B,MAAM,IAAI,YAAY,CAAC,KAAK,EAAE,SAAS,2BAA2B,CAAC;IACrE;IAEA,UAAU,GAAG,CAAC;IACd,OAAO;AACT;AAEA,OAAO,OAAO,GAAG;IAAE;AAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3372, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/lib/websocket-server.js"], "sourcesContent": ["/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex$\", \"caughtErrors\": \"none\" }] */\n\n'use strict';\n\nconst EventEmitter = require('events');\nconst http = require('http');\nconst { Duplex } = require('stream');\nconst { createHash } = require('crypto');\n\nconst extension = require('./extension');\nconst PerMessageDeflate = require('./permessage-deflate');\nconst subprotocol = require('./subprotocol');\nconst WebSocket = require('./websocket');\nconst { GUID, kWebSocket } = require('./constants');\n\nconst keyRegex = /^[+/0-9A-Za-z]{22}==$/;\n\nconst RUNNING = 0;\nconst CLOSING = 1;\nconst CLOSED = 2;\n\n/**\n * Class representing a WebSocket server.\n *\n * @extends EventEmitter\n */\nclass WebSocketServer extends EventEmitter {\n  /**\n   * Create a `WebSocketServer` instance.\n   *\n   * @param {Object} options Configuration options\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n   *     automatically send a pong in response to a ping\n   * @param {Number} [options.backlog=511] The maximum length of the queue of\n   *     pending connections\n   * @param {Boolean} [options.clientTracking=true] Specifies whether or not to\n   *     track clients\n   * @param {Function} [options.handleProtocols] A hook to handle protocols\n   * @param {String} [options.host] The hostname where to bind the server\n   * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n   *     size\n   * @param {Boolean} [options.noServer=false] Enable no server mode\n   * @param {String} [options.path] Accept only connections matching this path\n   * @param {(Boolean|Object)} [options.perMessageDeflate=false] Enable/disable\n   *     permessage-deflate\n   * @param {Number} [options.port] The port where to bind the server\n   * @param {(http.Server|https.Server)} [options.server] A pre-created HTTP/S\n   *     server to use\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @param {Function} [options.verifyClient] A hook to reject connections\n   * @param {Function} [options.WebSocket=WebSocket] Specifies the `WebSocket`\n   *     class to use. It must be the `WebSocket` class or class that extends it\n   * @param {Function} [callback] A listener for the `listening` event\n   */\n  constructor(options, callback) {\n    super();\n\n    options = {\n      allowSynchronousEvents: true,\n      autoPong: true,\n      maxPayload: 100 * 1024 * 1024,\n      skipUTF8Validation: false,\n      perMessageDeflate: false,\n      handleProtocols: null,\n      clientTracking: true,\n      verifyClient: null,\n      noServer: false,\n      backlog: null, // use default (511 as implemented in net.js)\n      server: null,\n      host: null,\n      path: null,\n      port: null,\n      WebSocket,\n      ...options\n    };\n\n    if (\n      (options.port == null && !options.server && !options.noServer) ||\n      (options.port != null && (options.server || options.noServer)) ||\n      (options.server && options.noServer)\n    ) {\n      throw new TypeError(\n        'One and only one of the \"port\", \"server\", or \"noServer\" options ' +\n          'must be specified'\n      );\n    }\n\n    if (options.port != null) {\n      this._server = http.createServer((req, res) => {\n        const body = http.STATUS_CODES[426];\n\n        res.writeHead(426, {\n          'Content-Length': body.length,\n          'Content-Type': 'text/plain'\n        });\n        res.end(body);\n      });\n      this._server.listen(\n        options.port,\n        options.host,\n        options.backlog,\n        callback\n      );\n    } else if (options.server) {\n      this._server = options.server;\n    }\n\n    if (this._server) {\n      const emitConnection = this.emit.bind(this, 'connection');\n\n      this._removeListeners = addListeners(this._server, {\n        listening: this.emit.bind(this, 'listening'),\n        error: this.emit.bind(this, 'error'),\n        upgrade: (req, socket, head) => {\n          this.handleUpgrade(req, socket, head, emitConnection);\n        }\n      });\n    }\n\n    if (options.perMessageDeflate === true) options.perMessageDeflate = {};\n    if (options.clientTracking) {\n      this.clients = new Set();\n      this._shouldEmitClose = false;\n    }\n\n    this.options = options;\n    this._state = RUNNING;\n  }\n\n  /**\n   * Returns the bound address, the address family name, and port of the server\n   * as reported by the operating system if listening on an IP socket.\n   * If the server is listening on a pipe or UNIX domain socket, the name is\n   * returned as a string.\n   *\n   * @return {(Object|String|null)} The address of the server\n   * @public\n   */\n  address() {\n    if (this.options.noServer) {\n      throw new Error('The server is operating in \"noServer\" mode');\n    }\n\n    if (!this._server) return null;\n    return this._server.address();\n  }\n\n  /**\n   * Stop the server from accepting new connections and emit the `'close'` event\n   * when all existing connections are closed.\n   *\n   * @param {Function} [cb] A one-time listener for the `'close'` event\n   * @public\n   */\n  close(cb) {\n    if (this._state === CLOSED) {\n      if (cb) {\n        this.once('close', () => {\n          cb(new Error('The server is not running'));\n        });\n      }\n\n      process.nextTick(emitClose, this);\n      return;\n    }\n\n    if (cb) this.once('close', cb);\n\n    if (this._state === CLOSING) return;\n    this._state = CLOSING;\n\n    if (this.options.noServer || this.options.server) {\n      if (this._server) {\n        this._removeListeners();\n        this._removeListeners = this._server = null;\n      }\n\n      if (this.clients) {\n        if (!this.clients.size) {\n          process.nextTick(emitClose, this);\n        } else {\n          this._shouldEmitClose = true;\n        }\n      } else {\n        process.nextTick(emitClose, this);\n      }\n    } else {\n      const server = this._server;\n\n      this._removeListeners();\n      this._removeListeners = this._server = null;\n\n      //\n      // The HTTP/S server was created internally. Close it, and rely on its\n      // `'close'` event.\n      //\n      server.close(() => {\n        emitClose(this);\n      });\n    }\n  }\n\n  /**\n   * See if a given request should be handled by this server instance.\n   *\n   * @param {http.IncomingMessage} req Request object to inspect\n   * @return {Boolean} `true` if the request is valid, else `false`\n   * @public\n   */\n  shouldHandle(req) {\n    if (this.options.path) {\n      const index = req.url.indexOf('?');\n      const pathname = index !== -1 ? req.url.slice(0, index) : req.url;\n\n      if (pathname !== this.options.path) return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Handle a HTTP Upgrade request.\n   *\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @public\n   */\n  handleUpgrade(req, socket, head, cb) {\n    socket.on('error', socketOnError);\n\n    const key = req.headers['sec-websocket-key'];\n    const upgrade = req.headers.upgrade;\n    const version = +req.headers['sec-websocket-version'];\n\n    if (req.method !== 'GET') {\n      const message = 'Invalid HTTP method';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 405, message);\n      return;\n    }\n\n    if (upgrade === undefined || upgrade.toLowerCase() !== 'websocket') {\n      const message = 'Invalid Upgrade header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n\n    if (key === undefined || !keyRegex.test(key)) {\n      const message = 'Missing or invalid Sec-WebSocket-Key header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n\n    if (version !== 8 && version !== 13) {\n      const message = 'Missing or invalid Sec-WebSocket-Version header';\n      abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n      return;\n    }\n\n    if (!this.shouldHandle(req)) {\n      abortHandshake(socket, 400);\n      return;\n    }\n\n    const secWebSocketProtocol = req.headers['sec-websocket-protocol'];\n    let protocols = new Set();\n\n    if (secWebSocketProtocol !== undefined) {\n      try {\n        protocols = subprotocol.parse(secWebSocketProtocol);\n      } catch (err) {\n        const message = 'Invalid Sec-WebSocket-Protocol header';\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n\n    const secWebSocketExtensions = req.headers['sec-websocket-extensions'];\n    const extensions = {};\n\n    if (\n      this.options.perMessageDeflate &&\n      secWebSocketExtensions !== undefined\n    ) {\n      const perMessageDeflate = new PerMessageDeflate(\n        this.options.perMessageDeflate,\n        true,\n        this.options.maxPayload\n      );\n\n      try {\n        const offers = extension.parse(secWebSocketExtensions);\n\n        if (offers[PerMessageDeflate.extensionName]) {\n          perMessageDeflate.accept(offers[PerMessageDeflate.extensionName]);\n          extensions[PerMessageDeflate.extensionName] = perMessageDeflate;\n        }\n      } catch (err) {\n        const message =\n          'Invalid or unacceptable Sec-WebSocket-Extensions header';\n        abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n        return;\n      }\n    }\n\n    //\n    // Optionally call external client verification handler.\n    //\n    if (this.options.verifyClient) {\n      const info = {\n        origin:\n          req.headers[`${version === 8 ? 'sec-websocket-origin' : 'origin'}`],\n        secure: !!(req.socket.authorized || req.socket.encrypted),\n        req\n      };\n\n      if (this.options.verifyClient.length === 2) {\n        this.options.verifyClient(info, (verified, code, message, headers) => {\n          if (!verified) {\n            return abortHandshake(socket, code || 401, message, headers);\n          }\n\n          this.completeUpgrade(\n            extensions,\n            key,\n            protocols,\n            req,\n            socket,\n            head,\n            cb\n          );\n        });\n        return;\n      }\n\n      if (!this.options.verifyClient(info)) return abortHandshake(socket, 401);\n    }\n\n    this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n  }\n\n  /**\n   * Upgrade the connection to WebSocket.\n   *\n   * @param {Object} extensions The accepted extensions\n   * @param {String} key The value of the `Sec-WebSocket-Key` header\n   * @param {Set} protocols The subprotocols\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @throws {Error} If called more than once with the same socket\n   * @private\n   */\n  completeUpgrade(extensions, key, protocols, req, socket, head, cb) {\n    //\n    // Destroy the socket if the client has already sent a FIN packet.\n    //\n    if (!socket.readable || !socket.writable) return socket.destroy();\n\n    if (socket[kWebSocket]) {\n      throw new Error(\n        'server.handleUpgrade() was called more than once with the same ' +\n          'socket, possibly due to a misconfiguration'\n      );\n    }\n\n    if (this._state > RUNNING) return abortHandshake(socket, 503);\n\n    const digest = createHash('sha1')\n      .update(key + GUID)\n      .digest('base64');\n\n    const headers = [\n      'HTTP/1.1 101 Switching Protocols',\n      'Upgrade: websocket',\n      'Connection: Upgrade',\n      `Sec-WebSocket-Accept: ${digest}`\n    ];\n\n    const ws = new this.options.WebSocket(null, undefined, this.options);\n\n    if (protocols.size) {\n      //\n      // Optionally call external protocol selection handler.\n      //\n      const protocol = this.options.handleProtocols\n        ? this.options.handleProtocols(protocols, req)\n        : protocols.values().next().value;\n\n      if (protocol) {\n        headers.push(`Sec-WebSocket-Protocol: ${protocol}`);\n        ws._protocol = protocol;\n      }\n    }\n\n    if (extensions[PerMessageDeflate.extensionName]) {\n      const params = extensions[PerMessageDeflate.extensionName].params;\n      const value = extension.format({\n        [PerMessageDeflate.extensionName]: [params]\n      });\n      headers.push(`Sec-WebSocket-Extensions: ${value}`);\n      ws._extensions = extensions;\n    }\n\n    //\n    // Allow external modification/inspection of handshake headers.\n    //\n    this.emit('headers', headers, req);\n\n    socket.write(headers.concat('\\r\\n').join('\\r\\n'));\n    socket.removeListener('error', socketOnError);\n\n    ws.setSocket(socket, head, {\n      allowSynchronousEvents: this.options.allowSynchronousEvents,\n      maxPayload: this.options.maxPayload,\n      skipUTF8Validation: this.options.skipUTF8Validation\n    });\n\n    if (this.clients) {\n      this.clients.add(ws);\n      ws.on('close', () => {\n        this.clients.delete(ws);\n\n        if (this._shouldEmitClose && !this.clients.size) {\n          process.nextTick(emitClose, this);\n        }\n      });\n    }\n\n    cb(ws, req);\n  }\n}\n\nmodule.exports = WebSocketServer;\n\n/**\n * Add event listeners on an `EventEmitter` using a map of <event, listener>\n * pairs.\n *\n * @param {EventEmitter} server The event emitter\n * @param {Object.<String, Function>} map The listeners to add\n * @return {Function} A function that will remove the added listeners when\n *     called\n * @private\n */\nfunction addListeners(server, map) {\n  for (const event of Object.keys(map)) server.on(event, map[event]);\n\n  return function removeListeners() {\n    for (const event of Object.keys(map)) {\n      server.removeListener(event, map[event]);\n    }\n  };\n}\n\n/**\n * Emit a `'close'` event on an `EventEmitter`.\n *\n * @param {EventEmitter} server The event emitter\n * @private\n */\nfunction emitClose(server) {\n  server._state = CLOSED;\n  server.emit('close');\n}\n\n/**\n * Handle socket errors.\n *\n * @private\n */\nfunction socketOnError() {\n  this.destroy();\n}\n\n/**\n * Close the connection when preconditions are not fulfilled.\n *\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} [message] The HTTP response body\n * @param {Object} [headers] Additional HTTP response headers\n * @private\n */\nfunction abortHandshake(socket, code, message, headers) {\n  //\n  // The socket is writable unless the user destroyed or ended it before calling\n  // `server.handleUpgrade()` or in the `verifyClient` function, which is a user\n  // error. Handling this does not make much sense as the worst that can happen\n  // is that some of the data written by the user might be discarded due to the\n  // call to `socket.end()` below, which triggers an `'error'` event that in\n  // turn causes the socket to be destroyed.\n  //\n  message = message || http.STATUS_CODES[code];\n  headers = {\n    Connection: 'close',\n    'Content-Type': 'text/html',\n    'Content-Length': Buffer.byteLength(message),\n    ...headers\n  };\n\n  socket.once('finish', socket.destroy);\n\n  socket.end(\n    `HTTP/1.1 ${code} ${http.STATUS_CODES[code]}\\r\\n` +\n      Object.keys(headers)\n        .map((h) => `${h}: ${headers[h]}`)\n        .join('\\r\\n') +\n      '\\r\\n\\r\\n' +\n      message\n  );\n}\n\n/**\n * Emit a `'wsClientError'` event on a `WebSocketServer` if there is at least\n * one listener for it, otherwise call `abortHandshake()`.\n *\n * @param {WebSocketServer} server The WebSocket server\n * @param {http.IncomingMessage} req The request object\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} message The HTTP response body\n * @private\n */\nfunction abortHandshakeOrEmitwsClientError(server, req, socket, code, message) {\n  if (server.listenerCount('wsClientError')) {\n    const err = new Error(message);\n    Error.captureStackTrace(err, abortHandshakeOrEmitwsClientError);\n\n    server.emit('wsClientError', err, socket, req);\n  } else {\n    abortHandshake(socket, code, message);\n  }\n}\n"], "names": [], "mappings": "AAAA,iGAAiG,GAEjG;AAEA,MAAM;AACN,MAAM;AACN,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM,EAAE,UAAU,EAAE;AAEpB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE;AAE1B,MAAM,WAAW;AAEjB,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,SAAS;AAEf;;;;CAIC,GACD,MAAM,wBAAwB;IAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BC,GACD,YAAY,OAAO,EAAE,QAAQ,CAAE;QAC7B,KAAK;QAEL,UAAU;YACR,wBAAwB;YACxB,UAAU;YACV,YAAY,MAAM,OAAO;YACzB,oBAAoB;YACpB,mBAAmB;YACnB,iBAAiB;YACjB,gBAAgB;YAChB,cAAc;YACd,UAAU;YACV,SAAS;YACT,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN;YACA,GAAG,OAAO;QACZ;QAEA,IACE,AAAC,QAAQ,IAAI,IAAI,QAAQ,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,QAAQ,IAC5D,QAAQ,IAAI,IAAI,QAAQ,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,KAC3D,QAAQ,MAAM,IAAI,QAAQ,QAAQ,EACnC;YACA,MAAM,IAAI,UACR,qEACE;QAEN;QAEA,IAAI,QAAQ,IAAI,IAAI,MAAM;YACxB,IAAI,CAAC,OAAO,GAAG,KAAK,YAAY,CAAC,CAAC,KAAK;gBACrC,MAAM,OAAO,KAAK,YAAY,CAAC,IAAI;gBAEnC,IAAI,SAAS,CAAC,KAAK;oBACjB,kBAAkB,KAAK,MAAM;oBAC7B,gBAAgB;gBAClB;gBACA,IAAI,GAAG,CAAC;YACV;YACA,IAAI,CAAC,OAAO,CAAC,MAAM,CACjB,QAAQ,IAAI,EACZ,QAAQ,IAAI,EACZ,QAAQ,OAAO,EACf;QAEJ,OAAO,IAAI,QAAQ,MAAM,EAAE;YACzB,IAAI,CAAC,OAAO,GAAG,QAAQ,MAAM;QAC/B;QAEA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,MAAM,iBAAiB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAE5C,IAAI,CAAC,gBAAgB,GAAG,aAAa,IAAI,CAAC,OAAO,EAAE;gBACjD,WAAW,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAChC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAC5B,SAAS,CAAC,KAAK,QAAQ;oBACrB,IAAI,CAAC,aAAa,CAAC,KAAK,QAAQ,MAAM;gBACxC;YACF;QACF;QAEA,IAAI,QAAQ,iBAAiB,KAAK,MAAM,QAAQ,iBAAiB,GAAG,CAAC;QACrE,IAAI,QAAQ,cAAc,EAAE;YAC1B,IAAI,CAAC,OAAO,GAAG,IAAI;YACnB,IAAI,CAAC,gBAAgB,GAAG;QAC1B;QAEA,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;;;;;;;GAQC,GACD,UAAU;QACR,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO;IAC7B;IAEA;;;;;;GAMC,GACD,MAAM,EAAE,EAAE;QACR,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YAC1B,IAAI,IAAI;gBACN,IAAI,CAAC,IAAI,CAAC,SAAS;oBACjB,GAAG,IAAI,MAAM;gBACf;YACF;YAEA,QAAQ,QAAQ,CAAC,WAAW,IAAI;YAChC;QACF;QAEA,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS;QAE3B,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS;QAC7B,IAAI,CAAC,MAAM,GAAG;QAEd,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YAChD,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,IAAI,CAAC,gBAAgB;gBACrB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,GAAG;YACzC;YAEA,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;oBACtB,QAAQ,QAAQ,CAAC,WAAW,IAAI;gBAClC,OAAO;oBACL,IAAI,CAAC,gBAAgB,GAAG;gBAC1B;YACF,OAAO;gBACL,QAAQ,QAAQ,CAAC,WAAW,IAAI;YAClC;QACF,OAAO;YACL,MAAM,SAAS,IAAI,CAAC,OAAO;YAE3B,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,GAAG;YAEvC,EAAE;YACF,sEAAsE;YACtE,mBAAmB;YACnB,EAAE;YACF,OAAO,KAAK,CAAC;gBACX,UAAU,IAAI;YAChB;QACF;IACF;IAEA;;;;;;GAMC,GACD,aAAa,GAAG,EAAE;QAChB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACrB,MAAM,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAC;YAC9B,MAAM,WAAW,UAAU,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,IAAI,GAAG;YAEjE,IAAI,aAAa,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO;QAC7C;QAEA,OAAO;IACT;IAEA;;;;;;;;GAQC,GACD,cAAc,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE;QACnC,OAAO,EAAE,CAAC,SAAS;QAEnB,MAAM,MAAM,IAAI,OAAO,CAAC,oBAAoB;QAC5C,MAAM,UAAU,IAAI,OAAO,CAAC,OAAO;QACnC,MAAM,UAAU,CAAC,IAAI,OAAO,CAAC,wBAAwB;QAErD,IAAI,IAAI,MAAM,KAAK,OAAO;YACxB,MAAM,UAAU;YAChB,kCAAkC,IAAI,EAAE,KAAK,QAAQ,KAAK;YAC1D;QACF;QAEA,IAAI,YAAY,aAAa,QAAQ,WAAW,OAAO,aAAa;YAClE,MAAM,UAAU;YAChB,kCAAkC,IAAI,EAAE,KAAK,QAAQ,KAAK;YAC1D;QACF;QAEA,IAAI,QAAQ,aAAa,CAAC,SAAS,IAAI,CAAC,MAAM;YAC5C,MAAM,UAAU;YAChB,kCAAkC,IAAI,EAAE,KAAK,QAAQ,KAAK;YAC1D;QACF;QAEA,IAAI,YAAY,KAAK,YAAY,IAAI;YACnC,MAAM,UAAU;YAChB,kCAAkC,IAAI,EAAE,KAAK,QAAQ,KAAK;YAC1D;QACF;QAEA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM;YAC3B,eAAe,QAAQ;YACvB;QACF;QAEA,MAAM,uBAAuB,IAAI,OAAO,CAAC,yBAAyB;QAClE,IAAI,YAAY,IAAI;QAEpB,IAAI,yBAAyB,WAAW;YACtC,IAAI;gBACF,YAAY,YAAY,KAAK,CAAC;YAChC,EAAE,OAAO,KAAK;gBACZ,MAAM,UAAU;gBAChB,kCAAkC,IAAI,EAAE,KAAK,QAAQ,KAAK;gBAC1D;YACF;QACF;QAEA,MAAM,yBAAyB,IAAI,OAAO,CAAC,2BAA2B;QACtE,MAAM,aAAa,CAAC;QAEpB,IACE,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAC9B,2BAA2B,WAC3B;YACA,MAAM,oBAAoB,IAAI,kBAC5B,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAC9B,MACA,IAAI,CAAC,OAAO,CAAC,UAAU;YAGzB,IAAI;gBACF,MAAM,SAAS,UAAU,KAAK,CAAC;gBAE/B,IAAI,MAAM,CAAC,kBAAkB,aAAa,CAAC,EAAE;oBAC3C,kBAAkB,MAAM,CAAC,MAAM,CAAC,kBAAkB,aAAa,CAAC;oBAChE,UAAU,CAAC,kBAAkB,aAAa,CAAC,GAAG;gBAChD;YACF,EAAE,OAAO,KAAK;gBACZ,MAAM,UACJ;gBACF,kCAAkC,IAAI,EAAE,KAAK,QAAQ,KAAK;gBAC1D;YACF;QACF;QAEA,EAAE;QACF,wDAAwD;QACxD,EAAE;QACF,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAC7B,MAAM,OAAO;gBACX,QACE,IAAI,OAAO,CAAC,GAAG,YAAY,IAAI,yBAAyB,UAAU,CAAC;gBACrE,QAAQ,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,UAAU,IAAI,IAAI,MAAM,CAAC,SAAS;gBACxD;YACF;YAEA,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,KAAK,GAAG;gBAC1C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,MAAM,SAAS;oBACxD,IAAI,CAAC,UAAU;wBACb,OAAO,eAAe,QAAQ,QAAQ,KAAK,SAAS;oBACtD;oBAEA,IAAI,CAAC,eAAe,CAClB,YACA,KACA,WACA,KACA,QACA,MACA;gBAEJ;gBACA;YACF;YAEA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,OAAO,eAAe,QAAQ;QACtE;QAEA,IAAI,CAAC,eAAe,CAAC,YAAY,KAAK,WAAW,KAAK,QAAQ,MAAM;IACtE;IAEA;;;;;;;;;;;;GAYC,GACD,gBAAgB,UAAU,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE;QACjE,EAAE;QACF,kEAAkE;QAClE,EAAE;QACF,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,EAAE,OAAO,OAAO,OAAO;QAE/D,IAAI,MAAM,CAAC,WAAW,EAAE;YACtB,MAAM,IAAI,MACR,oEACE;QAEN;QAEA,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,OAAO,eAAe,QAAQ;QAEzD,MAAM,SAAS,WAAW,QACvB,MAAM,CAAC,MAAM,MACb,MAAM,CAAC;QAEV,MAAM,UAAU;YACd;YACA;YACA;YACA,CAAC,sBAAsB,EAAE,QAAQ;SAClC;QAED,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,WAAW,IAAI,CAAC,OAAO;QAEnE,IAAI,UAAU,IAAI,EAAE;YAClB,EAAE;YACF,uDAAuD;YACvD,EAAE;YACF,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,eAAe,GACzC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,OACxC,UAAU,MAAM,GAAG,IAAI,GAAG,KAAK;YAEnC,IAAI,UAAU;gBACZ,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,UAAU;gBAClD,GAAG,SAAS,GAAG;YACjB;QACF;QAEA,IAAI,UAAU,CAAC,kBAAkB,aAAa,CAAC,EAAE;YAC/C,MAAM,SAAS,UAAU,CAAC,kBAAkB,aAAa,CAAC,CAAC,MAAM;YACjE,MAAM,QAAQ,UAAU,MAAM,CAAC;gBAC7B,CAAC,kBAAkB,aAAa,CAAC,EAAE;oBAAC;iBAAO;YAC7C;YACA,QAAQ,IAAI,CAAC,CAAC,0BAA0B,EAAE,OAAO;YACjD,GAAG,WAAW,GAAG;QACnB;QAEA,EAAE;QACF,+DAA+D;QAC/D,EAAE;QACF,IAAI,CAAC,IAAI,CAAC,WAAW,SAAS;QAE9B,OAAO,KAAK,CAAC,QAAQ,MAAM,CAAC,QAAQ,IAAI,CAAC;QACzC,OAAO,cAAc,CAAC,SAAS;QAE/B,GAAG,SAAS,CAAC,QAAQ,MAAM;YACzB,wBAAwB,IAAI,CAAC,OAAO,CAAC,sBAAsB;YAC3D,YAAY,IAAI,CAAC,OAAO,CAAC,UAAU;YACnC,oBAAoB,IAAI,CAAC,OAAO,CAAC,kBAAkB;QACrD;QAEA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;YACjB,GAAG,EAAE,CAAC,SAAS;gBACb,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;gBAEpB,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;oBAC/C,QAAQ,QAAQ,CAAC,WAAW,IAAI;gBAClC;YACF;QACF;QAEA,GAAG,IAAI;IACT;AACF;AAEA,OAAO,OAAO,GAAG;AAEjB;;;;;;;;;CASC,GACD,SAAS,aAAa,MAAM,EAAE,GAAG;IAC/B,KAAK,MAAM,SAAS,OAAO,IAAI,CAAC,KAAM,OAAO,EAAE,CAAC,OAAO,GAAG,CAAC,MAAM;IAEjE,OAAO,SAAS;QACd,KAAK,MAAM,SAAS,OAAO,IAAI,CAAC,KAAM;YACpC,OAAO,cAAc,CAAC,OAAO,GAAG,CAAC,MAAM;QACzC;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,UAAU,MAAM;IACvB,OAAO,MAAM,GAAG;IAChB,OAAO,IAAI,CAAC;AACd;AAEA;;;;CAIC,GACD,SAAS;IACP,IAAI,CAAC,OAAO;AACd;AAEA;;;;;;;;CAQC,GACD,SAAS,eAAe,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;IACpD,EAAE;IACF,8EAA8E;IAC9E,8EAA8E;IAC9E,6EAA6E;IAC7E,6EAA6E;IAC7E,0EAA0E;IAC1E,0CAA0C;IAC1C,EAAE;IACF,UAAU,WAAW,KAAK,YAAY,CAAC,KAAK;IAC5C,UAAU;QACR,YAAY;QACZ,gBAAgB;QAChB,kBAAkB,OAAO,UAAU,CAAC;QACpC,GAAG,OAAO;IACZ;IAEA,OAAO,IAAI,CAAC,UAAU,OAAO,OAAO;IAEpC,OAAO,GAAG,CACR,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,GAC/C,OAAO,IAAI,CAAC,SACT,GAAG,CAAC,CAAC,IAAM,GAAG,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,EAChC,IAAI,CAAC,UACR,aACA;AAEN;AAEA;;;;;;;;;;CAUC,GACD,SAAS,kCAAkC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO;IAC3E,IAAI,OAAO,aAAa,CAAC,kBAAkB;QACzC,MAAM,MAAM,IAAI,MAAM;QACtB,MAAM,iBAAiB,CAAC,KAAK;QAE7B,OAAO,IAAI,CAAC,iBAAiB,KAAK,QAAQ;IAC5C,OAAO;QACL,eAAe,QAAQ,MAAM;IAC/B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3794, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/engine.io-client/node_modules/ws/wrapper.mjs"], "sourcesContent": ["import createWebSocketStream from './lib/stream.js';\nimport Receiver from './lib/receiver.js';\nimport Sender from './lib/sender.js';\nimport WebSocket from './lib/websocket.js';\nimport WebSocketServer from './lib/websocket-server.js';\n\nexport { createWebSocketStream, Receiver, Sender, WebSocket, WebSocketServer };\nexport default WebSocket;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;;uCAGe,gLAAA,CAAA,UAAS", "ignoreList": [0], "debugId": null}}]}