'use client'

import { ReactNode } from 'react'
import { WagmiConfig, createConfig, configureChains, mainnet, polygon } from 'wagmi'
import { publicProvider } from 'wagmi/providers/public'
import { Web3Modal } from '@web3modal/react'
import { EthereumClient, w3mConnectors, w3mProvider } from '@web3modal/ethereum'

const chains = [mainnet, polygon]
const projectId = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || 'demo-project-id'

const { publicClient } = configureChains(chains, [
  w3mProvider({ projectId }),
  publicProvider()
])

const wagmiConfig = createConfig({
  autoConnect: true,
  connectors: w3mConnectors({ projectId, chains }),
  publicClient
})

const ethereumClient = new EthereumClient(wagmiConfig, chains)

interface ProvidersProps {
  children: ReactNode
}

export function Providers({ children }: ProvidersProps) {
  return (
    <>
      <WagmiConfig config={wagmiConfig}>
        {children}
      </WagmiConfig>
      <Web3Modal 
        projectId={projectId} 
        ethereumClient={ethereumClient}
        themeMode="dark"
        themeVariables={{
          '--w3m-font-family': 'Inter, system-ui, sans-serif',
          '--w3m-accent-color': '#d4af37',
          '--w3m-background-color': '#1a1a1a',
          '--w3m-logo-image-url': '/logo.png'
        }}
      />
    </>
  )
}
