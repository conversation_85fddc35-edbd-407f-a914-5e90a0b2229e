import { MarketData, TechnicalIndicators, AnalysisResult, Signal } from '../types';
import { DEFAULT_PARAMETERS, TECHNICAL_PATTERNS } from '../constants';

export class TechnicalAnalyzer {

  async analyze(marketData: MarketData[]): Promise<AnalysisResult> {
    try {
      console.log('Performing technical analysis...');

      if (marketData.length < 50) {
        throw new Error('Insufficient data for technical analysis');
      }

      // Calculate technical indicators
      const indicators = this.calculateIndicators(marketData);
      
      // Generate signals
      const signals = this.generateSignals(marketData, indicators);
      
      // Calculate overall score
      const score = this.calculateTechnicalScore(indicators, signals);
      
      // Detect patterns
      const patterns = this.detectPatterns(marketData);
      
      return {
        symbol: marketData[0]?.symbol || 'UNKNOWN',
        timestamp: Date.now(),
        analysis_type: 'technical',
        score,
        confidence: this.calculateConfidence(indicators, signals),
        signals: [...signals, ...patterns],
        recommendations: this.generateRecommendations(score, indicators),
        risk_assessment: this.assessTechnicalRisk(indicators, marketData)
      };

    } catch (error) {
      console.error('Technical analysis failed:', error);
      throw new Error(`Technical analysis failed: ${error.message}`);
    }
  }

  calculateIndicators(marketData: MarketData[]): TechnicalIndicators {
    const latest = marketData[marketData.length - 1];
    
    return {
      sma: {
        5: this.calculateSMA(marketData, 5),
        10: this.calculateSMA(marketData, 10),
        20: this.calculateSMA(marketData, 20),
        50: this.calculateSMA(marketData, 50),
        200: this.calculateSMA(marketData, 200)
      },
      ema: {
        12: this.calculateEMA(marketData, 12),
        26: this.calculateEMA(marketData, 26),
        50: this.calculateEMA(marketData, 50)
      },
      rsi: this.calculateRSI(marketData, DEFAULT_PARAMETERS.RSI_PERIOD),
      macd: this.calculateMACD(marketData),
      bollinger_bands: this.calculateBollingerBands(marketData),
      stochastic: this.calculateStochastic(marketData),
      atr: this.calculateATR(marketData, DEFAULT_PARAMETERS.ATR_PERIOD),
      adx: this.calculateADX(marketData, DEFAULT_PARAMETERS.ADX_PERIOD),
      williams_r: this.calculateWilliamsR(marketData, DEFAULT_PARAMETERS.WILLIAMS_R_PERIOD),
      cci: this.calculateCCI(marketData, DEFAULT_PARAMETERS.CCI_PERIOD)
    };
  }

  private calculateSMA(data: MarketData[], period: number): number {
    if (data.length < period) return data[data.length - 1].close;
    
    const sum = data.slice(-period).reduce((acc, item) => acc + item.close, 0);
    return sum / period;
  }

  private calculateEMA(data: MarketData[], period: number): number {
    if (data.length === 0) return 0;
    if (data.length === 1) return data[0].close;

    const multiplier = 2 / (period + 1);
    let ema = data[0].close;

    for (let i = 1; i < data.length; i++) {
      ema = (data[i].close * multiplier) + (ema * (1 - multiplier));
    }

    return ema;
  }

  private calculateRSI(data: MarketData[], period: number = 14): number {
    if (data.length < period + 1) return 50;

    let gains = 0;
    let losses = 0;

    // Calculate initial average gain and loss
    for (let i = 1; i <= period; i++) {
      const change = data[i].close - data[i - 1].close;
      if (change > 0) {
        gains += change;
      } else {
        losses += Math.abs(change);
      }
    }

    let avgGain = gains / period;
    let avgLoss = losses / period;

    // Calculate RSI for remaining periods
    for (let i = period + 1; i < data.length; i++) {
      const change = data[i].close - data[i - 1].close;
      
      if (change > 0) {
        avgGain = ((avgGain * (period - 1)) + change) / period;
        avgLoss = (avgLoss * (period - 1)) / period;
      } else {
        avgGain = (avgGain * (period - 1)) / period;
        avgLoss = ((avgLoss * (period - 1)) + Math.abs(change)) / period;
      }
    }

    if (avgLoss === 0) return 100;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }

  private calculateMACD(data: MarketData[]): { macd: number; signal: number; histogram: number } {
    const ema12 = this.calculateEMA(data, 12);
    const ema26 = this.calculateEMA(data, 26);
    const macd = ema12 - ema26;

    // Calculate signal line (9-period EMA of MACD)
    // Simplified calculation
    const signal = macd * 0.8; // Approximation
    const histogram = macd - signal;

    return { macd, signal, histogram };
  }

  private calculateBollingerBands(data: MarketData[], period: number = 20, stdDev: number = 2): {
    upper: number;
    middle: number;
    lower: number;
    bandwidth: number;
  } {
    const sma = this.calculateSMA(data, period);
    
    if (data.length < period) {
      return { upper: sma, middle: sma, lower: sma, bandwidth: 0 };
    }

    // Calculate standard deviation
    const recentData = data.slice(-period);
    const variance = recentData.reduce((sum, item) => {
      return sum + Math.pow(item.close - sma, 2);
    }, 0) / period;
    
    const standardDeviation = Math.sqrt(variance);
    
    const upper = sma + (standardDeviation * stdDev);
    const lower = sma - (standardDeviation * stdDev);
    const bandwidth = (upper - lower) / sma;

    return { upper, middle: sma, lower, bandwidth };
  }

  private calculateStochastic(data: MarketData[], kPeriod: number = 14, dPeriod: number = 3): {
    k: number;
    d: number;
  } {
    if (data.length < kPeriod) {
      return { k: 50, d: 50 };
    }

    const recentData = data.slice(-kPeriod);
    const highestHigh = Math.max(...recentData.map(d => d.high));
    const lowestLow = Math.min(...recentData.map(d => d.low));
    const currentClose = data[data.length - 1].close;

    const k = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;
    
    // Simplified %D calculation (should be SMA of %K)
    const d = k * 0.9; // Approximation

    return { k, d };
  }

  private calculateATR(data: MarketData[], period: number = 14): number {
    if (data.length < 2) return 0;

    const trueRanges = [];
    
    for (let i = 1; i < data.length; i++) {
      const high = data[i].high;
      const low = data[i].low;
      const prevClose = data[i - 1].close;
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      );
      
      trueRanges.push(tr);
    }

    // Calculate average of last 'period' true ranges
    const recentTR = trueRanges.slice(-period);
    return recentTR.reduce((sum, tr) => sum + tr, 0) / recentTR.length;
  }

  private calculateADX(data: MarketData[], period: number = 14): number {
    // Simplified ADX calculation
    if (data.length < period + 1) return 25;

    let totalDM = 0;
    let count = 0;

    for (let i = 1; i < Math.min(data.length, period + 1); i++) {
      const highDiff = data[i].high - data[i - 1].high;
      const lowDiff = data[i - 1].low - data[i].low;
      
      if (highDiff > lowDiff && highDiff > 0) {
        totalDM += highDiff;
      } else if (lowDiff > 0) {
        totalDM += lowDiff;
      }
      count++;
    }

    return count > 0 ? Math.min(100, (totalDM / count) * 100) : 25;
  }

  private calculateWilliamsR(data: MarketData[], period: number = 14): number {
    if (data.length < period) return -50;

    const recentData = data.slice(-period);
    const highestHigh = Math.max(...recentData.map(d => d.high));
    const lowestLow = Math.min(...recentData.map(d => d.low));
    const currentClose = data[data.length - 1].close;

    return ((highestHigh - currentClose) / (highestHigh - lowestLow)) * -100;
  }

  private calculateCCI(data: MarketData[], period: number = 20): number {
    if (data.length < period) return 0;

    const recentData = data.slice(-period);
    const typicalPrices = recentData.map(d => (d.high + d.low + d.close) / 3);
    const smaTP = typicalPrices.reduce((sum, tp) => sum + tp, 0) / period;
    
    const meanDeviation = typicalPrices.reduce((sum, tp) => sum + Math.abs(tp - smaTP), 0) / period;
    const currentTP = typicalPrices[typicalPrices.length - 1];
    
    return meanDeviation !== 0 ? (currentTP - smaTP) / (0.015 * meanDeviation) : 0;
  }

  private generateSignals(data: MarketData[], indicators: TechnicalIndicators): Signal[] {
    const signals: Signal[] = [];
    const currentPrice = data[data.length - 1].close;

    // RSI signals
    if (indicators.rsi > 70) {
      signals.push({
        type: 'sell',
        strength: (indicators.rsi - 70) / 30,
        source: 'RSI',
        description: `RSI overbought at ${indicators.rsi.toFixed(2)}`,
        timestamp: Date.now()
      });
    } else if (indicators.rsi < 30) {
      signals.push({
        type: 'buy',
        strength: (30 - indicators.rsi) / 30,
        source: 'RSI',
        description: `RSI oversold at ${indicators.rsi.toFixed(2)}`,
        timestamp: Date.now()
      });
    }

    // MACD signals
    if (indicators.macd.macd > indicators.macd.signal && indicators.macd.histogram > 0) {
      signals.push({
        type: 'buy',
        strength: 0.7,
        source: 'MACD',
        description: 'MACD bullish crossover',
        timestamp: Date.now()
      });
    } else if (indicators.macd.macd < indicators.macd.signal && indicators.macd.histogram < 0) {
      signals.push({
        type: 'sell',
        strength: 0.7,
        source: 'MACD',
        description: 'MACD bearish crossover',
        timestamp: Date.now()
      });
    }

    // Bollinger Bands signals
    if (currentPrice > indicators.bollinger_bands.upper) {
      signals.push({
        type: 'sell',
        strength: 0.6,
        source: 'Bollinger Bands',
        description: 'Price above upper Bollinger Band',
        timestamp: Date.now()
      });
    } else if (currentPrice < indicators.bollinger_bands.lower) {
      signals.push({
        type: 'buy',
        strength: 0.6,
        source: 'Bollinger Bands',
        description: 'Price below lower Bollinger Band',
        timestamp: Date.now()
      });
    }

    // Moving average signals
    if (currentPrice > indicators.sma[20] && indicators.sma[20] > indicators.sma[50]) {
      signals.push({
        type: 'buy',
        strength: 0.5,
        source: 'Moving Averages',
        description: 'Price above rising moving averages',
        timestamp: Date.now()
      });
    } else if (currentPrice < indicators.sma[20] && indicators.sma[20] < indicators.sma[50]) {
      signals.push({
        type: 'sell',
        strength: 0.5,
        source: 'Moving Averages',
        description: 'Price below falling moving averages',
        timestamp: Date.now()
      });
    }

    return signals;
  }

  private detectPatterns(data: MarketData[]): Signal[] {
    const patterns: Signal[] = [];
    
    if (data.length < 3) return patterns;

    const recent = data.slice(-3);
    
    // Simplified pattern detection
    // Hammer pattern
    if (this.isHammer(recent[2])) {
      patterns.push({
        type: 'buy',
        strength: 0.6,
        source: 'Pattern Recognition',
        description: 'Hammer candlestick pattern detected',
        timestamp: Date.now()
      });
    }

    // Shooting star pattern
    if (this.isShootingStar(recent[2])) {
      patterns.push({
        type: 'sell',
        strength: 0.6,
        source: 'Pattern Recognition',
        description: 'Shooting star pattern detected',
        timestamp: Date.now()
      });
    }

    // Engulfing patterns
    if (this.isBullishEngulfing(recent[1], recent[2])) {
      patterns.push({
        type: 'buy',
        strength: 0.8,
        source: 'Pattern Recognition',
        description: 'Bullish engulfing pattern detected',
        timestamp: Date.now()
      });
    }

    if (this.isBearishEngulfing(recent[1], recent[2])) {
      patterns.push({
        type: 'sell',
        strength: 0.8,
        source: 'Pattern Recognition',
        description: 'Bearish engulfing pattern detected',
        timestamp: Date.now()
      });
    }

    return patterns;
  }

  private isHammer(candle: MarketData): boolean {
    const body = Math.abs(candle.close - candle.open);
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
    const upperShadow = candle.high - Math.max(candle.open, candle.close);
    
    return lowerShadow > body * 2 && upperShadow < body * 0.5;
  }

  private isShootingStar(candle: MarketData): boolean {
    const body = Math.abs(candle.close - candle.open);
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
    const upperShadow = candle.high - Math.max(candle.open, candle.close);
    
    return upperShadow > body * 2 && lowerShadow < body * 0.5;
  }

  private isBullishEngulfing(prev: MarketData, current: MarketData): boolean {
    return prev.close < prev.open && // Previous candle is bearish
           current.close > current.open && // Current candle is bullish
           current.open < prev.close && // Current opens below previous close
           current.close > prev.open; // Current closes above previous open
  }

  private isBearishEngulfing(prev: MarketData, current: MarketData): boolean {
    return prev.close > prev.open && // Previous candle is bullish
           current.close < current.open && // Current candle is bearish
           current.open > prev.close && // Current opens above previous close
           current.close < prev.open; // Current closes below previous open
  }

  private calculateTechnicalScore(indicators: TechnicalIndicators, signals: Signal[]): number {
    let score = 0;
    let factors = 0;

    // RSI scoring
    if (indicators.rsi > 70) {
      score -= (indicators.rsi - 70) * 2; // Negative for overbought
    } else if (indicators.rsi < 30) {
      score += (30 - indicators.rsi) * 2; // Positive for oversold
    }
    factors++;

    // MACD scoring
    if (indicators.macd.histogram > 0) {
      score += 20;
    } else {
      score -= 20;
    }
    factors++;

    // Moving average scoring
    const currentPrice = indicators.sma[20]; // Use SMA20 as proxy for current price
    if (currentPrice > indicators.sma[50]) {
      score += 15;
    } else {
      score -= 15;
    }
    factors++;

    // Signal strength scoring
    const buySignals = signals.filter(s => s.type === 'buy');
    const sellSignals = signals.filter(s => s.type === 'sell');
    
    const buyStrength = buySignals.reduce((sum, s) => sum + s.strength, 0);
    const sellStrength = sellSignals.reduce((sum, s) => sum + s.strength, 0);
    
    score += (buyStrength - sellStrength) * 30;

    return Math.max(-100, Math.min(100, score));
  }

  private calculateConfidence(indicators: TechnicalIndicators, signals: Signal[]): number {
    let confidence = 0.5; // Base confidence

    // Increase confidence if multiple indicators agree
    const agreementCount = signals.length;
    confidence += Math.min(0.3, agreementCount * 0.1);

    // Increase confidence for strong RSI signals
    if (indicators.rsi > 80 || indicators.rsi < 20) {
      confidence += 0.2;
    }

    // Increase confidence for strong MACD signals
    if (Math.abs(indicators.macd.histogram) > Math.abs(indicators.macd.macd) * 0.1) {
      confidence += 0.1;
    }

    return Math.min(1, confidence);
  }

  private generateRecommendations(score: number, indicators: TechnicalIndicators): any[] {
    const recommendations = [];

    if (score > 50) {
      recommendations.push({
        action: 'buy',
        target_price: indicators.sma[20] * 1.05,
        stop_loss: indicators.sma[20] * 0.95,
        time_horizon: 'short',
        reasoning: 'Strong bullish technical signals',
        confidence: 0.8
      });
    } else if (score < -50) {
      recommendations.push({
        action: 'sell',
        target_price: indicators.sma[20] * 0.95,
        stop_loss: indicators.sma[20] * 1.05,
        time_horizon: 'short',
        reasoning: 'Strong bearish technical signals',
        confidence: 0.8
      });
    } else {
      recommendations.push({
        action: 'hold',
        time_horizon: 'short',
        reasoning: 'Mixed technical signals',
        confidence: 0.6
      });
    }

    return recommendations;
  }

  private assessTechnicalRisk(indicators: TechnicalIndicators, data: MarketData[]): any {
    const atr = indicators.atr;
    const currentPrice = data[data.length - 1].close;
    const volatilityRatio = atr / currentPrice;

    let riskLevel: string;
    let riskScore: number;

    if (volatilityRatio < 0.02) {
      riskLevel = 'low';
      riskScore = 30;
    } else if (volatilityRatio < 0.05) {
      riskLevel = 'medium';
      riskScore = 60;
    } else {
      riskLevel = 'high';
      riskScore = 90;
    }

    const factors = [`ATR/Price ratio: ${(volatilityRatio * 100).toFixed(2)}%`];
    const strategies = ['Use appropriate position sizing', 'Set stop losses based on ATR'];

    if (indicators.rsi > 70) {
      factors.push('Overbought conditions');
      strategies.push('Consider taking profits');
    }

    if (indicators.rsi < 30) {
      factors.push('Oversold conditions');
      strategies.push('Consider dollar cost averaging');
    }

    return {
      risk_level: riskLevel,
      risk_score: riskScore,
      factors,
      mitigation_strategies: strategies
    };
  }
}
