module.exports = {

"[project]/node_modules/@rainbow-me/rainbowkit/dist/zetachain-TLDS5IPW.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>zetachain_default)
});
"use client";
// src/components/RainbowKitProvider/chainIcons/zetachain.svg
var zetachain_default = "data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2228%22%20height%3D%2228%22%20fill%3D%22none%22%20viewBox%3D%220%200%20178%20178%22%3E%3Ccircle%20cx%3D%2289%22%20cy%3D%2289%22%20r%3D%2289%22%20fill%3D%22%23005741%22%2F%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M112.109%20108.673v12.02H62.523c.684-7.911%203.236-13.477%2012.064-21.304l37.522-32.01v28.09h13.507V43.79H48.813v25.76H62.32V57.297h40.803L65.784%2089.163l-.089.085c-15.648%2013.854-16.892%2025.036-16.892%2038.211v6.751h76.818v-25.527h-13.507z%22%2F%3E%3C%2Fsvg%3E";
;
}}),

};

//# sourceMappingURL=node_modules_%40rainbow-me_rainbowkit_dist_zetachain-TLDS5IPW_c7c572b6.js.map