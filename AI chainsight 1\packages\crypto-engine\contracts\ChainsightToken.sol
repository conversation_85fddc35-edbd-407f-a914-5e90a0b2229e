// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

/**
 * @title ChainsightToken
 * @dev Advanced ERC20 token with additional features for the Chainsight ecosystem
 */
contract ChainsightToken is ERC20, ERC20Burnable, ERC20Pausable, Ownable, ReentrancyGuard {
    uint256 public constant MAX_SUPPLY = 1_000_000_000 * 10**18; // 1 billion tokens
    uint256 public mintingFee = 0.001 ether; // Fee for minting new tokens
    
    mapping(address => bool) public minters;
    mapping(address => uint256) public lastMintTime;
    
    event MinterAdded(address indexed minter);
    event MinterRemoved(address indexed minter);
    event MintingFeeUpdated(uint256 newFee);
    event TokensMinted(address indexed to, uint256 amount, uint256 fee);

    modifier onlyMinter() {
        require(minters[msg.sender] || msg.sender == owner(), "Not authorized to mint");
        _;
    }

    modifier mintingCooldown() {
        require(
            block.timestamp >= lastMintTime[msg.sender] + 1 hours,
            "Minting cooldown active"
        );
        _;
    }

    constructor(
        string memory name,
        string memory symbol,
        uint256 initialSupply,
        address initialOwner
    ) ERC20(name, symbol) {
        require(initialSupply <= MAX_SUPPLY, "Initial supply exceeds maximum");
        require(initialOwner != address(0), "Invalid owner address");
        
        _transferOwnership(initialOwner);
        _mint(initialOwner, initialSupply);
    }

    /**
     * @dev Mint new tokens with fee payment
     */
    function mint(address to, uint256 amount) 
        external 
        payable 
        onlyMinter 
        mintingCooldown 
        nonReentrant 
        whenNotPaused 
    {
        require(to != address(0), "Cannot mint to zero address");
        require(amount > 0, "Amount must be greater than 0");
        require(totalSupply() + amount <= MAX_SUPPLY, "Would exceed max supply");
        require(msg.value >= mintingFee, "Insufficient minting fee");

        lastMintTime[msg.sender] = block.timestamp;
        _mint(to, amount);

        // Refund excess payment
        if (msg.value > mintingFee) {
            payable(msg.sender).transfer(msg.value - mintingFee);
        }

        emit TokensMinted(to, amount, mintingFee);
    }

    /**
     * @dev Batch mint to multiple addresses
     */
    function batchMint(address[] calldata recipients, uint256[] calldata amounts)
        external
        payable
        onlyMinter
        mintingCooldown
        nonReentrant
        whenNotPaused
    {
        require(recipients.length == amounts.length, "Arrays length mismatch");
        require(recipients.length <= 100, "Too many recipients");
        
        uint256 totalAmount = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            totalAmount += amounts[i];
        }
        
        require(totalSupply() + totalAmount <= MAX_SUPPLY, "Would exceed max supply");
        require(msg.value >= mintingFee * recipients.length, "Insufficient minting fee");

        lastMintTime[msg.sender] = block.timestamp;

        for (uint256 i = 0; i < recipients.length; i++) {
            require(recipients[i] != address(0), "Cannot mint to zero address");
            require(amounts[i] > 0, "Amount must be greater than 0");
            _mint(recipients[i], amounts[i]);
        }

        // Refund excess payment
        uint256 totalFee = mintingFee * recipients.length;
        if (msg.value > totalFee) {
            payable(msg.sender).transfer(msg.value - totalFee);
        }
    }

    /**
     * @dev Add authorized minter
     */
    function addMinter(address minter) external onlyOwner {
        require(minter != address(0), "Invalid minter address");
        require(!minters[minter], "Already a minter");
        
        minters[minter] = true;
        emit MinterAdded(minter);
    }

    /**
     * @dev Remove authorized minter
     */
    function removeMinter(address minter) external onlyOwner {
        require(minters[minter], "Not a minter");
        
        minters[minter] = false;
        emit MinterRemoved(minter);
    }

    /**
     * @dev Update minting fee
     */
    function setMintingFee(uint256 newFee) external onlyOwner {
        require(newFee <= 0.01 ether, "Fee too high");
        mintingFee = newFee;
        emit MintingFeeUpdated(newFee);
    }

    /**
     * @dev Pause token transfers
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause token transfers
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev Withdraw collected fees
     */
    function withdrawFees() external onlyOwner nonReentrant {
        uint256 balance = address(this).balance;
        require(balance > 0, "No fees to withdraw");
        
        payable(owner()).transfer(balance);
    }

    /**
     * @dev Get token information
     */
    function getTokenInfo() external view returns (
        string memory tokenName,
        string memory tokenSymbol,
        uint256 tokenDecimals,
        uint256 tokenTotalSupply,
        uint256 tokenMaxSupply,
        uint256 currentMintingFee,
        bool isPaused
    ) {
        return (
            name(),
            symbol(),
            decimals(),
            totalSupply(),
            MAX_SUPPLY,
            mintingFee,
            paused()
        );
    }

    // Override required by Solidity
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 amount
    ) internal override(ERC20, ERC20Pausable) {
        super._beforeTokenTransfer(from, to, amount);
    }

    // Fallback function to reject direct ETH transfers
    receive() external payable {
        revert("Direct ETH transfers not allowed");
    }
}
