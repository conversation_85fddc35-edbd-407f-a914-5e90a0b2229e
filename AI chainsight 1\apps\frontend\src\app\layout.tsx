import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from './providers'
import { Navigation } from '@/components/Navigation'
import { Toaster } from 'react-hot-toast'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Chainsight by Connectouch',
  description: 'Modular fullstack AI platform for Web3, Finance, and Enterprise',
  keywords: ['AI', 'Web3', 'Blockchain', 'Finance', 'Enterprise', 'Connectouch'],
  authors: [{ name: 'Connectouch' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#d4af37',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} bg-luxury-gradient min-h-screen`}>
        <Providers>
          <div className="flex flex-col min-h-screen">
            <Navigation />
            <main className="flex-1">
              {children}
            </main>
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#1a1a1a',
                  color: '#ffffff',
                  border: '1px solid #d4af37',
                },
                success: {
                  iconTheme: {
                    primary: '#d4af37',
                    secondary: '#1a1a1a',
                  },
                },
                error: {
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#1a1a1a',
                  },
                },
              }}
            />
          </div>
        </Providers>
      </body>
    </html>
  )
}
