from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os
import asyncio
import logging
from datetime import datetime

# Import route modules
from .routers import crypto, ai_chat, finance, face_scanner, legal_hr
from .core.config import settings
from .core.database import engine, Base
from .core.security import get_current_user
from .services.task_orchestrator import TaskOrchestratorService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create database tables
Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(
    title="Chainsight API",
    description="Comprehensive AI-powered platform for Web3, finance, and enterprise applications",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
task_orchestrator = TaskOrchestratorService()

# Pydantic models
class HealthResponse(BaseModel):
    status: str
    timestamp: datetime
    version: str
    services: Dict[str, str]

class TaskRequest(BaseModel):
    task_type: str
    payload: Dict[str, Any]
    priority: Optional[int] = 0

class TaskResponse(BaseModel):
    task_id: str
    status: str
    message: str

# Include routers
app.include_router(crypto.router, prefix="/api/crypto", tags=["crypto"])
app.include_router(ai_chat.router, prefix="/api/chat", tags=["ai-chat"])
app.include_router(finance.router, prefix="/api/finance", tags=["finance"])
app.include_router(face_scanner.router, prefix="/api/face", tags=["face-scanner"])
app.include_router(legal_hr.router, prefix="/api/legal-hr", tags=["legal-hr"])

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logger.info("Starting Chainsight API...")
    
    # Initialize task orchestrator
    await task_orchestrator.initialize()
    
    # Register plugins
    await task_orchestrator.register_default_plugins()
    
    logger.info("Chainsight API started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down Chainsight API...")
    await task_orchestrator.shutdown()
    logger.info("Chainsight API shutdown complete")

@app.get("/", response_model=HealthResponse)
async def root():
    """Root endpoint with API information"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(),
        version="1.0.0",
        services={
            "crypto_engine": "active",
            "ai_chat": "active",
            "finance_analyzer": "active",
            "face_scanner": "active",
            "legal_hr": "active",
            "task_orchestrator": "active"
        }
    )

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        from .core.database import SessionLocal
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        db_status = "healthy"
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        db_status = "unhealthy"
    
    # Check task orchestrator
    orchestrator_stats = await task_orchestrator.get_stats()
    orchestrator_status = "healthy" if orchestrator_stats else "unhealthy"
    
    return HealthResponse(
        status="healthy" if db_status == "healthy" and orchestrator_status == "healthy" else "degraded",
        timestamp=datetime.now(),
        version="1.0.0",
        services={
            "database": db_status,
            "task_orchestrator": orchestrator_status,
            "crypto_engine": "active",
            "ai_services": "active"
        }
    )

@app.post("/api/tasks", response_model=TaskResponse)
async def create_task(request: TaskRequest):
    """Create a new task for processing"""
    try:
        task_id = await task_orchestrator.create_task(
            task_type=request.task_type,
            payload=request.payload,
            priority=request.priority
        )
        
        return TaskResponse(
            task_id=task_id,
            status="created",
            message="Task created successfully"
        )
    except Exception as e:
        logger.error(f"Failed to create task: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/tasks/{task_id}")
async def get_task_status(task_id: str):
    """Get task status and result"""
    try:
        task = await task_orchestrator.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return {
            "task_id": task_id,
            "status": task.get("status"),
            "result": task.get("result"),
            "error": task.get("error"),
            "created_at": task.get("created_at"),
            "updated_at": task.get("updated_at")
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/tasks")
async def list_tasks(status: Optional[str] = None, limit: int = 50):
    """List tasks with optional status filter"""
    try:
        tasks = await task_orchestrator.get_tasks(status=status, limit=limit)
        return {
            "tasks": tasks,
            "count": len(tasks)
        }
    except Exception as e:
        logger.error(f"Failed to list tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/stats")
async def get_system_stats():
    """Get system statistics"""
    try:
        orchestrator_stats = await task_orchestrator.get_stats()
        
        return {
            "timestamp": datetime.now(),
            "task_orchestrator": orchestrator_stats,
            "system": {
                "uptime": "active",
                "memory_usage": "normal",
                "cpu_usage": "normal"
            }
        }
    except Exception as e:
        logger.error(f"Failed to get system stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload file for processing"""
    try:
        # Create uploads directory if it doesn't exist
        upload_dir = "uploads"
        os.makedirs(upload_dir, exist_ok=True)
        
        # Save uploaded file
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        return {
            "filename": file.filename,
            "size": len(content),
            "content_type": file.content_type,
            "file_path": file_path,
            "message": "File uploaded successfully"
        }
    except Exception as e:
        logger.error(f"File upload failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "status_code": 500,
            "timestamp": datetime.now().isoformat()
        }
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
