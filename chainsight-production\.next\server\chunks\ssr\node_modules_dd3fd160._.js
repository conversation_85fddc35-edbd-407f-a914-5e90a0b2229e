module.exports = {

"[project]/node_modules/@lit/reactive-element/node/development/decorators/custom-element.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ /**
 * Class decorator factory that defines the decorated class as a custom element.
 *
 * ```js
 * @customElement('my-element')
 * class MyElement extends LitElement {
 *   render() {
 *     return html``;
 *   }
 * }
 * ```
 * @category Decorator
 * @param tagName The tag name of the custom element to define.
 */ __turbopack_context__.s({
    "customElement": (()=>customElement)
});
const customElement = (tagName)=>(classOrTarget, context)=>{
        if (context !== undefined) {
            context.addInitializer(()=>{
                customElements.define(tagName, classOrTarget);
            });
        } else {
            customElements.define(tagName, classOrTarget);
        }
    };
;
 //# sourceMappingURL=custom-element.js.map
}}),
"[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "property": (()=>property),
    "standardProperty": (()=>standardProperty)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$reactive$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/reactive-element.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$reactive$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/reactive-element.js [app-ssr] (ecmascript) <locals>");
;
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ /*
 * IMPORTANT: For compatibility with tsickle and the Closure JS compiler, all
 * property decorators (but not class decorators) in this file that have
 * an @ExportDecoratedItems annotation must be defined as a regular function,
 * not an arrow function.
 */ let issueWarning;
{
    // Ensure warnings are issued only 1x, even if multiple versions of Lit
    // are loaded.
    globalThis.litIssuedWarnings ??= new Set();
    /**
     * Issue a warning if we haven't already, based either on `code` or `warning`.
     * Warnings are disabled automatically only by `warning`; disabling via `code`
     * can be done by users.
     */ issueWarning = (code, warning)=>{
        warning += ` See https://lit.dev/msg/${code} for more information.`;
        if (!globalThis.litIssuedWarnings.has(warning) && !globalThis.litIssuedWarnings.has(code)) {
            console.warn(warning);
            globalThis.litIssuedWarnings.add(warning);
        }
    };
}const legacyProperty = (options, proto, name)=>{
    const hasOwnProperty = proto.hasOwnProperty(name);
    proto.constructor.createProperty(name, options);
    // For accessors (which have a descriptor on the prototype) we need to
    // return a descriptor, otherwise TypeScript overwrites the descriptor we
    // define in createProperty() with the original descriptor. We don't do this
    // for fields, which don't have a descriptor, because this could overwrite
    // descriptor defined by other decorators.
    return hasOwnProperty ? Object.getOwnPropertyDescriptor(proto, name) : undefined;
};
// This is duplicated from a similar variable in reactive-element.ts, but
// actually makes sense to have this default defined with the decorator, so
// that different decorators could have different defaults.
const defaultPropertyDeclaration = {
    attribute: true,
    type: String,
    converter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$reactive$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["defaultConverter"],
    reflect: false,
    hasChanged: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$reactive$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["notEqual"]
};
/**
 * Wraps a class accessor or setter so that `requestUpdate()` is called with the
 * property name and old value when the accessor is set.
 */ const standardProperty = (options = defaultPropertyDeclaration, target, context)=>{
    const { kind, metadata } = context;
    if (metadata == null) {
        issueWarning('missing-class-metadata', `The class ${target} is missing decorator metadata. This ` + `could mean that you're using a compiler that supports decorators ` + `but doesn't support decorator metadata, such as TypeScript 5.1. ` + `Please update your compiler.`);
    }
    // Store the property options
    let properties = globalThis.litPropertyMetadata.get(metadata);
    if (properties === undefined) {
        globalThis.litPropertyMetadata.set(metadata, properties = new Map());
    }
    if (kind === 'setter') {
        options = Object.create(options);
        options.wrapped = true;
    }
    properties.set(context.name, options);
    if (kind === 'accessor') {
        // Standard decorators cannot dynamically modify the class, so we can't
        // replace a field with accessors. The user must use the new `accessor`
        // keyword instead.
        const { name } = context;
        return {
            set (v) {
                const oldValue = target.get.call(this);
                target.set.call(this, v);
                this.requestUpdate(name, oldValue, options);
            },
            init (v) {
                if (v !== undefined) {
                    this._$changeProperty(name, undefined, options, v);
                }
                return v;
            }
        };
    } else if (kind === 'setter') {
        const { name } = context;
        return function(value) {
            const oldValue = this[name];
            target.call(this, value);
            this.requestUpdate(name, oldValue, options);
        };
    }
    throw new Error(`Unsupported decorator location: ${kind}`);
};
/**
 * A class field or accessor decorator which creates a reactive property that
 * reflects a corresponding attribute value. When a decorated property is set
 * the element will update and render. A {@linkcode PropertyDeclaration} may
 * optionally be supplied to configure property features.
 *
 * This decorator should only be used for public fields. As public fields,
 * properties should be considered as primarily settable by element users,
 * either via attribute or the property itself.
 *
 * Generally, properties that are changed by the element should be private or
 * protected fields and should use the {@linkcode state} decorator.
 *
 * However, sometimes element code does need to set a public property. This
 * should typically only be done in response to user interaction, and an event
 * should be fired informing the user; for example, a checkbox sets its
 * `checked` property when clicked and fires a `changed` event. Mutating public
 * properties should typically not be done for non-primitive (object or array)
 * properties. In other cases when an element needs to manage state, a private
 * property decorated via the {@linkcode state} decorator should be used. When
 * needed, state properties can be initialized via public properties to
 * facilitate complex interactions.
 *
 * ```ts
 * class MyElement {
 *   @property({ type: Boolean })
 *   clicked = false;
 * }
 * ```
 * @category Decorator
 * @ExportDecoratedItems
 */ function property(options) {
    return (protoOrTarget, nameOrContext)=>{
        return typeof nameOrContext === 'object' ? standardProperty(options, protoOrTarget, nameOrContext) : legacyProperty(options, protoOrTarget, nameOrContext);
    };
}
;
 //# sourceMappingURL=property.js.map
}}),
"[project]/node_modules/@lit/reactive-element/node/development/decorators/state.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "state": (()=>state)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
;
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ /*
 * IMPORTANT: For compatibility with tsickle and the Closure JS compiler, all
 * property decorators (but not class decorators) in this file that have
 * an @ExportDecoratedItems annotation must be defined as a regular function,
 * not an arrow function.
 */ /**
 * Declares a private or protected reactive property that still triggers
 * updates to the element when it changes. It does not reflect from the
 * corresponding attribute.
 *
 * Properties declared this way must not be used from HTML or HTML templating
 * systems, they're solely for properties internal to the element. These
 * properties may be renamed by optimization tools like closure compiler.
 * @category Decorator
 */ function state(options) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])({
        ...options,
        // Add both `state` and `attribute` because we found a third party
        // controller that is keying off of PropertyOptions.state to determine
        // whether a field is a private internal property or not.
        state: true,
        attribute: false
    });
}
;
 //# sourceMappingURL=state.js.map
}}),
"[project]/node_modules/@lit/reactive-element/node/development/decorators/event-options.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ /**
 * Adds event listener options to a method used as an event listener in a
 * lit-html template.
 *
 * @param options An object that specifies event listener options as accepted by
 * `EventTarget#addEventListener` and `EventTarget#removeEventListener`.
 *
 * Current browsers support the `capture`, `passive`, and `once` options. See:
 * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Parameters
 *
 * ```ts
 * class MyElement {
 *   clicked = false;
 *
 *   render() {
 *     return html`
 *       <div @click=${this._onClick}>
 *         <button></button>
 *       </div>
 *     `;
 *   }
 *
 *   @eventOptions({capture: true})
 *   _onClick(e) {
 *     this.clicked = true;
 *   }
 * }
 * ```
 * @category Decorator
 */ __turbopack_context__.s({
    "eventOptions": (()=>eventOptions)
});
function eventOptions(options) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (protoOrValue, nameOrContext)=>{
        const method = typeof protoOrValue === 'function' ? protoOrValue : protoOrValue[nameOrContext];
        Object.assign(method, options);
    };
}
;
 //# sourceMappingURL=event-options.js.map
}}),
"[project]/node_modules/@lit/reactive-element/node/development/decorators/base.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ /**
 * Wraps up a few best practices when returning a property descriptor from a
 * decorator.
 *
 * Marks the defined property as configurable, and enumerable, and handles
 * the case where we have a busted Reflect.decorate zombiefill (e.g. in Angular
 * apps).
 *
 * @internal
 */ __turbopack_context__.s({
    "desc": (()=>desc)
});
const desc = (obj, name, descriptor)=>{
    // For backwards compatibility, we keep them configurable and enumerable.
    descriptor.configurable = true;
    descriptor.enumerable = true;
    if (// We check for Reflect.decorate each time, in case the zombiefill
    // is applied via lazy loading some Angular code.
    Reflect.decorate && typeof name !== 'object') {
        // If we're called as a legacy decorator, and Reflect.decorate is present
        // then we have no guarantees that the returned descriptor will be
        // defined on the class, so we must apply it directly ourselves.
        Object.defineProperty(obj, name, descriptor);
    }
    return descriptor;
};
;
 //# sourceMappingURL=base.js.map
}}),
"[project]/node_modules/@lit/reactive-element/node/development/decorators/query.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "query": (()=>query)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/base.js [app-ssr] (ecmascript)");
;
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ let issueWarning;
{
    // Ensure warnings are issued only 1x, even if multiple versions of Lit
    // are loaded.
    globalThis.litIssuedWarnings ??= new Set();
    /**
     * Issue a warning if we haven't already, based either on `code` or `warning`.
     * Warnings are disabled automatically only by `warning`; disabling via `code`
     * can be done by users.
     */ issueWarning = (code, warning)=>{
        warning += code ? ` See https://lit.dev/msg/${code} for more information.` : '';
        if (!globalThis.litIssuedWarnings.has(warning) && !globalThis.litIssuedWarnings.has(code)) {
            console.warn(warning);
            globalThis.litIssuedWarnings.add(warning);
        }
    };
}/**
 * A property decorator that converts a class property into a getter that
 * executes a querySelector on the element's renderRoot.
 *
 * @param selector A DOMString containing one or more selectors to match.
 * @param cache An optional boolean which when true performs the DOM query only
 *     once and caches the result.
 *
 * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector
 *
 * ```ts
 * class MyElement {
 *   @query('#first')
 *   first: HTMLDivElement;
 *
 *   render() {
 *     return html`
 *       <div id="first"></div>
 *       <div id="second"></div>
 *     `;
 *   }
 * }
 * ```
 * @category Decorator
 */ function query(selector, cache) {
    return (protoOrTarget, nameOrContext, descriptor)=>{
        const doQuery = (el)=>{
            const result = el.renderRoot?.querySelector(selector) ?? null;
            if (result === null && cache && !el.hasUpdated) {
                const name = typeof nameOrContext === 'object' ? nameOrContext.name : nameOrContext;
                issueWarning('', `@query'd field ${JSON.stringify(String(name))} with the 'cache' ` + `flag set for selector '${selector}' has been accessed before ` + `the first update and returned null. This is expected if the ` + `renderRoot tree has not been provided beforehand (e.g. via ` + `Declarative Shadow DOM). Therefore the value hasn't been cached.`);
            }
            // TODO: if we want to allow users to assert that the query will never
            // return null, we need a new option and to throw here if the result
            // is null.
            return result;
        };
        if (cache) {
            // Accessors to wrap from either:
            //   1. The decorator target, in the case of standard decorators
            //   2. The property descriptor, in the case of experimental decorators
            //      on auto-accessors.
            //   3. Functions that access our own cache-key property on the instance,
            //      in the case of experimental decorators on fields.
            const { get, set } = typeof nameOrContext === 'object' ? protoOrTarget : descriptor ?? (()=>{
                const key = Symbol(`${String(nameOrContext)} (@query() cache)`);
                return {
                    get () {
                        return this[key];
                    },
                    set (v) {
                        this[key] = v;
                    }
                };
            })();
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["desc"])(protoOrTarget, nameOrContext, {
                get () {
                    let result = get.call(this);
                    if (result === undefined) {
                        result = doQuery(this);
                        if (result !== null || this.hasUpdated) {
                            set.call(this, result);
                        }
                    }
                    return result;
                }
            });
        } else {
            // This object works as the return type for both standard and
            // experimental decorators.
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["desc"])(protoOrTarget, nameOrContext, {
                get () {
                    return doQuery(this);
                }
            });
        }
    };
}
;
 //# sourceMappingURL=query.js.map
}}),
"[project]/node_modules/@lit/reactive-element/node/development/decorators/query-all.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "queryAll": (()=>queryAll)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/base.js [app-ssr] (ecmascript)");
;
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ // Shared fragment used to generate empty NodeLists when a render root is
// undefined
let fragment;
/**
 * A property decorator that converts a class property into a getter
 * that executes a querySelectorAll on the element's renderRoot.
 *
 * @param selector A DOMString containing one or more selectors to match.
 *
 * See:
 * https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelectorAll
 *
 * ```ts
 * class MyElement {
 *   @queryAll('div')
 *   divs: NodeListOf<HTMLDivElement>;
 *
 *   render() {
 *     return html`
 *       <div id="first"></div>
 *       <div id="second"></div>
 *     `;
 *   }
 * }
 * ```
 * @category Decorator
 */ function queryAll(selector) {
    return (obj, name)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["desc"])(obj, name, {
            get () {
                const container = this.renderRoot ?? (fragment ??= document.createDocumentFragment());
                return container.querySelectorAll(selector);
            }
        });
    };
}
;
 //# sourceMappingURL=query-all.js.map
}}),
"[project]/node_modules/@lit/reactive-element/node/development/decorators/query-async.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "queryAsync": (()=>queryAsync)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/base.js [app-ssr] (ecmascript)");
;
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ // Note, in the future, we may extend this decorator to support the use case
// where the queried element may need to do work to become ready to interact
// with (e.g. load some implementation code). If so, we might elect to
// add a second argument defining a function that can be run to make the
// queried element loaded/updated/ready.
/**
 * A property decorator that converts a class property into a getter that
 * returns a promise that resolves to the result of a querySelector on the
 * element's renderRoot done after the element's `updateComplete` promise
 * resolves. When the queried property may change with element state, this
 * decorator can be used instead of requiring users to await the
 * `updateComplete` before accessing the property.
 *
 * @param selector A DOMString containing one or more selectors to match.
 *
 * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector
 *
 * ```ts
 * class MyElement {
 *   @queryAsync('#first')
 *   first: Promise<HTMLDivElement>;
 *
 *   render() {
 *     return html`
 *       <div id="first"></div>
 *       <div id="second"></div>
 *     `;
 *   }
 * }
 *
 * // external usage
 * async doSomethingWithFirst() {
 *  (await aMyElement.first).doSomething();
 * }
 * ```
 * @category Decorator
 */ function queryAsync(selector) {
    return (obj, name)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["desc"])(obj, name, {
            async get () {
                await this.updateComplete;
                return this.renderRoot?.querySelector(selector) ?? null;
            }
        });
    };
}
;
 //# sourceMappingURL=query-async.js.map
}}),
"[project]/node_modules/@lit/reactive-element/node/development/decorators/query-assigned-elements.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "queryAssignedElements": (()=>queryAssignedElements)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/base.js [app-ssr] (ecmascript)");
;
/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ /**
 * A property decorator that converts a class property into a getter that
 * returns the `assignedElements` of the given `slot`. Provides a declarative
 * way to use
 * [`HTMLSlotElement.assignedElements`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).
 *
 * Can be passed an optional {@linkcode QueryAssignedElementsOptions} object.
 *
 * Example usage:
 * ```ts
 * class MyElement {
 *   @queryAssignedElements({ slot: 'list' })
 *   listItems!: Array<HTMLElement>;
 *   @queryAssignedElements()
 *   unnamedSlotEls!: Array<HTMLElement>;
 *
 *   render() {
 *     return html`
 *       <slot name="list"></slot>
 *       <slot></slot>
 *     `;
 *   }
 * }
 * ```
 *
 * Note, the type of this property should be annotated as `Array<HTMLElement>`.
 *
 * @category Decorator
 */ function queryAssignedElements(options) {
    return (obj, name)=>{
        const { slot, selector } = options ?? {};
        const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["desc"])(obj, name, {
            get () {
                const slotEl = this.renderRoot?.querySelector(slotSelector);
                const elements = slotEl?.assignedElements(options) ?? [];
                return selector === undefined ? elements : elements.filter((node)=>node.matches(selector));
            }
        });
    };
}
;
 //# sourceMappingURL=query-assigned-elements.js.map
}}),
"[project]/node_modules/@lit/reactive-element/node/development/decorators/query-assigned-nodes.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "queryAssignedNodes": (()=>queryAssignedNodes)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/base.js [app-ssr] (ecmascript)");
;
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ /**
 * A property decorator that converts a class property into a getter that
 * returns the `assignedNodes` of the given `slot`.
 *
 * Can be passed an optional {@linkcode QueryAssignedNodesOptions} object.
 *
 * Example usage:
 * ```ts
 * class MyElement {
 *   @queryAssignedNodes({slot: 'list', flatten: true})
 *   listItems!: Array<Node>;
 *
 *   render() {
 *     return html`
 *       <slot name="list"></slot>
 *     `;
 *   }
 * }
 * ```
 *
 * Note the type of this property should be annotated as `Array<Node>`. Use the
 * queryAssignedElements decorator to list only elements, and optionally filter
 * the element list using a CSS selector.
 *
 * @category Decorator
 */ function queryAssignedNodes(options) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (obj, name)=>{
        const { slot } = options ?? {};
        const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["desc"])(obj, name, {
            get () {
                const slotEl = this.renderRoot?.querySelector(slotSelector);
                return slotEl?.assignedNodes(options) ?? [];
            }
        });
    };
}
;
 //# sourceMappingURL=query-assigned-nodes.js.map
}}),
"[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$custom$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/custom-element.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/state.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$event$2d$options$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/event-options.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$query$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/query.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$query$2d$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/query-all.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$query$2d$async$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/query-async.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$query$2d$assigned$2d$elements$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/query-assigned-elements.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$query$2d$assigned$2d$nodes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/query-assigned-nodes.js [app-ssr] (ecmascript)"); //# sourceMappingURL=decorators.js.map
;
;
;
;
;
;
;
;
;
}}),
"[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$custom$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/custom-element.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/state.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$event$2d$options$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/event-options.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$query$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/query.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$query$2d$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/query-all.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$query$2d$async$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/query-async.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$query$2d$assigned$2d$elements$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/query-assigned-elements.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$query$2d$assigned$2d$nodes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/query-assigned-nodes.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/lit-html/node/development/directives/if-defined.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ifDefined": (()=>ifDefined)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
;
/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ /**
 * For AttributeParts, sets the attribute if the value is defined and removes
 * the attribute if the value is undefined.
 *
 * For other part types, this directive is a no-op.
 */ const ifDefined = (value)=>value ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["nothing"];
;
 //# sourceMappingURL=if-defined.js.map
}}),
"[project]/node_modules/lit/directives/if-defined.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$if$2d$defined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directives/if-defined.js [app-ssr] (ecmascript)"); //# sourceMappingURL=if-defined.js.map
;
}}),
"[project]/node_modules/lit/directives/if-defined.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$if$2d$defined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directives/if-defined.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$directives$2f$if$2d$defined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/directives/if-defined.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-controllers/dist/esm/src/utils/ModalUtil.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ModalUtil": (()=>ModalUtil)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ModalController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/RouterController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$SIWXUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/utils/SIWXUtil.js [app-ssr] (ecmascript)");
;
;
;
const ModalUtil = {
    isUnsupportedChainView () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.view === 'UnsupportedChain' || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.view === 'SwitchNetwork' && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.history.includes('UnsupportedChain');
    },
    async safeClose () {
        if (this.isUnsupportedChainView()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalController"].shake();
            return;
        }
        const isSIWXCloseDisabled = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$SIWXUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SIWXUtil"].isSIWXCloseDisabled();
        if (isSIWXCloseDisabled) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalController"].shake();
            return;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalController"].close();
    }
}; //# sourceMappingURL=ModalUtil.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-card/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: block;
    border-radius: clamp(0px, var(--wui-border-radius-l), 44px);
    box-shadow: 0 0 0 1px var(--wui-color-gray-glass-005);
    background-color: var(--wui-color-modal-bg);
    overflow: hidden;
  }

  :host([data-embedded='true']) {
    box-shadow:
      0 0 0 1px var(--wui-color-gray-glass-005),
      0px 4px 12px 4px var(--w3m-card-embedded-shadow-color);
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-card/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiCard": (()=>WuiCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$card$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-card/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
let WuiCard = class WuiCard extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    render() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<slot></slot>`;
    }
};
WuiCard.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$card$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
WuiCard = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-card')
], WuiCard);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-card.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$card$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-card/index.js [app-ssr] (ecmascript)"); //# sourceMappingURL=wui-card.js.map
;
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-card.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$card$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-card/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-card.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/layout/wui-flex/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: flex;
    width: inherit;
    height: inherit;
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/layout/wui-flex/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiFlex": (()=>WuiFlex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$UiHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/UiHelperUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$layout$2f$wui$2d$flex$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/layout/wui-flex/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
let WuiFlex = class WuiFlex extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    render() {
        this.style.cssText = `
      flex-direction: ${this.flexDirection};
      flex-wrap: ${this.flexWrap};
      flex-basis: ${this.flexBasis};
      flex-grow: ${this.flexGrow};
      flex-shrink: ${this.flexShrink};
      align-items: ${this.alignItems};
      justify-content: ${this.justifyContent};
      column-gap: ${this.columnGap && `var(--wui-spacing-${this.columnGap})`};
      row-gap: ${this.rowGap && `var(--wui-spacing-${this.rowGap})`};
      gap: ${this.gap && `var(--wui-spacing-${this.gap})`};
      padding-top: ${this.padding && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$UiHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UiHelperUtil"].getSpacingStyles(this.padding, 0)};
      padding-right: ${this.padding && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$UiHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UiHelperUtil"].getSpacingStyles(this.padding, 1)};
      padding-bottom: ${this.padding && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$UiHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UiHelperUtil"].getSpacingStyles(this.padding, 2)};
      padding-left: ${this.padding && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$UiHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UiHelperUtil"].getSpacingStyles(this.padding, 3)};
      margin-top: ${this.margin && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$UiHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UiHelperUtil"].getSpacingStyles(this.margin, 0)};
      margin-right: ${this.margin && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$UiHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UiHelperUtil"].getSpacingStyles(this.margin, 1)};
      margin-bottom: ${this.margin && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$UiHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UiHelperUtil"].getSpacingStyles(this.margin, 2)};
      margin-left: ${this.margin && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$UiHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UiHelperUtil"].getSpacingStyles(this.margin, 3)};
    `;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<slot></slot>`;
    }
};
WuiFlex.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$layout$2f$wui$2d$flex$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "flexDirection", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "flexWrap", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "flexBasis", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "flexGrow", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "flexShrink", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "alignItems", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "justifyContent", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "columnGap", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "rowGap", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "gap", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "padding", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiFlex.prototype, "margin", void 0);
WuiFlex = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-flex')
], WuiFlex);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-flex.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$layout$2f$wui$2d$flex$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/layout/wui-flex/index.js [app-ssr] (ecmascript)"); //# sourceMappingURL=wui-flex.js.map
;
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-flex.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$layout$2f$wui$2d$flex$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/layout/wui-flex/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$flex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-flex.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/lit-html/node/development/directive-helpers.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TemplateResultType": (()=>TemplateResultType),
    "clearPart": (()=>clearPart),
    "getCommittedValue": (()=>getCommittedValue),
    "getDirectiveClass": (()=>getDirectiveClass),
    "insertPart": (()=>insertPart),
    "isCompiledTemplateResult": (()=>isCompiledTemplateResult),
    "isDirectiveResult": (()=>isDirectiveResult),
    "isPrimitive": (()=>isPrimitive),
    "isSingleExpression": (()=>isSingleExpression),
    "isTemplateResult": (()=>isTemplateResult),
    "removePart": (()=>removePart),
    "setChildPartValue": (()=>setChildPartValue),
    "setCommittedValue": (()=>setCommittedValue)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
;
/**
 * @license
 * Copyright 2020 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ const { _ChildPart: ChildPart } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["_$LH"];
const wrap = (node)=>node;
/**
 * Tests if a value is a primitive value.
 *
 * See https://tc39.github.io/ecma262/#sec-typeof-operator
 */ const isPrimitive = (value)=>value === null || typeof value != 'object' && typeof value != 'function';
const TemplateResultType = {
    HTML: 1,
    SVG: 2,
    MATHML: 3
};
/**
 * Tests if a value is a TemplateResult or a CompiledTemplateResult.
 */ const isTemplateResult = (value, type)=>type === undefined ? value?.['_$litType$'] !== undefined : value?.['_$litType$'] === type;
/**
 * Tests if a value is a CompiledTemplateResult.
 */ const isCompiledTemplateResult = (value)=>{
    return value?.['_$litType$']?.h != null;
};
/**
 * Tests if a value is a DirectiveResult.
 */ const isDirectiveResult = (value)=>// This property needs to remain unminified.
    value?.['_$litDirective$'] !== undefined;
/**
 * Retrieves the Directive class for a DirectiveResult
 */ const getDirectiveClass = (value)=>// This property needs to remain unminified.
    value?.['_$litDirective$'];
/**
 * Tests whether a part has only a single-expression with no strings to
 * interpolate between.
 *
 * Only AttributePart and PropertyPart can have multiple expressions.
 * Multi-expression parts have a `strings` property and single-expression
 * parts do not.
 */ const isSingleExpression = (part)=>part.strings === undefined;
const createMarker = ()=>document.createComment('');
/**
 * Inserts a ChildPart into the given container ChildPart's DOM, either at the
 * end of the container ChildPart, or before the optional `refPart`.
 *
 * This does not add the part to the containerPart's committed value. That must
 * be done by callers.
 *
 * @param containerPart Part within which to add the new ChildPart
 * @param refPart Part before which to add the new ChildPart; when omitted the
 *     part added to the end of the `containerPart`
 * @param part Part to insert, or undefined to create a new part
 */ const insertPart = (containerPart, refPart, part)=>{
    const container = wrap(containerPart._$startNode).parentNode;
    const refNode = refPart === undefined ? containerPart._$endNode : refPart._$startNode;
    if (part === undefined) {
        const startNode = wrap(container).insertBefore(createMarker(), refNode);
        const endNode = wrap(container).insertBefore(createMarker(), refNode);
        part = new ChildPart(startNode, endNode, containerPart, containerPart.options);
    } else {
        const endNode = wrap(part._$endNode).nextSibling;
        const oldParent = part._$parent;
        const parentChanged = oldParent !== containerPart;
        if (parentChanged) {
            part._$reparentDisconnectables?.(containerPart);
            // Note that although `_$reparentDisconnectables` updates the part's
            // `_$parent` reference after unlinking from its current parent, that
            // method only exists if Disconnectables are present, so we need to
            // unconditionally set it here
            part._$parent = containerPart;
            // Since the _$isConnected getter is somewhat costly, only
            // read it once we know the subtree has directives that need
            // to be notified
            let newConnectionState;
            if (part._$notifyConnectionChanged !== undefined && (newConnectionState = containerPart._$isConnected) !== oldParent._$isConnected) {
                part._$notifyConnectionChanged(newConnectionState);
            }
        }
        if (endNode !== refNode || parentChanged) {
            let start = part._$startNode;
            while(start !== endNode){
                const n = wrap(start).nextSibling;
                wrap(container).insertBefore(start, refNode);
                start = n;
            }
        }
    }
    return part;
};
/**
 * Sets the value of a Part.
 *
 * Note that this should only be used to set/update the value of user-created
 * parts (i.e. those created using `insertPart`); it should not be used
 * by directives to set the value of the directive's container part. Directives
 * should return a value from `update`/`render` to update their part state.
 *
 * For directives that require setting their part value asynchronously, they
 * should extend `AsyncDirective` and call `this.setValue()`.
 *
 * @param part Part to set
 * @param value Value to set
 * @param index For `AttributePart`s, the index to set
 * @param directiveParent Used internally; should not be set by user
 */ const setChildPartValue = (part, value, directiveParent = part)=>{
    part._$setValue(value, directiveParent);
    return part;
};
// A sentinel value that can never appear as a part value except when set by
// live(). Used to force a dirty-check to fail and cause a re-render.
const RESET_VALUE = {};
/**
 * Sets the committed value of a ChildPart directly without triggering the
 * commit stage of the part.
 *
 * This is useful in cases where a directive needs to update the part such
 * that the next update detects a value change or not. When value is omitted,
 * the next update will be guaranteed to be detected as a change.
 *
 * @param part
 * @param value
 */ const setCommittedValue = (part, value = RESET_VALUE)=>part._$committedValue = value;
/**
 * Returns the committed value of a ChildPart.
 *
 * The committed value is used for change detection and efficient updates of
 * the part. It can differ from the value set by the template or directive in
 * cases where the template value is transformed before being committed.
 *
 * - `TemplateResult`s are committed as a `TemplateInstance`
 * - Iterables are committed as `Array<ChildPart>`
 * - All other types are committed as the template value or value returned or
 *   set by a directive.
 *
 * @param part
 */ const getCommittedValue = (part)=>part._$committedValue;
/**
 * Removes a ChildPart from the DOM, including any of its content.
 *
 * @param part The Part to remove
 */ const removePart = (part)=>{
    part._$notifyConnectionChanged?.(false, true);
    let start = part._$startNode;
    const end = wrap(part._$endNode).nextSibling;
    while(start !== end){
        const n = wrap(start).nextSibling;
        wrap(start).remove();
        start = n;
    }
};
const clearPart = (part)=>{
    part._$clear();
};
;
 //# sourceMappingURL=directive-helpers.js.map
}}),
"[project]/node_modules/lit-html/node/development/directive.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ __turbopack_context__.s({
    "Directive": (()=>Directive),
    "PartType": (()=>PartType),
    "directive": (()=>directive)
});
const PartType = {
    ATTRIBUTE: 1,
    CHILD: 2,
    PROPERTY: 3,
    BOOLEAN_ATTRIBUTE: 4,
    EVENT: 5,
    ELEMENT: 6
};
/**
 * Creates a user-facing directive function from a Directive class. This
 * function has the same parameters as the directive's render() method.
 */ const directive = (c)=>(...values)=>({
            // This property needs to remain unminified.
            ['_$litDirective$']: c,
            values
        });
/**
 * Base class for creating custom directives. Users should extend this class,
 * implement `render` and/or `update`, and then pass their subclass to
 * `directive`.
 */ class Directive {
    constructor(_partInfo){}
    // See comment in Disconnectable interface for why this is a getter
    get _$isConnected() {
        return this._$parent._$isConnected;
    }
    /** @internal */ _$initialize(part, parent, attributeIndex) {
        this.__part = part;
        this._$parent = parent;
        this.__attributeIndex = attributeIndex;
    }
    /** @internal */ _$resolve(part, props) {
        return this.update(part, props);
    }
    update(_part, props) {
        return this.render(...props);
    }
}
;
 //# sourceMappingURL=directive.js.map
}}),
"[project]/node_modules/lit-html/node/development/async-directive.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AsyncDirective": (()=>AsyncDirective)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2d$helpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directive-helpers.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directive.js [app-ssr] (ecmascript)");
;
;
;
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ /**
 * Recursively walks down the tree of Parts/TemplateInstances/Directives to set
 * the connected state of directives and run `disconnected`/ `reconnected`
 * callbacks.
 *
 * @return True if there were children to disconnect; false otherwise
 */ const notifyChildrenConnectedChanged = (parent, isConnected)=>{
    const children = parent._$disconnectableChildren;
    if (children === undefined) {
        return false;
    }
    for (const obj of children){
        // The existence of `_$notifyDirectiveConnectionChanged` is used as a "brand" to
        // disambiguate AsyncDirectives from other DisconnectableChildren
        // (as opposed to using an instanceof check to know when to call it); the
        // redundancy of "Directive" in the API name is to avoid conflicting with
        // `_$notifyConnectionChanged`, which exists `ChildParts` which are also in
        // this list
        // Disconnect Directive (and any nested directives contained within)
        // This property needs to remain unminified.
        obj['_$notifyDirectiveConnectionChanged']?.(isConnected, false);
        // Disconnect Part/TemplateInstance
        notifyChildrenConnectedChanged(obj, isConnected);
    }
    return true;
};
/**
 * Removes the given child from its parent list of disconnectable children, and
 * if the parent list becomes empty as a result, removes the parent from its
 * parent, and so forth up the tree when that causes subsequent parent lists to
 * become empty.
 */ const removeDisconnectableFromParent = (obj)=>{
    let parent, children;
    do {
        if ((parent = obj._$parent) === undefined) {
            break;
        }
        children = parent._$disconnectableChildren;
        children.delete(obj);
        obj = parent;
    }while (children?.size === 0)
};
const addDisconnectableToParent = (obj)=>{
    // Climb the parent tree, creating a sparse tree of children needing
    // disconnection
    for(let parent; parent = obj._$parent; obj = parent){
        let children = parent._$disconnectableChildren;
        if (children === undefined) {
            parent._$disconnectableChildren = children = new Set();
        } else if (children.has(obj)) {
            break;
        }
        children.add(obj);
        installDisconnectAPI(parent);
    }
};
/**
 * Changes the parent reference of the ChildPart, and updates the sparse tree of
 * Disconnectable children accordingly.
 *
 * Note, this method will be patched onto ChildPart instances and called from
 * the core code when parts are moved between different parents.
 */ function reparentDisconnectables(newParent) {
    if (this._$disconnectableChildren !== undefined) {
        removeDisconnectableFromParent(this);
        this._$parent = newParent;
        addDisconnectableToParent(this);
    } else {
        this._$parent = newParent;
    }
}
/**
 * Sets the connected state on any directives contained within the committed
 * value of this part (i.e. within a TemplateInstance or iterable of
 * ChildParts) and runs their `disconnected`/`reconnected`s, as well as within
 * any directives stored on the ChildPart (when `valueOnly` is false).
 *
 * `isClearingValue` should be passed as `true` on a top-level part that is
 * clearing itself, and not as a result of recursively disconnecting directives
 * as part of a `clear` operation higher up the tree. This both ensures that any
 * directive on this ChildPart that produced a value that caused the clear
 * operation is not disconnected, and also serves as a performance optimization
 * to avoid needless bookkeeping when a subtree is going away; when clearing a
 * subtree, only the top-most part need to remove itself from the parent.
 *
 * `fromPartIndex` is passed only in the case of a partial `_clear` running as a
 * result of truncating an iterable.
 *
 * Note, this method will be patched onto ChildPart instances and called from the
 * core code when parts are cleared or the connection state is changed by the
 * user.
 */ function notifyChildPartConnectedChanged(isConnected, isClearingValue = false, fromPartIndex = 0) {
    const value = this._$committedValue;
    const children = this._$disconnectableChildren;
    if (children === undefined || children.size === 0) {
        return;
    }
    if (isClearingValue) {
        if (Array.isArray(value)) {
            // Iterable case: Any ChildParts created by the iterable should be
            // disconnected and removed from this ChildPart's disconnectable
            // children (starting at `fromPartIndex` in the case of truncation)
            for(let i = fromPartIndex; i < value.length; i++){
                notifyChildrenConnectedChanged(value[i], false);
                removeDisconnectableFromParent(value[i]);
            }
        } else if (value != null) {
            // TemplateInstance case: If the value has disconnectable children (will
            // only be in the case that it is a TemplateInstance), we disconnect it
            // and remove it from this ChildPart's disconnectable children
            notifyChildrenConnectedChanged(value, false);
            removeDisconnectableFromParent(value);
        }
    } else {
        notifyChildrenConnectedChanged(this, isConnected);
    }
}
/**
 * Patches disconnection API onto ChildParts.
 */ const installDisconnectAPI = (obj)=>{
    if (obj.type == __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PartType"].CHILD) {
        obj._$notifyConnectionChanged ??= notifyChildPartConnectedChanged;
        obj._$reparentDisconnectables ??= reparentDisconnectables;
    }
};
/**
 * An abstract `Directive` base class whose `disconnected` method will be
 * called when the part containing the directive is cleared as a result of
 * re-rendering, or when the user calls `part.setConnected(false)` on
 * a part that was previously rendered containing the directive (as happens
 * when e.g. a LitElement disconnects from the DOM).
 *
 * If `part.setConnected(true)` is subsequently called on a
 * containing part, the directive's `reconnected` method will be called prior
 * to its next `update`/`render` callbacks. When implementing `disconnected`,
 * `reconnected` should also be implemented to be compatible with reconnection.
 *
 * Note that updates may occur while the directive is disconnected. As such,
 * directives should generally check the `this.isConnected` flag during
 * render/update to determine whether it is safe to subscribe to resources
 * that may prevent garbage collection.
 */ class AsyncDirective extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Directive"] {
    constructor(){
        super(...arguments);
        // @internal
        this._$disconnectableChildren = undefined;
    }
    /**
     * Initialize the part with internal fields
     * @param part
     * @param parent
     * @param attributeIndex
     */ _$initialize(part, parent, attributeIndex) {
        super._$initialize(part, parent, attributeIndex);
        addDisconnectableToParent(this);
        this.isConnected = part._$isConnected;
    }
    // This property needs to remain unminified.
    /**
     * Called from the core code when a directive is going away from a part (in
     * which case `shouldRemoveFromParent` should be true), and from the
     * `setChildrenConnected` helper function when recursively changing the
     * connection state of a tree (in which case `shouldRemoveFromParent` should
     * be false).
     *
     * @param isConnected
     * @param isClearingDirective - True when the directive itself is being
     *     removed; false when the tree is being disconnected
     * @internal
     */ ['_$notifyDirectiveConnectionChanged'](isConnected, isClearingDirective = true) {
        if (isConnected !== this.isConnected) {
            this.isConnected = isConnected;
            if (isConnected) {
                this.reconnected?.();
            } else {
                this.disconnected?.();
            }
        }
        if (isClearingDirective) {
            notifyChildrenConnectedChanged(this, isConnected);
            removeDisconnectableFromParent(this);
        }
    }
    /**
     * Sets the value of the directive's Part outside the normal `update`/`render`
     * lifecycle of a directive.
     *
     * This method should not be called synchronously from a directive's `update`
     * or `render`.
     *
     * @param directive The directive to update
     * @param value The value to set
     */ setValue(value) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2d$helpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isSingleExpression"])(this.__part)) {
            this.__part._$setValue(value, this);
        } else {
            // this.__attributeIndex will be defined in this case, but
            // assert it in dev mode
            if (this.__attributeIndex === undefined) {
                throw new Error(`Expected this.__attributeIndex to be a number`);
            }
            const newValues = [
                ...this.__part._$committedValue
            ];
            newValues[this.__attributeIndex] = value;
            this.__part._$setValue(newValues, this, 0);
        }
    }
    /**
     * User callbacks for implementing logic to release any resources/subscriptions
     * that may have been retained by this directive. Since directives may also be
     * re-connected, `reconnected` should also be implemented to restore the
     * working state of the directive prior to the next render.
     */ disconnected() {}
    reconnected() {}
}
;
 //# sourceMappingURL=async-directive.js.map
}}),
"[project]/node_modules/lit-html/node/development/async-directive.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2d$helpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directive-helpers.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directive.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$async$2d$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/async-directive.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/lit-html/node/development/directives/private-async-helpers.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ // Note, this module is not included in package exports so that it's private to
// our first-party directives. If it ends up being useful, we can open it up and
// export it.
/**
 * Helper to iterate an AsyncIterable in its own closure.
 * @param iterable The iterable to iterate
 * @param callback The callback to call for each value. If the callback returns
 * `false`, the loop will be broken.
 */ __turbopack_context__.s({
    "Pauser": (()=>Pauser),
    "PseudoWeakRef": (()=>PseudoWeakRef),
    "forAwaitOf": (()=>forAwaitOf)
});
const forAwaitOf = async (iterable, callback)=>{
    for await (const v of iterable){
        if (await callback(v) === false) {
            return;
        }
    }
};
/**
 * Holds a reference to an instance that can be disconnected and reconnected,
 * so that a closure over the ref (e.g. in a then function to a promise) does
 * not strongly hold a ref to the instance. Approximates a WeakRef but must
 * be manually connected & disconnected to the backing instance.
 */ class PseudoWeakRef {
    constructor(ref){
        this._ref = ref;
    }
    /**
     * Disassociates the ref with the backing instance.
     */ disconnect() {
        this._ref = undefined;
    }
    /**
     * Reassociates the ref with the backing instance.
     */ reconnect(ref) {
        this._ref = ref;
    }
    /**
     * Retrieves the backing instance (will be undefined when disconnected)
     */ deref() {
        return this._ref;
    }
}
/**
 * A helper to pause and resume waiting on a condition in an async function
 */ class Pauser {
    constructor(){
        this._promise = undefined;
        this._resolve = undefined;
    }
    /**
     * When paused, returns a promise to be awaited; when unpaused, returns
     * undefined. Note that in the microtask between the pauser being resumed
     * an await of this promise resolving, the pauser could be paused again,
     * hence callers should check the promise in a loop when awaiting.
     * @returns A promise to be awaited when paused or undefined
     */ get() {
        return this._promise;
    }
    /**
     * Creates a promise to be awaited
     */ pause() {
        this._promise ??= new Promise((resolve)=>this._resolve = resolve);
    }
    /**
     * Resolves the promise which may be awaited
     */ resume() {
        this._resolve?.();
        this._promise = this._resolve = undefined;
    }
}
;
 //# sourceMappingURL=private-async-helpers.js.map
}}),
"[project]/node_modules/lit-html/node/development/directives/until.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "UntilDirective": (()=>UntilDirective),
    "until": (()=>until)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2d$helpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directive-helpers.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$async$2d$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/async-directive.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$async$2d$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/async-directive.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$private$2d$async$2d$helpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directives/private-async-helpers.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directive.js [app-ssr] (ecmascript)");
;
;
;
;
;
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ const isPromise = (x)=>{
    return !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2d$helpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isPrimitive"])(x) && typeof x.then === 'function';
};
// Effectively infinity, but a SMI.
const _infinity = 0x3fffffff;
class UntilDirective extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$async$2d$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["AsyncDirective"] {
    constructor(){
        super(...arguments);
        this.__lastRenderedIndex = _infinity;
        this.__values = [];
        this.__weakThis = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$private$2d$async$2d$helpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PseudoWeakRef"](this);
        this.__pauser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$private$2d$async$2d$helpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pauser"]();
    }
    render(...args) {
        return args.find((x)=>!isPromise(x)) ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noChange"];
    }
    update(_part, args) {
        const previousValues = this.__values;
        let previousLength = previousValues.length;
        this.__values = args;
        const weakThis = this.__weakThis;
        const pauser = this.__pauser;
        // If our initial render occurs while disconnected, ensure that the pauser
        // and weakThis are in the disconnected state
        if (!this.isConnected) {
            this.disconnected();
        }
        for(let i = 0; i < args.length; i++){
            // If we've rendered a higher-priority value already, stop.
            if (i > this.__lastRenderedIndex) {
                break;
            }
            const value = args[i];
            // Render non-Promise values immediately
            if (!isPromise(value)) {
                this.__lastRenderedIndex = i;
                // Since a lower-priority value will never overwrite a higher-priority
                // synchronous value, we can stop processing now.
                return value;
            }
            // If this is a Promise we've already handled, skip it.
            if (i < previousLength && value === previousValues[i]) {
                continue;
            }
            // We have a Promise that we haven't seen before, so priorities may have
            // changed. Forget what we rendered before.
            this.__lastRenderedIndex = _infinity;
            previousLength = 0;
            // Note, the callback avoids closing over `this` so that the directive
            // can be gc'ed before the promise resolves; instead `this` is retrieved
            // from `weakThis`, which can break the hard reference in the closure when
            // the directive disconnects
            Promise.resolve(value).then(async (result)=>{
                // If we're disconnected, wait until we're (maybe) reconnected
                // The while loop here handles the case that the connection state
                // thrashes, causing the pauser to resume and then get re-paused
                while(pauser.get()){
                    await pauser.get();
                }
                // If the callback gets here and there is no `this`, it means that the
                // directive has been disconnected and garbage collected and we don't
                // need to do anything else
                const _this = weakThis.deref();
                if (_this !== undefined) {
                    const index = _this.__values.indexOf(value);
                    // If state.values doesn't contain the value, we've re-rendered without
                    // the value, so don't render it. Then, only render if the value is
                    // higher-priority than what's already been rendered.
                    if (index > -1 && index < _this.__lastRenderedIndex) {
                        _this.__lastRenderedIndex = index;
                        _this.setValue(result);
                    }
                }
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noChange"];
    }
    disconnected() {
        this.__weakThis.disconnect();
        this.__pauser.pause();
    }
    reconnected() {
        this.__weakThis.reconnect(this);
        this.__pauser.resume();
    }
}
/**
 * Renders one of a series of values, including Promises, to a Part.
 *
 * Values are rendered in priority order, with the first argument having the
 * highest priority and the last argument having the lowest priority. If a
 * value is a Promise, low-priority values will be rendered until it resolves.
 *
 * The priority of values can be used to create placeholder content for async
 * data. For example, a Promise with pending content can be the first,
 * highest-priority, argument, and a non_promise loading indicator template can
 * be used as the second, lower-priority, argument. The loading indicator will
 * render immediately, and the primary content will render when the Promise
 * resolves.
 *
 * Example:
 *
 * ```js
 * const content = fetch('./content.txt').then(r => r.text());
 * html`${until(content, html`<span>Loading...</span>`)}`
 * ```
 */ const until = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["directive"])(UntilDirective);
;
 //# sourceMappingURL=until.js.map
}}),
"[project]/node_modules/lit/directives/until.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$until$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directives/until.js [app-ssr] (ecmascript)"); //# sourceMappingURL=until.js.map
;
}}),
"[project]/node_modules/lit/directives/until.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$until$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directives/until.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$directives$2f$until$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/directives/until.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/CacheUtil.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CacheUtil": (()=>CacheUtil),
    "globalSvgCache": (()=>globalSvgCache)
});
class CacheUtil {
    constructor(){
        this.cache = new Map();
    }
    set(key, value) {
        this.cache.set(key, value);
    }
    get(key) {
        return this.cache.get(key);
    }
    has(key) {
        return this.cache.has(key);
    }
    delete(key) {
        this.cache.delete(key);
    }
    clear() {
        this.cache.clear();
    }
}
const globalSvgCache = new CacheUtil(); //# sourceMappingURL=CacheUtil.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-icon/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: flex;
    aspect-ratio: var(--local-aspect-ratio);
    color: var(--local-color);
    width: var(--local-width);
  }

  svg {
    width: inherit;
    height: inherit;
    object-fit: contain;
    object-position: center;
  }

  .fallback {
    width: var(--local-width);
    height: var(--local-height);
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-icon/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiIcon": (()=>WuiIcon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$directives$2f$until$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/directives/until.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$until$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directives/until.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$CacheUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/CacheUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$icon$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-icon/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
;
const ICONS = {
    add: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).addSvg,
    allWallets: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).allWalletsSvg,
    arrowBottomCircle: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom-circle.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).arrowBottomCircleSvg,
    appStore: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/app-store.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).appStoreSvg,
    apple: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).appleSvg,
    arrowBottom: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).arrowBottomSvg,
    arrowLeft: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-left.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).arrowLeftSvg,
    arrowRight: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-right.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).arrowRightSvg,
    arrowTop: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-top.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).arrowTopSvg,
    bank: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/bank.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).bankSvg,
    browser: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/browser.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).browserSvg,
    card: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/card.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).cardSvg,
    checkmark: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).checkmarkSvg,
    checkmarkBold: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark-bold.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).checkmarkBoldSvg,
    chevronBottom: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-bottom.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).chevronBottomSvg,
    chevronLeft: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-left.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).chevronLeftSvg,
    chevronRight: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-right.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).chevronRightSvg,
    chevronTop: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-top.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).chevronTopSvg,
    chromeStore: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chrome-store.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).chromeStoreSvg,
    clock: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/clock.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).clockSvg,
    close: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/close.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).closeSvg,
    compass: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/compass.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).compassSvg,
    coinPlaceholder: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/coinPlaceholder.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).coinPlaceholderSvg,
    copy: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/copy.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).copySvg,
    cursor: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).cursorSvg,
    cursorTransparent: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).cursorTransparentSvg,
    desktop: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/desktop.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).desktopSvg,
    disconnect: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/disconnect.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).disconnectSvg,
    discord: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/discord.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).discordSvg,
    etherscan: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).etherscanSvg,
    extension: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/extension.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).extensionSvg,
    externalLink: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/external-link.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).externalLinkSvg,
    facebook: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/facebook.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).facebookSvg,
    farcaster: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).farcasterSvg,
    filters: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/filters.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).filtersSvg,
    github: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).githubSvg,
    google: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/google.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).googleSvg,
    helpCircle: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/help-circle.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).helpCircleSvg,
    image: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/image.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).imageSvg,
    id: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/id.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).idSvg,
    infoCircle: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info-circle.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).infoCircleSvg,
    lightbulb: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/lightbulb.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).lightbulbSvg,
    mail: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mail.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).mailSvg,
    mobile: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mobile.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).mobileSvg,
    more: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/more.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).moreSvg,
    networkPlaceholder: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/network-placeholder.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).networkPlaceholderSvg,
    nftPlaceholder: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/nftPlaceholder.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).nftPlaceholderSvg,
    off: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/off.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).offSvg,
    playStore: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).playStoreSvg,
    plus: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).plusSvg,
    qrCode: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).qrCodeIcon,
    recycleHorizontal: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/recycle-horizontal.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).recycleHorizontalSvg,
    refresh: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/refresh.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).refreshSvg,
    search: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/search.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).searchSvg,
    send: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/send.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).sendSvg,
    swapHorizontal: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontal.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).swapHorizontalSvg,
    swapHorizontalMedium: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalMedium.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).swapHorizontalMediumSvg,
    swapHorizontalBold: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).swapHorizontalBoldSvg,
    swapHorizontalRoundedBold: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalRoundedBold.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).swapHorizontalRoundedBoldSvg,
    swapVertical: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapVertical.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).swapVerticalSvg,
    telegram: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).telegramSvg,
    threeDots: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/three-dots.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).threeDotsSvg,
    twitch: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).twitchSvg,
    twitter: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).xSvg,
    twitterIcon: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitterIcon.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).twitterIconSvg,
    verify: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).verifySvg,
    verifyFilled: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify-filled.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).verifyFilledSvg,
    wallet: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).walletSvg,
    walletConnect: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).walletConnectSvg,
    walletConnectLightBrown: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).walletConnectLightBrownSvg,
    walletConnectBrown: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).walletConnectBrownSvg,
    walletPlaceholder: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet-placeholder.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).walletPlaceholderSvg,
    warningCircle: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/warning-circle.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).warningCircleSvg,
    x: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).xSvg,
    info: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).infoSvg,
    exclamationTriangle: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/exclamation-triangle.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).exclamationTriangleSvg,
    reown: async ()=>(await __turbopack_context__.r("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).reownSvg
};
async function getSvg(name) {
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$CacheUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["globalSvgCache"].has(name)) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$CacheUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["globalSvgCache"].get(name);
    }
    const importFn = ICONS[name] ?? ICONS.copy;
    const svgPromise = importFn();
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$CacheUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["globalSvgCache"].set(name, svgPromise);
    return svgPromise;
}
let WuiIcon = class WuiIcon extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super(...arguments);
        this.size = 'md';
        this.name = 'copy';
        this.color = 'fg-300';
        this.aspectRatio = '1 / 1';
    }
    render() {
        this.style.cssText = `
      --local-color: ${`var(--wui-color-${this.color});`}
      --local-width: ${`var(--wui-icon-size-${this.size});`}
      --local-aspect-ratio: ${this.aspectRatio}
    `;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$until$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["until"])(getSvg(this.name), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<div class="fallback"></div>`)}`;
    }
};
WuiIcon.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["colorStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$icon$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIcon.prototype, "size", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIcon.prototype, "name", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIcon.prototype, "color", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIcon.prototype, "aspectRatio", void 0);
WuiIcon = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-icon')
], WuiIcon);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/lit-html/node/development/directives/class-map.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "classMap": (()=>classMap)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directive.js [app-ssr] (ecmascript)");
;
;
/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */ class ClassMapDirective extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Directive"] {
    constructor(partInfo){
        super(partInfo);
        if (partInfo.type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PartType"].ATTRIBUTE || partInfo.name !== 'class' || partInfo.strings?.length > 2) {
            throw new Error('`classMap()` can only be used in the `class` attribute ' + 'and must be the only part in the attribute.');
        }
    }
    render(classInfo) {
        // Add spaces to ensure separation from static classes
        return ' ' + Object.keys(classInfo).filter((key)=>classInfo[key]).join(' ') + ' ';
    }
    update(part, [classInfo]) {
        // Remember dynamic classes on the first render
        if (this._previousClasses === undefined) {
            this._previousClasses = new Set();
            if (part.strings !== undefined) {
                this._staticClasses = new Set(part.strings.join(' ').split(/\s/).filter((s)=>s !== ''));
            }
            for(const name in classInfo){
                if (classInfo[name] && !this._staticClasses?.has(name)) {
                    this._previousClasses.add(name);
                }
            }
            return this.render(classInfo);
        }
        const classList = part.element.classList;
        // Remove old classes that no longer apply
        for (const name of this._previousClasses){
            if (!(name in classInfo)) {
                classList.remove(name);
                this._previousClasses.delete(name);
            }
        }
        // Add or remove classes based on their classMap value
        for(const name in classInfo){
            // We explicitly want a loose truthy check of `value` because it seems
            // more convenient that '' and 0 are skipped.
            const value = !!classInfo[name];
            if (value !== this._previousClasses.has(name) && !this._staticClasses?.has(name)) {
                if (value) {
                    classList.add(name);
                    this._previousClasses.add(name);
                } else {
                    classList.remove(name);
                    this._previousClasses.delete(name);
                }
            }
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noChange"];
    }
}
/**
 * A directive that applies dynamic CSS classes.
 *
 * This must be used in the `class` attribute and must be the only part used in
 * the attribute. It takes each property in the `classInfo` argument and adds
 * the property name to the element's `classList` if the property value is
 * truthy; if the property value is falsy, the property name is removed from
 * the element's `class`.
 *
 * For example `{foo: bar}` applies the class `foo` if the value of `bar` is
 * truthy.
 *
 * @param classInfo
 */ const classMap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directive$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["directive"])(ClassMapDirective);
;
 //# sourceMappingURL=class-map.js.map
}}),
"[project]/node_modules/lit/directives/class-map.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$class$2d$map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directives/class-map.js [app-ssr] (ecmascript)"); //# sourceMappingURL=class-map.js.map
;
}}),
"[project]/node_modules/lit/directives/class-map.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$class$2d$map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directives/class-map.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$directives$2f$class$2d$map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/directives/class-map.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-text/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: inline-flex !important;
  }

  slot {
    width: 100%;
    display: inline-block;
    font-style: normal;
    font-family: var(--wui-font-family);
    font-feature-settings:
      'tnum' on,
      'lnum' on,
      'case' on;
    line-height: 130%;
    font-weight: var(--wui-font-weight-regular);
    overflow: inherit;
    text-overflow: inherit;
    text-align: var(--local-align);
    color: var(--local-color);
  }

  .wui-line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .wui-line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .wui-font-medium-400 {
    font-size: var(--wui-font-size-medium);
    font-weight: var(--wui-font-weight-light);
    letter-spacing: var(--wui-letter-spacing-medium);
  }

  .wui-font-medium-600 {
    font-size: var(--wui-font-size-medium);
    letter-spacing: var(--wui-letter-spacing-medium);
  }

  .wui-font-title-600 {
    font-size: var(--wui-font-size-title);
    letter-spacing: var(--wui-letter-spacing-title);
  }

  .wui-font-title-6-600 {
    font-size: var(--wui-font-size-title-6);
    letter-spacing: var(--wui-letter-spacing-title-6);
  }

  .wui-font-mini-700 {
    font-size: var(--wui-font-size-mini);
    letter-spacing: var(--wui-letter-spacing-mini);
    text-transform: uppercase;
  }

  .wui-font-large-500,
  .wui-font-large-600,
  .wui-font-large-700 {
    font-size: var(--wui-font-size-large);
    letter-spacing: var(--wui-letter-spacing-large);
  }

  .wui-font-2xl-500,
  .wui-font-2xl-600,
  .wui-font-2xl-700 {
    font-size: var(--wui-font-size-2xl);
    letter-spacing: var(--wui-letter-spacing-2xl);
  }

  .wui-font-paragraph-400,
  .wui-font-paragraph-500,
  .wui-font-paragraph-600,
  .wui-font-paragraph-700 {
    font-size: var(--wui-font-size-paragraph);
    letter-spacing: var(--wui-letter-spacing-paragraph);
  }

  .wui-font-small-400,
  .wui-font-small-500,
  .wui-font-small-600 {
    font-size: var(--wui-font-size-small);
    letter-spacing: var(--wui-letter-spacing-small);
  }

  .wui-font-tiny-400,
  .wui-font-tiny-500,
  .wui-font-tiny-600 {
    font-size: var(--wui-font-size-tiny);
    letter-spacing: var(--wui-letter-spacing-tiny);
  }

  .wui-font-micro-700,
  .wui-font-micro-600 {
    font-size: var(--wui-font-size-micro);
    letter-spacing: var(--wui-letter-spacing-micro);
    text-transform: uppercase;
  }

  .wui-font-tiny-400,
  .wui-font-small-400,
  .wui-font-medium-400,
  .wui-font-paragraph-400 {
    font-weight: var(--wui-font-weight-light);
  }

  .wui-font-large-700,
  .wui-font-paragraph-700,
  .wui-font-micro-700,
  .wui-font-mini-700 {
    font-weight: var(--wui-font-weight-bold);
  }

  .wui-font-medium-600,
  .wui-font-medium-title-600,
  .wui-font-title-6-600,
  .wui-font-large-600,
  .wui-font-paragraph-600,
  .wui-font-small-600,
  .wui-font-tiny-600,
  .wui-font-micro-600 {
    font-weight: var(--wui-font-weight-medium);
  }

  :host([disabled]) {
    opacity: 0.4;
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-text/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiText": (()=>WuiText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$directives$2f$class$2d$map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/directives/class-map.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$class$2d$map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directives/class-map.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$text$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-text/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
let WuiText = class WuiText extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super(...arguments);
        this.variant = 'paragraph-500';
        this.color = 'fg-300';
        this.align = 'left';
        this.lineClamp = undefined;
    }
    render() {
        const classes = {
            [`wui-font-${this.variant}`]: true,
            [`wui-color-${this.color}`]: true,
            [`wui-line-clamp-${this.lineClamp}`]: this.lineClamp ? true : false
        };
        this.style.cssText = `
      --local-align: ${this.align};
      --local-color: var(--wui-color-${this.color});
    `;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<slot class=${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$class$2d$map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classMap"])(classes)}></slot>`;
    }
};
WuiText.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$text$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiText.prototype, "variant", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiText.prototype, "color", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiText.prototype, "align", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiText.prototype, "lineClamp", void 0);
WuiText = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-text')
], WuiText);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-alertbar/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--wui-spacing-s);
    border-radius: var(--wui-border-radius-s);
    border: 1px solid var(--wui-color-dark-glass-100);
    box-sizing: border-box;
    background-color: var(--wui-color-bg-325);
    box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.25);
  }

  wui-flex {
    width: 100%;
  }

  wui-text {
    word-break: break-word;
    flex: 1;
  }

  .close {
    cursor: pointer;
  }

  .icon-box {
    height: 40px;
    width: 40px;
    border-radius: var(--wui-border-radius-3xs);
    background-color: var(--local-icon-bg-value);
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-alertbar/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiAlertBar": (()=>WuiAlertBar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$AlertController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/AlertController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$icon$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-icon/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$text$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-text/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$layout$2f$wui$2d$flex$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/layout/wui-flex/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$alertbar$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-alertbar/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
;
;
;
let WuiAlertBar = class WuiAlertBar extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super(...arguments);
        this.message = '';
        this.backgroundColor = 'accent-100';
        this.iconColor = 'accent-100';
        this.icon = 'info';
    }
    render() {
        this.style.cssText = `
      --local-icon-bg-value: var(--wui-color-${this.backgroundColor});
   `;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`
      <wui-flex flexDirection="row" justifyContent="space-between" alignItems="center">
        <wui-flex columnGap="xs" flexDirection="row" alignItems="center">
          <wui-flex
            flexDirection="row"
            alignItems="center"
            justifyContent="center"
            class="icon-box"
          >
            <wui-icon color=${this.iconColor} size="md" name=${this.icon}></wui-icon>
          </wui-flex>
          <wui-text variant="small-500" color="bg-350" data-testid="wui-alertbar-text"
            >${this.message}</wui-text
          >
        </wui-flex>
        <wui-icon
          class="close"
          color="bg-350"
          size="sm"
          name="close"
          @click=${this.onClose}
        ></wui-icon>
      </wui-flex>
    `;
    }
    onClose() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$AlertController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlertController"].close();
    }
};
WuiAlertBar.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$alertbar$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiAlertBar.prototype, "message", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiAlertBar.prototype, "backgroundColor", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiAlertBar.prototype, "iconColor", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiAlertBar.prototype, "icon", void 0);
WuiAlertBar = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-alertbar')
], WuiAlertBar);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-alertbar.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$alertbar$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-alertbar/index.js [app-ssr] (ecmascript)"); //# sourceMappingURL=wui-alertbar.js.map
;
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-alertbar.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$alertbar$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-alertbar/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$alertbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-alertbar.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-alertbar/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: block;
    position: absolute;
    top: var(--wui-spacing-s);
    left: var(--wui-spacing-l);
    right: var(--wui-spacing-l);
    opacity: 0;
    pointer-events: none;
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-alertbar/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "W3mAlertBar": (()=>W3mAlertBar),
    "presets": (()=>presets)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/state.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$AlertController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/AlertController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$alertbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-alertbar.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$alertbar$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-alertbar/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
const presets = {
    info: {
        backgroundColor: 'fg-350',
        iconColor: 'fg-325',
        icon: 'info'
    },
    success: {
        backgroundColor: 'success-glass-reown-020',
        iconColor: 'success-125',
        icon: 'checkmark'
    },
    warning: {
        backgroundColor: 'warning-glass-reown-020',
        iconColor: 'warning-100',
        icon: 'warningCircle'
    },
    error: {
        backgroundColor: 'error-glass-reown-020',
        iconColor: 'error-125',
        icon: 'exclamationTriangle'
    }
};
let W3mAlertBar = class W3mAlertBar extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super();
        this.unsubscribe = [];
        this.open = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$AlertController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlertController"].state.open;
        this.onOpen(true);
        this.unsubscribe.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$AlertController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlertController"].subscribeKey('open', (val)=>{
            this.open = val;
            this.onOpen(false);
        }));
    }
    disconnectedCallback() {
        this.unsubscribe.forEach((unsubscribe)=>unsubscribe());
    }
    render() {
        const { message, variant } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$AlertController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlertController"].state;
        const preset = presets[variant];
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`
      <wui-alertbar
        message=${message}
        backgroundColor=${preset?.backgroundColor}
        iconColor=${preset?.iconColor}
        icon=${preset?.icon}
      ></wui-alertbar>
    `;
    }
    onOpen(isMounted) {
        if (this.open) {
            this.animate([
                {
                    opacity: 0,
                    transform: 'scale(0.85)'
                },
                {
                    opacity: 1,
                    transform: 'scale(1)'
                }
            ], {
                duration: 150,
                fill: 'forwards',
                easing: 'ease'
            });
            this.style.cssText = `pointer-events: auto`;
        } else if (!isMounted) {
            this.animate([
                {
                    opacity: 1,
                    transform: 'scale(1)'
                },
                {
                    opacity: 0,
                    transform: 'scale(0.85)'
                }
            ], {
                duration: 150,
                fill: 'forwards',
                easing: 'ease'
            });
            this.style.cssText = `pointer-events: none`;
        }
    }
};
W3mAlertBar.styles = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$alertbar$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mAlertBar.prototype, "open", void 0);
W3mAlertBar = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('w3m-alertbar')
], W3mAlertBar);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-link/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  button {
    border-radius: var(--local-border-radius);
    color: var(--wui-color-fg-100);
    padding: var(--local-padding);
  }

  @media (max-width: 700px) {
    button {
      padding: var(--wui-spacing-s);
    }
  }

  button > wui-icon {
    pointer-events: none;
  }

  button:disabled > wui-icon {
    color: var(--wui-color-bg-300) !important;
  }

  button:disabled {
    background-color: transparent;
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-link/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiIconLink": (()=>WuiIconLink)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$icon$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-icon/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$icon$2d$link$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-link/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
let WuiIconLink = class WuiIconLink extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super(...arguments);
        this.size = 'md';
        this.disabled = false;
        this.icon = 'copy';
        this.iconColor = 'inherit';
    }
    render() {
        const borderRadius = this.size === 'lg' ? '--wui-border-radius-xs' : '--wui-border-radius-xxs';
        const padding = this.size === 'lg' ? '--wui-spacing-1xs' : '--wui-spacing-2xs';
        this.style.cssText = `
    --local-border-radius: var(${borderRadius});
    --local-padding: var(${padding});
`;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`
      <button ?disabled=${this.disabled}>
        <wui-icon color=${this.iconColor} size=${this.size} name=${this.icon}></wui-icon>
      </button>
    `;
    }
};
WuiIconLink.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["elementStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["colorStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$icon$2d$link$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIconLink.prototype, "size", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])({
        type: Boolean
    })
], WuiIconLink.prototype, "disabled", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIconLink.prototype, "icon", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIconLink.prototype, "iconColor", void 0);
WuiIconLink = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-icon-link')
], WuiIconLink);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-icon-link.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$icon$2d$link$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-link/index.js [app-ssr] (ecmascript)"); //# sourceMappingURL=wui-icon-link.js.map
;
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-icon-link.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$icon$2d$link$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-link/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$icon$2d$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-icon-link.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-image/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: block;
    width: var(--local-width);
    height: var(--local-height);
  }

  img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
    border-radius: inherit;
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-image/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiImage": (()=>WuiImage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$image$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-image/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
let WuiImage = class WuiImage extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super(...arguments);
        this.src = './path/to/image.jpg';
        this.alt = 'Image';
        this.size = undefined;
    }
    render() {
        this.style.cssText = `
      --local-width: ${this.size ? `var(--wui-icon-size-${this.size});` : '100%'};
      --local-height: ${this.size ? `var(--wui-icon-size-${this.size});` : '100%'};
      `;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<img src=${this.src} alt=${this.alt} @error=${this.handleImageError} />`;
    }
    handleImageError() {
        this.dispatchEvent(new CustomEvent('onLoadError', {
            bubbles: true,
            composed: true
        }));
    }
};
WuiImage.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["colorStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$image$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiImage.prototype, "src", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiImage.prototype, "alt", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiImage.prototype, "size", void 0);
WuiImage = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-image')
], WuiImage);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-box/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: var(--wui-color-gray-glass-020);
    border-radius: var(--local-border-radius);
    border: var(--local-border);
    box-sizing: content-box;
    width: var(--local-size);
    height: var(--local-size);
    min-height: var(--local-size);
    min-width: var(--local-size);
  }

  @supports (background: color-mix(in srgb, white 50%, black)) {
    :host {
      background-color: color-mix(in srgb, var(--local-bg-value) var(--local-bg-mix), transparent);
    }
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-box/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiIconBox": (()=>WuiIconBox)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$icon$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-icon/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$icon$2d$box$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-box/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
let WuiIconBox = class WuiIconBox extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super(...arguments);
        this.size = 'md';
        this.backgroundColor = 'accent-100';
        this.iconColor = 'accent-100';
        this.background = 'transparent';
        this.border = false;
        this.borderColor = 'wui-color-bg-125';
        this.icon = 'copy';
    }
    render() {
        const iconSize = this.iconSize || this.size;
        const isLg = this.size === 'lg';
        const isXl = this.size === 'xl';
        const bgMix = isLg ? '12%' : '16%';
        const borderRadius = isLg ? 'xxs' : isXl ? 's' : '3xl';
        const isGray = this.background === 'gray';
        const isOpaque = this.background === 'opaque';
        const isColorChange = this.backgroundColor === 'accent-100' && isOpaque || this.backgroundColor === 'success-100' && isOpaque || this.backgroundColor === 'error-100' && isOpaque || this.backgroundColor === 'inverse-100' && isOpaque;
        let bgValueVariable = `var(--wui-color-${this.backgroundColor})`;
        if (isColorChange) {
            bgValueVariable = `var(--wui-icon-box-bg-${this.backgroundColor})`;
        } else if (isGray) {
            bgValueVariable = `var(--wui-color-gray-${this.backgroundColor})`;
        }
        this.style.cssText = `
       --local-bg-value: ${bgValueVariable};
       --local-bg-mix: ${isColorChange || isGray ? `100%` : bgMix};
       --local-border-radius: var(--wui-border-radius-${borderRadius});
       --local-size: var(--wui-icon-box-size-${this.size});
       --local-border: ${this.borderColor === 'wui-color-bg-125' ? `2px` : `1px`} solid ${this.border ? `var(--${this.borderColor})` : `transparent`}
   `;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]` <wui-icon color=${this.iconColor} size=${iconSize} name=${this.icon}></wui-icon> `;
    }
};
WuiIconBox.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["elementStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$icon$2d$box$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIconBox.prototype, "size", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIconBox.prototype, "backgroundColor", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIconBox.prototype, "iconColor", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIconBox.prototype, "iconSize", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIconBox.prototype, "background", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])({
        type: Boolean
    })
], WuiIconBox.prototype, "border", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIconBox.prototype, "borderColor", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiIconBox.prototype, "icon", void 0);
WuiIconBox = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-icon-box')
], WuiIconBox);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-select/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  button {
    display: block;
    display: flex;
    align-items: center;
    padding: var(--wui-spacing-xxs);
    gap: var(--wui-spacing-xxs);
    transition: all var(--wui-ease-out-power-1) var(--wui-duration-md);
    border-radius: var(--wui-border-radius-xxs);
  }

  wui-image {
    border-radius: 100%;
    width: var(--wui-spacing-xl);
    height: var(--wui-spacing-xl);
  }

  wui-icon-box {
    width: var(--wui-spacing-xl);
    height: var(--wui-spacing-xl);
  }

  button:hover {
    background-color: var(--wui-color-gray-glass-002);
  }

  button:active {
    background-color: var(--wui-color-gray-glass-005);
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-select/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiSelect": (()=>WuiSelect)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$icon$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-icon/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$image$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-image/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$icon$2d$box$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-box/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$select$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-select/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
;
;
let WuiSelect = class WuiSelect extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super(...arguments);
        this.imageSrc = '';
    }
    render() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<button>
      ${this.imageTemplate()}
      <wui-icon size="xs" color="fg-200" name="chevronBottom"></wui-icon>
    </button>`;
    }
    imageTemplate() {
        if (this.imageSrc) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<wui-image src=${this.imageSrc} alt="select visual"></wui-image>`;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<wui-icon-box
      size="xxs"
      iconColor="fg-200"
      backgroundColor="fg-100"
      background="opaque"
      icon="networkPlaceholder"
    ></wui-icon-box>`;
    }
};
WuiSelect.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["elementStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["colorStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$select$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiSelect.prototype, "imageSrc", void 0);
WuiSelect = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-select')
], WuiSelect);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-select.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$select$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-select/index.js [app-ssr] (ecmascript)"); //# sourceMappingURL=wui-select.js.map
;
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-select.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$select$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-select/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-select.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-tag/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: flex;
    justify-content: center;
    align-items: center;
    height: var(--wui-spacing-m);
    padding: 0 var(--wui-spacing-3xs) !important;
    border-radius: var(--wui-border-radius-5xs);
    transition:
      border-radius var(--wui-duration-lg) var(--wui-ease-out-power-1),
      background-color var(--wui-duration-lg) var(--wui-ease-out-power-1);
    will-change: border-radius, background-color;
  }

  :host > wui-text {
    transform: translateY(5%);
  }

  :host([data-variant='main']) {
    background-color: var(--wui-color-accent-glass-015);
    color: var(--wui-color-accent-100);
  }

  :host([data-variant='shade']) {
    background-color: var(--wui-color-gray-glass-010);
    color: var(--wui-color-fg-200);
  }

  :host([data-variant='success']) {
    background-color: var(--wui-icon-box-bg-success-100);
    color: var(--wui-color-success-100);
  }

  :host([data-variant='error']) {
    background-color: var(--wui-icon-box-bg-error-100);
    color: var(--wui-color-error-100);
  }

  :host([data-size='lg']) {
    padding: 11px 5px !important;
  }

  :host([data-size='lg']) > wui-text {
    transform: translateY(2%);
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-tag/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiTag": (()=>WuiTag)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$text$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-text/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$tag$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-tag/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
let WuiTag = class WuiTag extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super(...arguments);
        this.variant = 'main';
        this.size = 'lg';
    }
    render() {
        this.dataset['variant'] = this.variant;
        this.dataset['size'] = this.size;
        const textVariant = this.size === 'md' ? 'mini-700' : 'micro-700';
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`
      <wui-text data-variant=${this.variant} variant=${textVariant} color="inherit">
        <slot></slot>
      </wui-text>
    `;
    }
};
WuiTag.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$tag$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiTag.prototype, "variant", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiTag.prototype, "size", void 0);
WuiTag = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-tag')
], WuiTag);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-tag.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$tag$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-tag/index.js [app-ssr] (ecmascript)"); //# sourceMappingURL=wui-tag.js.map
;
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-tag.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$tag$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-tag/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-tag.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-text.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$text$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-text/index.js [app-ssr] (ecmascript)"); //# sourceMappingURL=wui-text.js.map
;
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-text.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$text$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-text/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-text.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-header/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    height: 64px;
  }

  wui-text {
    text-transform: capitalize;
  }

  wui-flex.w3m-header-title {
    transform: translateY(0);
    opacity: 1;
  }

  wui-flex.w3m-header-title[view-direction='prev'] {
    animation:
      slide-down-out 120ms forwards var(--wui-ease-out-power-2),
      slide-down-in 120ms forwards var(--wui-ease-out-power-2);
    animation-delay: 0ms, 200ms;
  }

  wui-flex.w3m-header-title[view-direction='next'] {
    animation:
      slide-up-out 120ms forwards var(--wui-ease-out-power-2),
      slide-up-in 120ms forwards var(--wui-ease-out-power-2);
    animation-delay: 0ms, 200ms;
  }

  wui-icon-link[data-hidden='true'] {
    opacity: 0 !important;
    pointer-events: none;
  }

  @keyframes slide-up-out {
    from {
      transform: translateY(0px);
      opacity: 1;
    }
    to {
      transform: translateY(3px);
      opacity: 0;
    }
  }

  @keyframes slide-up-in {
    from {
      transform: translateY(-3px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slide-down-out {
    from {
      transform: translateY(0px);
      opacity: 1;
    }
    to {
      transform: translateY(-3px);
      opacity: 0;
    }
  }

  @keyframes slide-down-in {
    from {
      transform: translateY(3px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-header/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "W3mHeader": (()=>W3mHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/state.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$directives$2f$if$2d$defined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/directives/if-defined.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$if$2d$defined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directives/if-defined.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$AccountController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/AccountController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$AssetController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/AssetController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$AssetUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/utils/AssetUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ChainController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ConnectorController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ConnectorController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$EventsController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/EventsController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$ModalUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/utils/ModalUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$OptionsController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/OptionsController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/RouterController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$flex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-flex.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$icon$2d$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-icon-link.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-select.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-tag.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-text.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ConstantsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/utils/ConstantsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$header$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-header/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
;
;
;
;
;
;
const BETA_SCREENS = [
    'SmartSessionList'
];
function headings() {
    const connectorName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.data?.connector?.name;
    const walletName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.data?.wallet?.name;
    const networkName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.data?.network?.name;
    const name = walletName ?? connectorName;
    const connectors = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ConnectorController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConnectorController"].getConnectors();
    const isEmail = connectors.length === 1 && connectors[0]?.id === 'w3m-email';
    return {
        Connect: `Connect ${isEmail ? 'Email' : ''} Wallet`,
        Create: 'Create Wallet',
        ChooseAccountName: undefined,
        Account: undefined,
        AccountSettings: undefined,
        AllWallets: 'All Wallets',
        ApproveTransaction: 'Approve Transaction',
        BuyInProgress: 'Buy',
        ConnectingExternal: name ?? 'Connect Wallet',
        ConnectingWalletConnect: name ?? 'WalletConnect',
        ConnectingWalletConnectBasic: 'WalletConnect',
        ConnectingSiwe: 'Sign In',
        Convert: 'Convert',
        ConvertSelectToken: 'Select token',
        ConvertPreview: 'Preview convert',
        Downloads: name ? `Get ${name}` : 'Downloads',
        EmailLogin: 'Email Login',
        EmailVerifyOtp: 'Confirm Email',
        EmailVerifyDevice: 'Register Device',
        GetWallet: 'Get a wallet',
        Networks: 'Choose Network',
        OnRampProviders: 'Choose Provider',
        OnRampActivity: 'Activity',
        OnRampTokenSelect: 'Select Token',
        OnRampFiatSelect: 'Select Currency',
        Pay: 'How you pay',
        Profile: undefined,
        SwitchNetwork: networkName ?? 'Switch Network',
        SwitchAddress: 'Switch Address',
        Transactions: 'Activity',
        UnsupportedChain: 'Switch Network',
        UpgradeEmailWallet: 'Upgrade your Wallet',
        UpdateEmailWallet: 'Edit Email',
        UpdateEmailPrimaryOtp: 'Confirm Current Email',
        UpdateEmailSecondaryOtp: 'Confirm New Email',
        WhatIsABuy: 'What is Buy?',
        RegisterAccountName: 'Choose name',
        RegisterAccountNameSuccess: '',
        WalletReceive: 'Receive',
        WalletCompatibleNetworks: 'Compatible Networks',
        Swap: 'Swap',
        SwapSelectToken: 'Select token',
        SwapPreview: 'Preview swap',
        WalletSend: 'Send',
        WalletSendPreview: 'Review send',
        WalletSendSelectToken: 'Select Token',
        WhatIsANetwork: 'What is a network?',
        WhatIsAWallet: 'What is a wallet?',
        ConnectWallets: 'Connect wallet',
        ConnectSocials: 'All socials',
        ConnectingSocial: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$AccountController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccountController"].state.socialProvider ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$AccountController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccountController"].state.socialProvider : 'Connect Social',
        ConnectingMultiChain: 'Select chain',
        ConnectingFarcaster: 'Farcaster',
        SwitchActiveChain: 'Switch chain',
        SmartSessionCreated: undefined,
        SmartSessionList: 'Smart Sessions',
        SIWXSignMessage: 'Sign In',
        PayLoading: 'Payment in progress'
    };
}
let W3mHeader = class W3mHeader extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super();
        this.unsubscribe = [];
        this.heading = headings()[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.view];
        this.network = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainController"].state.activeCaipNetwork;
        this.networkImage = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$AssetUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AssetUtil"].getNetworkImage(this.network);
        this.showBack = false;
        this.prevHistoryLength = 1;
        this.view = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.view;
        this.viewDirection = '';
        this.headerText = headings()[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.view];
        this.unsubscribe.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$AssetController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AssetController"].subscribeNetworkImages(()=>{
            this.networkImage = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$AssetUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AssetUtil"].getNetworkImage(this.network);
        }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].subscribeKey('view', (val)=>{
            setTimeout(()=>{
                this.view = val;
                this.headerText = headings()[val];
            }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ConstantsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConstantsUtil"].ANIMATION_DURATIONS.HeaderText);
            this.onViewChange();
            this.onHistoryChange();
        }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainController"].subscribeKey('activeCaipNetwork', (val)=>{
            this.network = val;
            this.networkImage = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$AssetUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AssetUtil"].getNetworkImage(this.network);
        }));
    }
    disconnectCallback() {
        this.unsubscribe.forEach((unsubscribe)=>unsubscribe());
    }
    render() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`
      <wui-flex .padding=${this.getPadding()} justifyContent="space-between" alignItems="center">
        ${this.leftHeaderTemplate()} ${this.titleTemplate()} ${this.rightHeaderTemplate()}
      </wui-flex>
    `;
    }
    onWalletHelp() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$EventsController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EventsController"].sendEvent({
            type: 'track',
            event: 'CLICK_WALLET_HELP'
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].push('WhatIsAWallet');
    }
    async onClose() {
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$ModalUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalUtil"].safeClose();
    }
    rightHeaderTemplate() {
        const isSmartSessionsEnabled = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$OptionsController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OptionsController"]?.state?.features?.smartSessions;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.view !== 'Account' || !isSmartSessionsEnabled) {
            return this.closeButtonTemplate();
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<wui-flex>
      <wui-icon-link
        icon="clock"
        @click=${()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].push('SmartSessionList')}
        data-testid="w3m-header-smart-sessions"
      ></wui-icon-link>
      ${this.closeButtonTemplate()}
    </wui-flex> `;
    }
    closeButtonTemplate() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`
      <wui-icon-link
        icon="close"
        @click=${this.onClose.bind(this)}
        data-testid="w3m-header-close"
      ></wui-icon-link>
    `;
    }
    titleTemplate() {
        const isBeta = BETA_SCREENS.includes(this.view);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`
      <wui-flex
        view-direction="${this.viewDirection}"
        class="w3m-header-title"
        alignItems="center"
        gap="xs"
      >
        <wui-text variant="paragraph-700" color="fg-100" data-testid="w3m-header-text"
          >${this.headerText}</wui-text
        >
        ${isBeta ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<wui-tag variant="main">Beta</wui-tag>` : null}
      </wui-flex>
    `;
    }
    leftHeaderTemplate() {
        const { view } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state;
        const isConnectHelp = view === 'Connect';
        const isEmbeddedEnable = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$OptionsController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OptionsController"].state.enableEmbedded;
        const isApproveTransaction = view === 'ApproveTransaction';
        const isConnectingSIWEView = view === 'ConnectingSiwe';
        const isAccountView = view === 'Account';
        const enableNetworkSwitch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$OptionsController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OptionsController"].state.enableNetworkSwitch;
        const shouldHideBack = isApproveTransaction || isConnectingSIWEView || isConnectHelp && isEmbeddedEnable;
        if (isAccountView && enableNetworkSwitch) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<wui-select
        id="dynamic"
        data-testid="w3m-account-select-network"
        active-network=${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$if$2d$defined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ifDefined"])(this.network?.name)}
        @click=${this.onNetworks.bind(this)}
        imageSrc=${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$if$2d$defined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ifDefined"])(this.networkImage)}
      ></wui-select>`;
        }
        if (this.showBack && !shouldHideBack) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<wui-icon-link
        data-testid="header-back"
        id="dynamic"
        icon="chevronLeft"
        @click=${this.onGoBack.bind(this)}
      ></wui-icon-link>`;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<wui-icon-link
      data-hidden=${!isConnectHelp}
      id="dynamic"
      icon="helpCircle"
      @click=${this.onWalletHelp.bind(this)}
    ></wui-icon-link>`;
    }
    onNetworks() {
        if (this.isAllowedNetworkSwitch()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$EventsController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EventsController"].sendEvent({
                type: 'track',
                event: 'CLICK_NETWORKS'
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].push('Networks');
        }
    }
    isAllowedNetworkSwitch() {
        const requestedCaipNetworks = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainController"].getAllRequestedCaipNetworks();
        const isMultiNetwork = requestedCaipNetworks ? requestedCaipNetworks.length > 1 : false;
        const isValidNetwork = requestedCaipNetworks?.find(({ id })=>id === this.network?.id);
        return isMultiNetwork || !isValidNetwork;
    }
    getPadding() {
        if (this.heading) {
            return [
                'l',
                '2l',
                'l',
                '2l'
            ];
        }
        return [
            '0',
            '2l',
            '0',
            '2l'
        ];
    }
    onViewChange() {
        const { history } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state;
        let direction = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ConstantsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConstantsUtil"].VIEW_DIRECTION.Next;
        if (history.length < this.prevHistoryLength) {
            direction = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ConstantsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConstantsUtil"].VIEW_DIRECTION.Prev;
        }
        this.prevHistoryLength = history.length;
        this.viewDirection = direction;
    }
    async onHistoryChange() {
        const { history } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state;
        const buttonEl = this.shadowRoot?.querySelector('#dynamic');
        if (history.length > 1 && !this.showBack && buttonEl) {
            await buttonEl.animate([
                {
                    opacity: 1
                },
                {
                    opacity: 0
                }
            ], {
                duration: 200,
                fill: 'forwards',
                easing: 'ease'
            }).finished;
            this.showBack = true;
            buttonEl.animate([
                {
                    opacity: 0
                },
                {
                    opacity: 1
                }
            ], {
                duration: 200,
                fill: 'forwards',
                easing: 'ease'
            });
        } else if (history.length <= 1 && this.showBack && buttonEl) {
            await buttonEl.animate([
                {
                    opacity: 1
                },
                {
                    opacity: 0
                }
            ], {
                duration: 200,
                fill: 'forwards',
                easing: 'ease'
            }).finished;
            this.showBack = false;
            buttonEl.animate([
                {
                    opacity: 0
                },
                {
                    opacity: 1
                }
            ], {
                duration: 200,
                fill: 'forwards',
                easing: 'ease'
            });
        }
    }
    onGoBack() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].goBack();
    }
};
W3mHeader.styles = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$header$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mHeader.prototype, "heading", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mHeader.prototype, "network", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mHeader.prototype, "networkImage", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mHeader.prototype, "showBack", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mHeader.prototype, "prevHistoryLength", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mHeader.prototype, "view", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mHeader.prototype, "viewDirection", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mHeader.prototype, "headerText", void 0);
W3mHeader = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('w3m-header')
], W3mHeader);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-loading-spinner/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: flex;
  }

  :host([data-size='sm']) > svg {
    width: 12px;
    height: 12px;
  }

  :host([data-size='md']) > svg {
    width: 16px;
    height: 16px;
  }

  :host([data-size='lg']) > svg {
    width: 24px;
    height: 24px;
  }

  :host([data-size='xl']) > svg {
    width: 32px;
    height: 32px;
  }

  svg {
    animation: rotate 2s linear infinite;
  }

  circle {
    fill: none;
    stroke: var(--local-color);
    stroke-width: 4px;
    stroke-dasharray: 1, 124;
    stroke-dashoffset: 0;
    stroke-linecap: round;
    animation: dash 1.5s ease-in-out infinite;
  }

  :host([data-size='md']) > svg > circle {
    stroke-width: 6px;
  }

  :host([data-size='sm']) > svg > circle {
    stroke-width: 8px;
  }

  @keyframes rotate {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes dash {
    0% {
      stroke-dasharray: 1, 124;
      stroke-dashoffset: 0;
    }

    50% {
      stroke-dasharray: 90, 124;
      stroke-dashoffset: -35;
    }

    100% {
      stroke-dashoffset: -125;
    }
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-loading-spinner/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiLoadingSpinner": (()=>WuiLoadingSpinner)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$loading$2d$spinner$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-loading-spinner/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
let WuiLoadingSpinner = class WuiLoadingSpinner extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super(...arguments);
        this.color = 'accent-100';
        this.size = 'lg';
    }
    render() {
        this.style.cssText = `--local-color: ${this.color === 'inherit' ? 'inherit' : `var(--wui-color-${this.color})`}`;
        this.dataset['size'] = this.size;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<svg viewBox="25 25 50 50">
      <circle r="20" cy="50" cx="50"></circle>
    </svg>`;
    }
};
WuiLoadingSpinner.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$loading$2d$spinner$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiLoadingSpinner.prototype, "color", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiLoadingSpinner.prototype, "size", void 0);
WuiLoadingSpinner = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-loading-spinner')
], WuiLoadingSpinner);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-snackbar/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: flex;
    column-gap: var(--wui-spacing-s);
    align-items: center;
    padding: var(--wui-spacing-xs) var(--wui-spacing-m) var(--wui-spacing-xs) var(--wui-spacing-xs);
    border-radius: var(--wui-border-radius-s);
    border: 1px solid var(--wui-color-gray-glass-005);
    box-sizing: border-box;
    background-color: var(--wui-color-bg-175);
    box-shadow:
      0px 14px 64px -4px rgba(0, 0, 0, 0.15),
      0px 8px 22px -6px rgba(0, 0, 0, 0.15);

    max-width: 300px;
  }

  :host wui-loading-spinner {
    margin-left: var(--wui-spacing-3xs);
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-snackbar/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WuiSnackbar": (()=>WuiSnackbar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$icon$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-icon/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$loading$2d$spinner$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-loading-spinner/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$text$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-text/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$icon$2d$box$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-icon-box/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$snackbar$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-snackbar/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
;
;
;
let WuiSnackbar = class WuiSnackbar extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super(...arguments);
        this.backgroundColor = 'accent-100';
        this.iconColor = 'accent-100';
        this.icon = 'checkmark';
        this.message = '';
        this.loading = false;
        this.iconType = 'default';
    }
    render() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`
      ${this.templateIcon()}
      <wui-text variant="paragraph-500" color="fg-100" data-testid="wui-snackbar-message"
        >${this.message}</wui-text
      >
    `;
    }
    templateIcon() {
        if (this.loading) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<wui-loading-spinner size="md" color="accent-100"></wui-loading-spinner>`;
        }
        if (this.iconType === 'default') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<wui-icon size="xl" color=${this.iconColor} name=${this.icon}></wui-icon>`;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<wui-icon-box
      size="sm"
      iconSize="xs"
      iconColor=${this.iconColor}
      backgroundColor=${this.backgroundColor}
      icon=${this.icon}
      background="opaque"
    ></wui-icon-box>`;
    }
};
WuiSnackbar.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resetStyles"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$snackbar$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiSnackbar.prototype, "backgroundColor", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiSnackbar.prototype, "iconColor", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiSnackbar.prototype, "icon", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiSnackbar.prototype, "message", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiSnackbar.prototype, "loading", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])()
], WuiSnackbar.prototype, "iconType", void 0);
WuiSnackbar = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('wui-snackbar')
], WuiSnackbar);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-snackbar.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$snackbar$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-snackbar/index.js [app-ssr] (ecmascript)"); //# sourceMappingURL=wui-snackbar.js.map
;
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-snackbar.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$composites$2f$wui$2d$snackbar$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/composites/wui-snackbar/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$snackbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-snackbar.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-snackbar/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    display: block;
    position: absolute;
    opacity: 0;
    pointer-events: none;
    top: 11px;
    left: 50%;
    width: max-content;
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-snackbar/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "W3mSnackBar": (()=>W3mSnackBar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/state.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$SnackController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/SnackController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$snackbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-snackbar.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$snackbar$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-snackbar/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
const presets = {
    loading: undefined,
    success: {
        backgroundColor: 'success-100',
        iconColor: 'success-100',
        icon: 'checkmark'
    },
    error: {
        backgroundColor: 'error-100',
        iconColor: 'error-100',
        icon: 'close'
    }
};
let W3mSnackBar = class W3mSnackBar extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super();
        this.unsubscribe = [];
        this.timeout = undefined;
        this.open = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$SnackController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SnackController"].state.open;
        this.unsubscribe.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$SnackController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SnackController"].subscribeKey('open', (val)=>{
            this.open = val;
            this.onOpen();
        }));
    }
    disconnectedCallback() {
        clearTimeout(this.timeout);
        this.unsubscribe.forEach((unsubscribe)=>unsubscribe());
    }
    render() {
        const { message, variant, svg } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$SnackController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SnackController"].state;
        const preset = presets[variant];
        const { icon, iconColor } = svg ?? preset ?? {};
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`
      <wui-snackbar
        message=${message}
        backgroundColor=${preset?.backgroundColor}
        iconColor=${iconColor}
        icon=${icon}
        .loading=${variant === 'loading'}
      ></wui-snackbar>
    `;
    }
    onOpen() {
        clearTimeout(this.timeout);
        if (this.open) {
            this.animate([
                {
                    opacity: 0,
                    transform: 'translateX(-50%) scale(0.85)'
                },
                {
                    opacity: 1,
                    transform: 'translateX(-50%) scale(1)'
                }
            ], {
                duration: 150,
                fill: 'forwards',
                easing: 'ease'
            });
            if (this.timeout) {
                clearTimeout(this.timeout);
            }
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$SnackController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SnackController"].state.autoClose) {
                this.timeout = setTimeout(()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$SnackController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SnackController"].hide(), 2500);
            }
        } else {
            this.animate([
                {
                    opacity: 1,
                    transform: 'translateX(-50%) scale(1)'
                },
                {
                    opacity: 0,
                    transform: 'translateX(-50%) scale(0.85)'
                }
            ], {
                duration: 150,
                fill: 'forwards',
                easing: 'ease'
            });
        }
    }
};
W3mSnackBar.styles = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$snackbar$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mSnackBar.prototype, "open", void 0);
W3mSnackBar = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('w3m-snackbar')
], W3mSnackBar);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/TooltipController.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TooltipController": (()=>TooltipController)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$valtio$2f$esm$2f$vanilla$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/valtio/esm/vanilla.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$valtio$2f$esm$2f$vanilla$2f$utils$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/valtio/esm/vanilla/utils.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$withErrorBoundary$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/utils/withErrorBoundary.js [app-ssr] (ecmascript)");
;
;
;
// -- State --------------------------------------------- //
const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$valtio$2f$esm$2f$vanilla$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["proxy"])({
    message: '',
    open: false,
    triggerRect: {
        width: 0,
        height: 0,
        top: 0,
        left: 0
    },
    variant: 'shade'
});
// -- Controller ---------------------------------------- //
const controller = {
    state,
    subscribe (callback) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$valtio$2f$esm$2f$vanilla$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subscribe"])(state, ()=>callback(state));
    },
    subscribeKey (key, callback) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$valtio$2f$esm$2f$vanilla$2f$utils$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["subscribeKey"])(state, key, callback);
    },
    showTooltip ({ message, triggerRect, variant }) {
        state.open = true;
        state.message = message;
        state.triggerRect = triggerRect;
        state.variant = variant;
    },
    hide () {
        state.open = false;
        state.message = '';
        state.triggerRect = {
            width: 0,
            height: 0,
            top: 0,
            left: 0
        };
    }
};
const TooltipController = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$withErrorBoundary$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["withErrorBoundary"])(controller); //# sourceMappingURL=TooltipController.js.map
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-icon.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$icon$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-icon/index.js [app-ssr] (ecmascript)"); //# sourceMappingURL=wui-icon.js.map
;
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-icon.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$components$2f$wui$2d$icon$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/components/wui-icon/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$icon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-icon.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-tooltip/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    pointer-events: none;
  }

  :host > wui-flex {
    display: var(--w3m-tooltip-display);
    opacity: var(--w3m-tooltip-opacity);
    padding: 9px var(--wui-spacing-s) 10px var(--wui-spacing-s);
    border-radius: var(--wui-border-radius-xxs);
    color: var(--wui-color-bg-100);
    position: fixed;
    top: var(--w3m-tooltip-top);
    left: var(--w3m-tooltip-left);
    transform: translate(calc(-50% + var(--w3m-tooltip-parent-width)), calc(-100% - 8px));
    max-width: calc(var(--w3m-modal-width) - var(--wui-spacing-xl));
    transition: opacity 0.2s var(--wui-ease-out-power-2);
    will-change: opacity;
  }

  :host([data-variant='shade']) > wui-flex {
    background-color: var(--wui-color-bg-150);
    border: 1px solid var(--wui-color-gray-glass-005);
  }

  :host([data-variant='shade']) > wui-flex > wui-text {
    color: var(--wui-color-fg-150);
  }

  :host([data-variant='fill']) > wui-flex {
    background-color: var(--wui-color-fg-100);
    border: none;
  }

  wui-icon {
    position: absolute;
    width: 12px !important;
    height: 4px !important;
    color: var(--wui-color-bg-150);
  }

  wui-icon[data-placement='top'] {
    bottom: 0px;
    left: 50%;
    transform: translate(-50%, 95%);
  }

  wui-icon[data-placement='bottom'] {
    top: 0;
    left: 50%;
    transform: translate(-50%, -95%) rotate(180deg);
  }

  wui-icon[data-placement='right'] {
    top: 50%;
    left: 0;
    transform: translate(-65%, -50%) rotate(90deg);
  }

  wui-icon[data-placement='left'] {
    top: 50%;
    right: 0%;
    transform: translate(65%, -50%) rotate(270deg);
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-tooltip/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "W3mTooltip": (()=>W3mTooltip)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/state.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$TooltipController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/TooltipController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$flex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-flex.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$icon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-icon.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-text.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$tooltip$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-tooltip/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
;
;
let W3mTooltip = class W3mTooltip extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super();
        this.unsubscribe = [];
        this.open = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$TooltipController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipController"].state.open;
        this.message = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$TooltipController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipController"].state.message;
        this.triggerRect = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$TooltipController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipController"].state.triggerRect;
        this.variant = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$TooltipController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipController"].state.variant;
        this.unsubscribe.push(...[
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$TooltipController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipController"].subscribe((newState)=>{
                this.open = newState.open;
                this.message = newState.message;
                this.triggerRect = newState.triggerRect;
                this.variant = newState.variant;
            })
        ]);
    }
    disconnectedCallback() {
        this.unsubscribe.forEach((unsubscribe)=>unsubscribe());
    }
    render() {
        this.dataset['variant'] = this.variant;
        const topValue = this.triggerRect.top;
        const leftValue = this.triggerRect.left;
        this.style.cssText = `
    --w3m-tooltip-top: ${topValue}px;
    --w3m-tooltip-left: ${leftValue}px;
    --w3m-tooltip-parent-width: ${this.triggerRect.width / 2}px;
    --w3m-tooltip-display: ${this.open ? 'flex' : 'none'};
    --w3m-tooltip-opacity: ${this.open ? 1 : 0};
    `;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<wui-flex>
      <wui-icon data-placement="top" color="fg-100" size="inherit" name="cursor"></wui-icon>
      <wui-text color="inherit" variant="small-500">${this.message}</wui-text>
    </wui-flex>`;
    }
};
W3mTooltip.styles = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$tooltip$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mTooltip.prototype, "open", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mTooltip.prototype, "message", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mTooltip.prototype, "triggerRect", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mTooltip.prototype, "variant", void 0);
W3mTooltip = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('w3m-tooltip'),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('w3m-tooltip')
], W3mTooltip);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-router/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    --prev-height: 0px;
    --new-height: 0px;
    display: block;
  }

  div.w3m-router-container {
    transform: translateY(0);
    opacity: 1;
  }

  div.w3m-router-container[view-direction='prev'] {
    animation:
      slide-left-out 150ms forwards ease,
      slide-left-in 150ms forwards ease;
    animation-delay: 0ms, 200ms;
  }

  div.w3m-router-container[view-direction='next'] {
    animation:
      slide-right-out 150ms forwards ease,
      slide-right-in 150ms forwards ease;
    animation-delay: 0ms, 200ms;
  }

  @keyframes slide-left-out {
    from {
      transform: translateX(0px);
      opacity: 1;
    }
    to {
      transform: translateX(10px);
      opacity: 0;
    }
  }

  @keyframes slide-left-in {
    from {
      transform: translateX(-10px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slide-right-out {
    from {
      transform: translateX(0px);
      opacity: 1;
    }
    to {
      transform: translateX(-10px);
      opacity: 0;
    }
  }

  @keyframes slide-right-in {
    from {
      transform: translateX(10px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-router/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "W3mRouter": (()=>W3mRouter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/state.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/RouterController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$TooltipController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/TooltipController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ConstantsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/utils/ConstantsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$modal$2f$w3m$2d$router$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-router/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
let W3mRouter = class W3mRouter extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super();
        this.resizeObserver = undefined;
        this.prevHeight = '0px';
        this.prevHistoryLength = 1;
        this.unsubscribe = [];
        this.view = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.view;
        this.viewDirection = '';
        this.unsubscribe.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].subscribeKey('view', (val)=>this.onViewChange(val)));
    }
    firstUpdated() {
        this.resizeObserver = new ResizeObserver(([content])=>{
            const height = `${content?.contentRect.height}px`;
            if (this.prevHeight !== '0px') {
                this.style.setProperty('--prev-height', this.prevHeight);
                this.style.setProperty('--new-height', height);
                this.style.animation = 'w3m-view-height 150ms forwards ease';
                this.style.height = 'auto';
            }
            setTimeout(()=>{
                this.prevHeight = height;
                this.style.animation = 'unset';
            }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ConstantsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConstantsUtil"].ANIMATION_DURATIONS.ModalHeight);
        });
        this.resizeObserver?.observe(this.getWrapper());
    }
    disconnectedCallback() {
        this.resizeObserver?.unobserve(this.getWrapper());
        this.unsubscribe.forEach((unsubscribe)=>unsubscribe());
    }
    render() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<div class="w3m-router-container" view-direction="${this.viewDirection}">
      ${this.viewTemplate()}
    </div>`;
    }
    viewTemplate() {
        switch(this.view){
            case 'AccountSettings':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-account-settings-view></w3m-account-settings-view>`;
            case 'Account':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-account-view></w3m-account-view>`;
            case 'AllWallets':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-all-wallets-view></w3m-all-wallets-view>`;
            case 'ApproveTransaction':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-approve-transaction-view></w3m-approve-transaction-view>`;
            case 'BuyInProgress':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-buy-in-progress-view></w3m-buy-in-progress-view>`;
            case 'ChooseAccountName':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-choose-account-name-view></w3m-choose-account-name-view>`;
            case 'Connect':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connect-view></w3m-connect-view>`;
            case 'Create':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connect-view walletGuide="explore"></w3m-connect-view>`;
            case 'ConnectingWalletConnect':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connecting-wc-view></w3m-connecting-wc-view>`;
            case 'ConnectingWalletConnectBasic':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connecting-wc-basic-view></w3m-connecting-wc-basic-view>`;
            case 'ConnectingExternal':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connecting-external-view></w3m-connecting-external-view>`;
            case 'ConnectingSiwe':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connecting-siwe-view></w3m-connecting-siwe-view>`;
            case 'ConnectWallets':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connect-wallets-view></w3m-connect-wallets-view>`;
            case 'ConnectSocials':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connect-socials-view></w3m-connect-socials-view>`;
            case 'ConnectingSocial':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connecting-social-view></w3m-connecting-social-view>`;
            case 'Downloads':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-downloads-view></w3m-downloads-view>`;
            case 'EmailLogin':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-email-login-view></w3m-email-login-view>`;
            case 'EmailVerifyOtp':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-email-verify-otp-view></w3m-email-verify-otp-view>`;
            case 'EmailVerifyDevice':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-email-verify-device-view></w3m-email-verify-device-view>`;
            case 'GetWallet':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-get-wallet-view></w3m-get-wallet-view>`;
            case 'Networks':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-networks-view></w3m-networks-view>`;
            case 'SwitchNetwork':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-network-switch-view></w3m-network-switch-view>`;
            case 'Profile':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-profile-view></w3m-profile-view>`;
            case 'SwitchAddress':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-switch-address-view></w3m-switch-address-view>`;
            case 'Transactions':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-transactions-view></w3m-transactions-view>`;
            case 'OnRampProviders':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-onramp-providers-view></w3m-onramp-providers-view>`;
            case 'OnRampActivity':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-onramp-activity-view></w3m-onramp-activity-view>`;
            case 'OnRampTokenSelect':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-onramp-token-select-view></w3m-onramp-token-select-view>`;
            case 'OnRampFiatSelect':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-onramp-fiat-select-view></w3m-onramp-fiat-select-view>`;
            case 'UpgradeEmailWallet':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-upgrade-wallet-view></w3m-upgrade-wallet-view>`;
            case 'UpdateEmailWallet':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-update-email-wallet-view></w3m-update-email-wallet-view>`;
            case 'UpdateEmailPrimaryOtp':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-update-email-primary-otp-view></w3m-update-email-primary-otp-view>`;
            case 'UpdateEmailSecondaryOtp':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-update-email-secondary-otp-view></w3m-update-email-secondary-otp-view>`;
            case 'UnsupportedChain':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-unsupported-chain-view></w3m-unsupported-chain-view>`;
            case 'Swap':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-swap-view></w3m-swap-view>`;
            case 'SwapSelectToken':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-swap-select-token-view></w3m-swap-select-token-view>`;
            case 'SwapPreview':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-swap-preview-view></w3m-swap-preview-view>`;
            case 'WalletSend':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-wallet-send-view></w3m-wallet-send-view>`;
            case 'WalletSendSelectToken':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-wallet-send-select-token-view></w3m-wallet-send-select-token-view>`;
            case 'WalletSendPreview':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-wallet-send-preview-view></w3m-wallet-send-preview-view>`;
            case 'WhatIsABuy':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-what-is-a-buy-view></w3m-what-is-a-buy-view>`;
            case 'WalletReceive':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-wallet-receive-view></w3m-wallet-receive-view>`;
            case 'WalletCompatibleNetworks':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-wallet-compatible-networks-view></w3m-wallet-compatible-networks-view>`;
            case 'WhatIsAWallet':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-what-is-a-wallet-view></w3m-what-is-a-wallet-view>`;
            case 'ConnectingMultiChain':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connecting-multi-chain-view></w3m-connecting-multi-chain-view>`;
            case 'WhatIsANetwork':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-what-is-a-network-view></w3m-what-is-a-network-view>`;
            case 'ConnectingFarcaster':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connecting-farcaster-view></w3m-connecting-farcaster-view>`;
            case 'SwitchActiveChain':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-switch-active-chain-view></w3m-switch-active-chain-view>`;
            case 'RegisterAccountName':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-register-account-name-view></w3m-register-account-name-view>`;
            case 'RegisterAccountNameSuccess':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-register-account-name-success-view></w3m-register-account-name-success-view>`;
            case 'SmartSessionCreated':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-smart-session-created-view></w3m-smart-session-created-view>`;
            case 'SmartSessionList':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-smart-session-list-view></w3m-smart-session-list-view>`;
            case 'SIWXSignMessage':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-siwx-sign-message-view></w3m-siwx-sign-message-view>`;
            case 'Pay':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-pay-view></w3m-pay-view>`;
            case 'PayLoading':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-pay-loading-view></w3m-pay-loading-view>`;
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`<w3m-connect-view></w3m-connect-view>`;
        }
    }
    onViewChange(newView) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$TooltipController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipController"].hide();
        let direction = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ConstantsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConstantsUtil"].VIEW_DIRECTION.Next;
        const { history } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state;
        if (history.length < this.prevHistoryLength) {
            direction = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ConstantsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConstantsUtil"].VIEW_DIRECTION.Prev;
        }
        this.prevHistoryLength = history.length;
        this.viewDirection = direction;
        setTimeout(()=>{
            this.view = newView;
        }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ConstantsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConstantsUtil"].ANIMATION_DURATIONS.ViewTransition);
    }
    getWrapper() {
        return this.shadowRoot?.querySelector('div');
    }
};
W3mRouter.styles = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$modal$2f$w3m$2d$router$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mRouter.prototype, "view", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mRouter.prototype, "viewDirection", void 0);
W3mRouter = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('w3m-router')
], W3mRouter);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-modal/styles.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/css-tag.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$css$2d$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  :host {
    z-index: var(--w3m-z-index);
    display: block;
    backface-visibility: hidden;
    will-change: opacity;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    opacity: 0;
    background-color: var(--wui-cover);
    transition: opacity 0.2s var(--wui-ease-out-power-2);
    will-change: opacity;
  }

  :host(.open) {
    opacity: 1;
  }

  :host(.appkit-modal) {
    position: relative;
    pointer-events: unset;
    background: none;
    width: 100%;
    opacity: 1;
  }

  wui-card {
    max-width: var(--w3m-modal-width);
    width: 100%;
    position: relative;
    animation: zoom-in 0.2s var(--wui-ease-out-power-2);
    animation-fill-mode: backwards;
    outline: none;
    transition:
      border-radius var(--wui-duration-lg) var(--wui-ease-out-power-1),
      background-color var(--wui-duration-lg) var(--wui-ease-out-power-1);
    will-change: border-radius, background-color;
  }

  :host(.appkit-modal) wui-card {
    max-width: 400px;
  }

  wui-card[shake='true'] {
    animation:
      zoom-in 0.2s var(--wui-ease-out-power-2),
      w3m-shake 0.5s var(--wui-ease-out-power-2);
  }

  wui-flex {
    overflow-x: hidden;
    overflow-y: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  @media (max-height: 700px) and (min-width: 431px) {
    wui-flex {
      align-items: flex-start;
    }

    wui-card {
      margin: var(--wui-spacing-xxl) 0px;
    }
  }

  @media (max-width: 430px) {
    wui-flex {
      align-items: flex-end;
    }

    wui-card {
      max-width: 100%;
      border-bottom-left-radius: var(--local-border-bottom-mobile-radius);
      border-bottom-right-radius: var(--local-border-bottom-mobile-radius);
      border-bottom: none;
      animation: slide-in 0.2s var(--wui-ease-out-power-2);
    }

    wui-card[shake='true'] {
      animation:
        slide-in 0.2s var(--wui-ease-out-power-2),
        w3m-shake 0.5s var(--wui-ease-out-power-2);
    }
  }

  @keyframes zoom-in {
    0% {
      transform: scale(0.95) translateY(0);
    }
    100% {
      transform: scale(1) translateY(0);
    }
  }

  @keyframes slide-in {
    0% {
      transform: scale(1) translateY(50px);
    }
    100% {
      transform: scale(1) translateY(0);
    }
  }

  @keyframes w3m-shake {
    0% {
      transform: scale(1) rotate(0deg);
    }
    20% {
      transform: scale(1) rotate(-1deg);
    }
    40% {
      transform: scale(1) rotate(1.5deg);
    }
    60% {
      transform: scale(1) rotate(-1.5deg);
    }
    80% {
      transform: scale(1) rotate(1deg);
    }
    100% {
      transform: scale(1) rotate(0deg);
    }
  }

  @keyframes w3m-view-height {
    from {
      height: var(--prev-height);
    }
    to {
      height: var(--new-height);
    }
  }
`;
 //# sourceMappingURL=styles.js.map
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-modal/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AppKitModal": (()=>AppKitModal),
    "W3mModal": (()=>W3mModal),
    "W3mModalBase": (()=>W3mModalBase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/lit-element/development/lit-element.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/lit-html.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$decorators$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/decorators.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/property.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lit/reactive-element/node/development/decorators/state.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2f$directives$2f$if$2d$defined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/lit/directives/if-defined.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$if$2d$defined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lit-html/node/development/directives/if-defined.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$common$2f$dist$2f$esm$2f$src$2f$utils$2f$ConstantsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-common/dist/esm/src/utils/ConstantsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ApiController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ApiController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ChainController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ConnectorController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ConnectorController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$CoreHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/utils/CoreHelperUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ModalController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$ModalUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/utils/ModalUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$OptionsController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/OptionsController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/RouterController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$SIWXUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/utils/SIWXUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$SnackController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/SnackController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ThemeController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/dist/esm/src/controllers/ThemeController.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$UiHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/UiHelperUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/WebComponentsUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/src/utils/ThemeUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-card.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$exports$2f$wui$2d$flex$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-ui/dist/esm/exports/wui-flex.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$alertbar$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-alertbar/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$header$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-header/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$snackbar$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-snackbar/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$partials$2f$w3m$2d$tooltip$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/partials/w3m-tooltip/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$modal$2f$w3m$2d$router$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-router/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$modal$2f$w3m$2d$modal$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-modal/styles.js [app-ssr] (ecmascript)");
var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const SCROLL_LOCK = 'scroll-lock';
class W3mModalBase extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$element$2f$development$2f$lit$2d$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LitElement"] {
    constructor(){
        super();
        this.unsubscribe = [];
        this.abortController = undefined;
        this.hasPrefetched = false;
        this.enableEmbedded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$OptionsController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OptionsController"].state.enableEmbedded;
        this.open = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalController"].state.open;
        this.caipAddress = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainController"].state.activeCaipAddress;
        this.caipNetwork = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainController"].state.activeCaipNetwork;
        this.shake = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalController"].state.shake;
        this.filterByNamespace = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ConnectorController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConnectorController"].state.filterByNamespace;
        this.initializeTheming();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ApiController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiController"].prefetchAnalyticsConfig();
        this.unsubscribe.push(...[
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalController"].subscribeKey('open', (val)=>val ? this.onOpen() : this.onClose()),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalController"].subscribeKey('shake', (val)=>this.shake = val),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainController"].subscribeKey('activeCaipNetwork', (val)=>this.onNewNetwork(val)),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainController"].subscribeKey('activeCaipAddress', (val)=>this.onNewAddress(val)),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$OptionsController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OptionsController"].subscribeKey('enableEmbedded', (val)=>this.enableEmbedded = val),
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ConnectorController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConnectorController"].subscribeKey('filterByNamespace', (val)=>{
                if (this.filterByNamespace !== val && !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainController"].getAccountData(val)?.caipAddress) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ApiController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiController"].fetchRecommendedWallets();
                    this.filterByNamespace = val;
                }
            })
        ]);
    }
    firstUpdated() {
        if (this.caipAddress) {
            if (this.enableEmbedded) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalController"].close();
                this.prefetch();
                return;
            }
            this.onNewAddress(this.caipAddress);
        }
        if (this.open) {
            this.onOpen();
        }
        if (this.enableEmbedded) {
            this.prefetch();
        }
    }
    disconnectedCallback() {
        this.unsubscribe.forEach((unsubscribe)=>unsubscribe());
        this.onRemoveKeyboardListener();
    }
    render() {
        this.style.cssText = `
      --local-border-bottom-mobile-radius: ${this.enableEmbedded ? 'clamp(0px, var(--wui-border-radius-l), 44px)' : '0px'};
    `;
        if (this.enableEmbedded) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`${this.contentTemplate()}
        <w3m-tooltip></w3m-tooltip> `;
        }
        return this.open ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]`
          <wui-flex @click=${this.onOverlayClick.bind(this)} data-testid="w3m-modal-overlay">
            ${this.contentTemplate()}
          </wui-flex>
          <w3m-tooltip></w3m-tooltip>
        ` : null;
    }
    contentTemplate() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$lit$2d$html$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["html"]` <wui-card
      shake="${this.shake}"
      data-embedded="${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lit$2d$html$2f$node$2f$development$2f$directives$2f$if$2d$defined$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ifDefined"])(this.enableEmbedded)}"
      role="alertdialog"
      aria-modal="true"
      tabindex="0"
      data-testid="w3m-modal-card"
    >
      <w3m-header></w3m-header>
      <w3m-router></w3m-router>
      <w3m-snackbar></w3m-snackbar>
      <w3m-alertbar></w3m-alertbar>
    </wui-card>`;
    }
    async onOverlayClick(event) {
        if (event.target === event.currentTarget) {
            await this.handleClose();
        }
    }
    async handleClose() {
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$ModalUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalUtil"].safeClose();
    }
    initializeTheming() {
        const { themeVariables, themeMode } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ThemeController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ThemeController"].state;
        const defaultThemeMode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$UiHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UiHelperUtil"].getColorTheme(themeMode);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$ThemeUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initializeTheming"])(themeVariables, defaultThemeMode);
    }
    onClose() {
        this.open = false;
        this.classList.remove('open');
        this.onScrollUnlock();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$SnackController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SnackController"].hide();
        this.onRemoveKeyboardListener();
    }
    onOpen() {
        this.open = true;
        this.classList.add('open');
        this.onScrollLock();
        this.onAddKeyboardListener();
    }
    onScrollLock() {
        const styleTag = document.createElement('style');
        styleTag.dataset['w3m'] = SCROLL_LOCK;
        styleTag.textContent = `
      body {
        touch-action: none;
        overflow: hidden;
        overscroll-behavior: contain;
      }
      w3m-modal {
        pointer-events: auto;
      }
    `;
        document.head.appendChild(styleTag);
    }
    onScrollUnlock() {
        const styleTag = document.head.querySelector(`style[data-w3m="${SCROLL_LOCK}"]`);
        if (styleTag) {
            styleTag.remove();
        }
    }
    onAddKeyboardListener() {
        this.abortController = new AbortController();
        const card = this.shadowRoot?.querySelector('wui-card');
        card?.focus();
        window.addEventListener('keydown', (event)=>{
            if (event.key === 'Escape') {
                this.handleClose();
            } else if (event.key === 'Tab') {
                const { tagName } = event.target;
                if (tagName && !tagName.includes('W3M-') && !tagName.includes('WUI-')) {
                    card?.focus();
                }
            }
        }, this.abortController);
    }
    onRemoveKeyboardListener() {
        this.abortController?.abort();
        this.abortController = undefined;
    }
    async onNewAddress(caipAddress) {
        const isSwitchingNamespace = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainController"].state.isSwitchingNamespace;
        const nextConnected = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$CoreHelperUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CoreHelperUtil"].getPlainAddress(caipAddress);
        const isDisconnectedInSameNamespace = !nextConnected && !isSwitchingNamespace;
        const isSwitchingNamespaceAndConnected = isSwitchingNamespace && nextConnected;
        if (isDisconnectedInSameNamespace) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalController"].close();
        } else if (isSwitchingNamespaceAndConnected) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].goBack();
        }
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$utils$2f$SIWXUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SIWXUtil"].initializeIfEnabled();
        this.caipAddress = caipAddress;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainController"].setIsSwitchingNamespace(false);
    }
    onNewNetwork(nextCaipNetwork) {
        const prevCaipNetwork = this.caipNetwork;
        const prevCaipNetworkId = prevCaipNetwork?.caipNetworkId?.toString();
        const prevChainNamespace = prevCaipNetwork?.chainNamespace;
        const nextNetworkId = nextCaipNetwork?.caipNetworkId?.toString();
        const nextChainNamespace = nextCaipNetwork?.chainNamespace;
        const networkIdChanged = prevCaipNetworkId !== nextNetworkId;
        const namespaceChanged = prevChainNamespace !== nextChainNamespace;
        const isNetworkChangedInSameNamespace = networkIdChanged && !namespaceChanged;
        const wasUnsupportedNetwork = prevCaipNetwork?.name === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$common$2f$dist$2f$esm$2f$src$2f$utils$2f$ConstantsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ConstantsUtil"].UNSUPPORTED_NETWORK_NAME;
        const isConnectingExternal = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.view === 'ConnectingExternal';
        const isNotConnected = !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ChainController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainController"].getAccountData(nextCaipNetwork?.chainNamespace)?.caipAddress;
        const isUnsupportedNetworkScreen = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.view === 'UnsupportedChain';
        const isModalOpen = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ModalController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModalController"].state.open;
        let shouldGoBack = false;
        if (isModalOpen && !isConnectingExternal) {
            if (isNotConnected) {
                if (networkIdChanged) {
                    shouldGoBack = true;
                }
            } else if (isUnsupportedNetworkScreen) {
                shouldGoBack = true;
            } else if (isNetworkChangedInSameNamespace && !wasUnsupportedNetwork) {
                shouldGoBack = true;
            }
        }
        if (shouldGoBack && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].state.view !== 'SIWXSignMessage') {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$RouterController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RouterController"].goBack();
        }
        this.caipNetwork = nextCaipNetwork;
    }
    prefetch() {
        if (!this.hasPrefetched) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ApiController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiController"].prefetch();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$dist$2f$esm$2f$src$2f$controllers$2f$ApiController$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiController"].fetchWalletsByPage({
                page: 1
            });
            this.hasPrefetched = true;
        }
    }
}
W3mModalBase.styles = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$modal$2f$w3m$2d$modal$2f$styles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$property$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["property"])({
        type: Boolean
    })
], W3mModalBase.prototype, "enableEmbedded", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mModalBase.prototype, "open", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mModalBase.prototype, "caipAddress", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mModalBase.prototype, "caipNetwork", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mModalBase.prototype, "shake", void 0);
__decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lit$2f$reactive$2d$element$2f$node$2f$development$2f$decorators$2f$state$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["state"])()
], W3mModalBase.prototype, "filterByNamespace", void 0);
let W3mModal = class W3mModal extends W3mModalBase {
};
W3mModal = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('w3m-modal')
], W3mModal);
;
let AppKitModal = class AppKitModal extends W3mModalBase {
};
AppKitModal = __decorate([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$ui$2f$dist$2f$esm$2f$src$2f$utils$2f$WebComponentsUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["customElement"])('appkit-modal')
], AppKitModal);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$modal$2f$w3m$2d$modal$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-modal/index.js [app-ssr] (ecmascript)"); //# sourceMappingURL=w3m-modal.js.map
;
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$modal$2f$w3m$2d$modal$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-modal/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$exports$2f$w3m$2d$modal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AppKitModal": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$modal$2f$w3m$2d$modal$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AppKitModal"]),
    "W3mModal": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$modal$2f$w3m$2d$modal$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["W3mModal"]),
    "W3mModalBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$modal$2f$w3m$2d$modal$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["W3mModalBase"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$src$2f$modal$2f$w3m$2d$modal$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/src/modal/w3m-modal/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$exports$2f$w3m$2d$modal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AppKitModal": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$exports$2f$w3m$2d$modal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["AppKitModal"]),
    "W3mModal": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$exports$2f$w3m$2d$modal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["W3mModal"]),
    "W3mModalBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$exports$2f$w3m$2d$modal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["W3mModalBase"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$exports$2f$w3m$2d$modal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$scaffold$2d$ui$2f$dist$2f$esm$2f$exports$2f$w3m$2d$modal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-ssr] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=node_modules_dd3fd160._.js.map