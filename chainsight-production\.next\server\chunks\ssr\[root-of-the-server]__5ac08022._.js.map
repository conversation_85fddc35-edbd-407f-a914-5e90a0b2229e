{"version": 3, "sources": [], "sections": [{"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9e72d27f-module__JKMi0a__className\",\n  \"variable\": \"inter_9e72d27f-module__JKMi0a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Component, ErrorInfo, ReactNode } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error: Error | null;\n  errorInfo: ErrorInfo | null;\n}\n\nexport class ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null,\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return {\n      hasError: true,\n      error,\n      errorInfo: null,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error('🚨 Chainsight Error Boundary Caught:', error);\n    console.error('📍 Error Info:', errorInfo);\n    \n    this.setState({\n      error,\n      errorInfo,\n    });\n\n    // Log to external service in production\n    if (process.env.NODE_ENV === 'production') {\n      // You can integrate with error reporting services here\n      console.error('Production Error:', {\n        error: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        timestamp: new Date().toISOString(),\n      });\n    }\n  }\n\n  handleReload = () => {\n    window.location.reload();\n  };\n\n  handleGoHome = () => {\n    window.location.href = '/';\n  };\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      return (\n        <div className=\"min-h-screen bg-gradient-to-br from-cosmic-dark via-cosmic-mid to-cosmic-light flex items-center justify-center p-4\">\n          <div className=\"max-w-2xl w-full\">\n            {/* Error Card */}\n            <div className=\"glass rounded-2xl p-8 text-center\">\n              <div className=\"flex justify-center mb-6\">\n                <div className=\"p-4 bg-red-500/20 rounded-full\">\n                  <AlertTriangle className=\"w-12 h-12 text-red-400\" />\n                </div>\n              </div>\n\n              <h1 className=\"text-3xl font-bold text-white mb-4\">\n                Oops! Something went wrong\n              </h1>\n              \n              <p className=\"text-gray-300 mb-8 text-lg\">\n                The Chainsight platform encountered an unexpected error. \n                Don't worry, our AI agents are working to fix this!\n              </p>\n\n              {/* Error Details (Development Only) */}\n              {process.env.NODE_ENV === 'development' && this.state.error && (\n                <div className=\"mb-8 p-4 bg-red-900/20 rounded-lg border border-red-500/30 text-left\">\n                  <h3 className=\"text-red-400 font-semibold mb-2 flex items-center\">\n                    <Bug className=\"w-4 h-4 mr-2\" />\n                    Development Error Details:\n                  </h3>\n                  <pre className=\"text-sm text-red-300 overflow-auto max-h-40\">\n                    {this.state.error.message}\n                  </pre>\n                  {this.state.error.stack && (\n                    <details className=\"mt-2\">\n                      <summary className=\"text-red-400 cursor-pointer\">Stack Trace</summary>\n                      <pre className=\"text-xs text-red-300 mt-2 overflow-auto max-h-32\">\n                        {this.state.error.stack}\n                      </pre>\n                    </details>\n                  )}\n                </div>\n              )}\n\n              {/* Action Buttons */}\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <button\n                  onClick={this.handleReload}\n                  className=\"flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105\"\n                >\n                  <RefreshCw className=\"w-5 h-5 mr-2\" />\n                  Reload Platform\n                </button>\n                \n                <button\n                  onClick={this.handleGoHome}\n                  className=\"flex items-center justify-center px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105\"\n                >\n                  <Home className=\"w-5 h-5 mr-2\" />\n                  Go Home\n                </button>\n              </div>\n\n              {/* Support Info */}\n              <div className=\"mt-8 p-4 bg-blue-900/20 rounded-lg border border-blue-500/30\">\n                <p className=\"text-blue-300 text-sm\">\n                  If this error persists, please contact our support team with the error details above.\n                </p>\n              </div>\n            </div>\n\n            {/* Background Effects */}\n            <div className=\"absolute inset-0 -z-10\">\n              <div className=\"absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full blur-3xl\"></div>\n              <div className=\"absolute bottom-1/4 right-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl\"></div>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// Hook version for functional components\nexport function useErrorHandler() {\n  const handleError = (error: Error, errorInfo?: any) => {\n    console.error('🚨 Chainsight Error Handler:', error);\n    if (errorInfo) {\n      console.error('📍 Error Context:', errorInfo);\n    }\n  };\n\n  return { handleError };\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAgBO,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IAC1C,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YACX,UAAU;YACV,OAAO;YACP,WAAW;QACb;IACF;IAEA,OAAO,yBAAyB,KAAY,EAAS;QACnD,OAAO;YACL,UAAU;YACV;YACA,WAAW;QACb;IACF;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAE;QACpD,QAAQ,KAAK,CAAC,wCAAwC;QACtD,QAAQ,KAAK,CAAC,kBAAkB;QAEhC,IAAI,CAAC,QAAQ,CAAC;YACZ;YACA;QACF;QAEA,wCAAwC;QACxC,uCAA2C;;QAQ3C;IACF;IAEA,eAAe;QACb,OAAO,QAAQ,CAAC,MAAM;IACxB,EAAE;IAEF,eAAe;QACb,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAI7B,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CAInD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;gCAMzC,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;sDACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;;;;;;wCAE1B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,kBACrB,8OAAC;4CAAQ,WAAU;;8DACjB,8OAAC;oDAAQ,WAAU;8DAA8B;;;;;;8DACjD,8OAAC;oDAAI,WAAU;8DACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;;;;;;;;;;;;;;;;;;8CAQjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAI,CAAC,YAAY;4CAC1B,WAAU;;8DAEV,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAIxC,8OAAC;4CACC,SAAS,IAAI,CAAC,YAAY;4CAC1B,WAAU;;8DAEV,8OAAC,mMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAMrC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAOzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;QAKzB;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAC,OAAc;QACjC,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,qBAAqB;QACrC;IACF;IAEA,OAAO;QAAE;IAAY;AACvB", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/ConsoleErrorDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { AlertTriangle, X, ChevronDown, ChevronUp } from 'lucide-react';\n\ninterface ConsoleError {\n  id: string;\n  message: string;\n  stack?: string;\n  timestamp: Date;\n  type: 'error' | 'warning' | 'info';\n}\n\nexport function ConsoleErrorDisplay() {\n  const [errors, setErrors] = useState<ConsoleError[]>([]);\n  const [isVisible, setIsVisible] = useState(false);\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  useEffect(() => {\n    // Only show in development\n    if (process.env.NODE_ENV !== 'development') return;\n\n    const errorHandler = (event: ErrorEvent) => {\n      const newError: ConsoleError = {\n        id: Date.now().toString(),\n        message: event.message,\n        stack: event.error?.stack,\n        timestamp: new Date(),\n        type: 'error',\n      };\n\n      setErrors(prev => [...prev.slice(-9), newError]); // Keep last 10 errors\n      setIsVisible(true);\n    };\n\n    const rejectionHandler = (event: PromiseRejectionEvent) => {\n      const newError: ConsoleError = {\n        id: Date.now().toString(),\n        message: `Unhandled Promise Rejection: ${event.reason}`,\n        timestamp: new Date(),\n        type: 'error',\n      };\n\n      setErrors(prev => [...prev.slice(-9), newError]);\n      setIsVisible(true);\n    };\n\n    // Override console methods to catch warnings and errors\n    const originalError = console.error;\n    const originalWarn = console.warn;\n\n    console.error = (...args) => {\n      const message = args.map(arg => \n        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n      ).join(' ');\n\n      if (!message.includes('Chainsight Debug')) { // Avoid recursive logging\n        const newError: ConsoleError = {\n          id: Date.now().toString(),\n          message,\n          timestamp: new Date(),\n          type: 'error',\n        };\n\n        setErrors(prev => [...prev.slice(-9), newError]);\n        setIsVisible(true);\n      }\n\n      originalError.apply(console, args);\n    };\n\n    console.warn = (...args) => {\n      const message = args.map(arg => \n        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n      ).join(' ');\n\n      const newError: ConsoleError = {\n        id: Date.now().toString(),\n        message,\n        timestamp: new Date(),\n        type: 'warning',\n      };\n\n      setErrors(prev => [...prev.slice(-9), newError]);\n      setIsVisible(true);\n\n      originalWarn.apply(console, args);\n    };\n\n    window.addEventListener('error', errorHandler);\n    window.addEventListener('unhandledrejection', rejectionHandler);\n\n    return () => {\n      window.removeEventListener('error', errorHandler);\n      window.removeEventListener('unhandledrejection', rejectionHandler);\n      console.error = originalError;\n      console.warn = originalWarn;\n    };\n  }, []);\n\n  const clearErrors = () => {\n    setErrors([]);\n    setIsVisible(false);\n  };\n\n  const getErrorColor = (type: string) => {\n    switch (type) {\n      case 'error': return 'text-red-400 bg-red-500/10 border-red-500/20';\n      case 'warning': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20';\n      default: return 'text-blue-400 bg-blue-500/10 border-blue-500/20';\n    }\n  };\n\n  if (!isVisible || errors.length === 0) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0, y: 50 }}\n        animate={{ opacity: 1, y: 0 }}\n        exit={{ opacity: 0, y: 50 }}\n        className=\"fixed bottom-4 right-4 z-50 max-w-md\"\n      >\n        <div className=\"bg-cosmic-dark/95 backdrop-blur-xl border border-red-500/30 rounded-lg shadow-2xl\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-3 border-b border-red-500/20\">\n            <div className=\"flex items-center space-x-2\">\n              <AlertTriangle className=\"w-5 h-5 text-red-400\" />\n              <span className=\"text-red-400 font-semibold\">\n                Console Errors ({errors.length})\n              </span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <button\n                onClick={() => setIsExpanded(!isExpanded)}\n                className=\"p-1 hover:bg-white/10 rounded transition-colors\"\n              >\n                {isExpanded ? (\n                  <ChevronUp className=\"w-4 h-4 text-gray-400\" />\n                ) : (\n                  <ChevronDown className=\"w-4 h-4 text-gray-400\" />\n                )}\n              </button>\n              <button\n                onClick={clearErrors}\n                className=\"p-1 hover:bg-white/10 rounded transition-colors\"\n              >\n                <X className=\"w-4 h-4 text-gray-400\" />\n              </button>\n            </div>\n          </div>\n\n          {/* Error List */}\n          <AnimatePresence>\n            {isExpanded && (\n              <motion.div\n                initial={{ height: 0 }}\n                animate={{ height: 'auto' }}\n                exit={{ height: 0 }}\n                className=\"overflow-hidden\"\n              >\n                <div className=\"max-h-64 overflow-y-auto p-3 space-y-2\">\n                  {errors.map((error) => (\n                    <div\n                      key={error.id}\n                      className={`p-2 rounded border ${getErrorColor(error.type)}`}\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1 min-w-0\">\n                          <p className=\"text-sm font-medium truncate\">\n                            {error.type.toUpperCase()}\n                          </p>\n                          <p className=\"text-xs text-gray-300 mt-1 break-words\">\n                            {error.message}\n                          </p>\n                          {error.stack && (\n                            <details className=\"mt-1\">\n                              <summary className=\"text-xs text-gray-400 cursor-pointer\">\n                                Stack trace\n                              </summary>\n                              <pre className=\"text-xs text-gray-400 mt-1 overflow-auto max-h-20\">\n                                {error.stack}\n                              </pre>\n                            </details>\n                          )}\n                        </div>\n                        <span className=\"text-xs text-gray-500 ml-2 flex-shrink-0\">\n                          {error.timestamp.toLocaleTimeString()}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Quick Summary */}\n          {!isExpanded && errors.length > 0 && (\n            <div className=\"p-3\">\n              <p className=\"text-sm text-gray-300\">\n                Latest: {errors[errors.length - 1]?.message.slice(0, 50)}\n                {errors[errors.length - 1]?.message.length > 50 ? '...' : ''}\n              </p>\n            </div>\n          )}\n        </div>\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAcO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2BAA2B;QAC3B,uCAA4C;;QAAM;QAElD,MAAM,eAAe,CAAC;YACpB,MAAM,WAAyB;gBAC7B,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK,EAAE;gBACpB,WAAW,IAAI;gBACf,MAAM;YACR;YAEA,UAAU,CAAA,OAAQ;uBAAI,KAAK,KAAK,CAAC,CAAC;oBAAI;iBAAS,GAAG,sBAAsB;YACxE,aAAa;QACf;QAEA,MAAM,mBAAmB,CAAC;YACxB,MAAM,WAAyB;gBAC7B,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,SAAS,CAAC,6BAA6B,EAAE,MAAM,MAAM,EAAE;gBACvD,WAAW,IAAI;gBACf,MAAM;YACR;YAEA,UAAU,CAAA,OAAQ;uBAAI,KAAK,KAAK,CAAC,CAAC;oBAAI;iBAAS;YAC/C,aAAa;QACf;QAEA,wDAAwD;QACxD,MAAM,gBAAgB,QAAQ,KAAK;QACnC,MAAM,eAAe,QAAQ,IAAI;QAEjC,QAAQ,KAAK,GAAG,CAAC,GAAG;YAClB,MAAM,UAAU,KAAK,GAAG,CAAC,CAAA,MACvB,OAAO,QAAQ,WAAW,KAAK,SAAS,CAAC,OAAO,OAAO,MACvD,IAAI,CAAC;YAEP,IAAI,CAAC,QAAQ,QAAQ,CAAC,qBAAqB;gBACzC,MAAM,WAAyB;oBAC7B,IAAI,KAAK,GAAG,GAAG,QAAQ;oBACvB;oBACA,WAAW,IAAI;oBACf,MAAM;gBACR;gBAEA,UAAU,CAAA,OAAQ;2BAAI,KAAK,KAAK,CAAC,CAAC;wBAAI;qBAAS;gBAC/C,aAAa;YACf;YAEA,cAAc,KAAK,CAAC,SAAS;QAC/B;QAEA,QAAQ,IAAI,GAAG,CAAC,GAAG;YACjB,MAAM,UAAU,KAAK,GAAG,CAAC,CAAA,MACvB,OAAO,QAAQ,WAAW,KAAK,SAAS,CAAC,OAAO,OAAO,MACvD,IAAI,CAAC;YAEP,MAAM,WAAyB;gBAC7B,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB;gBACA,WAAW,IAAI;gBACf,MAAM;YACR;YAEA,UAAU,CAAA,OAAQ;uBAAI,KAAK,KAAK,CAAC,CAAC;oBAAI;iBAAS;YAC/C,aAAa;YAEb,aAAa,KAAK,CAAC,SAAS;QAC9B;QAEA,OAAO,gBAAgB,CAAC,SAAS;QACjC,OAAO,gBAAgB,CAAC,sBAAsB;QAE9C,OAAO;YACL,OAAO,mBAAmB,CAAC,SAAS;YACpC,OAAO,mBAAmB,CAAC,sBAAsB;YACjD,QAAQ,KAAK,GAAG;YAChB,QAAQ,IAAI,GAAG;QACjB;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,UAAU,EAAE;QACZ,aAAa;IACf;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,aAAa,OAAO,MAAM,KAAK,GAAG,OAAO;IAE9C,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC1B,WAAU;sBAEV,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;wCAAK,WAAU;;4CAA6B;4CAC1B,OAAO,MAAM;4CAAC;;;;;;;;;;;;;0CAGnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BACC,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAErB,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAG3B,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMnB,8OAAC,yLAAA,CAAA,kBAAe;kCACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,QAAQ;4BAAE;4BACrB,SAAS;gCAAE,QAAQ;4BAAO;4BAC1B,MAAM;gCAAE,QAAQ;4BAAE;4BAClB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;0CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;wCAEC,WAAW,CAAC,mBAAmB,EAAE,cAAc,MAAM,IAAI,GAAG;kDAE5D,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,MAAM,IAAI,CAAC,WAAW;;;;;;sEAEzB,8OAAC;4DAAE,WAAU;sEACV,MAAM,OAAO;;;;;;wDAEf,MAAM,KAAK,kBACV,8OAAC;4DAAQ,WAAU;;8EACjB,8OAAC;oEAAQ,WAAU;8EAAuC;;;;;;8EAG1D,8OAAC;oEAAI,WAAU;8EACZ,MAAM,KAAK;;;;;;;;;;;;;;;;;;8DAKpB,8OAAC;oDAAK,WAAU;8DACb,MAAM,SAAS,CAAC,kBAAkB;;;;;;;;;;;;uCAvBlC,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;oBAkCxB,CAAC,cAAc,OAAO,MAAM,GAAG,mBAC9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCAC1B,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,EAAE,QAAQ,MAAM,GAAG;gCACpD,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,EAAE,QAAQ,SAAS,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1E", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/app/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { Inter } from \"next/font/google\";\nimport { WagmiProvider } from 'wagmi';\nimport { RainbowKitProvider, getDefaultConfig } from '@rainbow-me/rainbowkit';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { mainnet, polygon, optimism, arbitrum, base } from 'wagmi/chains';\nimport { Toaster } from 'react-hot-toast';\nimport { ErrorBoundary } from '@/components/ErrorBoundary';\nimport { ConsoleErrorDisplay } from '@/components/ConsoleErrorDisplay';\nimport \"./globals.css\";\nimport '@rainbow-me/rainbowkit/styles.css';\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\n// Web3 Configuration\nconst config = getDefaultConfig({\n  appName: 'Chainsight by Connectouch',\n  projectId: process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || 'chainsight-demo',\n  chains: [mainnet, polygon, optimism, arbitrum, base],\n  ssr: true,\n});\n\nconst queryClient = new QueryClient();\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <html lang=\"en\">\n      <head>\n        <title>Chainsight - AI-Powered Blockchain Platform</title>\n        <meta name=\"description\" content=\"Modular fullstack AI agent platform with crypto, Web3, finance, and AI capabilities\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n      </head>\n      <body className={`${inter.className} antialiased`}>\n        <ErrorBoundary>\n          <WagmiProvider config={config}>\n            <QueryClientProvider client={queryClient}>\n              <RainbowKitProvider>\n                {children}\n                <Toaster\n                  position=\"top-right\"\n                  toastOptions={{\n                    duration: 4000,\n                    style: {\n                      background: 'rgba(0, 0, 0, 0.8)',\n                      color: '#fff',\n                      border: '1px solid rgba(255, 255, 255, 0.1)',\n                      backdropFilter: 'blur(10px)',\n                    },\n                  }}\n                />\n                <ConsoleErrorDisplay />\n              </RainbowKitProvider>\n            </QueryClientProvider>\n          </WagmiProvider>\n        </ErrorBoundary>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AATA;;;;;;;;;;;;AAkBA,qBAAqB;AACrB,MAAM,SAAS,CAAA,GAAA,8KAAA,CAAA,mBAAgB,AAAD,EAAE;IAC9B,SAAS;IACT,WAAW,QAAQ,GAAG,CAAC,oCAAoC,IAAI;IAC/D,QAAQ;QAAC,gKAAA,CAAA,UAAO;QAAE,gKAAA,CAAA,UAAO;QAAE,iKAAA,CAAA,WAAQ;QAAE,iKAAA,CAAA,WAAQ;QAAE,6JAAA,CAAA,OAAI;KAAC;IACpD,KAAK;AACP;AAEA,MAAM,cAAc,IAAI,6KAAA,CAAA,cAAW;AAEpB,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;kCAAM;;;;;;kCACP,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAEhC,8OAAC;gBAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,SAAS,CAAC,YAAY,CAAC;0BAC/C,cAAA,8OAAC,mIAAA,CAAA,gBAAa;8BACZ,cAAA,8OAAC,+IAAA,CAAA,gBAAa;wBAAC,QAAQ;kCACrB,cAAA,8OAAC,sLAAA,CAAA,sBAAmB;4BAAC,QAAQ;sCAC3B,cAAA,8OAAC,8KAAA,CAAA,qBAAkB;;oCAChB;kDACD,8OAAC,uJAAA,CAAA,UAAO;wCACN,UAAS;wCACT,cAAc;4CACZ,UAAU;4CACV,OAAO;gDACL,YAAY;gDACZ,OAAO;gDACP,QAAQ;gDACR,gBAAgB;4CAClB;wCACF;;;;;;kDAEF,8OAAC,yIAAA,CAAA,sBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpC", "debugId": null}}]}