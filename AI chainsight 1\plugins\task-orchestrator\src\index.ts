import { EventEmitter } from 'eventemitter3'
import { v4 as uuidv4 } from 'uuid'

export interface Task {
  id: string
  type: string
  payload: any
  status: 'pending' | 'running' | 'completed' | 'failed'
  priority: number
  createdAt: Date
  updatedAt: Date
  result?: any
  error?: string
  retries: number
  maxRetries: number
}

export interface Plugin {
  name: string
  version: string
  description: string
  supportedTasks: string[]
  execute: (task: Task) => Promise<any>
  initialize?: () => Promise<void>
  cleanup?: () => Promise<void>
}

export interface TaskOrchestratorConfig {
  maxConcurrentTasks: number
  defaultRetries: number
  taskTimeout: number
}

export class TaskOrchestrator extends EventEmitter {
  private plugins: Map<string, Plugin> = new Map()
  private tasks: Map<string, Task> = new Map()
  private runningTasks: Set<string> = new Set()
  private config: TaskOrchestratorConfig

  constructor(config: Partial<TaskOrchestratorConfig> = {}) {
    super()
    this.config = {
      maxConcurrentTasks: 10,
      defaultRetries: 3,
      taskTimeout: 30000,
      ...config
    }
  }

  // Plugin Management
  async registerPlugin(plugin: Plugin): Promise<void> {
    if (this.plugins.has(plugin.name)) {
      throw new Error(`Plugin ${plugin.name} is already registered`)
    }

    if (plugin.initialize) {
      await plugin.initialize()
    }

    this.plugins.set(plugin.name, plugin)
    this.emit('plugin:registered', plugin)
  }

  async unregisterPlugin(pluginName: string): Promise<void> {
    const plugin = this.plugins.get(pluginName)
    if (!plugin) {
      throw new Error(`Plugin ${pluginName} not found`)
    }

    if (plugin.cleanup) {
      await plugin.cleanup()
    }

    this.plugins.delete(pluginName)
    this.emit('plugin:unregistered', pluginName)
  }

  getPlugins(): Plugin[] {
    return Array.from(this.plugins.values())
  }

  // Task Management
  createTask(type: string, payload: any, priority: number = 0): string {
    const task: Task = {
      id: uuidv4(),
      type,
      payload,
      status: 'pending',
      priority,
      createdAt: new Date(),
      updatedAt: new Date(),
      retries: 0,
      maxRetries: this.config.defaultRetries
    }

    this.tasks.set(task.id, task)
    this.emit('task:created', task)
    
    // Auto-execute if capacity allows
    this.processQueue()
    
    return task.id
  }

  async executeTask(taskId: string): Promise<any> {
    const task = this.tasks.get(taskId)
    if (!task) {
      throw new Error(`Task ${taskId} not found`)
    }

    if (task.status === 'running') {
      throw new Error(`Task ${taskId} is already running`)
    }

    // Find compatible plugin
    const plugin = this.findPluginForTask(task.type)
    if (!plugin) {
      task.status = 'failed'
      task.error = `No plugin found for task type: ${task.type}`
      task.updatedAt = new Date()
      this.emit('task:failed', task)
      throw new Error(task.error)
    }

    task.status = 'running'
    task.updatedAt = new Date()
    this.runningTasks.add(taskId)
    this.emit('task:started', task)

    try {
      const result = await Promise.race([
        plugin.execute(task),
        this.createTimeout(this.config.taskTimeout)
      ])

      task.status = 'completed'
      task.result = result
      task.updatedAt = new Date()
      this.runningTasks.delete(taskId)
      this.emit('task:completed', task)
      
      // Process next tasks in queue
      this.processQueue()
      
      return result
    } catch (error: any) {
      task.retries++
      task.error = error.message
      task.updatedAt = new Date()
      this.runningTasks.delete(taskId)

      if (task.retries < task.maxRetries) {
        task.status = 'pending'
        this.emit('task:retry', task)
        // Retry after delay
        setTimeout(() => this.processQueue(), 1000 * task.retries)
      } else {
        task.status = 'failed'
        this.emit('task:failed', task)
      }

      throw error
    }
  }

  private async processQueue(): Promise<void> {
    if (this.runningTasks.size >= this.config.maxConcurrentTasks) {
      return
    }

    // Get pending tasks sorted by priority
    const pendingTasks = Array.from(this.tasks.values())
      .filter(task => task.status === 'pending')
      .sort((a, b) => b.priority - a.priority)

    const availableSlots = this.config.maxConcurrentTasks - this.runningTasks.size
    const tasksToExecute = pendingTasks.slice(0, availableSlots)

    for (const task of tasksToExecute) {
      this.executeTask(task.id).catch(() => {
        // Error already handled in executeTask
      })
    }
  }

  private findPluginForTask(taskType: string): Plugin | undefined {
    return Array.from(this.plugins.values())
      .find(plugin => plugin.supportedTasks.includes(taskType))
  }

  private createTimeout(ms: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Task timeout')), ms)
    })
  }

  // Task Queries
  getTask(taskId: string): Task | undefined {
    return this.tasks.get(taskId)
  }

  getTasks(status?: Task['status']): Task[] {
    const tasks = Array.from(this.tasks.values())
    return status ? tasks.filter(task => task.status === status) : tasks
  }

  getTasksByType(type: string): Task[] {
    return Array.from(this.tasks.values())
      .filter(task => task.type === type)
  }

  // Statistics
  getStats() {
    const tasks = Array.from(this.tasks.values())
    return {
      total: tasks.length,
      pending: tasks.filter(t => t.status === 'pending').length,
      running: tasks.filter(t => t.status === 'running').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      failed: tasks.filter(t => t.status === 'failed').length,
      plugins: this.plugins.size,
      runningTasks: this.runningTasks.size,
      maxConcurrentTasks: this.config.maxConcurrentTasks
    }
  }

  // Cleanup
  async shutdown(): Promise<void> {
    // Wait for running tasks to complete or timeout
    const timeout = 10000 // 10 seconds
    const start = Date.now()
    
    while (this.runningTasks.size > 0 && Date.now() - start < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // Cleanup all plugins
    for (const plugin of this.plugins.values()) {
      if (plugin.cleanup) {
        await plugin.cleanup()
      }
    }

    this.plugins.clear()
    this.tasks.clear()
    this.runningTasks.clear()
    this.emit('orchestrator:shutdown')
  }
}

// Export singleton instance
export const taskOrchestrator = new TaskOrchestrator()

// Plugin factory helper
export function createPlugin(
  name: string,
  version: string,
  description: string,
  supportedTasks: string[],
  execute: (task: Task) => Promise<any>,
  options: {
    initialize?: () => Promise<void>
    cleanup?: () => Promise<void>
  } = {}
): Plugin {
  return {
    name,
    version,
    description,
    supportedTasks,
    execute,
    ...options
  }
}
