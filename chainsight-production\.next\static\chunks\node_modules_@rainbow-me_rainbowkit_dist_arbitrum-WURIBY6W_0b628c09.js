(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@rainbow-me/rainbowkit/dist/arbitrum-WURIBY6W.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>arbitrum_default)
});
"use client";
// src/components/RainbowKitProvider/chainIcons/arbitrum.svg
var arbitrum_default = "data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2028%2028%22%3E%3Cg%20transform%3D%22translate(0%2C0)%20scale(0.7)%22%3E%3Cg%20clip-path%3D%22url(%23a)%22%3E%3Cpath%20fill%3D%22%2396BEDC%22%20d%3D%22M0%200h40v40H0z%22%2F%3E%3Cpath%20fill%3D%22url(%23b)%22%20fill-opacity%3D%22.3%22%20d%3D%22M0%200h40v40H0z%22%2F%3E%3Cpath%20fill%3D%22%232D374B%22%20fill-rule%3D%22evenodd%22%20d%3D%22M20%202.5C10.335%202.5%202.5%2010.335%202.5%2020c0%203.293.91%206.373%202.49%209.004L0%2031v9h10v-5.637A17.42%2017.42%200%200%200%2020%2037.5c9.665%200%2017.5-7.835%2017.5-17.5S29.665%202.5%2020%202.5Z%22%20clip-rule%3D%22evenodd%22%2F%3E%3Cmask%20id%3D%22c%22%20width%3D%2238%22%20height%3D%2238%22%20x%3D%220%22%20y%3D%222%22%20maskUnits%3D%22userSpaceOnUse%22%20style%3D%22mask-type%3Aalpha%22%3E%3Cpath%20fill%3D%22%232D374B%22%20fill-rule%3D%22evenodd%22%20d%3D%22M20%202.5C10.335%202.5%202.5%2010.335%202.5%2020a17.42%2017.42%200%200%200%203.137%2010H0v10h10v-5.637A17.42%2017.42%200%200%200%2020%2037.5c9.665%200%2017.5-7.835%2017.5-17.5S29.665%202.5%2020%202.5Z%22%20clip-rule%3D%22evenodd%22%2F%3E%3C%2Fmask%3E%3Cg%20mask%3D%22url(%23c)%22%3E%3Cpath%20fill%3D%22%2328A0F0%22%20d%3D%22m26.873%2037.192-6.75-10.615%203.782-6.416%208.672%2013.676-5.704%203.355ZM34.126%2032.79l3.471-5.786-9.238-14.423-3.299%205.596%209.066%2014.613Z%22%2F%3E%3Cpath%20fill%3D%22url(%23d)%22%20fill-opacity%3D%22.2%22%20d%3D%22M0%2020C0%208.954%208.954%200%2020%200s20%208.954%2020%2020-8.954%2020-20%2020H0V20Z%22%2F%3E%3C%2Fg%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22m.52%2040.315-4.387-2.524L-4.2%2036.6l15.267-23.715c1.042-1.702%203.314-2.25%205.422-2.22l2.475.065L.519%2040.315ZM27.38%2010.73l-6.523.024L-2.9%2050%202%2053.5l6.358-10.597%201.402-2.379L27.38%2010.73Z%22%2F%3E%3C%2Fg%3E%3Cdefs%3E%3ClinearGradient%20id%3D%22b%22%20x1%3D%220%22%20x2%3D%2220%22%20y1%3D%220%22%20y2%3D%2240%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23fff%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23fff%22%20stop-opacity%3D%220%22%2F%3E%3C%2FlinearGradient%3E%3ClinearGradient%20id%3D%22d%22%20x1%3D%220%22%20x2%3D%2220%22%20y1%3D%220%22%20y2%3D%2240%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23fff%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23fff%22%20stop-opacity%3D%220%22%2F%3E%3C%2FlinearGradient%3E%3CclipPath%20id%3D%22a%22%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M0%200h40v40H0z%22%2F%3E%3C%2FclipPath%3E%3C%2Fdefs%3E%3C%2Fg%3E%3C%2Fsvg%3E";
;
}}),
}]);

//# sourceMappingURL=node_modules_%40rainbow-me_rainbowkit_dist_arbitrum-WURIBY6W_0b628c09.js.map