import { ethers } from 'ethers'
import { createPublicClient, createWalletClient, custom, http, Chain } from 'viem'
import { mainnet, sepolia, localhost } from 'viem/chains'

export interface WalletConnection {
  address: string
  chainId: number
  isConnected: boolean
  balance: string
  provider: ethers.BrowserProvider
}

export interface TransactionRequest {
  to: string
  value?: string
  data?: string
  gasLimit?: string
  gasPrice?: string
}

export interface ContractCall {
  contractAddress: string
  abi: any[]
  functionName: string
  args?: any[]
  value?: string
}

export class Web3DAppCore {
  private provider: ethers.BrowserProvider | null = null
  private signer: ethers.Signer | null = null
  private publicClient: any = null
  private walletClient: any = null
  private currentChain: Chain = mainnet

  constructor() {
    this.initializeClients()
  }

  private initializeClients() {
    // Initialize viem clients
    this.publicClient = createPublicClient({
      chain: this.currentChain,
      transport: http()
    })
  }

  /**
   * Connect to Web3 wallet
   */
  async connectWallet(): Promise<WalletConnection> {
    try {
      if (!window.ethereum) {
        throw new Error('No Web3 wallet detected. Please install MetaMask or another Web3 wallet.')
      }

      // Request account access
      await window.ethereum.request({ method: 'eth_requestAccounts' })

      // Initialize ethers provider
      this.provider = new ethers.BrowserProvider(window.ethereum)
      this.signer = await this.provider.getSigner()

      // Get wallet info
      const address = await this.signer.getAddress()
      const network = await this.provider.getNetwork()
      const balance = await this.provider.getBalance(address)

      // Initialize wallet client
      this.walletClient = createWalletClient({
        chain: this.currentChain,
        transport: custom(window.ethereum)
      })

      const connection: WalletConnection = {
        address,
        chainId: Number(network.chainId),
        isConnected: true,
        balance: ethers.formatEther(balance),
        provider: this.provider
      }

      // Listen for account changes
      window.ethereum.on('accountsChanged', this.handleAccountsChanged.bind(this))
      window.ethereum.on('chainChanged', this.handleChainChanged.bind(this))

      return connection

    } catch (error: any) {
      throw new Error(`Failed to connect wallet: ${error.message}`)
    }
  }

  /**
   * Disconnect wallet
   */
  async disconnectWallet(): Promise<void> {
    this.provider = null
    this.signer = null
    this.walletClient = null

    // Remove event listeners
    if (window.ethereum) {
      window.ethereum.removeAllListeners('accountsChanged')
      window.ethereum.removeAllListeners('chainChanged')
    }
  }

  /**
   * Get current wallet connection status
   */
  async getConnectionStatus(): Promise<WalletConnection | null> {
    if (!this.provider || !this.signer) {
      return null
    }

    try {
      const address = await this.signer.getAddress()
      const network = await this.provider.getNetwork()
      const balance = await this.provider.getBalance(address)

      return {
        address,
        chainId: Number(network.chainId),
        isConnected: true,
        balance: ethers.formatEther(balance),
        provider: this.provider
      }
    } catch (error) {
      return null
    }
  }

  /**
   * Switch to a different network
   */
  async switchNetwork(chainId: number): Promise<void> {
    if (!window.ethereum) {
      throw new Error('No Web3 wallet detected')
    }

    try {
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${chainId.toString(16)}` }]
      })

      // Update current chain
      switch (chainId) {
        case 1:
          this.currentChain = mainnet
          break
        case ********:
          this.currentChain = sepolia
          break
        case 1337:
          this.currentChain = localhost
          break
        default:
          throw new Error(`Unsupported chain ID: ${chainId}`)
      }

      this.initializeClients()

    } catch (error: any) {
      if (error.code === 4902) {
        throw new Error('Network not added to wallet. Please add it manually.')
      }
      throw new Error(`Failed to switch network: ${error.message}`)
    }
  }

  /**
   * Send a transaction
   */
  async sendTransaction(request: TransactionRequest): Promise<string> {
    if (!this.signer) {
      throw new Error('Wallet not connected')
    }

    try {
      const tx = await this.signer.sendTransaction({
        to: request.to,
        value: request.value ? ethers.parseEther(request.value) : undefined,
        data: request.data,
        gasLimit: request.gasLimit ? BigInt(request.gasLimit) : undefined,
        gasPrice: request.gasPrice ? ethers.parseUnits(request.gasPrice, 'gwei') : undefined
      })

      await tx.wait()
      return tx.hash

    } catch (error: any) {
      throw new Error(`Transaction failed: ${error.message}`)
    }
  }

  /**
   * Call a smart contract function
   */
  async callContract(call: ContractCall): Promise<any> {
    if (!this.provider) {
      throw new Error('Provider not initialized')
    }

    try {
      const contract = new ethers.Contract(
        call.contractAddress,
        call.abi,
        call.value ? this.signer : this.provider
      )

      if (call.value) {
        // This is a transaction (requires signer)
        if (!this.signer) {
          throw new Error('Wallet not connected for transaction')
        }
        const tx = await contract[call.functionName](...(call.args || []), {
          value: ethers.parseEther(call.value)
        })
        await tx.wait()
        return tx.hash
      } else {
        // This is a view function call
        return await contract[call.functionName](...(call.args || []))
      }

    } catch (error: any) {
      throw new Error(`Contract call failed: ${error.message}`)
    }
  }

  /**
   * Get token balance
   */
  async getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string> {
    if (!this.provider) {
      throw new Error('Provider not initialized')
    }

    try {
      const tokenContract = new ethers.Contract(
        tokenAddress,
        [
          'function balanceOf(address owner) view returns (uint256)',
          'function decimals() view returns (uint8)'
        ],
        this.provider
      )

      const [balance, decimals] = await Promise.all([
        tokenContract.balanceOf(walletAddress),
        tokenContract.decimals()
      ])

      return ethers.formatUnits(balance, decimals)

    } catch (error: any) {
      throw new Error(`Failed to get token balance: ${error.message}`)
    }
  }

  /**
   * Get transaction receipt
   */
  async getTransactionReceipt(txHash: string): Promise<any> {
    if (!this.provider) {
      throw new Error('Provider not initialized')
    }

    try {
      return await this.provider.getTransactionReceipt(txHash)
    } catch (error: any) {
      throw new Error(`Failed to get transaction receipt: ${error.message}`)
    }
  }

  /**
   * Sign a message
   */
  async signMessage(message: string): Promise<string> {
    if (!this.signer) {
      throw new Error('Wallet not connected')
    }

    try {
      return await this.signer.signMessage(message)
    } catch (error: any) {
      throw new Error(`Failed to sign message: ${error.message}`)
    }
  }

  /**
   * Get gas price
   */
  async getGasPrice(): Promise<string> {
    if (!this.provider) {
      throw new Error('Provider not initialized')
    }

    try {
      const feeData = await this.provider.getFeeData()
      return feeData.gasPrice ? ethers.formatUnits(feeData.gasPrice, 'gwei') : '0'
    } catch (error: any) {
      throw new Error(`Failed to get gas price: ${error.message}`)
    }
  }

  /**
   * Estimate gas for transaction
   */
  async estimateGas(request: TransactionRequest): Promise<string> {
    if (!this.provider) {
      throw new Error('Provider not initialized')
    }

    try {
      const gasEstimate = await this.provider.estimateGas({
        to: request.to,
        value: request.value ? ethers.parseEther(request.value) : undefined,
        data: request.data
      })

      return gasEstimate.toString()
    } catch (error: any) {
      throw new Error(`Failed to estimate gas: ${error.message}`)
    }
  }

  // Event handlers
  private handleAccountsChanged(accounts: string[]) {
    if (accounts.length === 0) {
      this.disconnectWallet()
    } else {
      // Account changed, reinitialize
      this.connectWallet()
    }
  }

  private handleChainChanged(chainId: string) {
    // Chain changed, reload the page or reinitialize
    window.location.reload()
  }

  /**
   * Get supported networks
   */
  getSupportedNetworks() {
    return [
      { chainId: 1, name: 'Ethereum Mainnet', rpcUrl: 'https://mainnet.infura.io' },
      { chainId: ********, name: 'Sepolia Testnet', rpcUrl: 'https://sepolia.infura.io' },
      { chainId: 1337, name: 'Localhost', rpcUrl: 'http://localhost:8545' }
    ]
  }

  /**
   * Check if wallet is installed
   */
  isWalletInstalled(): boolean {
    return typeof window !== 'undefined' && !!window.ethereum
  }
}

// Export singleton instance
export const web3DAppCore = new Web3DAppCore()

// Export utility functions
export const formatAddress = (address: string): string => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`
}

export const formatBalance = (balance: string, decimals: number = 4): string => {
  const num = parseFloat(balance)
  return num.toFixed(decimals)
}

export const isValidAddress = (address: string): boolean => {
  return ethers.isAddress(address)
}

// Declare global ethereum object
declare global {
  interface Window {
    ethereum?: any
  }
}
