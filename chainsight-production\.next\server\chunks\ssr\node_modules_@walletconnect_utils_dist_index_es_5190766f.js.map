{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.es.js", "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/caip.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/misc.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/hashes/esm/_assert.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/hashes/esm/_u64.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/hashes/esm/crypto.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/hashes/esm/utils.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/hashes/esm/sha3.js", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/signatures.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/cacao.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/ciphers/esm/_assert.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/ciphers/esm/utils.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/ciphers/esm/_arx.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/ciphers/esm/_poly1305.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/ciphers/esm/chacha.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/hashes/esm/hmac.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/hashes/esm/hkdf.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/hashes/esm/_md.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/hashes/esm/sha256.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/curves/esm/abstract/utils.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/curves/esm/abstract/modular.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/curves/esm/abstract/curve.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/curves/esm/abstract/edwards.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/curves/esm/abstract/montgomery.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/curves/esm/ed25519.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/curves/esm/abstract/weierstrass.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/curves/esm/_shortw_utils.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/%40noble/curves/esm/p256.js", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/crypto.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/relay.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/uri.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/namespaces.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/errors.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/validators.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/network.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/utils/src/memoryStore.ts"], "sourcesContent": ["import { SessionTypes, ProposalTypes } from \"@walletconnect/types\";\n\ninterface ChainIdParams {\n  namespace: string;\n  reference: string;\n}\n\ninterface AccountIdParams extends ChainIdParams {\n  address: string;\n}\n\nconst CAIP_DELIMITER = \":\";\n\nexport function parseChainId(chain: string): ChainIdParams {\n  const [namespace, reference] = chain.split(CAIP_DELIMITER);\n  return { namespace, reference };\n}\n\nexport function formatChainId(params: ChainIdParams): string {\n  const { namespace, reference } = params;\n  return [namespace, reference].join(CAIP_DELIMITER);\n}\n\nexport function parseAccountId(account: string): AccountIdParams {\n  const [namespace, reference, address] = account.split(CAIP_DELIMITER);\n  return { namespace, reference, address };\n}\n\nexport function formatAccountId(params: AccountIdParams): string {\n  const { namespace, reference, address } = params;\n  return [namespace, reference, address].join(CAIP_DELIMITER);\n}\n\nexport function getUniqueValues(array: string[], parser: (str: string) => string): string[] {\n  const unique: string[] = [];\n  array.forEach((str) => {\n    const value = parser(str);\n    if (!unique.includes(value)) unique.push(value);\n  });\n  return unique;\n}\n\nexport function getAddressFromAccount(account: string) {\n  const { address } = parseAccountId(account);\n  return address;\n}\n\nexport function getChainFromAccount(account: string) {\n  const { namespace, reference } = parseAccountId(account);\n  const chain = formatChainId({ namespace, reference });\n  return chain;\n}\n\nexport function formatAccountWithChain(address: string, chain: string) {\n  const { namespace, reference } = parseChainId(chain);\n  const account = formatAccountId({ namespace, reference, address });\n  return account;\n}\n\nexport function getAddressesFromAccounts(accounts: string[]) {\n  return getUniqueValues(accounts, getAddressFromAccount);\n}\n\nexport function getChainsFromAccounts(accounts: string[]) {\n  return getUniqueValues(accounts, getChainFromAccount);\n}\n\nexport function getAccountsFromNamespaces(\n  namespaces: SessionTypes.Namespaces,\n  keys: string[] = [],\n): string[] {\n  const accounts: string[] = [];\n  Object.keys(namespaces).forEach((key) => {\n    if (keys.length && !keys.includes(key)) return;\n    const ns = namespaces[key];\n    accounts.push(...ns.accounts);\n  });\n  return accounts;\n}\n\nexport function getChainsFromNamespaces(\n  namespaces: SessionTypes.Namespaces,\n  keys: string[] = [],\n): string[] {\n  const chains: string[] = [];\n  Object.keys(namespaces).forEach((key) => {\n    if (keys.length && !keys.includes(key)) return;\n    const ns = namespaces[key];\n    chains.push(...getChainsFromAccounts(ns.accounts));\n  });\n  return chains;\n}\n\nexport function getChainsFromRequiredNamespaces(\n  requiredNamespaces: ProposalTypes.RequiredNamespaces,\n  keys: string[] = [],\n): string[] {\n  const chains: string[] = [];\n  Object.keys(requiredNamespaces).forEach((key) => {\n    if (keys.length && !keys.includes(key)) return;\n    const ns = requiredNamespaces[key];\n    chains.push(...getChainsFromNamespace(key, ns));\n  });\n  return chains;\n}\n\nexport function getChainsFromNamespace(\n  namespace: string,\n  namespaceProps: ProposalTypes.BaseRequiredNamespace,\n) {\n  // check if chainId is provided in the key as `eip155:1` or in the namespace as chains[]\n  return namespace.includes(\":\") ? [namespace] : namespaceProps.chains || [];\n}\n", "import { detect } from \"detect-browser\";\nimport { FIVE_MINUTES, fromMiliseconds, toMiliseconds } from \"@walletconnect/time\";\nimport {\n  SignClientTypes,\n  RelayerClientMetadata,\n  EngineTypes,\n  RelayerTypes,\n} from \"@walletconnect/types\";\nimport { getDocument, getLocation, getNavigator } from \"@walletconnect/window-getters\";\nimport { getWindowMetadata } from \"@walletconnect/window-metadata\";\nimport { ErrorResponse } from \"@walletconnect/jsonrpc-utils\";\nimport { IKeyValueStorage } from \"@walletconnect/keyvaluestorage\";\n\n// -- constants -----------------------------------------//\nexport const REACT_NATIVE_PRODUCT = \"ReactNative\";\n\nexport const ENV_MAP = {\n  reactNative: \"react-native\",\n  node: \"node\",\n  browser: \"browser\",\n  unknown: \"unknown\",\n};\n\nexport const EMPTY_SPACE = \" \";\n\nexport const COLON = \":\";\n\nexport const SLASH = \"/\";\n\nexport const DEFAULT_DEPTH = 2;\n\nexport const ONE_THOUSAND = 1000;\n\nexport const SDK_TYPE = \"js\";\n\n// -- env -----------------------------------------------//\n\nexport function isNode(): boolean {\n  return (\n    typeof process !== \"undefined\" &&\n    typeof process.versions !== \"undefined\" &&\n    typeof process.versions.node !== \"undefined\"\n  );\n}\n\nexport function isReactNative(): boolean {\n  return !getDocument() && !!getNavigator() && navigator.product === REACT_NATIVE_PRODUCT;\n}\n\nexport function isAndroid(): boolean {\n  return (\n    isReactNative() &&\n    typeof global !== \"undefined\" &&\n    typeof (global as any)?.Platform !== \"undefined\" &&\n    (global as any)?.Platform.OS === \"android\"\n  );\n}\n\nexport function isIos(): boolean {\n  return (\n    isReactNative() &&\n    typeof global !== \"undefined\" &&\n    typeof (global as any)?.Platform !== \"undefined\" &&\n    (global as any)?.Platform.OS === \"ios\"\n  );\n}\n\nexport function isBrowser(): boolean {\n  return !isNode() && !!getNavigator() && !!getDocument();\n}\n\nexport function getEnvironment(): string {\n  if (isReactNative()) return ENV_MAP.reactNative;\n  if (isNode()) return ENV_MAP.node;\n  if (isBrowser()) return ENV_MAP.browser;\n  return ENV_MAP.unknown;\n}\n\nexport function getAppId(): string | undefined {\n  try {\n    if (\n      isReactNative() &&\n      typeof global !== \"undefined\" &&\n      typeof (global as any)?.Application !== \"undefined\"\n    ) {\n      return (global as any).Application?.applicationId;\n    }\n    return undefined;\n  } catch {\n    return undefined;\n  }\n}\n\n// -- query -----------------------------------------------//\n\nexport function appendToQueryString(\n  queryString: string,\n  newQueryParams: Record<string, any>,\n): string {\n  const urlSearchParams = new URLSearchParams(queryString);\n\n  for (const key of Object.keys(newQueryParams).sort()) {\n    if (newQueryParams.hasOwnProperty(key)) {\n      const value = newQueryParams[key];\n      if (value !== undefined) {\n        urlSearchParams.set(key, value);\n      }\n    }\n  }\n\n  return urlSearchParams.toString();\n}\n\n// -- metadata ----------------------------------------------//\n\nexport function populateAppMetadata(metadata?: SignClientTypes.Metadata): SignClientTypes.Metadata {\n  const appMetadata = getAppMetadata();\n  try {\n    if (metadata?.url && appMetadata.url) {\n      if (new URL(metadata.url).host !== new URL(appMetadata.url).host) {\n        console.warn(\n          `The configured WalletConnect 'metadata.url':${metadata.url} differs from the actual page url:${appMetadata.url}. This is probably unintended and can lead to issues.`,\n        );\n        metadata.url = appMetadata.url;\n      }\n    }\n\n    if (metadata?.icons?.length && metadata.icons.length > 0) {\n      metadata.icons = metadata.icons.filter((icon) => icon !== \"\");\n    }\n\n    return {\n      ...appMetadata,\n      ...metadata,\n      url: metadata?.url || appMetadata.url,\n      name: metadata?.name || appMetadata.name,\n      description: metadata?.description || appMetadata.description,\n      icons:\n        metadata?.icons?.length && metadata.icons.length > 0 ? metadata.icons : appMetadata.icons,\n    };\n  } catch (error) {\n    console.warn(\"Error populating app metadata\", error);\n    return metadata || appMetadata;\n  }\n}\n\nexport function getAppMetadata(): SignClientTypes.Metadata {\n  return (\n    getWindowMetadata() || {\n      name: \"\",\n      description: \"\",\n      url: \"\",\n      icons: [\"\"],\n    }\n  );\n}\n\nexport function getRelayClientMetadata(protocol: string, version: number): RelayerClientMetadata {\n  const env = getEnvironment();\n\n  const metadata: RelayerClientMetadata = { protocol, version, env };\n  if (env === \"browser\") {\n    metadata.host = getLocation()?.host || \"unknown\";\n  }\n  return metadata;\n}\n\n// -- rpcUrl ----------------------------------------------//\n\nexport function getJavascriptOS() {\n  const env = getEnvironment();\n  // global.Platform is set by react-native-compat\n  if (\n    env === ENV_MAP.reactNative &&\n    typeof global !== \"undefined\" &&\n    typeof (global as any)?.Platform !== \"undefined\"\n  ) {\n    const { OS, Version } = (global as any).Platform;\n    return [OS, Version].join(\"-\");\n  }\n\n  const info = detect();\n  if (info === null) return \"unknown\";\n  const os = info.os ? info.os.replace(\" \", \"\").toLowerCase() : \"unknown\";\n  if (info.type === \"browser\") {\n    return [os, info.name, info.version].join(\"-\");\n  }\n  return [os, info.version].join(\"-\");\n}\n\nexport function getJavascriptID() {\n  const env = getEnvironment();\n  return env === ENV_MAP.browser ? [env, getLocation()?.host || \"unknown\"].join(\":\") : env;\n}\n\nexport function formatUA(protocol: string, version: number, sdkVersion: string) {\n  const os = getJavascriptOS();\n  const id = getJavascriptID();\n  return [[protocol, version].join(\"-\"), [SDK_TYPE, sdkVersion].join(\"-\"), os, id].join(\"/\");\n}\nconsole;\n\nexport function formatRelayRpcUrl({\n  protocol,\n  version,\n  relayUrl,\n  sdkVersion,\n  auth,\n  projectId,\n  useOnCloseEvent,\n  bundleId,\n  packageName,\n}: RelayerTypes.RpcUrlParams) {\n  const splitUrl = relayUrl.split(\"?\");\n  const ua = formatUA(protocol, version, sdkVersion);\n  const params = {\n    auth,\n    ua,\n    projectId,\n    useOnCloseEvent: useOnCloseEvent || undefined,\n    packageName: packageName || undefined,\n    bundleId: bundleId || undefined,\n  };\n  const queryString = appendToQueryString(splitUrl[1] || \"\", params);\n  return splitUrl[0] + \"?\" + queryString;\n}\n\nexport function getHttpUrl(url: string) {\n  // regex from https://stackoverflow.com/questions/3883871/regexp-to-grab-protocol-from-url\n  const matches = url.match(/^[^:]+(?=:\\/\\/)/gi) || [];\n  let protocol = matches[0];\n  const domain = typeof protocol !== \"undefined\" ? url.split(\"://\")[1] : url;\n  protocol = protocol === \"wss\" ? \"https\" : \"http\";\n  return [protocol, domain].join(\"://\");\n}\n\n// -- assert ------------------------------------------------- //\n\nexport function assertType(obj: any, key: string, type: string) {\n  // eslint-disable-next-line valid-typeof\n  if (!obj[key] || typeof obj[key] !== type) {\n    throw new Error(`Missing or invalid \"${key}\" param`);\n  }\n}\n\n// -- context ------------------------------------------------- //\n\nexport function parseContextNames(context: string, depth = DEFAULT_DEPTH) {\n  return getLastItems(context.split(SLASH), depth);\n}\n\nexport function formatMessageContext(context: string): string {\n  return parseContextNames(context).join(EMPTY_SPACE);\n}\n\n// -- array ------------------------------------------------- //\n\nexport function hasOverlap(a: any[], b: any[]): boolean {\n  const matches = a.filter((x) => b.includes(x));\n  return matches.length === a.length;\n}\n\nexport function getLastItems(arr: any[], depth = DEFAULT_DEPTH): any[] {\n  return arr.slice(Math.max(arr.length - depth, 0));\n}\n\n// -- map ------------------------------------------------- //\n\nexport function mapToObj<T = any>(map: Map<string, T>): Record<string, T> {\n  return Object.fromEntries(map.entries());\n}\n\nexport function objToMap<T = any>(obj: Record<string, T>): Map<string, T> {\n  return new Map<string, T>(Object.entries<T>(obj));\n}\n\nexport function mapEntries<A = any, B = any>(\n  obj: Record<string, A>,\n  cb: (x: A) => B,\n): Record<string, B> {\n  const res: any = {};\n  Object.keys(obj).forEach((key) => {\n    res[key] = cb(obj[key]);\n  });\n  return res;\n}\n\n// -- enum ------------------------------------------------- //\n\n// source: https://github.com/microsoft/TypeScript/issues/3192#issuecomment-261720275\nexport const enumify = <T extends { [index: string]: U }, U extends string>(x: T): T => x;\n\n// -- string ------------------------------------------------- //\n\nexport function capitalizeWord(word: string) {\n  return word.trim().replace(/^\\w/, (c) => c.toUpperCase());\n}\n\nexport function capitalize(str: string) {\n  return str\n    .split(EMPTY_SPACE)\n    .map((w) => capitalizeWord(w))\n    .join(EMPTY_SPACE);\n}\n\n// -- promises --------------------------------------------- //\nexport function createDelayedPromise<T>(\n  expiry: number = FIVE_MINUTES,\n  expireErrorMessage?: string,\n) {\n  const timeout = toMiliseconds(expiry || FIVE_MINUTES);\n  let cacheResolve: undefined | ((value: T | PromiseLike<T>) => void);\n  let cacheReject: undefined | ((value?: ErrorResponse) => void);\n  let cacheTimeout: undefined | NodeJS.Timeout;\n  let result: Promise<Awaited<T>> | Promise<T> | undefined;\n\n  const done = () =>\n    new Promise<T>((promiseResolve, promiseReject) => {\n      if (result) {\n        return promiseResolve(result);\n      }\n      cacheTimeout = setTimeout(() => {\n        const err = new Error(expireErrorMessage);\n        result = Promise.reject(err);\n        promiseReject(err);\n      }, timeout);\n      cacheResolve = promiseResolve;\n      cacheReject = promiseReject;\n    });\n  const resolve = (value?: T) => {\n    if (cacheTimeout && cacheResolve) {\n      clearTimeout(cacheTimeout);\n      cacheResolve(value as T);\n      result = Promise.resolve(value) as Promise<Awaited<T>>;\n    }\n  };\n  const reject = (value?: ErrorResponse) => {\n    if (cacheTimeout && cacheReject) {\n      clearTimeout(cacheTimeout);\n      cacheReject(value);\n    }\n  };\n\n  return {\n    resolve,\n    reject,\n    done,\n  };\n}\n\nexport function createExpiringPromise<T>(\n  promise: Promise<T>,\n  expiry: number,\n  expireErrorMessage?: string,\n) {\n  return new Promise(async (resolve, reject) => {\n    const timeout = setTimeout(() => reject(new Error(expireErrorMessage)), expiry);\n    try {\n      const result = await promise;\n      resolve(result);\n    } catch (error) {\n      reject(error);\n    }\n    clearTimeout(timeout);\n  });\n}\n\n// -- expirer --------------------------------------------- //\n\nexport function formatExpirerTarget(type: \"topic\" | \"id\", value: string | number): string {\n  if (typeof value === \"string\" && value.startsWith(`${type}:`)) return value;\n  if (type.toLowerCase() === \"topic\") {\n    if (typeof value !== \"string\")\n      throw new Error(`Value must be \"string\" for expirer target type: topic`);\n    return `topic:${value}`;\n  } else if (type.toLowerCase() === \"id\") {\n    if (typeof value !== \"number\")\n      throw new Error(`Value must be \"number\" for expirer target type: id`);\n    return `id:${value}`;\n  }\n  throw new Error(`Unknown expirer target type: ${type}`);\n}\n\nexport function formatTopicTarget(topic: string): string {\n  return formatExpirerTarget(\"topic\", topic);\n}\n\nexport function formatIdTarget(id: number): string {\n  return formatExpirerTarget(\"id\", id);\n}\n\nexport function parseExpirerTarget(target: string) {\n  const [type, value] = target.split(\":\");\n  const parsed: { id?: number; topic?: string } = { id: undefined, topic: undefined };\n  if (type === \"topic\" && typeof value === \"string\") {\n    parsed.topic = value;\n  } else if (type === \"id\" && Number.isInteger(Number(value))) {\n    parsed.id = Number(value);\n  } else {\n    throw new Error(`Invalid target, expected id:number or topic:string, got ${type}:${value}`);\n  }\n\n  return parsed;\n}\n\nexport function calcExpiry(ttl: number, now?: number): number {\n  return fromMiliseconds((now || Date.now()) + toMiliseconds(ttl));\n}\n\nexport function isExpired(expiry: number) {\n  return Date.now() >= toMiliseconds(expiry);\n}\n\n// -- events ---------------------------------------------- //\n\nexport function engineEvent(event: EngineTypes.Event, id?: number | string | undefined) {\n  return `${event}${id ? `:${id}` : \"\"}`;\n}\n\nexport function mergeArrays<T>(a: T[] = [], b: T[] = []): T[] {\n  return [...new Set([...a, ...b])];\n}\n\nexport async function handleDeeplinkRedirect({\n  id,\n  topic,\n  wcDeepLink,\n}: {\n  id: number;\n  topic: string;\n  wcDeepLink: string;\n}) {\n  try {\n    if (!wcDeepLink) return;\n\n    const json = typeof wcDeepLink === \"string\" ? JSON.parse(wcDeepLink) : wcDeepLink;\n    const deeplink = json?.href;\n    if (typeof deeplink !== \"string\") return;\n    const link = formatDeeplinkUrl(deeplink, id, topic);\n    const env = getEnvironment();\n\n    if (env === ENV_MAP.browser) {\n      if (!getDocument()?.hasFocus()) {\n        console.warn(\"Document does not have focus, skipping deeplink.\");\n        return;\n      }\n\n      openDeeplink(link);\n    } else if (env === ENV_MAP.reactNative) {\n      // global.Linking is set by react-native-compat\n      if (typeof (global as any)?.Linking !== \"undefined\") {\n        await (global as any).Linking.openURL(link);\n      }\n    }\n  } catch (err) {\n    // Silent error, just log in console\n    // eslint-disable-next-line no-console\n    console.error(err);\n  }\n}\n\nexport function formatDeeplinkUrl(deeplink: string, requestId: number, sessionTopic: string) {\n  const payload = `requestId=${requestId}&sessionTopic=${sessionTopic}`;\n  if (deeplink.endsWith(\"/\")) deeplink = deeplink.slice(0, -1);\n  let link = `${deeplink}`;\n  if (deeplink.startsWith(\"https://t.me\")) {\n    const startApp = deeplink.includes(\"?\") ? \"&startapp=\" : \"?startapp=\";\n    link = `${link}${startApp}${toBase64(payload, true)}`;\n  } else {\n    link = `${link}/wc?${payload}`;\n  }\n  return link;\n}\n\nexport function openDeeplink(url: string) {\n  let target = \"_self\";\n  if (isIframe()) {\n    target = \"_top\";\n  } else if (isTelegram() || url.startsWith(\"https://\") || url.startsWith(\"http://\")) {\n    target = \"_blank\";\n  }\n\n  window.open(url, target, \"noreferrer noopener\");\n}\n\nexport async function getDeepLink(storage: IKeyValueStorage, key: string) {\n  let link: string | undefined = \"\";\n  try {\n    if (isBrowser()) {\n      link = localStorage.getItem(key) as string;\n      if (link) return link;\n    }\n    link = await storage.getItem(key);\n  } catch (err) {\n    // eslint-disable-next-line no-console\n    console.error(err);\n  }\n  return link;\n}\n\nexport function getCommonValuesInArrays<T = string | number | boolean>(arr1: T[], arr2: T[]): T[] {\n  return arr1.filter((value) => arr2.includes(value));\n}\n\nexport function getSearchParamFromURL(url: string, param: any) {\n  const include = url.includes(param);\n  if (!include) return null;\n  const params = url.split(/([&,?,=])/);\n  const index = params.indexOf(param);\n  const value = params[index + 2];\n  return value;\n}\n\nexport function uuidv4() {\n  if (typeof crypto !== \"undefined\" && crypto?.randomUUID) {\n    return crypto.randomUUID();\n  }\n\n  return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/gu, (c) => {\n    const r = (Math.random() * 16) | 0;\n    const v = c === \"x\" ? r : (r & 0x3) | 0x8;\n\n    return v.toString(16);\n  });\n}\n\nexport function isTestRun() {\n  return typeof process !== \"undefined\" && process.env.IS_VITEST === \"true\";\n}\n\nexport function isTelegram() {\n  return (\n    typeof window !== \"undefined\" &&\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (Boolean((window as any).TelegramWebviewProxy) ||\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      Boolean((window as any).Telegram) ||\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      Boolean((window as any).TelegramWebviewProxyProto))\n  );\n}\n\nexport function isIframe() {\n  try {\n    return window.self !== window.top;\n  } catch {\n    return false;\n  }\n}\n\nexport function toBase64(input: string, removePadding = false): string {\n  const encoded = Buffer.from(input).toString(\"base64\");\n  return removePadding ? encoded.replace(/[=]/g, \"\") : encoded;\n}\n\nexport function fromBase64(encodedString: string): string {\n  return Buffer.from(encodedString, \"base64\").toString(\"utf-8\");\n}\n\nexport function sleep(ms: number) {\n  return new Promise((resolve) => setTimeout(resolve, ms));\n}\n", "/**\n * Internal assertion helpers.\n * @module\n */\n/** Asserts something is positive integer. */\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error('positive integer expected, got ' + n);\n}\n/** Is number an Uint8Array? Copied from utils for perf. */\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n/** Asserts something is hash */\nfunction ahash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.wrapConstructor');\n    anumber(h.outputLen);\n    anumber(h.blockLen);\n}\n/** Asserts a hash instance has not been destroyed / finished */\nfunction aexists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\n/** Asserts output is properly-sized byte array */\nfunction aoutput(out, instance) {\n    abytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error('digestInto() expects output buffer of length at least ' + min);\n    }\n}\nexport { anumber, abytes, ahash, aexists, aoutput };\n//# sourceMappingURL=_assert.js.map", "/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    let Ah = new Uint32Array(lst.length);\n    let Al = new Uint32Array(lst.length);\n    for (let i = 0; i < lst.length; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], <PERSON>[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\nexport { fromBig, split, toBig, shrSH, shrSL, rotrSH, rotrSL, rotrBH, rotrBL, rotr32H, rotr32L, rotlSH, rotlSL, rotlBH, rotlBL, add, add3L, add3H, add4L, add4H, add5H, add5L, };\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n//# sourceMappingURL=_u64.js.map", "export const crypto = typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n//# sourceMappingURL=crypto.js.map", "/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\nimport { abytes } from './_assert.js';\n// export { isBytes } from './_assert.js';\n// We can't reuse isBytes from _assert, because somehow this causes huge perf issues\nexport function isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n// Cast array to different type\nexport function u8(arr) {\n    return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\nexport function u32(arr) {\n    return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n// Cast array to view\nexport function createView(arr) {\n    return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** The rotate right (circular right shift) operation for uint32 */\nexport function rotr(word, shift) {\n    return (word << (32 - shift)) | (word >>> shift);\n}\n/** The rotate left (circular left shift) operation for uint32 */\nexport function rotl(word, shift) {\n    return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexport const isLE = /* @__PURE__ */ (() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n// The byte swap operation for uint32\nexport function byteSwap(word) {\n    return (((word << 24) & 0xff000000) |\n        ((word << 8) & 0xff0000) |\n        ((word >>> 8) & 0xff00) |\n        ((word >>> 24) & 0xff));\n}\n/** Conditionally byte swap if on a big-endian platform */\nexport const byteSwapIfBE = isLE\n    ? (n) => n\n    : (n) => byteSwap(n);\n/** In place byte swap for Uint32Array */\nexport function byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n}\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    abytes(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nexport const nextTick = async () => { };\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nexport async function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * Convert JS string to byte array.\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('utf8ToBytes expected string, got ' + typeof str);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n/** For runtime check if class implements interface */\nexport class Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nexport function checkOpts(defaults, opts) {\n    if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n        throw new Error('Options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\n/** Wraps hash function, creating an interface on top of it */\nexport function wrapConstructor(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nexport function wrapConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexport function wrapXOFConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nexport function randomBytes(bytesLength = 32) {\n    if (crypto && typeof crypto.getRandomValues === 'function') {\n        return crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    // Legacy Node.js compatibility\n    if (crypto && typeof crypto.randomBytes === 'function') {\n        return crypto.randomBytes(bytesLength);\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map", "/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHA<PERSON>, k12, and others.\n * @module\n */\nimport { abytes, aexists, anumber, aoutput } from './_assert.js';\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from './_u64.js';\nimport { byteSwap32, Hash, isLE, toBytes, u32, wrapConstructor, wrapXOFConstructorWithOpts, } from './utils.js';\n// Various per round constants calculations\nconst SHA3_PI = [];\nconst SHA3_ROTL = [];\nconst _SHA3_IOTA = [];\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nconst _7n = /* @__PURE__ */ BigInt(7);\nconst _256n = /* @__PURE__ */ BigInt(256);\nconst _0x71n = /* @__PURE__ */ BigInt(0x71);\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n    // Pi\n    [x, y] = [y, (2 * x + 3 * y) % 5];\n    SHA3_PI.push(2 * (5 * y + x));\n    // Rotational\n    SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n    // Iota\n    let t = _0n;\n    for (let j = 0; j < 7; j++) {\n        R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n        if (R & _2n)\n            t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n    }\n    _SHA3_IOTA.push(t);\n}\nconst [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */ split(_SHA3_IOTA, true);\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => (s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s));\nconst rotlL = (h, l, s) => (s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s));\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nexport function keccakP(s, rounds = 24) {\n    const B = new Uint32Array(5 * 2);\n    // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n    for (let round = 24 - rounds; round < 24; round++) {\n        // Theta θ\n        for (let x = 0; x < 10; x++)\n            B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n        for (let x = 0; x < 10; x += 2) {\n            const idx1 = (x + 8) % 10;\n            const idx0 = (x + 2) % 10;\n            const B0 = B[idx0];\n            const B1 = B[idx0 + 1];\n            const Th = rotlH(B0, B1, 1) ^ B[idx1];\n            const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n            for (let y = 0; y < 50; y += 10) {\n                s[x + y] ^= Th;\n                s[x + y + 1] ^= Tl;\n            }\n        }\n        // Rho (ρ) and Pi (π)\n        let curH = s[2];\n        let curL = s[3];\n        for (let t = 0; t < 24; t++) {\n            const shift = SHA3_ROTL[t];\n            const Th = rotlH(curH, curL, shift);\n            const Tl = rotlL(curH, curL, shift);\n            const PI = SHA3_PI[t];\n            curH = s[PI];\n            curL = s[PI + 1];\n            s[PI] = Th;\n            s[PI + 1] = Tl;\n        }\n        // Chi (χ)\n        for (let y = 0; y < 50; y += 10) {\n            for (let x = 0; x < 10; x++)\n                B[x] = s[y + x];\n            for (let x = 0; x < 10; x++)\n                s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n        }\n        // Iota (ι)\n        s[0] ^= SHA3_IOTA_H[round];\n        s[1] ^= SHA3_IOTA_L[round];\n    }\n    B.fill(0);\n}\n/** Keccak sponge function. */\nexport class Keccak extends Hash {\n    // NOTE: we accept arguments in bytes instead of bits here.\n    constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n        super();\n        this.blockLen = blockLen;\n        this.suffix = suffix;\n        this.outputLen = outputLen;\n        this.enableXOF = enableXOF;\n        this.rounds = rounds;\n        this.pos = 0;\n        this.posOut = 0;\n        this.finished = false;\n        this.destroyed = false;\n        // Can be passed from user as dkLen\n        anumber(outputLen);\n        // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n        // 0 < blockLen < 200\n        if (0 >= this.blockLen || this.blockLen >= 200)\n            throw new Error('Sha3 supports only keccak-f1600 function');\n        this.state = new Uint8Array(200);\n        this.state32 = u32(this.state);\n    }\n    keccak() {\n        if (!isLE)\n            byteSwap32(this.state32);\n        keccakP(this.state32, this.rounds);\n        if (!isLE)\n            byteSwap32(this.state32);\n        this.posOut = 0;\n        this.pos = 0;\n    }\n    update(data) {\n        aexists(this);\n        const { blockLen, state } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            for (let i = 0; i < take; i++)\n                state[this.pos++] ^= data[pos++];\n            if (this.pos === blockLen)\n                this.keccak();\n        }\n        return this;\n    }\n    finish() {\n        if (this.finished)\n            return;\n        this.finished = true;\n        const { state, suffix, pos, blockLen } = this;\n        // Do the padding\n        state[pos] ^= suffix;\n        if ((suffix & 0x80) !== 0 && pos === blockLen - 1)\n            this.keccak();\n        state[blockLen - 1] ^= 0x80;\n        this.keccak();\n    }\n    writeInto(out) {\n        aexists(this, false);\n        abytes(out);\n        this.finish();\n        const bufferOut = this.state;\n        const { blockLen } = this;\n        for (let pos = 0, len = out.length; pos < len;) {\n            if (this.posOut >= blockLen)\n                this.keccak();\n            const take = Math.min(blockLen - this.posOut, len - pos);\n            out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n            this.posOut += take;\n            pos += take;\n        }\n        return out;\n    }\n    xofInto(out) {\n        // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n        if (!this.enableXOF)\n            throw new Error('XOF is not possible for this instance');\n        return this.writeInto(out);\n    }\n    xof(bytes) {\n        anumber(bytes);\n        return this.xofInto(new Uint8Array(bytes));\n    }\n    digestInto(out) {\n        aoutput(out, this);\n        if (this.finished)\n            throw new Error('digest() was already called');\n        this.writeInto(out);\n        this.destroy();\n        return out;\n    }\n    digest() {\n        return this.digestInto(new Uint8Array(this.outputLen));\n    }\n    destroy() {\n        this.destroyed = true;\n        this.state.fill(0);\n    }\n    _cloneInto(to) {\n        const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n        to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n        to.state32.set(this.state32);\n        to.pos = this.pos;\n        to.posOut = this.posOut;\n        to.finished = this.finished;\n        to.rounds = rounds;\n        // Suffix can change in cSHAKE\n        to.suffix = suffix;\n        to.outputLen = outputLen;\n        to.enableXOF = enableXOF;\n        to.destroyed = this.destroyed;\n        return to;\n    }\n}\nconst gen = (suffix, blockLen, outputLen) => wrapConstructor(() => new Keccak(blockLen, suffix, outputLen));\n/** SHA3-224 hash function. */\nexport const sha3_224 = /* @__PURE__ */ gen(0x06, 144, 224 / 8);\n/** SHA3-256 hash function. Different from keccak-256. */\nexport const sha3_256 = /* @__PURE__ */ gen(0x06, 136, 256 / 8);\n/** SHA3-384 hash function. */\nexport const sha3_384 = /* @__PURE__ */ gen(0x06, 104, 384 / 8);\n/** SHA3-512 hash function. */\nexport const sha3_512 = /* @__PURE__ */ gen(0x06, 72, 512 / 8);\n/** keccak-224 hash function. */\nexport const keccak_224 = /* @__PURE__ */ gen(0x01, 144, 224 / 8);\n/** keccak-256 hash function. Different from SHA3-256. */\nexport const keccak_256 = /* @__PURE__ */ gen(0x01, 136, 256 / 8);\n/** keccak-384 hash function. */\nexport const keccak_384 = /* @__PURE__ */ gen(0x01, 104, 384 / 8);\n/** keccak-512 hash function. */\nexport const keccak_512 = /* @__PURE__ */ gen(0x01, 72, 512 / 8);\nconst genShake = (suffix, blockLen, outputLen) => wrapXOFConstructorWithOpts((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\n/** SHAKE128 XOF with 128-bit security. */\nexport const shake128 = /* @__PURE__ */ genShake(0x1f, 168, 128 / 8);\n/** SHAKE256 XOF with 256-bit security. */\nexport const shake256 = /* @__PURE__ */ genShake(0x1f, 136, 256 / 8);\n//# sourceMappingURL=sha3.js.map", "import { keccak_256 } from \"@noble/hashes/sha3\";\nimport { recoverAddress } from \"viem\";\nimport { AuthTypes } from \"@walletconnect/types\";\nimport bs58 from \"bs58\";\n\nimport { parseChainId } from \"./caip\";\n\nconst DEFAULT_RPC_URL = \"https://rpc.walletconnect.org/v1\";\n\nexport function hashEthereumMessage(message: string) {\n  const prefix = `\\x19Ethereum Signed Message:\\n${message.length}`;\n  const prefixedMessage = new TextEncoder().encode(prefix + message);\n  return \"0x\" + Buffer.from(keccak_256(prefixedMessage)).toString(\"hex\");\n}\n\nexport async function verifySignature(\n  address: string,\n  reconstructedMessage: string,\n  cacaoSignature: AuthTypes.CacaoSignature,\n  chainId: string,\n  projectId: string,\n  baseRpcUrl?: string,\n): Promise<boolean> {\n  // Determine if this signature is from an EOA or a contract.\n  switch (cacaoSignature.t) {\n    case \"eip191\":\n      return await isValidEip191Signature(address, reconstructedMessage, cacaoSignature.s);\n    case \"eip1271\":\n      return await isValidEip1271Signature(\n        address,\n        reconstructedMessage,\n        cacaoSignature.s,\n        chainId,\n        projectId,\n        baseRpcUrl,\n      );\n      break;\n    default:\n      throw new Error(\n        `verifySignature failed: Attempted to verify CacaoSignature with unknown type: ${cacaoSignature.t}`,\n      );\n  }\n}\n\nexport async function isValidEip191Signature(\n  address: string,\n  message: string,\n  signature: string,\n): Promise<boolean> {\n  const recoveredAddress = await recoverAddress({\n    hash: hashEthereumMessage(message) as `0x${string}`,\n    signature: signature as `0x${string}`,\n  });\n  return recoveredAddress.toLowerCase() === address.toLowerCase();\n}\n\nexport async function isValidEip1271Signature(\n  address: string,\n  reconstructedMessage: string,\n  signature: string,\n  chainId: string,\n  projectId: string,\n  baseRpcUrl?: string,\n) {\n  const parsedChain = parseChainId(chainId);\n  if (!parsedChain.namespace || !parsedChain.reference) {\n    throw new Error(\n      `isValidEip1271Signature failed: chainId must be in CAIP-2 format, received: ${chainId}`,\n    );\n  }\n  try {\n    const eip1271MagicValue = \"0x1626ba7e\";\n    const dynamicTypeOffset = \"0000000000000000000000000000000000000000000000000000000000000040\";\n    const dynamicTypeLength = \"0000000000000000000000000000000000000000000000000000000000000041\";\n    const nonPrefixedSignature = signature.substring(2);\n    const nonPrefixedHashedMessage = hashEthereumMessage(reconstructedMessage).substring(2);\n\n    const data =\n      eip1271MagicValue +\n      nonPrefixedHashedMessage +\n      dynamicTypeOffset +\n      dynamicTypeLength +\n      nonPrefixedSignature;\n    const response = await fetch(\n      `${baseRpcUrl || DEFAULT_RPC_URL}/?chainId=${chainId}&projectId=${projectId}`,\n      {\n        method: \"POST\",\n        body: JSON.stringify({\n          id: generateJsonRpcId(),\n          jsonrpc: \"2.0\",\n          method: \"eth_call\",\n          params: [{ to: address, data }, \"latest\"],\n        }),\n      },\n    );\n    const { result } = await response.json();\n    if (!result) return false;\n\n    // Remove right-padded zeros from result to get only the concrete recovered value.\n    const recoveredValue = result.slice(0, eip1271MagicValue.length);\n    return recoveredValue.toLowerCase() === eip1271MagicValue.toLowerCase();\n  } catch (error: any) {\n    // eslint-disable-next-line no-console\n    console.error(\"isValidEip1271Signature: \", error);\n    return false;\n  }\n}\n\nfunction generateJsonRpcId() {\n  return Date.now() + Math.floor(Math.random() * 1000);\n}\n\nexport function extractSolanaTransactionId(solanaTransaction: string): string {\n  const binary = atob(solanaTransaction);\n  const bytes = new Uint8Array(binary.length);\n  for (let i = 0; i < binary.length; i++) {\n    bytes[i] = binary.charCodeAt(i);\n  }\n\n  // Check signature count (first byte)\n  const signatureCount = bytes[0];\n  if (signatureCount === 0) {\n    throw new Error(\"No signatures found\");\n  }\n\n  // Verify we have enough bytes for all signatures\n  // Each signature is 64 bytes\n  const signatureEndPos = 1 + signatureCount * 64;\n  if (bytes.length < signatureEndPos) {\n    throw new Error(\"Transaction data too short for claimed signature count\");\n  }\n\n  // A transaction must have at least some minimum length\n  if (bytes.length < 100) {\n    throw new Error(\"Transaction too short\");\n  }\n\n  const transactionBuffer = Buffer.from(solanaTransaction, \"base64\");\n\n  const signatureBuffer = transactionBuffer.slice(1, 65);\n\n  return bs58.encode(signatureBuffer);\n}\n", "import { AuthTypes } from \"@walletconnect/types\";\nimport { getCommonValuesInArrays } from \"./misc\";\nimport { verifySignature } from \"./signatures\";\nconst didPrefix = \"did:pkh:\";\nexport const getDidAddressSegments = (iss: string) => {\n  return iss?.split(\":\");\n};\n\nexport const getDidChainId = (iss: string) => {\n  const segments = iss && getDidAddressSegments(iss);\n  if (segments) {\n    return iss.includes(didPrefix) ? segments[3] : segments[1];\n  }\n  return undefined;\n};\n\nexport const getNamespacedDidChainId = (iss: string) => {\n  const segments = iss && getDidAddressSegments(iss);\n  if (segments) {\n    return segments[2] + \":\" + segments[3];\n  }\n  return undefined;\n};\n\nexport const getDidAddress = (iss: string) => {\n  const segments = iss && getDidAddressSegments(iss);\n  if (segments) {\n    return segments.pop();\n  }\n  return undefined;\n};\n\nexport async function validateSignedCacao(params: { cacao: AuthTypes.Cacao; projectId?: string }) {\n  const { cacao, projectId } = params;\n  const { s: signature, p: payload } = cacao;\n  const reconstructed = formatMessage(payload, payload.iss);\n  const walletAddress = getDidAddress(payload.iss) as string;\n  const isValid = await verifySignature(\n    walletAddress,\n    reconstructed,\n    signature,\n    getNamespacedDidChainId(payload.iss) as string,\n    projectId as string,\n  );\n\n  return isValid;\n}\n\nexport const formatMessage = (cacao: AuthTypes.FormatMessageParams, iss: string) => {\n  const header = `${cacao.domain} wants you to sign in with your Ethereum account:`;\n  const walletAddress = getDidAddress(iss);\n\n  if (!cacao.aud && !cacao.uri) {\n    throw new Error(\"Either `aud` or `uri` is required to construct the message\");\n  }\n\n  let statement = cacao.statement || undefined;\n  const uri = `URI: ${cacao.aud || cacao.uri}`;\n  const version = `Version: ${cacao.version}`;\n  const chainId = `Chain ID: ${getDidChainId(iss)}`;\n  const nonce = `Nonce: ${cacao.nonce}`;\n  const issuedAt = `Issued At: ${cacao.iat}`;\n  const expirationTime = cacao.exp ? `Expiration Time: ${cacao.exp}` : undefined;\n  const notBefore = cacao.nbf ? `Not Before: ${cacao.nbf}` : undefined;\n  const requestId = cacao.requestId ? `Request ID: ${cacao.requestId}` : undefined;\n  const resources = cacao.resources\n    ? `Resources:${cacao.resources.map((resource) => `\\n- ${resource}`).join(\"\")}`\n    : undefined;\n  const recap = getRecapFromResources(cacao.resources);\n  if (recap) {\n    const decoded = decodeRecap(recap);\n    statement = formatStatementFromRecap(statement, decoded);\n  }\n\n  const message = [\n    header,\n    walletAddress,\n    ``,\n    statement,\n    ``,\n    uri,\n    version,\n    chainId,\n    nonce,\n    issuedAt,\n    expirationTime,\n    notBefore,\n    requestId,\n    resources,\n  ]\n    .filter((val) => val !== undefined && val !== null) // remove unnecessary empty lines\n    .join(\"\\n\");\n\n  return message;\n};\n\nexport function buildAuthObject(\n  requestPayload: AuthTypes.PayloadParams,\n  signature: AuthTypes.CacaoSignature,\n  iss: string,\n) {\n  if (!iss.includes(\"did:pkh:\")) {\n    iss = `did:pkh:${iss}`;\n  }\n\n  const authObject: AuthTypes.Cacao = {\n    h: {\n      t: \"caip122\",\n    },\n    p: {\n      iss,\n      domain: requestPayload.domain,\n      aud: requestPayload.aud,\n      version: requestPayload.version,\n      nonce: requestPayload.nonce,\n      iat: requestPayload.iat,\n      statement: requestPayload.statement,\n      requestId: requestPayload.requestId,\n      resources: requestPayload.resources,\n      nbf: requestPayload.nbf,\n      exp: requestPayload.exp,\n    },\n    s: signature,\n  };\n  return authObject;\n}\ntype PopulateAuthPayloadParams = {\n  authPayload: AuthTypes.PayloadParams;\n  chains: string[];\n  methods: string[];\n};\nexport function populateAuthPayload(params: PopulateAuthPayloadParams): AuthTypes.PayloadParams {\n  const { authPayload, chains, methods } = params;\n  const statement = authPayload.statement || \"\";\n\n  if (!chains?.length) return authPayload;\n\n  const requested = authPayload.chains;\n  const supported = chains;\n\n  const supportedChains = getCommonValuesInArrays<string>(requested, supported);\n  if (!supportedChains?.length) {\n    throw new Error(\"No supported chains\");\n  }\n\n  const requestedRecaps = getDecodedRecapFromResources(authPayload.resources);\n  if (!requestedRecaps) return authPayload;\n\n  isValidRecap(requestedRecaps);\n  const resource = getRecapResource(requestedRecaps, \"eip155\");\n  let updatedResources = authPayload?.resources || [];\n\n  if (resource?.length) {\n    const actions = getReCapActions(resource);\n    const supportedActions = getCommonValuesInArrays<string>(actions, methods);\n    if (!supportedActions?.length) {\n      throw new Error(\n        `Supported methods don't satisfy the requested: ${JSON.stringify(\n          actions,\n        )}, supported: ${JSON.stringify(methods)}`,\n      );\n    }\n    const formattedActions = assignAbilityToActions(\"request\", supportedActions as string[], {\n      chains: supportedChains,\n    });\n    const updatedRecap = addResourceToRecap(requestedRecaps, \"eip155\", formattedActions);\n    // remove recap from resources as we will add the updated one\n    updatedResources = authPayload?.resources?.slice(0, -1) || [];\n    updatedResources.push(encodeRecap(updatedRecap));\n  }\n\n  return {\n    ...authPayload,\n    statement: buildRecapStatement(statement, getRecapFromResources(updatedResources)),\n    chains: supportedChains,\n    resources: authPayload?.resources || updatedResources.length > 0 ? updatedResources : undefined,\n  };\n}\n\nexport function getDecodedRecapFromResources(resources?: string[]) {\n  const resource = getRecapFromResources(resources);\n  if (!resource) return;\n  if (!isRecap(resource)) return;\n  return decodeRecap(resource);\n}\n\nexport function recapHasResource(recap: any, resource: string) {\n  return recap?.att?.hasOwnProperty(resource);\n}\n\nexport function getRecapResource(recap: any, resource: string): any[] {\n  return recap?.att?.[resource] ? Object.keys(recap?.att?.[resource]) : [];\n}\n\nexport function getRecapAbilitiesFromResource(actions: any[]) {\n  return actions?.map((action) => Object.keys(action)) || [];\n}\n\nexport function getReCapActions(abilities: any[]) {\n  return abilities?.map((ability) => ability.split(\"/\")?.[1]) || [];\n}\n\nexport function base64Encode(input: unknown): string {\n  return Buffer.from(JSON.stringify(input)).toString(\"base64\");\n}\n\nexport function base64Decode(encodedString: string): string {\n  return JSON.parse(Buffer.from(encodedString, \"base64\").toString(\"utf-8\"));\n}\n\nexport function isValidRecap(recap: any) {\n  if (!recap) throw new Error(\"No recap provided, value is undefined\");\n  if (!recap.att) throw new Error(\"No `att` property found\");\n  const resources = Object.keys(recap.att);\n  if (!resources?.length) throw new Error(\"No resources found in `att` property\");\n  resources.forEach((resource) => {\n    const resourceAbilities = recap.att[resource];\n    if (Array.isArray(resourceAbilities))\n      throw new Error(`Resource must be an object: ${resource}`);\n    if (typeof resourceAbilities !== \"object\")\n      throw new Error(`Resource must be an object: ${resource}`);\n    if (!Object.keys(resourceAbilities).length)\n      throw new Error(`Resource object is empty: ${resource}`);\n\n    Object.keys(resourceAbilities).forEach((ability) => {\n      const limits = resourceAbilities[ability];\n      if (!Array.isArray(limits))\n        throw new Error(`Ability limits ${ability} must be an array of objects, found: ${limits}`);\n      if (!limits.length)\n        throw new Error(`Value of ${ability} is empty array, must be an array with objects`);\n      limits.forEach((limit) => {\n        if (typeof limit !== \"object\")\n          throw new Error(\n            `Ability limits (${ability}) must be an array of objects, found: ${limit}`,\n          );\n      });\n    });\n  });\n}\n\nexport function createRecap(resource: string, ability: string, actions: string[], limits = {}) {\n  actions?.sort((a, b) => a.localeCompare(b));\n  return {\n    att: { [resource]: assignAbilityToActions(ability, actions, limits) },\n  };\n}\n\ntype RecapType = {\n  att: {\n    [key: string]: Record<string, unknown>;\n  };\n};\nexport function addResourceToRecap(recap: RecapType, resource: string, actions: unknown[]) {\n  recap.att[resource] = {\n    ...actions,\n  };\n  const keys = Object.keys(recap.att)?.sort((a, b) => a.localeCompare(b));\n  const baseRecap: RecapType = { att: {} };\n  const sorted = keys.reduce((obj, key) => {\n    obj.att[key] = recap.att[key];\n    return obj;\n  }, baseRecap);\n  return sorted;\n}\n\nexport function assignAbilityToActions(ability: string, actions: string[], limits = {}) {\n  // sort resources alphabetically\n  actions = actions?.sort((a, b) => a.localeCompare(b));\n  const abilities = actions.map((action) => {\n    return {\n      [`${ability}/${action}`]: [limits],\n    };\n  });\n  return Object.assign({}, ...abilities);\n}\n\nexport function encodeRecap(recap: any) {\n  isValidRecap(recap);\n  // remove the padding from the base64 string as per recap spec\n  return `urn:recap:${base64Encode(recap).replace(/=/g, \"\")}`;\n}\n\nexport function decodeRecap(recap: any): RecapType {\n  // base64Decode adds padding internally so don't need to add it back if it was removed\n  const decoded = base64Decode(recap.replace(\"urn:recap:\", \"\"));\n  isValidRecap(decoded);\n  return decoded as unknown as RecapType;\n}\n\nexport function createEncodedRecap(resource: string, ability: string, actions: string[]): string {\n  const recap = createRecap(resource, ability, actions);\n  return encodeRecap(recap);\n}\n\nexport function isRecap(resource: string) {\n  return resource && resource.includes(\"urn:recap:\");\n}\n\nexport function mergeEncodedRecaps(recap1: string, recap2: string) {\n  const decoded1 = decodeRecap(recap1);\n  const decoded2 = decodeRecap(recap2);\n  const merged = mergeRecaps(decoded1, decoded2);\n  return encodeRecap(merged);\n}\n\nexport function mergeRecaps(recap1: RecapType, recap2: RecapType) {\n  isValidRecap(recap1);\n  isValidRecap(recap2);\n  const keys = Object.keys(recap1.att)\n    .concat(Object.keys(recap2.att))\n    .sort((a, b) => a.localeCompare(b));\n  const mergedRecap: RecapType = { att: {} };\n  keys.forEach((key) => {\n    const actions = Object.keys(recap1.att?.[key] || {})\n      .concat(Object.keys(recap2.att?.[key] || {}))\n      .sort((a, b) => a.localeCompare(b));\n    actions.forEach((action) => {\n      mergedRecap.att[key] = {\n        ...mergedRecap.att[key],\n        [action]: recap1.att[key]?.[action] || recap2.att[key]?.[action],\n      };\n    });\n  });\n  return mergedRecap;\n}\n\nexport function formatStatementFromRecap(statement = \"\", recap: RecapType) {\n  isValidRecap(recap);\n  const base = \"I further authorize the stated URI to perform the following actions on my behalf: \";\n\n  if (statement.includes(base)) return statement;\n\n  const statementForRecap: string[] = [];\n  let currentCounter = 0;\n  Object.keys(recap.att).forEach((resource) => {\n    const actions = Object.keys(recap.att[resource]).map((ability: any) => {\n      return {\n        ability: ability.split(\"/\")[0],\n        action: ability.split(\"/\")[1],\n      };\n    });\n    //\n    actions.sort((a, b) => a.action.localeCompare(b.action));\n    const uniqueAbilities: Record<string, string[]> = {};\n    actions.forEach((action: any) => {\n      if (!uniqueAbilities[action.ability]) {\n        uniqueAbilities[action.ability] = [];\n      }\n      uniqueAbilities[action.ability].push(action.action);\n    });\n    const abilities = Object.keys(uniqueAbilities).map((ability) => {\n      currentCounter++;\n      return `(${currentCounter}) '${ability}': '${uniqueAbilities[ability].join(\n        \"', '\",\n      )}' for '${resource}'.`;\n    });\n    statementForRecap.push(abilities.join(\", \").replace(\".,\", \".\"));\n  });\n\n  const recapStatemet = statementForRecap.join(\" \");\n  const recapStatement = `${base}${recapStatemet}`;\n  // add a space if there is a statement\n  return `${statement ? statement + \" \" : \"\"}${recapStatement}`;\n}\n\nexport function getMethodsFromRecap(recap: string) {\n  const decoded = decodeRecap(recap);\n  isValidRecap(decoded);\n  // methods are only available for eip155 as per the current implementation\n  const resource = decoded.att?.eip155;\n  if (!resource) return [];\n  return Object.keys(resource).map((ability: any) => ability.split(\"/\")[1]);\n}\n\nexport function getChainsFromRecap(recap: string) {\n  const decoded = decodeRecap(recap);\n  isValidRecap(decoded);\n  const chains: string[] = [];\n\n  Object.values(decoded.att).forEach((resource: any) => {\n    Object.values(resource).forEach((ability: any) => {\n      if (ability?.[0]?.chains) {\n        chains.push(ability[0].chains);\n      }\n    });\n  });\n  return [...new Set(chains.flat())];\n}\n\nexport function buildRecapStatement(statement: string, recap: unknown) {\n  if (!recap) return statement;\n  const decoded = decodeRecap(recap);\n  isValidRecap(decoded);\n  return formatStatementFromRecap(statement, decoded);\n}\n\nexport function getRecapFromResources(resources?: string[]) {\n  if (!resources) return;\n  // per spec, recap is always the last resource\n  const resource = resources?.[resources.length - 1];\n  return isRecap(resource) ? resource : undefined;\n}\n", "/**\n * Internal assertion helpers.\n * @module\n */\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error('positive integer expected, got ' + n);\n}\n// copied from utils\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\nfunction ahash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.wrapConstructor');\n    anumber(h.outputLen);\n    anumber(h.blockLen);\n}\nfunction aexists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction aoutput(out, instance) {\n    abytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error('digestInto() expects output buffer of length at least ' + min);\n    }\n}\nfunction abool(b) {\n    if (typeof b !== 'boolean')\n        throw new Error(`boolean expected, not ${b}`);\n}\nexport { abool, abytes, aexists, ahash, anumber, aoutput, isBytes };\n//# sourceMappingURL=_assert.js.map", "/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-ciphers - MIT License (c) 2023 <PERSON> (paulmillr.com) */\nimport { abytes, isBytes } from './_assert.js';\n// Cast array to different type\nexport const u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nexport const createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// big-endian hardware is rare. Just in case someone still decides to run ciphers:\n// early-throw an error because we don't support BE yet.\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE)\n    throw new Error('Non little-endian hardware is not supported');\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    abytes(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\nexport function hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    return BigInt(hex === '' ? '0' : '0x' + hex); // Big Endian\n}\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nexport function numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => { };\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * @example bytesToUtf8(new Uint8Array([97, 98, 99])) // 'abc'\n */\nexport function bytesToUtf8(bytes) {\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    else if (isBytes(data))\n        data = copyBytes(data);\n    else\n        throw new Error('Uint8Array expected, got ' + typeof data);\n    return data;\n}\n/**\n * Checks if two U8A use same underlying buffer and overlaps (will corrupt and break if input and output same)\n */\nexport function overlapBytes(a, b) {\n    return (a.buffer === b.buffer && // probably will fail with some obscure proxies, but this is best we can do\n        a.byteOffset < b.byteOffset + b.byteLength && // a starts before b end\n        b.byteOffset < a.byteOffset + a.byteLength // b starts before a end\n    );\n}\n/**\n * If input and output overlap and input starts before output, we will overwrite end of input before\n * we start processing it, so this is not supported for most ciphers (except chacha/salse, which designed with this)\n */\nexport function complexOverlapBytes(input, output) {\n    // This is very cursed. It works somehow, but I'm completely unsure,\n    // reasoning about overlapping aligned windows is very hard.\n    if (overlapBytes(input, output) && input.byteOffset < output.byteOffset)\n        throw new Error('complex overlap of input and output is not supported');\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\nexport function checkOpts(defaults, opts) {\n    if (opts == null || typeof opts !== 'object')\n        throw new Error('options must be defined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\n// Compares 2 u8a-s in kinda constant time\nexport function equalBytes(a, b) {\n    if (a.length !== b.length)\n        return false;\n    let diff = 0;\n    for (let i = 0; i < a.length; i++)\n        diff |= a[i] ^ b[i];\n    return diff === 0;\n}\n// For runtime check if class implements interface\nexport class Hash {\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nexport const wrapCipher = (params, constructor) => {\n    function wrappedCipher(key, ...args) {\n        // Validate key\n        abytes(key);\n        // Validate nonce if nonceLength is present\n        if (params.nonceLength !== undefined) {\n            const nonce = args[0];\n            if (!nonce)\n                throw new Error('nonce / iv required');\n            if (params.varSizeNonce)\n                abytes(nonce);\n            else\n                abytes(nonce, params.nonceLength);\n        }\n        // Validate AAD if tagLength present\n        const tagl = params.tagLength;\n        if (tagl && args[1] !== undefined) {\n            abytes(args[1]);\n        }\n        const cipher = constructor(key, ...args);\n        const checkOutput = (fnLength, output) => {\n            if (output !== undefined) {\n                if (fnLength !== 2)\n                    throw new Error('cipher output not supported');\n                abytes(output);\n            }\n        };\n        // Create wrapped cipher with validation and single-use encryption\n        let called = false;\n        const wrCipher = {\n            encrypt(data, output) {\n                if (called)\n                    throw new Error('cannot encrypt() twice with same key + nonce');\n                called = true;\n                abytes(data);\n                checkOutput(cipher.encrypt.length, output);\n                return cipher.encrypt(data, output);\n            },\n            decrypt(data, output) {\n                abytes(data);\n                if (tagl && data.length < tagl)\n                    throw new Error('invalid ciphertext length: smaller than tagLength=' + tagl);\n                checkOutput(cipher.decrypt.length, output);\n                return cipher.decrypt(data, output);\n            },\n        };\n        return wrCipher;\n    }\n    Object.assign(wrappedCipher, params);\n    return wrappedCipher;\n};\nexport function getOutput(expectedLength, out, onlyAligned = true) {\n    if (out === undefined)\n        return new Uint8Array(expectedLength);\n    if (out.length !== expectedLength)\n        throw new Error('invalid output length, expected ' + expectedLength + ', got: ' + out.length);\n    if (onlyAligned && !isAligned32(out))\n        throw new Error('invalid output, must be aligned');\n    return out;\n}\n// Polyfill for Safari 14\nexport function setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\nexport function u64Lengths(ciphertext, AAD) {\n    const num = new Uint8Array(16);\n    const view = createView(num);\n    setBigUint64(view, 0, BigInt(AAD ? AAD.length : 0), true);\n    setBigUint64(view, 8, BigInt(ciphertext.length), true);\n    return num;\n}\n// Is byte array aligned to 4 byte offset (u32)?\nexport function isAligned32(bytes) {\n    return bytes.byteOffset % 4 === 0;\n}\n// copy bytes to new u8a (aligned). Because Buffer.slice is broken.\nexport function copyBytes(bytes) {\n    return Uint8Array.from(bytes);\n}\nexport function clean(...arrays) {\n    for (let i = 0; i < arrays.length; i++) {\n        arrays[i].fill(0);\n    }\n}\n//# sourceMappingURL=utils.js.map", "/**\n * Basic utils for ARX (add-rotate-xor) salsa and chacha ciphers.\n\nRFC8439 requires multi-step cipher stream, where\nauth<PERSON>ey starts with counter: 0, actual msg with counter: 1.\n\nFor this, we need a way to re-use nonce / counter:\n\n    const counter = new Uint8Array(4);\n    chacha(..., counter, ...); // counter is now 1\n    chacha(..., counter, ...); // counter is now 2\n\nThis is complicated:\n\n- 32-bit counters are enough, no need for 64-bit: max ArrayBuffer size in JS is 4GB\n- Original papers don't allow mutating counters\n- Counter overflow is undefined [^1]\n- Idea A: allow providing (nonce | counter) instead of just nonce, re-use it\n- Caveat: Cannot be re-used through all cases:\n- * chacha has (counter | nonce)\n- * xchacha has (nonce16 | counter | nonce16)\n- Idea B: separate nonce / counter and provide separate API for counter re-use\n- Caveat: there are different counter sizes depending on an algorithm.\n- salsa & chacha also differ in structures of key & sigma:\n  salsa20:      s[0] | k(4) | s[1] | nonce(2) | ctr(2) | s[2] | k(4) | s[3]\n  chacha:       s(4) | k(8) | ctr(1) | nonce(3)\n  chacha20orig: s(4) | k(8) | ctr(2) | nonce(2)\n- Idea C: helper method such as `setSalsaState(key, nonce, sigma, data)`\n- Caveat: we can't re-use counter array\n\nxchacha [^2] uses the subkey and remaining 8 byte nonce with ChaCha20 as normal\n(prefixed by 4 NUL bytes, since [RFC8439] specifies a 12-byte nonce).\n\n[^1]: https://mailarchive.ietf.org/arch/msg/cfrg/gsOnTJzcbgG6OqD8Sc0GO5aR_tU/\n[^2]: https://datatracker.ietf.org/doc/html/draft-irtf-cfrg-xchacha#appendix-A.2\n\n * @module\n */\nimport { abool, abytes, anumber } from './_assert.js';\nimport { checkOpts, clean, copyBytes, u32 } from './utils.js';\n// We can't make top-level var depend on utils.utf8ToBytes\n// because it's not present in all envs. Creating a similar fn here\nconst _utf8ToBytes = (str) => Uint8Array.from(str.split('').map((c) => c.charCodeAt(0)));\nconst sigma16 = _utf8ToBytes('expand 16-byte k');\nconst sigma32 = _utf8ToBytes('expand 32-byte k');\nconst sigma16_32 = u32(sigma16);\nconst sigma32_32 = u32(sigma32);\nexport function rotl(a, b) {\n    return (a << b) | (a >>> (32 - b));\n}\n// Is byte array aligned to 4 byte offset (u32)?\nfunction isAligned32(b) {\n    return b.byteOffset % 4 === 0;\n}\n// Salsa and Chacha block length is always 512-bit\nconst BLOCK_LEN = 64;\nconst BLOCK_LEN32 = 16;\n// new Uint32Array([2**32])   // => Uint32Array(1) [ 0 ]\n// new Uint32Array([2**32-1]) // => Uint32Array(1) [ 4294967295 ]\nconst MAX_COUNTER = 2 ** 32 - 1;\nconst U32_EMPTY = new Uint32Array();\nfunction runCipher(core, sigma, key, nonce, data, output, counter, rounds) {\n    const len = data.length;\n    const block = new Uint8Array(BLOCK_LEN);\n    const b32 = u32(block);\n    // Make sure that buffers aligned to 4 bytes\n    const isAligned = isAligned32(data) && isAligned32(output);\n    const d32 = isAligned ? u32(data) : U32_EMPTY;\n    const o32 = isAligned ? u32(output) : U32_EMPTY;\n    for (let pos = 0; pos < len; counter++) {\n        core(sigma, key, nonce, b32, counter, rounds);\n        if (counter >= MAX_COUNTER)\n            throw new Error('arx: counter overflow');\n        const take = Math.min(BLOCK_LEN, len - pos);\n        // aligned to 4 bytes\n        if (isAligned && take === BLOCK_LEN) {\n            const pos32 = pos / 4;\n            if (pos % 4 !== 0)\n                throw new Error('arx: invalid block position');\n            for (let j = 0, posj; j < BLOCK_LEN32; j++) {\n                posj = pos32 + j;\n                o32[posj] = d32[posj] ^ b32[j];\n            }\n            pos += BLOCK_LEN;\n            continue;\n        }\n        for (let j = 0, posj; j < take; j++) {\n            posj = pos + j;\n            output[posj] = data[posj] ^ block[j];\n        }\n        pos += take;\n    }\n}\n/** Creates ARX-like (ChaCha, Salsa) cipher stream from core function. */\nexport function createCipher(core, opts) {\n    const { allowShortKeys, extendNonceFn, counterLength, counterRight, rounds } = checkOpts({ allowShortKeys: false, counterLength: 8, counterRight: false, rounds: 20 }, opts);\n    if (typeof core !== 'function')\n        throw new Error('core must be a function');\n    anumber(counterLength);\n    anumber(rounds);\n    abool(counterRight);\n    abool(allowShortKeys);\n    return (key, nonce, data, output, counter = 0) => {\n        abytes(key);\n        abytes(nonce);\n        abytes(data);\n        const len = data.length;\n        if (output === undefined)\n            output = new Uint8Array(len);\n        abytes(output);\n        anumber(counter);\n        if (counter < 0 || counter >= MAX_COUNTER)\n            throw new Error('arx: counter overflow');\n        if (output.length < len)\n            throw new Error(`arx: output (${output.length}) is shorter than data (${len})`);\n        const toClean = [];\n        // Key & sigma\n        // key=16 -> sigma16, k=key|key\n        // key=32 -> sigma32, k=key\n        let l = key.length;\n        let k;\n        let sigma;\n        if (l === 32) {\n            toClean.push((k = copyBytes(key)));\n            sigma = sigma32_32;\n        }\n        else if (l === 16 && allowShortKeys) {\n            k = new Uint8Array(32);\n            k.set(key);\n            k.set(key, 16);\n            sigma = sigma16_32;\n            toClean.push(k);\n        }\n        else {\n            throw new Error(`arx: invalid 32-byte key, got length=${l}`);\n        }\n        // Nonce\n        // salsa20:      8   (8-byte counter)\n        // chacha20orig: 8   (8-byte counter)\n        // chacha20:     12  (4-byte counter)\n        // xsalsa20:     24  (16 -> hsalsa,  8 -> old nonce)\n        // xchacha20:    24  (16 -> hchacha, 8 -> old nonce)\n        // Align nonce to 4 bytes\n        if (!isAligned32(nonce))\n            toClean.push((nonce = copyBytes(nonce)));\n        const k32 = u32(k);\n        // hsalsa & hchacha: handle extended nonce\n        if (extendNonceFn) {\n            if (nonce.length !== 24)\n                throw new Error(`arx: extended nonce must be 24 bytes`);\n            extendNonceFn(sigma, k32, u32(nonce.subarray(0, 16)), k32);\n            nonce = nonce.subarray(16);\n        }\n        // Handle nonce counter\n        const nonceNcLen = 16 - counterLength;\n        if (nonceNcLen !== nonce.length)\n            throw new Error(`arx: nonce must be ${nonceNcLen} or 16 bytes`);\n        // Pad counter when nonce is 64 bit\n        if (nonceNcLen !== 12) {\n            const nc = new Uint8Array(12);\n            nc.set(nonce, counterRight ? 0 : 12 - nonce.length);\n            nonce = nc;\n            toClean.push(nonce);\n        }\n        const n32 = u32(nonce);\n        runCipher(core, sigma, k32, n32, data, output, counter, rounds);\n        clean(...toClean);\n        return output;\n    };\n}\n//# sourceMappingURL=_arx.js.map", "/**\n * Poly1305 ([PDF](https://cr.yp.to/mac/poly1305-20050329.pdf),\n * [wiki](https://en.wikipedia.org/wiki/Poly1305))\n * is a fast and parallel secret-key message-authentication code suitable for\n * a wide variety of applications. It was standardized in\n * [RFC 8439](https://datatracker.ietf.org/doc/html/rfc8439) and is now used in TLS 1.3.\n *\n * Polynomial MACs are not perfect for every situation:\n * they lack Random Key Robustness: the MAC can be forged, and can't be used in PAKE schemes.\n * See [invisible salamanders attack](https://keymaterial.net/2020/09/07/invisible-salamanders-in-aes-gcm-siv/).\n * To combat invisible salamanders, `hash(key)` can be included in ciphertext,\n * however, this would violate ciphertext indistinguishability:\n * an attacker would know which key was used - so `HKDF(key, i)`\n * could be used instead.\n *\n * Check out [original website](https://cr.yp.to/mac.html).\n * @module\n */\nimport { abytes, aexists, aoutput } from './_assert.js';\nimport { Hash, clean, toBytes } from './utils.js';\n// Based on Public Domain poly1305-donna https://github.com/floodyberry/poly1305-donna\nconst u8to16 = (a, i) => (a[i++] & 0xff) | ((a[i++] & 0xff) << 8);\nclass Poly1305 {\n    constructor(key) {\n        this.blockLen = 16;\n        this.outputLen = 16;\n        this.buffer = new Uint8Array(16);\n        this.r = new Uint16Array(10);\n        this.h = new Uint16Array(10);\n        this.pad = new Uint16Array(8);\n        this.pos = 0;\n        this.finished = false;\n        key = toBytes(key);\n        abytes(key, 32);\n        const t0 = u8to16(key, 0);\n        const t1 = u8to16(key, 2);\n        const t2 = u8to16(key, 4);\n        const t3 = u8to16(key, 6);\n        const t4 = u8to16(key, 8);\n        const t5 = u8to16(key, 10);\n        const t6 = u8to16(key, 12);\n        const t7 = u8to16(key, 14);\n        // https://github.com/floodyberry/poly1305-donna/blob/e6ad6e091d30d7f4ec2d4f978be1fcfcbce72781/poly1305-donna-16.h#L47\n        this.r[0] = t0 & 0x1fff;\n        this.r[1] = ((t0 >>> 13) | (t1 << 3)) & 0x1fff;\n        this.r[2] = ((t1 >>> 10) | (t2 << 6)) & 0x1f03;\n        this.r[3] = ((t2 >>> 7) | (t3 << 9)) & 0x1fff;\n        this.r[4] = ((t3 >>> 4) | (t4 << 12)) & 0x00ff;\n        this.r[5] = (t4 >>> 1) & 0x1ffe;\n        this.r[6] = ((t4 >>> 14) | (t5 << 2)) & 0x1fff;\n        this.r[7] = ((t5 >>> 11) | (t6 << 5)) & 0x1f81;\n        this.r[8] = ((t6 >>> 8) | (t7 << 8)) & 0x1fff;\n        this.r[9] = (t7 >>> 5) & 0x007f;\n        for (let i = 0; i < 8; i++)\n            this.pad[i] = u8to16(key, 16 + 2 * i);\n    }\n    process(data, offset, isLast = false) {\n        const hibit = isLast ? 0 : 1 << 11;\n        const { h, r } = this;\n        const r0 = r[0];\n        const r1 = r[1];\n        const r2 = r[2];\n        const r3 = r[3];\n        const r4 = r[4];\n        const r5 = r[5];\n        const r6 = r[6];\n        const r7 = r[7];\n        const r8 = r[8];\n        const r9 = r[9];\n        const t0 = u8to16(data, offset + 0);\n        const t1 = u8to16(data, offset + 2);\n        const t2 = u8to16(data, offset + 4);\n        const t3 = u8to16(data, offset + 6);\n        const t4 = u8to16(data, offset + 8);\n        const t5 = u8to16(data, offset + 10);\n        const t6 = u8to16(data, offset + 12);\n        const t7 = u8to16(data, offset + 14);\n        let h0 = h[0] + (t0 & 0x1fff);\n        let h1 = h[1] + (((t0 >>> 13) | (t1 << 3)) & 0x1fff);\n        let h2 = h[2] + (((t1 >>> 10) | (t2 << 6)) & 0x1fff);\n        let h3 = h[3] + (((t2 >>> 7) | (t3 << 9)) & 0x1fff);\n        let h4 = h[4] + (((t3 >>> 4) | (t4 << 12)) & 0x1fff);\n        let h5 = h[5] + ((t4 >>> 1) & 0x1fff);\n        let h6 = h[6] + (((t4 >>> 14) | (t5 << 2)) & 0x1fff);\n        let h7 = h[7] + (((t5 >>> 11) | (t6 << 5)) & 0x1fff);\n        let h8 = h[8] + (((t6 >>> 8) | (t7 << 8)) & 0x1fff);\n        let h9 = h[9] + ((t7 >>> 5) | hibit);\n        let c = 0;\n        let d0 = c + h0 * r0 + h1 * (5 * r9) + h2 * (5 * r8) + h3 * (5 * r7) + h4 * (5 * r6);\n        c = d0 >>> 13;\n        d0 &= 0x1fff;\n        d0 += h5 * (5 * r5) + h6 * (5 * r4) + h7 * (5 * r3) + h8 * (5 * r2) + h9 * (5 * r1);\n        c += d0 >>> 13;\n        d0 &= 0x1fff;\n        let d1 = c + h0 * r1 + h1 * r0 + h2 * (5 * r9) + h3 * (5 * r8) + h4 * (5 * r7);\n        c = d1 >>> 13;\n        d1 &= 0x1fff;\n        d1 += h5 * (5 * r6) + h6 * (5 * r5) + h7 * (5 * r4) + h8 * (5 * r3) + h9 * (5 * r2);\n        c += d1 >>> 13;\n        d1 &= 0x1fff;\n        let d2 = c + h0 * r2 + h1 * r1 + h2 * r0 + h3 * (5 * r9) + h4 * (5 * r8);\n        c = d2 >>> 13;\n        d2 &= 0x1fff;\n        d2 += h5 * (5 * r7) + h6 * (5 * r6) + h7 * (5 * r5) + h8 * (5 * r4) + h9 * (5 * r3);\n        c += d2 >>> 13;\n        d2 &= 0x1fff;\n        let d3 = c + h0 * r3 + h1 * r2 + h2 * r1 + h3 * r0 + h4 * (5 * r9);\n        c = d3 >>> 13;\n        d3 &= 0x1fff;\n        d3 += h5 * (5 * r8) + h6 * (5 * r7) + h7 * (5 * r6) + h8 * (5 * r5) + h9 * (5 * r4);\n        c += d3 >>> 13;\n        d3 &= 0x1fff;\n        let d4 = c + h0 * r4 + h1 * r3 + h2 * r2 + h3 * r1 + h4 * r0;\n        c = d4 >>> 13;\n        d4 &= 0x1fff;\n        d4 += h5 * (5 * r9) + h6 * (5 * r8) + h7 * (5 * r7) + h8 * (5 * r6) + h9 * (5 * r5);\n        c += d4 >>> 13;\n        d4 &= 0x1fff;\n        let d5 = c + h0 * r5 + h1 * r4 + h2 * r3 + h3 * r2 + h4 * r1;\n        c = d5 >>> 13;\n        d5 &= 0x1fff;\n        d5 += h5 * r0 + h6 * (5 * r9) + h7 * (5 * r8) + h8 * (5 * r7) + h9 * (5 * r6);\n        c += d5 >>> 13;\n        d5 &= 0x1fff;\n        let d6 = c + h0 * r6 + h1 * r5 + h2 * r4 + h3 * r3 + h4 * r2;\n        c = d6 >>> 13;\n        d6 &= 0x1fff;\n        d6 += h5 * r1 + h6 * r0 + h7 * (5 * r9) + h8 * (5 * r8) + h9 * (5 * r7);\n        c += d6 >>> 13;\n        d6 &= 0x1fff;\n        let d7 = c + h0 * r7 + h1 * r6 + h2 * r5 + h3 * r4 + h4 * r3;\n        c = d7 >>> 13;\n        d7 &= 0x1fff;\n        d7 += h5 * r2 + h6 * r1 + h7 * r0 + h8 * (5 * r9) + h9 * (5 * r8);\n        c += d7 >>> 13;\n        d7 &= 0x1fff;\n        let d8 = c + h0 * r8 + h1 * r7 + h2 * r6 + h3 * r5 + h4 * r4;\n        c = d8 >>> 13;\n        d8 &= 0x1fff;\n        d8 += h5 * r3 + h6 * r2 + h7 * r1 + h8 * r0 + h9 * (5 * r9);\n        c += d8 >>> 13;\n        d8 &= 0x1fff;\n        let d9 = c + h0 * r9 + h1 * r8 + h2 * r7 + h3 * r6 + h4 * r5;\n        c = d9 >>> 13;\n        d9 &= 0x1fff;\n        d9 += h5 * r4 + h6 * r3 + h7 * r2 + h8 * r1 + h9 * r0;\n        c += d9 >>> 13;\n        d9 &= 0x1fff;\n        c = ((c << 2) + c) | 0;\n        c = (c + d0) | 0;\n        d0 = c & 0x1fff;\n        c = c >>> 13;\n        d1 += c;\n        h[0] = d0;\n        h[1] = d1;\n        h[2] = d2;\n        h[3] = d3;\n        h[4] = d4;\n        h[5] = d5;\n        h[6] = d6;\n        h[7] = d7;\n        h[8] = d8;\n        h[9] = d9;\n    }\n    finalize() {\n        const { h, pad } = this;\n        const g = new Uint16Array(10);\n        let c = h[1] >>> 13;\n        h[1] &= 0x1fff;\n        for (let i = 2; i < 10; i++) {\n            h[i] += c;\n            c = h[i] >>> 13;\n            h[i] &= 0x1fff;\n        }\n        h[0] += c * 5;\n        c = h[0] >>> 13;\n        h[0] &= 0x1fff;\n        h[1] += c;\n        c = h[1] >>> 13;\n        h[1] &= 0x1fff;\n        h[2] += c;\n        g[0] = h[0] + 5;\n        c = g[0] >>> 13;\n        g[0] &= 0x1fff;\n        for (let i = 1; i < 10; i++) {\n            g[i] = h[i] + c;\n            c = g[i] >>> 13;\n            g[i] &= 0x1fff;\n        }\n        g[9] -= 1 << 13;\n        let mask = (c ^ 1) - 1;\n        for (let i = 0; i < 10; i++)\n            g[i] &= mask;\n        mask = ~mask;\n        for (let i = 0; i < 10; i++)\n            h[i] = (h[i] & mask) | g[i];\n        h[0] = (h[0] | (h[1] << 13)) & 0xffff;\n        h[1] = ((h[1] >>> 3) | (h[2] << 10)) & 0xffff;\n        h[2] = ((h[2] >>> 6) | (h[3] << 7)) & 0xffff;\n        h[3] = ((h[3] >>> 9) | (h[4] << 4)) & 0xffff;\n        h[4] = ((h[4] >>> 12) | (h[5] << 1) | (h[6] << 14)) & 0xffff;\n        h[5] = ((h[6] >>> 2) | (h[7] << 11)) & 0xffff;\n        h[6] = ((h[7] >>> 5) | (h[8] << 8)) & 0xffff;\n        h[7] = ((h[8] >>> 8) | (h[9] << 5)) & 0xffff;\n        let f = h[0] + pad[0];\n        h[0] = f & 0xffff;\n        for (let i = 1; i < 8; i++) {\n            f = (((h[i] + pad[i]) | 0) + (f >>> 16)) | 0;\n            h[i] = f & 0xffff;\n        }\n        clean(g);\n    }\n    update(data) {\n        aexists(this);\n        const { buffer, blockLen } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input\n            if (take === blockLen) {\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(data, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(buffer, 0, false);\n                this.pos = 0;\n            }\n        }\n        return this;\n    }\n    destroy() {\n        clean(this.h, this.r, this.buffer, this.pad);\n    }\n    digestInto(out) {\n        aexists(this);\n        aoutput(out, this);\n        this.finished = true;\n        const { buffer, h } = this;\n        let { pos } = this;\n        if (pos) {\n            buffer[pos++] = 1;\n            for (; pos < 16; pos++)\n                buffer[pos] = 0;\n            this.process(buffer, 0, true);\n        }\n        this.finalize();\n        let opos = 0;\n        for (let i = 0; i < 8; i++) {\n            out[opos++] = h[i] >>> 0;\n            out[opos++] = h[i] >>> 8;\n        }\n        return out;\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n}\nexport function wrapConstructorWithKey(hashCons) {\n    const hashC = (msg, key) => hashCons(key).update(toBytes(msg)).digest();\n    const tmp = hashCons(new Uint8Array(32));\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (key) => hashCons(key);\n    return hashC;\n}\n/** Poly1305 MAC from RFC 8439. */\nexport const poly1305 = wrapConstructorWithKey((key) => new Poly1305(key));\n//# sourceMappingURL=_poly1305.js.map", "/**\n * [<PERSON><PERSON>ha20](https://cr.yp.to/chacha.html) stream cipher, released\n * in 2008. Developed after Salsa20, ChaCha aims to increase diffusion per round.\n * It was standardized in [RFC 8439](https://datatracker.ietf.org/doc/html/rfc8439) and\n * is now used in TLS 1.3.\n *\n * [XChaCha20](https://datatracker.ietf.org/doc/html/draft-irtf-cfrg-xchacha)\n * extended-nonce variant is also provided. Similar to XSalsa, it's safe to use with\n * randomly-generated nonces.\n *\n * Check out [PDF](http://cr.yp.to/chacha/chacha-20080128.pdf) and\n * [wiki](https://en.wikipedia.org/wiki/Salsa20).\n * @module\n */\nimport { createCipher, rotl } from './_arx.js';\nimport { poly1305 } from './_poly1305.js';\nimport { clean, createView, equalBytes, getOutput, setBigUint64, wrapCipher, } from './utils.js';\n/**\n * <PERSON><PERSON>ha core function.\n */\n// prettier-ignore\nfunction chachaCore(s, k, n, out, cnt, rounds = 20) {\n    let y00 = s[0], y01 = s[1], y02 = s[2], y03 = s[3], // \"expa\"   \"nd 3\"  \"2-by\"  \"te k\"\n    y04 = k[0], y05 = k[1], y06 = k[2], y07 = k[3], // Key      Key     Key     Key\n    y08 = k[4], y09 = k[5], y10 = k[6], y11 = k[7], // Key      Key     Key     Key\n    y12 = cnt, y13 = n[0], y14 = n[1], y15 = n[2]; // Counter  Counter\tNonce   Nonce\n    // Save state to temporary variables\n    let x00 = y00, x01 = y01, x02 = y02, x03 = y03, x04 = y04, x05 = y05, x06 = y06, x07 = y07, x08 = y08, x09 = y09, x10 = y10, x11 = y11, x12 = y12, x13 = y13, x14 = y14, x15 = y15;\n    for (let r = 0; r < rounds; r += 2) {\n        x00 = (x00 + x04) | 0;\n        x12 = rotl(x12 ^ x00, 16);\n        x08 = (x08 + x12) | 0;\n        x04 = rotl(x04 ^ x08, 12);\n        x00 = (x00 + x04) | 0;\n        x12 = rotl(x12 ^ x00, 8);\n        x08 = (x08 + x12) | 0;\n        x04 = rotl(x04 ^ x08, 7);\n        x01 = (x01 + x05) | 0;\n        x13 = rotl(x13 ^ x01, 16);\n        x09 = (x09 + x13) | 0;\n        x05 = rotl(x05 ^ x09, 12);\n        x01 = (x01 + x05) | 0;\n        x13 = rotl(x13 ^ x01, 8);\n        x09 = (x09 + x13) | 0;\n        x05 = rotl(x05 ^ x09, 7);\n        x02 = (x02 + x06) | 0;\n        x14 = rotl(x14 ^ x02, 16);\n        x10 = (x10 + x14) | 0;\n        x06 = rotl(x06 ^ x10, 12);\n        x02 = (x02 + x06) | 0;\n        x14 = rotl(x14 ^ x02, 8);\n        x10 = (x10 + x14) | 0;\n        x06 = rotl(x06 ^ x10, 7);\n        x03 = (x03 + x07) | 0;\n        x15 = rotl(x15 ^ x03, 16);\n        x11 = (x11 + x15) | 0;\n        x07 = rotl(x07 ^ x11, 12);\n        x03 = (x03 + x07) | 0;\n        x15 = rotl(x15 ^ x03, 8);\n        x11 = (x11 + x15) | 0;\n        x07 = rotl(x07 ^ x11, 7);\n        x00 = (x00 + x05) | 0;\n        x15 = rotl(x15 ^ x00, 16);\n        x10 = (x10 + x15) | 0;\n        x05 = rotl(x05 ^ x10, 12);\n        x00 = (x00 + x05) | 0;\n        x15 = rotl(x15 ^ x00, 8);\n        x10 = (x10 + x15) | 0;\n        x05 = rotl(x05 ^ x10, 7);\n        x01 = (x01 + x06) | 0;\n        x12 = rotl(x12 ^ x01, 16);\n        x11 = (x11 + x12) | 0;\n        x06 = rotl(x06 ^ x11, 12);\n        x01 = (x01 + x06) | 0;\n        x12 = rotl(x12 ^ x01, 8);\n        x11 = (x11 + x12) | 0;\n        x06 = rotl(x06 ^ x11, 7);\n        x02 = (x02 + x07) | 0;\n        x13 = rotl(x13 ^ x02, 16);\n        x08 = (x08 + x13) | 0;\n        x07 = rotl(x07 ^ x08, 12);\n        x02 = (x02 + x07) | 0;\n        x13 = rotl(x13 ^ x02, 8);\n        x08 = (x08 + x13) | 0;\n        x07 = rotl(x07 ^ x08, 7);\n        x03 = (x03 + x04) | 0;\n        x14 = rotl(x14 ^ x03, 16);\n        x09 = (x09 + x14) | 0;\n        x04 = rotl(x04 ^ x09, 12);\n        x03 = (x03 + x04) | 0;\n        x14 = rotl(x14 ^ x03, 8);\n        x09 = (x09 + x14) | 0;\n        x04 = rotl(x04 ^ x09, 7);\n    }\n    // Write output\n    let oi = 0;\n    out[oi++] = (y00 + x00) | 0;\n    out[oi++] = (y01 + x01) | 0;\n    out[oi++] = (y02 + x02) | 0;\n    out[oi++] = (y03 + x03) | 0;\n    out[oi++] = (y04 + x04) | 0;\n    out[oi++] = (y05 + x05) | 0;\n    out[oi++] = (y06 + x06) | 0;\n    out[oi++] = (y07 + x07) | 0;\n    out[oi++] = (y08 + x08) | 0;\n    out[oi++] = (y09 + x09) | 0;\n    out[oi++] = (y10 + x10) | 0;\n    out[oi++] = (y11 + x11) | 0;\n    out[oi++] = (y12 + x12) | 0;\n    out[oi++] = (y13 + x13) | 0;\n    out[oi++] = (y14 + x14) | 0;\n    out[oi++] = (y15 + x15) | 0;\n}\n/**\n * hchacha helper method, used primarily in xchacha, to hash\n * key and nonce into key' and nonce'.\n * Same as chachaCore, but there doesn't seem to be a way to move the block\n * out without 25% performance hit.\n */\n// prettier-ignore\nexport function hchacha(s, k, i, o32) {\n    let x00 = s[0], x01 = s[1], x02 = s[2], x03 = s[3], x04 = k[0], x05 = k[1], x06 = k[2], x07 = k[3], x08 = k[4], x09 = k[5], x10 = k[6], x11 = k[7], x12 = i[0], x13 = i[1], x14 = i[2], x15 = i[3];\n    for (let r = 0; r < 20; r += 2) {\n        x00 = (x00 + x04) | 0;\n        x12 = rotl(x12 ^ x00, 16);\n        x08 = (x08 + x12) | 0;\n        x04 = rotl(x04 ^ x08, 12);\n        x00 = (x00 + x04) | 0;\n        x12 = rotl(x12 ^ x00, 8);\n        x08 = (x08 + x12) | 0;\n        x04 = rotl(x04 ^ x08, 7);\n        x01 = (x01 + x05) | 0;\n        x13 = rotl(x13 ^ x01, 16);\n        x09 = (x09 + x13) | 0;\n        x05 = rotl(x05 ^ x09, 12);\n        x01 = (x01 + x05) | 0;\n        x13 = rotl(x13 ^ x01, 8);\n        x09 = (x09 + x13) | 0;\n        x05 = rotl(x05 ^ x09, 7);\n        x02 = (x02 + x06) | 0;\n        x14 = rotl(x14 ^ x02, 16);\n        x10 = (x10 + x14) | 0;\n        x06 = rotl(x06 ^ x10, 12);\n        x02 = (x02 + x06) | 0;\n        x14 = rotl(x14 ^ x02, 8);\n        x10 = (x10 + x14) | 0;\n        x06 = rotl(x06 ^ x10, 7);\n        x03 = (x03 + x07) | 0;\n        x15 = rotl(x15 ^ x03, 16);\n        x11 = (x11 + x15) | 0;\n        x07 = rotl(x07 ^ x11, 12);\n        x03 = (x03 + x07) | 0;\n        x15 = rotl(x15 ^ x03, 8);\n        x11 = (x11 + x15) | 0;\n        x07 = rotl(x07 ^ x11, 7);\n        x00 = (x00 + x05) | 0;\n        x15 = rotl(x15 ^ x00, 16);\n        x10 = (x10 + x15) | 0;\n        x05 = rotl(x05 ^ x10, 12);\n        x00 = (x00 + x05) | 0;\n        x15 = rotl(x15 ^ x00, 8);\n        x10 = (x10 + x15) | 0;\n        x05 = rotl(x05 ^ x10, 7);\n        x01 = (x01 + x06) | 0;\n        x12 = rotl(x12 ^ x01, 16);\n        x11 = (x11 + x12) | 0;\n        x06 = rotl(x06 ^ x11, 12);\n        x01 = (x01 + x06) | 0;\n        x12 = rotl(x12 ^ x01, 8);\n        x11 = (x11 + x12) | 0;\n        x06 = rotl(x06 ^ x11, 7);\n        x02 = (x02 + x07) | 0;\n        x13 = rotl(x13 ^ x02, 16);\n        x08 = (x08 + x13) | 0;\n        x07 = rotl(x07 ^ x08, 12);\n        x02 = (x02 + x07) | 0;\n        x13 = rotl(x13 ^ x02, 8);\n        x08 = (x08 + x13) | 0;\n        x07 = rotl(x07 ^ x08, 7);\n        x03 = (x03 + x04) | 0;\n        x14 = rotl(x14 ^ x03, 16);\n        x09 = (x09 + x14) | 0;\n        x04 = rotl(x04 ^ x09, 12);\n        x03 = (x03 + x04) | 0;\n        x14 = rotl(x14 ^ x03, 8);\n        x09 = (x09 + x14) | 0;\n        x04 = rotl(x04 ^ x09, 7);\n    }\n    let oi = 0;\n    o32[oi++] = x00;\n    o32[oi++] = x01;\n    o32[oi++] = x02;\n    o32[oi++] = x03;\n    o32[oi++] = x12;\n    o32[oi++] = x13;\n    o32[oi++] = x14;\n    o32[oi++] = x15;\n}\n/**\n * Original, non-RFC chacha20 from DJB. 8-byte nonce, 8-byte counter.\n */\nexport const chacha20orig = /* @__PURE__ */ createCipher(chachaCore, {\n    counterRight: false,\n    counterLength: 8,\n    allowShortKeys: true,\n});\n/**\n * ChaCha stream cipher. Conforms to RFC 8439 (IETF, TLS). 12-byte nonce, 4-byte counter.\n * With 12-byte nonce, it's not safe to use fill it with random (CSPRNG), due to collision chance.\n */\nexport const chacha20 = /* @__PURE__ */ createCipher(chachaCore, {\n    counterRight: false,\n    counterLength: 4,\n    allowShortKeys: false,\n});\n/**\n * XChaCha eXtended-nonce ChaCha. 24-byte nonce.\n * With 24-byte nonce, it's safe to use fill it with random (CSPRNG).\n * https://datatracker.ietf.org/doc/html/draft-irtf-cfrg-xchacha\n */\nexport const xchacha20 = /* @__PURE__ */ createCipher(chachaCore, {\n    counterRight: false,\n    counterLength: 8,\n    extendNonceFn: hchacha,\n    allowShortKeys: false,\n});\n/**\n * Reduced 8-round chacha, described in original paper.\n */\nexport const chacha8 = /* @__PURE__ */ createCipher(chachaCore, {\n    counterRight: false,\n    counterLength: 4,\n    rounds: 8,\n});\n/**\n * Reduced 12-round chacha, described in original paper.\n */\nexport const chacha12 = /* @__PURE__ */ createCipher(chachaCore, {\n    counterRight: false,\n    counterLength: 4,\n    rounds: 12,\n});\nconst ZEROS16 = /* @__PURE__ */ new Uint8Array(16);\n// Pad to digest size with zeros\nconst updatePadded = (h, msg) => {\n    h.update(msg);\n    const left = msg.length % 16;\n    if (left)\n        h.update(ZEROS16.subarray(left));\n};\nconst ZEROS32 = /* @__PURE__ */ new Uint8Array(32);\nfunction computeTag(fn, key, nonce, data, AAD) {\n    const authKey = fn(key, nonce, ZEROS32);\n    const h = poly1305.create(authKey);\n    if (AAD)\n        updatePadded(h, AAD);\n    updatePadded(h, data);\n    const num = new Uint8Array(16);\n    const view = createView(num);\n    setBigUint64(view, 0, BigInt(AAD ? AAD.length : 0), true);\n    setBigUint64(view, 8, BigInt(data.length), true);\n    h.update(num);\n    const res = h.digest();\n    clean(authKey, num);\n    return res;\n}\n/**\n * AEAD algorithm from RFC 8439.\n * Salsa20 and chacha (RFC 8439) use poly1305 differently.\n * We could have composed them similar to:\n * https://github.com/paulmillr/scure-base/blob/b266c73dde977b1dd7ef40ef7a23cc15aab526b3/index.ts#L250\n * But it's hard because of authKey:\n * In salsa20, authKey changes position in salsa stream.\n * In chacha, authKey can't be computed inside computeTag, it modifies the counter.\n */\nexport const _poly1305_aead = (xorStream) => (key, nonce, AAD) => {\n    const tagLength = 16;\n    return {\n        encrypt(plaintext, output) {\n            const plength = plaintext.length;\n            output = getOutput(plength + tagLength, output, false);\n            output.set(plaintext);\n            const oPlain = output.subarray(0, -tagLength);\n            xorStream(key, nonce, oPlain, oPlain, 1);\n            const tag = computeTag(xorStream, key, nonce, oPlain, AAD);\n            output.set(tag, plength); // append tag\n            clean(tag);\n            return output;\n        },\n        decrypt(ciphertext, output) {\n            output = getOutput(ciphertext.length - tagLength, output, false);\n            const data = ciphertext.subarray(0, -tagLength);\n            const passedTag = ciphertext.subarray(-tagLength);\n            const tag = computeTag(xorStream, key, nonce, data, AAD);\n            if (!equalBytes(passedTag, tag))\n                throw new Error('invalid tag');\n            output.set(ciphertext.subarray(0, -tagLength));\n            xorStream(key, nonce, output, output, 1); // start stream with i=1\n            clean(tag);\n            return output;\n        },\n    };\n};\n/**\n * ChaCha20-Poly1305 from RFC 8439.\n *\n * Unsafe to use random nonces under the same key, due to collision chance.\n * Prefer XChaCha instead.\n */\nexport const chacha20poly1305 = /* @__PURE__ */ wrapCipher({ blockSize: 64, nonceLength: 12, tagLength: 16 }, _poly1305_aead(chacha20));\n/**\n * XChaCha20-Poly1305 extended-nonce chacha.\n *\n * Can be safely used with random nonces (CSPRNG).\n * See [IRTF draft](https://datatracker.ietf.org/doc/html/draft-irtf-cfrg-xchacha).\n */\nexport const xchacha20poly1305 = /* @__PURE__ */ wrapCipher({ blockSize: 64, nonceLength: 24, tagLength: 16 }, _poly1305_aead(xchacha20));\n//# sourceMappingURL=chacha.js.map", "/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\nimport { abytes, aexists, ahash } from './_assert.js';\nimport { Hash, toBytes } from './utils.js';\nexport class HMAC extends Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        ahash(hash);\n        const key = toBytes(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        pad.fill(0);\n    }\n    update(buf) {\n        aexists(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        aexists(this);\n        abytes(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nexport const hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map", "/**\n * HKDF (RFC 5869): extract + expand in one step.\n * See https://soatok.blog/2021/11/17/understanding-hkdf/.\n * @module\n */\nimport { ahash, anumber } from './_assert.js';\nimport { hmac } from './hmac.js';\nimport { toBytes } from './utils.js';\n/**\n * HKDF-extract from spec. Less important part. `HKDF-Extract(IKM, salt) -> PRK`\n * Arguments position differs from spec (IKM is first one, since it is not optional)\n * @param hash - hash function that would be used (e.g. sha256)\n * @param ikm - input keying material, the initial key\n * @param salt - optional salt value (a non-secret random value)\n */\nexport function extract(hash, ikm, salt) {\n    ahash(hash);\n    // NOTE: some libraries treat zero-length array as 'not provided';\n    // we don't, since we have undefined as 'not provided'\n    // https://github.com/RustCrypto/KDFs/issues/15\n    if (salt === undefined)\n        salt = new Uint8Array(hash.outputLen);\n    return hmac(hash, toBytes(salt), toBytes(ikm));\n}\nconst HKDF_COUNTER = /* @__PURE__ */ new Uint8Array([0]);\nconst EMPTY_BUFFER = /* @__PURE__ */ new Uint8Array();\n/**\n * HKDF-expand from the spec. The most important part. `HKDF-Expand(PRK, info, L) -> OKM`\n * @param hash - hash function that would be used (e.g. sha256)\n * @param prk - a pseudorandom key of at least HashLen octets (usually, the output from the extract step)\n * @param info - optional context and application specific information (can be a zero-length string)\n * @param length - length of output keying material in bytes\n */\nexport function expand(hash, prk, info, length = 32) {\n    ahash(hash);\n    anumber(length);\n    if (length > 255 * hash.outputLen)\n        throw new Error('Length should be <= 255*HashLen');\n    const blocks = Math.ceil(length / hash.outputLen);\n    if (info === undefined)\n        info = EMPTY_BUFFER;\n    // first L(ength) octets of T\n    const okm = new Uint8Array(blocks * hash.outputLen);\n    // Re-use HMAC instance between blocks\n    const HMAC = hmac.create(hash, prk);\n    const HMACTmp = HMAC._cloneInto();\n    const T = new Uint8Array(HMAC.outputLen);\n    for (let counter = 0; counter < blocks; counter++) {\n        HKDF_COUNTER[0] = counter + 1;\n        // T(0) = empty string (zero length)\n        // T(N) = HMAC-Hash(PRK, T(N-1) | info | N)\n        HMACTmp.update(counter === 0 ? EMPTY_BUFFER : T)\n            .update(info)\n            .update(HKDF_COUNTER)\n            .digestInto(T);\n        okm.set(T, hash.outputLen * counter);\n        HMAC._cloneInto(HMACTmp);\n    }\n    HMAC.destroy();\n    HMACTmp.destroy();\n    T.fill(0);\n    HKDF_COUNTER.fill(0);\n    return okm.slice(0, length);\n}\n/**\n * HKDF (RFC 5869): derive keys from an initial input.\n * Combines hkdf_extract + hkdf_expand in one step\n * @param hash - hash function that would be used (e.g. sha256)\n * @param ikm - input keying material, the initial key\n * @param salt - optional salt value (a non-secret random value)\n * @param info - optional context and application specific information (can be a zero-length string)\n * @param length - length of output keying material in bytes\n * @example\n * import { hkdf } from '@noble/hashes/hkdf';\n * import { sha256 } from '@noble/hashes/sha2';\n * import { randomBytes } from '@noble/hashes/utils';\n * const inputKey = randomBytes(32);\n * const salt = randomBytes(32);\n * const info = 'application-key';\n * const hk1 = hkdf(sha256, inputKey, salt, info, 32);\n */\nexport const hkdf = (hash, ikm, salt, info, length) => expand(hash, extract(hash, ikm, salt), info, length);\n//# sourceMappingURL=hkdf.js.map", "/**\n * Internal Merkle-Damgard hash utils.\n * @module\n */\nimport { aexists, aoutput } from './_assert.js';\nimport { Hash, createView, toBytes } from './utils.js';\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nexport function setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nexport function Chi(a, b, c) {\n    return (a & b) ^ (~a & c);\n}\n/** Majority function, true if any two inputs is true. */\nexport function Maj(a, b, c) {\n    return (a & b) ^ (a & c) ^ (b & c);\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nexport class HashMD extends Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = createView(this.buffer);\n    }\n    update(data) {\n        aexists(this);\n        const { view, buffer, blockLen } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = createView(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        aexists(this);\n        aoutput(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = createView(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n//# sourceMappingURL=_md.js.map", "/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake<PERSON>.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\nimport { Chi, HashMD, Maj } from './_md.js';\nimport { rotr, wrapConstructor } from './utils.js';\n/** Round constants: first 32 bits of fractional parts of the cube roots of the first 64 primes 2..311). */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n/** Initial state: first 32 bits of fractional parts of the square roots of the first 8 primes 2..19. */\n// prettier-ignore\nconst SHA256_IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n/**\n * Temporary buffer, not used to store anything between runs.\n * Named this way because it matches specification.\n */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nexport class SHA256 extends HashMD {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = SHA256_IV[0] | 0;\n        this.B = SHA256_IV[1] | 0;\n        this.C = SHA256_IV[2] | 0;\n        this.D = SHA256_IV[3] | 0;\n        this.E = SHA256_IV[4] | 0;\n        this.F = SHA256_IV[5] | 0;\n        this.G = SHA256_IV[6] | 0;\n        this.H = SHA256_IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n            const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n            const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n            const T2 = (sigma0 + Maj(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n/**\n * Constants taken from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf.\n */\nclass SHA224 extends SHA256 {\n    constructor() {\n        super();\n        this.A = 0xc1059ed8 | 0;\n        this.B = 0x367cd507 | 0;\n        this.C = 0x3070dd17 | 0;\n        this.D = 0xf70e5939 | 0;\n        this.E = 0xffc00b31 | 0;\n        this.F = 0x68581511 | 0;\n        this.G = 0x64f98fa7 | 0;\n        this.H = 0xbefa4fa4 | 0;\n        this.outputLen = 28;\n    }\n}\n/** SHA2-256 hash function */\nexport const sha256 = /* @__PURE__ */ wrapConstructor(() => new SHA256());\n/** SHA2-224 hash function */\nexport const sha224 = /* @__PURE__ */ wrapConstructor(() => new SHA224());\n//# sourceMappingURL=sha256.js.map", "/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nexport function isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\nexport function abytes(item) {\n    if (!isBytes(item))\n        throw new Error('Uint8Array expected');\n}\nexport function abool(title, value) {\n    if (typeof value !== 'boolean')\n        throw new Error(title + ' boolean expected, got ' + value);\n}\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    abytes(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\nexport function numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? '0' + hex : hex;\n}\nexport function hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nexport function bytesToNumberLE(bytes) {\n    abytes(bytes);\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nexport function numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nexport function numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nexport function numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nexport function ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = hexToBytes(hex);\n        }\n        catch (e) {\n            throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n        }\n    }\n    else if (isBytes(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(title + ' must be hex string or Uint8Array');\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n// Compares 2 u8a-s in kinda constant time\nexport function equalBytes(a, b) {\n    if (a.length !== b.length)\n        return false;\n    let diff = 0;\n    for (let i = 0; i < a.length; i++)\n        diff |= a[i] ^ b[i];\n    return diff === 0;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Is positive bigint\nconst isPosBig = (n) => typeof n === 'bigint' && _0n <= n;\nexport function inRange(n, min, max) {\n    return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nexport function aInRange(title, n, min, max) {\n    // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n    // consider P=256n, min=0n, max=P\n    // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n    // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n    // - our way is the cleanest:               `inRange('x', x, 0n, P)\n    if (!inRange(n, min, max))\n        throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nexport function bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nexport function bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nexport function bitSet(n, pos, value) {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nexport const bitMask = (n) => (_2n << BigInt(n - 1)) - _1n;\n// DRBG\nconst u8n = (data) => new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr) => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nexport function createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n()) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || isBytes(val),\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nexport function validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error('invalid validator function');\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error('param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n/**\n * throws not implemented error\n */\nexport const notImplemented = () => {\n    throw new Error('not implemented');\n};\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nexport function memoized(fn) {\n    const map = new WeakMap();\n    return (arg, ...args) => {\n        const val = map.get(arg);\n        if (val !== undefined)\n            return val;\n        const computed = fn(arg, ...args);\n        map.set(arg, computed);\n        return computed;\n    };\n}\n//# sourceMappingURL=utils.js.map", "/**\n * Utils for modular division and finite fields.\n * A finite field over 11 is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { bitMask, bytesToNumberBE, bytesToNumberLE, ensureBytes, numberToBytesBE, numberToBytesLE, validateObject, } from './utils.js';\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5), _8n = /* @__PURE__ */ BigInt(8);\n// prettier-ignore\nconst _9n = /* @__PURE__ */ BigInt(9), _16n = /* @__PURE__ */ BigInt(16);\n// Calculates a modulo b\nexport function mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @todo use field version && remove\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nexport function pow(num, power, modulo) {\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (modulo <= _0n)\n        throw new Error('invalid modulus');\n    if (modulo === _1n)\n        return _0n;\n    let res = _1n;\n    while (power > _0n) {\n        if (power & _1n)\n            res = (res * num) % modulo;\n        num = (num * num) % modulo;\n        power >>= _1n;\n    }\n    return res;\n}\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nexport function pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nexport function invert(number, modulo) {\n    if (number === _0n)\n        throw new Error('invert: expected non-zero number');\n    if (modulo <= _0n)\n        throw new Error('invert: expected positive modulus, got ' + modulo);\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nexport function tonelliShanks(P) {\n    // Legendre constant: used to calculate Legendre symbol (a | p),\n    // which denotes the value of a^((p-1)/2) (mod p).\n    // (a | p) ≡ 1    if a is a square (mod p)\n    // (a | p) ≡ -1   if a is not a square (mod p)\n    // (a | p) ≡ 0    if a ≡ 0 (mod p)\n    const legendreC = (P - _1n) / _2n;\n    let Q, S, Z;\n    // Step 1: By factoring out powers of 2 from p - 1,\n    // find q and s such that p - 1 = q*(2^s) with q odd\n    for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++)\n        ;\n    // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n    for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++) {\n        // Crash instead of infinity loop, we cannot reasonable count until P.\n        if (Z > 1000)\n            throw new Error('Cannot find square root: likely non-prime P');\n    }\n    // Fast-path\n    if (S === 1) {\n        const p1div4 = (P + _1n) / _4n;\n        return function tonelliFast(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Slow-path\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n        if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE))\n            throw new Error('Cannot find square root');\n        let r = S;\n        // TODO: will fail at Fp2/etc\n        let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n        let x = Fp.pow(n, Q1div2); // first guess at the square root\n        let b = Fp.pow(n, Q); // first guess at the fudge factor\n        while (!Fp.eql(b, Fp.ONE)) {\n            if (Fp.eql(b, Fp.ZERO))\n                return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n            // Find m such b^(2^m)==1\n            let m = 1;\n            for (let t2 = Fp.sqr(b); m < r; m++) {\n                if (Fp.eql(t2, Fp.ONE))\n                    break;\n                t2 = Fp.sqr(t2); // t2 *= t2\n            }\n            // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n            const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n            g = Fp.sqr(ge); // g = ge * ge\n            x = Fp.mul(x, ge); // x *= ge\n            b = Fp.mul(b, g); // b *= g\n            r = m;\n        }\n        return x;\n    };\n}\n/**\n * Square root for a finite field. It will try to check if optimizations are applicable and fall back to 4:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. P ≡ 9 (mod 16)\n * 4. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nexport function FpSqrt(P) {\n    // P ≡ 3 (mod 4)\n    // √n = n^((P+1)/4)\n    if (P % _4n === _3n) {\n        // Not all roots possible!\n        // const ORDER =\n        //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n        // const NUM = 72057594037927816n;\n        const p1div4 = (P + _1n) / _4n;\n        return function sqrt3mod4(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            // Throw if root**2 != n\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n    if (P % _8n === _5n) {\n        const c1 = (P - _5n) / _8n;\n        return function sqrt5mod8(Fp, n) {\n            const n2 = Fp.mul(n, _2n);\n            const v = Fp.pow(n2, c1);\n            const nv = Fp.mul(n, v);\n            const i = Fp.mul(Fp.mul(nv, _2n), v);\n            const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // P ≡ 9 (mod 16)\n    if (P % _16n === _9n) {\n        // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n        // Means we cannot use sqrt for constants at all!\n        //\n        // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n        // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n        // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n        // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n        // sqrt = (x) => {\n        //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n        //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n        //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n        //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n        //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n        //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n        //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n        //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n        //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n        //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n        // }\n    }\n    // Other cases: Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nexport const isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nexport function validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return validateObject(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nexport function FpPow(f, num, power) {\n    // Should have same speed as pow for bigints\n    // TODO: benchmark!\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (power === _0n)\n        return f.ONE;\n    if (power === _1n)\n        return num;\n    let p = f.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = f.mul(p, d);\n        d = f.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nexport function FpInvertBatch(f, nums) {\n    const tmp = new Array(nums.length);\n    // Walk from first to last, multiply them by each other MOD p\n    const lastMultiplied = nums.reduce((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = acc;\n        return f.mul(acc, num);\n    }, f.ONE);\n    // Invert last element\n    const inverted = f.inv(lastMultiplied);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = f.mul(acc, tmp[i]);\n        return f.mul(acc, num);\n    }, inverted);\n    return tmp;\n}\nexport function FpDiv(f, lhs, rhs) {\n    return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n/**\n * Legendre symbol.\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nexport function FpLegendre(order) {\n    const legendreConst = (order - _1n) / _2n; // Integer arithmetic\n    return (f, x) => f.pow(x, legendreConst);\n}\n// This function returns True whenever the value x is a square in the field F.\nexport function FpIsSquare(f) {\n    const legendre = FpLegendre(f.ORDER);\n    return (x) => {\n        const p = legendre(f, x);\n        return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n    };\n}\n// CURVE.n lengths\nexport function nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nexport function Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n    let sqrtP; // cached sqrtP\n    const f = Object.freeze({\n        ORDER,\n        isLE,\n        BITS,\n        BYTES,\n        MASK: bitMask(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error('invalid field element: expected bigint, got ' + typeof num);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt ||\n            ((n) => {\n                if (!sqrtP)\n                    sqrtP = FpSqrt(ORDER);\n                return sqrtP(f, n);\n            }),\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // TODO: do we really need constant cmov?\n        // We don't have const-time bigints anyway, so probably will be not very useful\n        cmov: (a, b, c) => (c ? b : a),\n        toBytes: (num) => (isLE ? numberToBytesLE(num, BYTES) : numberToBytesBE(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n            return isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n        },\n    });\n    return Object.freeze(f);\n}\nexport function FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nexport function FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nexport function hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = ensureBytes('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error('hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen);\n    const num = isLE ? bytesToNumberLE(hash) : bytesToNumberBE(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nexport function getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nexport function getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nexport function mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n    const num = isLE ? bytesToNumberLE(key) : bytesToNumberBE(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? numberToBytesLE(reduced, fieldLen) : numberToBytesBE(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map", "/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNAF, pippenger\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { nLength, validateField } from './modular.js';\nimport { bitLen, validateObject } from './utils.js';\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nfunction constTimeNegate(condition, item) {\n    const neg = item.negate();\n    return condition ? neg : item;\n}\nfunction validateW(W, bits) {\n    if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n        throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\nfunction calcWOpts(W, bits) {\n    validateW(W, bits);\n    const windows = Math.ceil(bits / W) + 1; // +1, because\n    const windowSize = 2 ** (W - 1); // -1 because we skip zero\n    return { windows, windowSize };\n}\nfunction validateMSMPoints(points, c) {\n    if (!Array.isArray(points))\n        throw new Error('array expected');\n    points.forEach((p, i) => {\n        if (!(p instanceof c))\n            throw new Error('invalid point at index ' + i);\n    });\n}\nfunction validateMSMScalars(scalars, field) {\n    if (!Array.isArray(scalars))\n        throw new Error('array of scalars expected');\n    scalars.forEach((s, i) => {\n        if (!field.isValid(s))\n            throw new Error('invalid scalar at index ' + i);\n    });\n}\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes\nconst pointPrecomputes = new WeakMap();\nconst pointWindowSizes = new WeakMap(); // This allows use make points immutable (nothing changes inside)\nfunction getW(P) {\n    return pointWindowSizes.get(P) || 1;\n}\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nexport function wNAF(c, bits) {\n    return {\n        constTimeNegate,\n        hasPrecomputes(elm) {\n            return getW(elm) !== 1;\n        },\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n, p = c.ZERO) {\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @param elm Point instance\n         * @param W window size\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // =1, because we skip zero\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n            // But need to carefully remove other checks before wNAF. ORDER == bits here\n            const { windows, windowSize } = calcWOpts(W, bits);\n            let p = c.ZERO;\n            let f = c.BASE;\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n                // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n                // there is negate now: it is possible that negated element from low value\n                // would be the same as high element, which will create carry into next window.\n                // It's not obvious how this can fail, but still worth investigating later.\n                // Check if we're onto Zero point.\n                // Add random point inside current window to f.\n                const offset1 = offset;\n                const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n                const cond1 = window % 2 !== 0;\n                const cond2 = wbits < 0;\n                if (wbits === 0) {\n                    // The most important part for const-time getPublicKey\n                    f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n                }\n                else {\n                    p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n                }\n            }\n            // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n            // Even if the variable is still unused, there are some checks which will\n            // throw an exception, so compiler needs to prove they won't happen, which is hard.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        /**\n         * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @param acc accumulator point to add result of multiplication\n         * @returns point\n         */\n        wNAFUnsafe(W, precomputes, n, acc = c.ZERO) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                if (n === _0n)\n                    break; // No need to go over empty scalar\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                if (wbits === 0)\n                    continue;\n                let curr = precomputes[offset + Math.abs(wbits) - 1]; // -1 because we skip zero\n                if (wbits < 0)\n                    curr = curr.negate();\n                // NOTE: by re-using acc, we can save a lot of additions in case of MSM\n                acc = acc.add(curr);\n            }\n            return acc;\n        },\n        getPrecomputes(W, P, transform) {\n            // Calculate precomputes on a first run, reuse them after\n            let comp = pointPrecomputes.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1)\n                    pointPrecomputes.set(P, transform(comp));\n            }\n            return comp;\n        },\n        wNAFCached(P, n, transform) {\n            const W = getW(P);\n            return this.wNAF(W, this.getPrecomputes(W, P, transform), n);\n        },\n        wNAFCachedUnsafe(P, n, transform, prev) {\n            const W = getW(P);\n            if (W === 1)\n                return this.unsafeLadder(P, n, prev); // For W=1 ladder is ~x2 faster\n            return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);\n        },\n        // We calculate precomputes for elliptic curve point multiplication\n        // using windowed method. This specifies window size and\n        // stores precomputed values. Usually only base point would be precomputed.\n        setWindowSize(P, W) {\n            validateW(W, bits);\n            pointWindowSizes.set(P, W);\n            pointPrecomputes.delete(P);\n        },\n    };\n}\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster with precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka private keys / bigints)\n */\nexport function pippenger(c, fieldN, points, scalars) {\n    // If we split scalars by some window (let's say 8 bits), every chunk will only\n    // take 256 buckets even if there are 4096 scalars, also re-uses double.\n    // TODO:\n    // - https://eprint.iacr.org/2024/750.pdf\n    // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n    // 0 is accepted in scalars\n    validateMSMPoints(points, c);\n    validateMSMScalars(scalars, fieldN);\n    if (points.length !== scalars.length)\n        throw new Error('arrays of points and scalars must have equal length');\n    const zero = c.ZERO;\n    const wbits = bitLen(BigInt(points.length));\n    const windowSize = wbits > 12 ? wbits - 3 : wbits > 4 ? wbits - 2 : wbits ? 2 : 1; // in bits\n    const MASK = (1 << windowSize) - 1;\n    const buckets = new Array(MASK + 1).fill(zero); // +1 for zero array\n    const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n    let sum = zero;\n    for (let i = lastBits; i >= 0; i -= windowSize) {\n        buckets.fill(zero);\n        for (let j = 0; j < scalars.length; j++) {\n            const scalar = scalars[j];\n            const wbits = Number((scalar >> BigInt(i)) & BigInt(MASK));\n            buckets[wbits] = buckets[wbits].add(points[j]);\n        }\n        let resI = zero; // not using this will do small speed-up, but will lose ct\n        // Skip first bucket, because it is zero\n        for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n            sumI = sumI.add(buckets[j]);\n            resI = resI.add(sumI);\n        }\n        sum = sum.add(resI);\n        if (i !== 0)\n            for (let j = 0; j < windowSize; j++)\n                sum = sum.double();\n    }\n    return sum;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nexport function precomputeMSMUnsafe(c, fieldN, points, windowSize) {\n    /**\n     * Performance Analysis of Window-based Precomputation\n     *\n     * Base Case (256-bit scalar, 8-bit window):\n     * - Standard precomputation requires:\n     *   - 31 additions per scalar × 256 scalars = 7,936 ops\n     *   - Plus 255 summary additions = 8,191 total ops\n     *   Note: Summary additions can be optimized via accumulator\n     *\n     * Chunked Precomputation Analysis:\n     * - Using 32 chunks requires:\n     *   - 255 additions per chunk\n     *   - 256 doublings\n     *   - Total: (255 × 32) + 256 = 8,416 ops\n     *\n     * Memory Usage Comparison:\n     * Window Size | Standard Points | Chunked Points\n     * ------------|-----------------|---------------\n     *     4-bit   |     520         |      15\n     *     8-bit   |    4,224        |     255\n     *    10-bit   |   13,824        |   1,023\n     *    16-bit   |  557,056        |  65,535\n     *\n     * Key Advantages:\n     * 1. Enables larger window sizes due to reduced memory overhead\n     * 2. More efficient for smaller scalar counts:\n     *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n     *    - ~2x faster than standard 8,191 ops\n     *\n     * Limitations:\n     * - Not suitable for plain precomputes (requires 256 constant doublings)\n     * - Performance degrades with larger scalar counts:\n     *   - Optimal for ~256 scalars\n     *   - Less efficient for 4096+ scalars (Pippenger preferred)\n     */\n    validateW(windowSize, fieldN.BITS);\n    validateMSMPoints(points, c);\n    const zero = c.ZERO;\n    const tableSize = 2 ** windowSize - 1; // table size (without zero)\n    const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n    const MASK = BigInt((1 << windowSize) - 1);\n    const tables = points.map((p) => {\n        const res = [];\n        for (let i = 0, acc = p; i < tableSize; i++) {\n            res.push(acc);\n            acc = acc.add(p);\n        }\n        return res;\n    });\n    return (scalars) => {\n        validateMSMScalars(scalars, fieldN);\n        if (scalars.length > points.length)\n            throw new Error('array of scalars must be smaller than array of points');\n        let res = zero;\n        for (let i = 0; i < chunks; i++) {\n            // No need to double if accumulator is still zero.\n            if (res !== zero)\n                for (let j = 0; j < windowSize; j++)\n                    res = res.double();\n            const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n            for (let j = 0; j < scalars.length; j++) {\n                const n = scalars[j];\n                const curr = Number((n >> shiftBy) & MASK);\n                if (!curr)\n                    continue; // skip zero scalars chunks\n                res = res.add(tables[j][curr - 1]);\n            }\n        }\n        return res;\n    };\n}\nexport function validateBasic(curve) {\n    validateField(curve.Fp);\n    validateObject(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...nLength(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map", "/**\n * Twisted <PERSON> curve. The formula is: ax² + y² = 1 + dx²y².\n * For design rationale of types / exports, see weierstrass module documentation.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { pippenger, validateBasic, wNAF, } from './curve.js';\nimport { Field, mod } from './modular.js';\nimport * as ut from './utils.js';\nimport { abool, ensureBytes, memoized } from './utils.js';\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _8n = BigInt(8);\n// verification rule is either zip215 or rfc8032 / nist186-5. Consult fromHex:\nconst VERIFY_DEFAULT = { zip215: true };\nfunction validateOpts(curve) {\n    const opts = validateBasic(curve);\n    ut.validateObject(curve, {\n        hash: 'function',\n        a: 'bigint',\n        d: 'bigint',\n        randomBytes: 'function',\n    }, {\n        adjustScalarBytes: 'function',\n        domain: 'function',\n        uvRatio: 'function',\n        mapToCurve: 'function',\n    });\n    // Set defaults\n    return Object.freeze({ ...opts });\n}\n/**\n * Creates Twisted Edwards curve with EdDSA signatures.\n * @example\n * import { Field } from '@noble/curves/abstract/modular';\n * // Before that, define BigInt-s: a, d, p, n, Gx, Gy, h\n * const curve = twistedEdwards({ a, d, Fp: Field(p), n, Gx, Gy, h })\n */\nexport function twistedEdwards(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER, prehash: prehash, hash: cHash, randomBytes, nByteLength, h: cofactor, } = CURVE;\n    // Important:\n    // There are some places where Fp.BYTES is used instead of nByteLength.\n    // So far, everything has been tested with curves of Fp.BYTES == nByteLength.\n    // TODO: test and find curves which behave otherwise.\n    const MASK = _2n << (BigInt(nByteLength * 8) - _1n);\n    const modP = Fp.create; // Function overrides\n    const Fn = Field(CURVE.n, CURVE.nBitLength);\n    // sqrt(u/v)\n    const uvRatio = CURVE.uvRatio ||\n        ((u, v) => {\n            try {\n                return { isValid: true, value: Fp.sqrt(u * Fp.inv(v)) };\n            }\n            catch (e) {\n                return { isValid: false, value: _0n };\n            }\n        });\n    const adjustScalarBytes = CURVE.adjustScalarBytes || ((bytes) => bytes); // NOOP\n    const domain = CURVE.domain ||\n        ((data, ctx, phflag) => {\n            abool('phflag', phflag);\n            if (ctx.length || phflag)\n                throw new Error('Contexts/pre-hash are not supported');\n            return data;\n        }); // NOOP\n    // 0 <= n < MASK\n    // Coordinates larger than Fp.ORDER are allowed for zip215\n    function aCoordinate(title, n) {\n        ut.aInRange('coordinate ' + title, n, _0n, MASK);\n    }\n    function assertPoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ExtendedPoint expected');\n    }\n    // Converts Extended point to default (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    const toAffineMemo = memoized((p, iz) => {\n        const { ex: x, ey: y, ez: z } = p;\n        const is0 = p.is0();\n        if (iz == null)\n            iz = is0 ? _8n : Fp.inv(z); // 8 was chosen arbitrarily\n        const ax = modP(x * iz);\n        const ay = modP(y * iz);\n        const zz = modP(z * iz);\n        if (is0)\n            return { x: _0n, y: _1n };\n        if (zz !== _1n)\n            throw new Error('invZ was invalid');\n        return { x: ax, y: ay };\n    });\n    const assertValidMemo = memoized((p) => {\n        const { a, d } = CURVE;\n        if (p.is0())\n            throw new Error('bad point: ZERO'); // TODO: optimize, with vars below?\n        // Equation in affine coordinates: ax² + y² = 1 + dx²y²\n        // Equation in projective coordinates (X/Z, Y/Z, Z):  (aX² + Y²)Z² = Z⁴ + dX²Y²\n        const { ex: X, ey: Y, ez: Z, et: T } = p;\n        const X2 = modP(X * X); // X²\n        const Y2 = modP(Y * Y); // Y²\n        const Z2 = modP(Z * Z); // Z²\n        const Z4 = modP(Z2 * Z2); // Z⁴\n        const aX2 = modP(X2 * a); // aX²\n        const left = modP(Z2 * modP(aX2 + Y2)); // (aX² + Y²)Z²\n        const right = modP(Z4 + modP(d * modP(X2 * Y2))); // Z⁴ + dX²Y²\n        if (left !== right)\n            throw new Error('bad point: equation left != right (1)');\n        // In Extended coordinates we also have T, which is x*y=T/Z: check X*Y == Z*T\n        const XY = modP(X * Y);\n        const ZT = modP(Z * T);\n        if (XY !== ZT)\n            throw new Error('bad point: equation left != right (2)');\n        return true;\n    });\n    // Extended Point works in extended coordinates: (x, y, z, t) ∋ (x=x/z, y=y/z, t=xy).\n    // https://en.wikipedia.org/wiki/Twisted_Edwards_curve#Extended_coordinates\n    class Point {\n        constructor(ex, ey, ez, et) {\n            this.ex = ex;\n            this.ey = ey;\n            this.ez = ez;\n            this.et = et;\n            aCoordinate('x', ex);\n            aCoordinate('y', ey);\n            aCoordinate('z', ez);\n            aCoordinate('t', et);\n            Object.freeze(this);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        static fromAffine(p) {\n            if (p instanceof Point)\n                throw new Error('extended point not allowed');\n            const { x, y } = p || {};\n            aCoordinate('x', x);\n            aCoordinate('y', y);\n            return new Point(x, y, _1n, modP(x * y));\n        }\n        static normalizeZ(points) {\n            const toInv = Fp.invertBatch(points.map((p) => p.ez));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        // Multiscalar Multiplication\n        static msm(points, scalars) {\n            return pippenger(Point, Fn, points, scalars);\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            wnaf.setWindowSize(this, windowSize);\n        }\n        // Not required for fromHex(), which always creates valid points.\n        // Could be useful for fromAffine().\n        assertValidity() {\n            assertValidMemo(this);\n        }\n        // Compare one point to another.\n        equals(other) {\n            assertPoint(other);\n            const { ex: X1, ey: Y1, ez: Z1 } = this;\n            const { ex: X2, ey: Y2, ez: Z2 } = other;\n            const X1Z2 = modP(X1 * Z2);\n            const X2Z1 = modP(X2 * Z1);\n            const Y1Z2 = modP(Y1 * Z2);\n            const Y2Z1 = modP(Y2 * Z1);\n            return X1Z2 === X2Z1 && Y1Z2 === Y2Z1;\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        negate() {\n            // Flips point sign to a negative one (-x, y in affine coords)\n            return new Point(modP(-this.ex), this.ey, this.ez, modP(-this.et));\n        }\n        // Fast algo for doubling Extended Point.\n        // https://hyperelliptic.org/EFD/g1p/auto-twisted-extended.html#doubling-dbl-2008-hwcd\n        // Cost: 4M + 4S + 1*a + 6add + 1*2.\n        double() {\n            const { a } = CURVE;\n            const { ex: X1, ey: Y1, ez: Z1 } = this;\n            const A = modP(X1 * X1); // A = X12\n            const B = modP(Y1 * Y1); // B = Y12\n            const C = modP(_2n * modP(Z1 * Z1)); // C = 2*Z12\n            const D = modP(a * A); // D = a*A\n            const x1y1 = X1 + Y1;\n            const E = modP(modP(x1y1 * x1y1) - A - B); // E = (X1+Y1)2-A-B\n            const G = D + B; // G = D+B\n            const F = G - C; // F = G-C\n            const H = D - B; // H = D-B\n            const X3 = modP(E * F); // X3 = E*F\n            const Y3 = modP(G * H); // Y3 = G*H\n            const T3 = modP(E * H); // T3 = E*H\n            const Z3 = modP(F * G); // Z3 = F*G\n            return new Point(X3, Y3, Z3, T3);\n        }\n        // Fast algo for adding 2 Extended Points.\n        // https://hyperelliptic.org/EFD/g1p/auto-twisted-extended.html#addition-add-2008-hwcd\n        // Cost: 9M + 1*a + 1*d + 7add.\n        add(other) {\n            assertPoint(other);\n            const { a, d } = CURVE;\n            const { ex: X1, ey: Y1, ez: Z1, et: T1 } = this;\n            const { ex: X2, ey: Y2, ez: Z2, et: T2 } = other;\n            // Faster algo for adding 2 Extended Points when curve's a=-1.\n            // http://hyperelliptic.org/EFD/g1p/auto-twisted-extended-1.html#addition-add-2008-hwcd-4\n            // Cost: 8M + 8add + 2*2.\n            // Note: It does not check whether the `other` point is valid.\n            if (a === BigInt(-1)) {\n                const A = modP((Y1 - X1) * (Y2 + X2));\n                const B = modP((Y1 + X1) * (Y2 - X2));\n                const F = modP(B - A);\n                if (F === _0n)\n                    return this.double(); // Same point. Tests say it doesn't affect timing\n                const C = modP(Z1 * _2n * T2);\n                const D = modP(T1 * _2n * Z2);\n                const E = D + C;\n                const G = B + A;\n                const H = D - C;\n                const X3 = modP(E * F);\n                const Y3 = modP(G * H);\n                const T3 = modP(E * H);\n                const Z3 = modP(F * G);\n                return new Point(X3, Y3, Z3, T3);\n            }\n            const A = modP(X1 * X2); // A = X1*X2\n            const B = modP(Y1 * Y2); // B = Y1*Y2\n            const C = modP(T1 * d * T2); // C = T1*d*T2\n            const D = modP(Z1 * Z2); // D = Z1*Z2\n            const E = modP((X1 + Y1) * (X2 + Y2) - A - B); // E = (X1+Y1)*(X2+Y2)-A-B\n            const F = D - C; // F = D-C\n            const G = D + C; // G = D+C\n            const H = modP(B - a * A); // H = B-a*A\n            const X3 = modP(E * F); // X3 = E*F\n            const Y3 = modP(G * H); // Y3 = G*H\n            const T3 = modP(E * H); // T3 = E*H\n            const Z3 = modP(F * G); // Z3 = F*G\n            return new Point(X3, Y3, Z3, T3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, n, Point.normalizeZ);\n        }\n        // Constant-time multiplication.\n        multiply(scalar) {\n            const n = scalar;\n            ut.aInRange('scalar', n, _1n, CURVE_ORDER); // 1 <= scalar < L\n            const { p, f } = this.wNAF(n);\n            return Point.normalizeZ([p, f])[0];\n        }\n        // Non-constant-time multiplication. Uses double-and-add algorithm.\n        // It's faster, but should only be used when you don't care about\n        // an exposed private key e.g. sig verification.\n        // Does NOT allow scalars higher than CURVE.n.\n        // Accepts optional accumulator to merge with multiply (important for sparse scalars)\n        multiplyUnsafe(scalar, acc = Point.ZERO) {\n            const n = scalar;\n            ut.aInRange('scalar', n, _0n, CURVE_ORDER); // 0 <= scalar < L\n            if (n === _0n)\n                return I;\n            if (this.is0() || n === _1n)\n                return this;\n            return wnaf.wNAFCachedUnsafe(this, n, Point.normalizeZ, acc);\n        }\n        // Checks if point is of small order.\n        // If you add something to small order point, you will have \"dirty\"\n        // point with torsion component.\n        // Multiplies point by cofactor and checks if the result is 0.\n        isSmallOrder() {\n            return this.multiplyUnsafe(cofactor).is0();\n        }\n        // Multiplies point by curve order and checks if the result is 0.\n        // Returns `false` is the point is dirty.\n        isTorsionFree() {\n            return wnaf.unsafeLadder(this, CURVE_ORDER).is0();\n        }\n        // Converts Extended point to default (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        toAffine(iz) {\n            return toAffineMemo(this, iz);\n        }\n        clearCofactor() {\n            const { h: cofactor } = CURVE;\n            if (cofactor === _1n)\n                return this;\n            return this.multiplyUnsafe(cofactor);\n        }\n        // Converts hash string or Uint8Array to Point.\n        // Uses algo from RFC8032 5.1.3.\n        static fromHex(hex, zip215 = false) {\n            const { d, a } = CURVE;\n            const len = Fp.BYTES;\n            hex = ensureBytes('pointHex', hex, len); // copy hex to a new array\n            abool('zip215', zip215);\n            const normed = hex.slice(); // copy again, we'll manipulate it\n            const lastByte = hex[len - 1]; // select last byte\n            normed[len - 1] = lastByte & ~0x80; // clear last bit\n            const y = ut.bytesToNumberLE(normed);\n            // zip215=true is good for consensus-critical apps. =false follows RFC8032 / NIST186-5.\n            // RFC8032 prohibits >= p, but ZIP215 doesn't\n            // zip215=true:  0 <= y < MASK (2^256 for ed25519)\n            // zip215=false: 0 <= y < P (2^255-19 for ed25519)\n            const max = zip215 ? MASK : Fp.ORDER;\n            ut.aInRange('pointHex.y', y, _0n, max);\n            // Ed25519: x² = (y²-1)/(dy²+1) mod p. Ed448: x² = (y²-1)/(dy²-1) mod p. Generic case:\n            // ax²+y²=1+dx²y² => y²-1=dx²y²-ax² => y²-1=x²(dy²-a) => x²=(y²-1)/(dy²-a)\n            const y2 = modP(y * y); // denominator is always non-0 mod p.\n            const u = modP(y2 - _1n); // u = y² - 1\n            const v = modP(d * y2 - a); // v = d y² + 1.\n            let { isValid, value: x } = uvRatio(u, v); // √(u/v)\n            if (!isValid)\n                throw new Error('Point.fromHex: invalid y coordinate');\n            const isXOdd = (x & _1n) === _1n; // There are 2 square roots. Use x_0 bit to select proper\n            const isLastByteOdd = (lastByte & 0x80) !== 0; // x_0, last bit\n            if (!zip215 && x === _0n && isLastByteOdd)\n                // if x=0 and x_0 = 1, fail\n                throw new Error('Point.fromHex: x=0 and x_0=1');\n            if (isLastByteOdd !== isXOdd)\n                x = modP(-x); // if x_0 != x mod 2, set x = p-x\n            return Point.fromAffine({ x, y });\n        }\n        static fromPrivateKey(privKey) {\n            return getExtendedPublicKey(privKey).point;\n        }\n        toRawBytes() {\n            const { x, y } = this.toAffine();\n            const bytes = ut.numberToBytesLE(y, Fp.BYTES); // each y has 2 x values (x, -y)\n            bytes[bytes.length - 1] |= x & _1n ? 0x80 : 0; // when compressing, it's enough to store y\n            return bytes; // and use the last byte to encode sign of x\n        }\n        toHex() {\n            return ut.bytesToHex(this.toRawBytes()); // Same as toRawBytes, but returns string.\n        }\n    }\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, _1n, modP(CURVE.Gx * CURVE.Gy));\n    Point.ZERO = new Point(_0n, _1n, _1n, _0n); // 0, 1, 1, 0\n    const { BASE: G, ZERO: I } = Point;\n    const wnaf = wNAF(Point, nByteLength * 8);\n    function modN(a) {\n        return mod(a, CURVE_ORDER);\n    }\n    // Little-endian SHA512 with modulo n\n    function modN_LE(hash) {\n        return modN(ut.bytesToNumberLE(hash));\n    }\n    /** Convenience method that creates public key and other stuff. RFC8032 5.1.5 */\n    function getExtendedPublicKey(key) {\n        const len = Fp.BYTES;\n        key = ensureBytes('private key', key, len);\n        // Hash private key with curve's hash function to produce uniformingly random input\n        // Check byte lengths: ensure(64, h(ensure(32, key)))\n        const hashed = ensureBytes('hashed private key', cHash(key), 2 * len);\n        const head = adjustScalarBytes(hashed.slice(0, len)); // clear first half bits, produce FE\n        const prefix = hashed.slice(len, 2 * len); // second half is called key prefix (5.1.6)\n        const scalar = modN_LE(head); // The actual private scalar\n        const point = G.multiply(scalar); // Point on Edwards curve aka public key\n        const pointBytes = point.toRawBytes(); // Uint8Array representation\n        return { head, prefix, scalar, point, pointBytes };\n    }\n    // Calculates EdDSA pub key. RFC8032 5.1.5. Privkey is hashed. Use first half with 3 bits cleared\n    function getPublicKey(privKey) {\n        return getExtendedPublicKey(privKey).pointBytes;\n    }\n    // int('LE', SHA512(dom2(F, C) || msgs)) mod N\n    function hashDomainToScalar(context = new Uint8Array(), ...msgs) {\n        const msg = ut.concatBytes(...msgs);\n        return modN_LE(cHash(domain(msg, ensureBytes('context', context), !!prehash)));\n    }\n    /** Signs message with privateKey. RFC8032 5.1.6 */\n    function sign(msg, privKey, options = {}) {\n        msg = ensureBytes('message', msg);\n        if (prehash)\n            msg = prehash(msg); // for ed25519ph etc.\n        const { prefix, scalar, pointBytes } = getExtendedPublicKey(privKey);\n        const r = hashDomainToScalar(options.context, prefix, msg); // r = dom2(F, C) || prefix || PH(M)\n        const R = G.multiply(r).toRawBytes(); // R = rG\n        const k = hashDomainToScalar(options.context, R, pointBytes, msg); // R || A || PH(M)\n        const s = modN(r + k * scalar); // S = (r + k * s) mod L\n        ut.aInRange('signature.s', s, _0n, CURVE_ORDER); // 0 <= s < l\n        const res = ut.concatBytes(R, ut.numberToBytesLE(s, Fp.BYTES));\n        return ensureBytes('result', res, Fp.BYTES * 2); // 64-byte signature\n    }\n    const verifyOpts = VERIFY_DEFAULT;\n    /**\n     * Verifies EdDSA signature against message and public key. RFC8032 5.1.7.\n     * An extended group equation is checked.\n     */\n    function verify(sig, msg, publicKey, options = verifyOpts) {\n        const { context, zip215 } = options;\n        const len = Fp.BYTES; // Verifies EdDSA signature against message and public key. RFC8032 5.1.7.\n        sig = ensureBytes('signature', sig, 2 * len); // An extended group equation is checked.\n        msg = ensureBytes('message', msg);\n        publicKey = ensureBytes('publicKey', publicKey, len);\n        if (zip215 !== undefined)\n            abool('zip215', zip215);\n        if (prehash)\n            msg = prehash(msg); // for ed25519ph, etc\n        const s = ut.bytesToNumberLE(sig.slice(len, 2 * len));\n        let A, R, SB;\n        try {\n            // zip215=true is good for consensus-critical apps. =false follows RFC8032 / NIST186-5.\n            // zip215=true:  0 <= y < MASK (2^256 for ed25519)\n            // zip215=false: 0 <= y < P (2^255-19 for ed25519)\n            A = Point.fromHex(publicKey, zip215);\n            R = Point.fromHex(sig.slice(0, len), zip215);\n            SB = G.multiplyUnsafe(s); // 0 <= s < l is done inside\n        }\n        catch (error) {\n            return false;\n        }\n        if (!zip215 && A.isSmallOrder())\n            return false;\n        const k = hashDomainToScalar(context, R.toRawBytes(), A.toRawBytes(), msg);\n        const RkA = R.add(A.multiplyUnsafe(k));\n        // Extended group equation\n        // [8][S]B = [8]R + [8][k]A'\n        return RkA.subtract(SB).clearCofactor().equals(Point.ZERO);\n    }\n    G._setWindowSize(8); // Enable precomputes. Slows down first publicKey computation by 20ms.\n    const utils = {\n        getExtendedPublicKey,\n        // ed25519 private keys are uniform 32b. No need to check for modulo bias, like in secp256k1.\n        randomPrivateKey: () => randomBytes(Fp.BYTES),\n        /**\n         * We're doing scalar multiplication (used in getPublicKey etc) with precomputed BASE_POINT\n         * values. This slows down first getPublicKey() by milliseconds (see Speed section),\n         * but allows to speed-up subsequent getPublicKey() calls up to 20x.\n         * @param windowSize 2, 4, 8, 16\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3));\n            return point;\n        },\n    };\n    return {\n        CURVE,\n        getPublicKey,\n        sign,\n        verify,\n        ExtendedPoint: Point,\n        utils,\n    };\n}\n//# sourceMappingURL=edwards.js.map", "/**\n * Montgomery curve methods. It's not really whole montgomery curve,\n * just bunch of very specific methods for X25519 / X448 from\n * [RFC 7748](https://www.rfc-editor.org/rfc/rfc7748)\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { mod, pow } from './modular.js';\nimport { aInRange, bytesToNumberLE, ensureBytes, numberToBytesLE, validateObject, } from './utils.js';\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nfunction validateOpts(curve) {\n    validateObject(curve, {\n        a: 'bigint',\n    }, {\n        montgomeryBits: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n        adjustScalarBytes: 'function',\n        domain: 'function',\n        powPminus2: 'function',\n        Gu: 'bigint',\n    });\n    // Set defaults\n    return Object.freeze({ ...curve });\n}\n// Uses only one coordinate instead of two\nexport function montgomery(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { P } = CURVE;\n    const modP = (n) => mod(n, P);\n    const montgomeryBits = CURVE.montgomeryBits;\n    const montgomeryBytes = Math.ceil(montgomeryBits / 8);\n    const fieldLen = CURVE.nByteLength;\n    const adjustScalarBytes = CURVE.adjustScalarBytes || ((bytes) => bytes);\n    const powPminus2 = CURVE.powPminus2 || ((x) => pow(x, P - BigInt(2), P));\n    // cswap from RFC7748. But it is not from RFC7748!\n    /*\n      cswap(swap, x_2, x_3):\n           dummy = mask(swap) AND (x_2 XOR x_3)\n           x_2 = x_2 XOR dummy\n           x_3 = x_3 XOR dummy\n           Return (x_2, x_3)\n    Where mask(swap) is the all-1 or all-0 word of the same length as x_2\n     and x_3, computed, e.g., as mask(swap) = 0 - swap.\n    */\n    function cswap(swap, x_2, x_3) {\n        const dummy = modP(swap * (x_2 - x_3));\n        x_2 = modP(x_2 - dummy);\n        x_3 = modP(x_3 + dummy);\n        return [x_2, x_3];\n    }\n    // x25519 from 4\n    // The constant a24 is (486662 - 2) / 4 = 121665 for curve25519/X25519\n    const a24 = (CURVE.a - BigInt(2)) / BigInt(4);\n    /**\n     *\n     * @param pointU u coordinate (x) on Montgomery Curve 25519\n     * @param scalar by which the point would be multiplied\n     * @returns new Point on Montgomery curve\n     */\n    function montgomeryLadder(u, scalar) {\n        aInRange('u', u, _0n, P);\n        aInRange('scalar', scalar, _0n, P);\n        // Section 5: Implementations MUST accept non-canonical values and process them as\n        // if they had been reduced modulo the field prime.\n        const k = scalar;\n        const x_1 = u;\n        let x_2 = _1n;\n        let z_2 = _0n;\n        let x_3 = u;\n        let z_3 = _1n;\n        let swap = _0n;\n        let sw;\n        for (let t = BigInt(montgomeryBits - 1); t >= _0n; t--) {\n            const k_t = (k >> t) & _1n;\n            swap ^= k_t;\n            sw = cswap(swap, x_2, x_3);\n            x_2 = sw[0];\n            x_3 = sw[1];\n            sw = cswap(swap, z_2, z_3);\n            z_2 = sw[0];\n            z_3 = sw[1];\n            swap = k_t;\n            const A = x_2 + z_2;\n            const AA = modP(A * A);\n            const B = x_2 - z_2;\n            const BB = modP(B * B);\n            const E = AA - BB;\n            const C = x_3 + z_3;\n            const D = x_3 - z_3;\n            const DA = modP(D * A);\n            const CB = modP(C * B);\n            const dacb = DA + CB;\n            const da_cb = DA - CB;\n            x_3 = modP(dacb * dacb);\n            z_3 = modP(x_1 * modP(da_cb * da_cb));\n            x_2 = modP(AA * BB);\n            z_2 = modP(E * (AA + modP(a24 * E)));\n        }\n        // (x_2, x_3) = cswap(swap, x_2, x_3)\n        sw = cswap(swap, x_2, x_3);\n        x_2 = sw[0];\n        x_3 = sw[1];\n        // (z_2, z_3) = cswap(swap, z_2, z_3)\n        sw = cswap(swap, z_2, z_3);\n        z_2 = sw[0];\n        z_3 = sw[1];\n        // z_2^(p - 2)\n        const z2 = powPminus2(z_2);\n        // Return x_2 * (z_2^(p - 2))\n        return modP(x_2 * z2);\n    }\n    function encodeUCoordinate(u) {\n        return numberToBytesLE(modP(u), montgomeryBytes);\n    }\n    function decodeUCoordinate(uEnc) {\n        // Section 5: When receiving such an array, implementations of X25519\n        // MUST mask the most significant bit in the final byte.\n        const u = ensureBytes('u coordinate', uEnc, montgomeryBytes);\n        if (fieldLen === 32)\n            u[31] &= 127; // 0b0111_1111\n        return bytesToNumberLE(u);\n    }\n    function decodeScalar(n) {\n        const bytes = ensureBytes('scalar', n);\n        const len = bytes.length;\n        if (len !== montgomeryBytes && len !== fieldLen) {\n            let valid = '' + montgomeryBytes + ' or ' + fieldLen;\n            throw new Error('invalid scalar, expected ' + valid + ' bytes, got ' + len);\n        }\n        return bytesToNumberLE(adjustScalarBytes(bytes));\n    }\n    function scalarMult(scalar, u) {\n        const pointU = decodeUCoordinate(u);\n        const _scalar = decodeScalar(scalar);\n        const pu = montgomeryLadder(pointU, _scalar);\n        // The result was not contributory\n        // https://cr.yp.to/ecdh.html#validate\n        if (pu === _0n)\n            throw new Error('invalid private or public key received');\n        return encodeUCoordinate(pu);\n    }\n    // Computes public key from private. By doing scalar multiplication of base point.\n    const GuBytes = encodeUCoordinate(CURVE.Gu);\n    function scalarMultBase(scalar) {\n        return scalarMult(scalar, GuBytes);\n    }\n    return {\n        scalarMult,\n        scalarMultBase,\n        getSharedSecret: (privateKey, publicKey) => scalarMult(privateKey, publicKey),\n        getPublicKey: (privateKey) => scalarMultBase(privateKey),\n        utils: { randomPrivateKey: () => CURVE.randomBytes(CURVE.nByteLength) },\n        GuBytes: GuBytes,\n    };\n}\n//# sourceMappingURL=montgomery.js.map", "/**\n * ed25519 Twisted <PERSON> curve with following addons:\n * - X25519 ECDH\n * - Ristretto cofactor elimination\n * - Elligator hash-to-group / point indistinguishability\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha512 } from '@noble/hashes/sha512';\nimport { concatBytes, randomBytes, utf8ToBytes } from '@noble/hashes/utils';\nimport { pippenger } from './abstract/curve.js';\nimport { twistedEdwards } from './abstract/edwards.js';\nimport { createHasher, expand_message_xmd, } from './abstract/hash-to-curve.js';\nimport { Field, FpSqrtEven, isNegativeLE, mod, pow2 } from './abstract/modular.js';\nimport { montgomery } from './abstract/montgomery.js';\nimport { bytesToHex, bytesToNumberLE, ensureBytes, equalBytes, numberToBytesLE, } from './abstract/utils.js';\nconst ED25519_P = BigInt('57896044618658097711785492504343953926634992332820282019728792003956564819949');\n// √(-1) aka √(a) aka 2^((p-1)/4)\nconst ED25519_SQRT_M1 = /* @__PURE__ */ BigInt('19681161376707505956807079304988542015446066515923890162744021073123829784752');\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3);\n// prettier-ignore\nconst _5n = BigInt(5), _8n = BigInt(8);\nfunction ed25519_pow_2_252_3(x) {\n    // prettier-ignore\n    const _10n = BigInt(10), _20n = BigInt(20), _40n = BigInt(40), _80n = BigInt(80);\n    const P = ED25519_P;\n    const x2 = (x * x) % P;\n    const b2 = (x2 * x) % P; // x^3, 11\n    const b4 = (pow2(b2, _2n, P) * b2) % P; // x^15, 1111\n    const b5 = (pow2(b4, _1n, P) * x) % P; // x^31\n    const b10 = (pow2(b5, _5n, P) * b5) % P;\n    const b20 = (pow2(b10, _10n, P) * b10) % P;\n    const b40 = (pow2(b20, _20n, P) * b20) % P;\n    const b80 = (pow2(b40, _40n, P) * b40) % P;\n    const b160 = (pow2(b80, _80n, P) * b80) % P;\n    const b240 = (pow2(b160, _80n, P) * b80) % P;\n    const b250 = (pow2(b240, _10n, P) * b10) % P;\n    const pow_p_5_8 = (pow2(b250, _2n, P) * x) % P;\n    // ^ To pow to (p+3)/8, multiply it by x.\n    return { pow_p_5_8, b2 };\n}\nfunction adjustScalarBytes(bytes) {\n    // Section 5: For X25519, in order to decode 32 random bytes as an integer scalar,\n    // set the three least significant bits of the first byte\n    bytes[0] &= 248; // 0b1111_1000\n    // and the most significant bit of the last to zero,\n    bytes[31] &= 127; // 0b0111_1111\n    // set the second most significant bit of the last byte to 1\n    bytes[31] |= 64; // 0b0100_0000\n    return bytes;\n}\n// sqrt(u/v)\nfunction uvRatio(u, v) {\n    const P = ED25519_P;\n    const v3 = mod(v * v * v, P); // v³\n    const v7 = mod(v3 * v3 * v, P); // v⁷\n    // (p+3)/8 and (p-5)/8\n    const pow = ed25519_pow_2_252_3(u * v7).pow_p_5_8;\n    let x = mod(u * v3 * pow, P); // (uv³)(uv⁷)^(p-5)/8\n    const vx2 = mod(v * x * x, P); // vx²\n    const root1 = x; // First root candidate\n    const root2 = mod(x * ED25519_SQRT_M1, P); // Second root candidate\n    const useRoot1 = vx2 === u; // If vx² = u (mod p), x is a square root\n    const useRoot2 = vx2 === mod(-u, P); // If vx² = -u, set x <-- x * 2^((p-1)/4)\n    const noRoot = vx2 === mod(-u * ED25519_SQRT_M1, P); // There is no valid root, vx² = -u√(-1)\n    if (useRoot1)\n        x = root1;\n    if (useRoot2 || noRoot)\n        x = root2; // We return root2 anyway, for const-time\n    if (isNegativeLE(x, P))\n        x = mod(-x, P);\n    return { isValid: useRoot1 || useRoot2, value: x };\n}\n// Just in case\nexport const ED25519_TORSION_SUBGROUP = [\n    '0100000000000000000000000000000000000000000000000000000000000000',\n    'c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac037a',\n    '0000000000000000000000000000000000000000000000000000000000000080',\n    '26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05',\n    'ecffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f',\n    '26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc85',\n    '0000000000000000000000000000000000000000000000000000000000000000',\n    'c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac03fa',\n];\nconst Fp = /* @__PURE__ */ (() => Field(ED25519_P, undefined, true))();\nconst ed25519Defaults = /* @__PURE__ */ (() => ({\n    // Param: a\n    a: BigInt(-1), // Fp.create(-1) is proper; our way still works and is faster\n    // d is equal to -121665/121666 over finite field.\n    // Negative number is P - number, and division is invert(number, P)\n    d: BigInt('37095705934669439343138083508754565189542113879843219016388785533085940283555'),\n    // Finite field 𝔽p over which we'll do calculations; 2n**255n - 19n\n    Fp,\n    // Subgroup order: how many points curve has\n    // 2n**252n + 27742317777372353535851937790883648493n;\n    n: BigInt('7237005577332262213973186563042994240857116359379907606001950938285454250989'),\n    // Cofactor\n    h: _8n,\n    // Base point (x, y) aka generator point\n    Gx: BigInt('15112221349535400772501151409588531511454012693041857206046113283949847762202'),\n    Gy: BigInt('46316835694926478169428394003475163141307993866256225615783033603165251855960'),\n    hash: sha512,\n    randomBytes,\n    adjustScalarBytes,\n    // dom2\n    // Ratio of u to v. Allows us to combine inversion and square root. Uses algo from RFC8032 5.1.3.\n    // Constant-time, u/√v\n    uvRatio,\n}))();\n/**\n * ed25519 curve with EdDSA signatures.\n * @example\n * import { ed25519 } from '@noble/curves/ed25519';\n * const priv = ed25519.utils.randomPrivateKey();\n * const pub = ed25519.getPublicKey(priv);\n * const msg = new TextEncoder().encode('hello');\n * const sig = ed25519.sign(msg, priv);\n * ed25519.verify(sig, msg, pub); // Default mode: follows ZIP215\n * ed25519.verify(sig, msg, pub, { zip215: false }); // RFC8032 / FIPS 186-5\n */\nexport const ed25519 = /* @__PURE__ */ (() => twistedEdwards(ed25519Defaults))();\nfunction ed25519_domain(data, ctx, phflag) {\n    if (ctx.length > 255)\n        throw new Error('Context is too big');\n    return concatBytes(utf8ToBytes('SigEd25519 no Ed25519 collisions'), new Uint8Array([phflag ? 1 : 0, ctx.length]), ctx, data);\n}\nexport const ed25519ctx = /* @__PURE__ */ (() => twistedEdwards({\n    ...ed25519Defaults,\n    domain: ed25519_domain,\n}))();\nexport const ed25519ph = /* @__PURE__ */ (() => twistedEdwards(Object.assign({}, ed25519Defaults, {\n    domain: ed25519_domain,\n    prehash: sha512,\n})))();\n/**\n * ECDH using curve25519 aka x25519.\n * @example\n * import { x25519 } from '@noble/curves/ed25519';\n * const priv = 'a546e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449ac4';\n * const pub = 'e6db6867583030db3594c1a424b15f7c726624ec26b3353b10a903a6d0ab1c4c';\n * x25519.getSharedSecret(priv, pub) === x25519.scalarMult(priv, pub); // aliases\n * x25519.getPublicKey(priv) === x25519.scalarMultBase(priv);\n * x25519.getPublicKey(x25519.utils.randomPrivateKey());\n */\nexport const x25519 = /* @__PURE__ */ (() => montgomery({\n    P: ED25519_P,\n    a: BigInt(486662),\n    montgomeryBits: 255, // n is 253 bits\n    nByteLength: 32,\n    Gu: BigInt(9),\n    powPminus2: (x) => {\n        const P = ED25519_P;\n        // x^(p-2) aka x^(2^255-21)\n        const { pow_p_5_8, b2 } = ed25519_pow_2_252_3(x);\n        return mod(pow2(pow_p_5_8, _3n, P) * b2, P);\n    },\n    adjustScalarBytes,\n    randomBytes,\n}))();\n/**\n * Converts ed25519 public key to x25519 public key. Uses formula:\n * * `(u, v) = ((1+y)/(1-y), sqrt(-486664)*u/x)`\n * * `(x, y) = (sqrt(-486664)*u/v, (u-1)/(u+1))`\n * @example\n *   const someonesPub = ed25519.getPublicKey(ed25519.utils.randomPrivateKey());\n *   const aPriv = x25519.utils.randomPrivateKey();\n *   x25519.getSharedSecret(aPriv, edwardsToMontgomeryPub(someonesPub))\n */\nexport function edwardsToMontgomeryPub(edwardsPub) {\n    const { y } = ed25519.ExtendedPoint.fromHex(edwardsPub);\n    const _1n = BigInt(1);\n    return Fp.toBytes(Fp.create((_1n + y) * Fp.inv(_1n - y)));\n}\nexport const edwardsToMontgomery = edwardsToMontgomeryPub; // deprecated\n/**\n * Converts ed25519 secret key to x25519 secret key.\n * @example\n *   const someonesPub = x25519.getPublicKey(x25519.utils.randomPrivateKey());\n *   const aPriv = ed25519.utils.randomPrivateKey();\n *   x25519.getSharedSecret(edwardsToMontgomeryPriv(aPriv), someonesPub)\n */\nexport function edwardsToMontgomeryPriv(edwardsPriv) {\n    const hashed = ed25519Defaults.hash(edwardsPriv.subarray(0, 32));\n    return ed25519Defaults.adjustScalarBytes(hashed).subarray(0, 32);\n}\n// Hash To Curve Elligator2 Map (NOTE: different from ristretto255 elligator)\n// NOTE: very important part is usage of FpSqrtEven for ELL2_C1_EDWARDS, since\n// SageMath returns different root first and everything falls apart\nconst ELL2_C1 = /* @__PURE__ */ (() => (Fp.ORDER + _3n) / _8n)(); // 1. c1 = (q + 3) / 8       # Integer arithmetic\nconst ELL2_C2 = /* @__PURE__ */ (() => Fp.pow(_2n, ELL2_C1))(); // 2. c2 = 2^c1\nconst ELL2_C3 = /* @__PURE__ */ (() => Fp.sqrt(Fp.neg(Fp.ONE)))(); // 3. c3 = sqrt(-1)\n// prettier-ignore\nfunction map_to_curve_elligator2_curve25519(u) {\n    const ELL2_C4 = (Fp.ORDER - _5n) / _8n; // 4. c4 = (q - 5) / 8       # Integer arithmetic\n    const ELL2_J = BigInt(486662);\n    let tv1 = Fp.sqr(u); //  1.  tv1 = u^2\n    tv1 = Fp.mul(tv1, _2n); //  2.  tv1 = 2 * tv1\n    let xd = Fp.add(tv1, Fp.ONE); //  3.   xd = tv1 + 1         # Nonzero: -1 is square (mod p), tv1 is not\n    let x1n = Fp.neg(ELL2_J); //  4.  x1n = -J              # x1 = x1n / xd = -J / (1 + 2 * u^2)\n    let tv2 = Fp.sqr(xd); //  5.  tv2 = xd^2\n    let gxd = Fp.mul(tv2, xd); //  6.  gxd = tv2 * xd        # gxd = xd^3\n    let gx1 = Fp.mul(tv1, ELL2_J); //  7.  gx1 = J * tv1         # x1n + J * xd\n    gx1 = Fp.mul(gx1, x1n); //  8.  gx1 = gx1 * x1n       # x1n^2 + J * x1n * xd\n    gx1 = Fp.add(gx1, tv2); //  9.  gx1 = gx1 + tv2       # x1n^2 + J * x1n * xd + xd^2\n    gx1 = Fp.mul(gx1, x1n); //  10. gx1 = gx1 * x1n       # x1n^3 + J * x1n^2 * xd + x1n * xd^2\n    let tv3 = Fp.sqr(gxd); //  11. tv3 = gxd^2\n    tv2 = Fp.sqr(tv3); //  12. tv2 = tv3^2           # gxd^4\n    tv3 = Fp.mul(tv3, gxd); //  13. tv3 = tv3 * gxd       # gxd^3\n    tv3 = Fp.mul(tv3, gx1); //  14. tv3 = tv3 * gx1       # gx1 * gxd^3\n    tv2 = Fp.mul(tv2, tv3); //  15. tv2 = tv2 * tv3       # gx1 * gxd^7\n    let y11 = Fp.pow(tv2, ELL2_C4); //  16. y11 = tv2^c4        # (gx1 * gxd^7)^((p - 5) / 8)\n    y11 = Fp.mul(y11, tv3); //  17. y11 = y11 * tv3       # gx1*gxd^3*(gx1*gxd^7)^((p-5)/8)\n    let y12 = Fp.mul(y11, ELL2_C3); //  18. y12 = y11 * c3\n    tv2 = Fp.sqr(y11); //  19. tv2 = y11^2\n    tv2 = Fp.mul(tv2, gxd); //  20. tv2 = tv2 * gxd\n    let e1 = Fp.eql(tv2, gx1); //  21.  e1 = tv2 == gx1\n    let y1 = Fp.cmov(y12, y11, e1); //  22.  y1 = CMOV(y12, y11, e1)  # If g(x1) is square, this is its sqrt\n    let x2n = Fp.mul(x1n, tv1); //  23. x2n = x1n * tv1       # x2 = x2n / xd = 2 * u^2 * x1n / xd\n    let y21 = Fp.mul(y11, u); //  24. y21 = y11 * u\n    y21 = Fp.mul(y21, ELL2_C2); //  25. y21 = y21 * c2\n    let y22 = Fp.mul(y21, ELL2_C3); //  26. y22 = y21 * c3\n    let gx2 = Fp.mul(gx1, tv1); //  27. gx2 = gx1 * tv1       # g(x2) = gx2 / gxd = 2 * u^2 * g(x1)\n    tv2 = Fp.sqr(y21); //  28. tv2 = y21^2\n    tv2 = Fp.mul(tv2, gxd); //  29. tv2 = tv2 * gxd\n    let e2 = Fp.eql(tv2, gx2); //  30.  e2 = tv2 == gx2\n    let y2 = Fp.cmov(y22, y21, e2); //  31.  y2 = CMOV(y22, y21, e2)  # If g(x2) is square, this is its sqrt\n    tv2 = Fp.sqr(y1); //  32. tv2 = y1^2\n    tv2 = Fp.mul(tv2, gxd); //  33. tv2 = tv2 * gxd\n    let e3 = Fp.eql(tv2, gx1); //  34.  e3 = tv2 == gx1\n    let xn = Fp.cmov(x2n, x1n, e3); //  35.  xn = CMOV(x2n, x1n, e3)  # If e3, x = x1, else x = x2\n    let y = Fp.cmov(y2, y1, e3); //  36.   y = CMOV(y2, y1, e3)    # If e3, y = y1, else y = y2\n    let e4 = Fp.isOdd(y); //  37.  e4 = sgn0(y) == 1        # Fix sign of y\n    y = Fp.cmov(y, Fp.neg(y), e3 !== e4); //  38.   y = CMOV(y, -y, e3 XOR e4)\n    return { xMn: xn, xMd: xd, yMn: y, yMd: _1n }; //  39. return (xn, xd, y, 1)\n}\nconst ELL2_C1_EDWARDS = /* @__PURE__ */ (() => FpSqrtEven(Fp, Fp.neg(BigInt(486664))))(); // sgn0(c1) MUST equal 0\nfunction map_to_curve_elligator2_edwards25519(u) {\n    const { xMn, xMd, yMn, yMd } = map_to_curve_elligator2_curve25519(u); //  1.  (xMn, xMd, yMn, yMd) =\n    // map_to_curve_elligator2_curve25519(u)\n    let xn = Fp.mul(xMn, yMd); //  2.  xn = xMn * yMd\n    xn = Fp.mul(xn, ELL2_C1_EDWARDS); //  3.  xn = xn * c1\n    let xd = Fp.mul(xMd, yMn); //  4.  xd = xMd * yMn    # xn / xd = c1 * xM / yM\n    let yn = Fp.sub(xMn, xMd); //  5.  yn = xMn - xMd\n    let yd = Fp.add(xMn, xMd); //  6.  yd = xMn + xMd    # (n / d - 1) / (n / d + 1) = (n - d) / (n + d)\n    let tv1 = Fp.mul(xd, yd); //  7. tv1 = xd * yd\n    let e = Fp.eql(tv1, Fp.ZERO); //  8.   e = tv1 == 0\n    xn = Fp.cmov(xn, Fp.ZERO, e); //  9.  xn = CMOV(xn, 0, e)\n    xd = Fp.cmov(xd, Fp.ONE, e); //  10. xd = CMOV(xd, 1, e)\n    yn = Fp.cmov(yn, Fp.ONE, e); //  11. yn = CMOV(yn, 1, e)\n    yd = Fp.cmov(yd, Fp.ONE, e); //  12. yd = CMOV(yd, 1, e)\n    const inv = Fp.invertBatch([xd, yd]); // batch division\n    return { x: Fp.mul(xn, inv[0]), y: Fp.mul(yn, inv[1]) }; //  13. return (xn, xd, yn, yd)\n}\nconst htf = /* @__PURE__ */ (() => createHasher(ed25519.ExtendedPoint, (scalars) => map_to_curve_elligator2_edwards25519(scalars[0]), {\n    DST: 'edwards25519_XMD:SHA-512_ELL2_RO_',\n    encodeDST: 'edwards25519_XMD:SHA-512_ELL2_NU_',\n    p: Fp.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: sha512,\n}))();\nexport const hashToCurve = /* @__PURE__ */ (() => htf.hashToCurve)();\nexport const encodeToCurve = /* @__PURE__ */ (() => htf.encodeToCurve)();\nfunction assertRstPoint(other) {\n    if (!(other instanceof RistPoint))\n        throw new Error('RistrettoPoint expected');\n}\n// √(-1) aka √(a) aka 2^((p-1)/4)\nconst SQRT_M1 = ED25519_SQRT_M1;\n// √(ad - 1)\nconst SQRT_AD_MINUS_ONE = /* @__PURE__ */ BigInt('25063068953384623474111414158702152701244531502492656460079210482610430750235');\n// 1 / √(a-d)\nconst INVSQRT_A_MINUS_D = /* @__PURE__ */ BigInt('54469307008909316920995813868745141605393597292927456921205312896311721017578');\n// 1-d²\nconst ONE_MINUS_D_SQ = /* @__PURE__ */ BigInt('1159843021668779879193775521855586647937357759715417654439879720876111806838');\n// (d-1)²\nconst D_MINUS_ONE_SQ = /* @__PURE__ */ BigInt('40440834346308536858101042469323190826248399146238708352240133220865137265952');\n// Calculates 1/√(number)\nconst invertSqrt = (number) => uvRatio(_1n, number);\nconst MAX_255B = /* @__PURE__ */ BigInt('0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff');\nconst bytes255ToNumberLE = (bytes) => ed25519.CURVE.Fp.create(bytesToNumberLE(bytes) & MAX_255B);\n// Computes Elligator map for Ristretto\n// https://ristretto.group/formulas/elligator.html\nfunction calcElligatorRistrettoMap(r0) {\n    const { d } = ed25519.CURVE;\n    const P = ed25519.CURVE.Fp.ORDER;\n    const mod = ed25519.CURVE.Fp.create;\n    const r = mod(SQRT_M1 * r0 * r0); // 1\n    const Ns = mod((r + _1n) * ONE_MINUS_D_SQ); // 2\n    let c = BigInt(-1); // 3\n    const D = mod((c - d * r) * mod(r + d)); // 4\n    let { isValid: Ns_D_is_sq, value: s } = uvRatio(Ns, D); // 5\n    let s_ = mod(s * r0); // 6\n    if (!isNegativeLE(s_, P))\n        s_ = mod(-s_);\n    if (!Ns_D_is_sq)\n        s = s_; // 7\n    if (!Ns_D_is_sq)\n        c = r; // 8\n    const Nt = mod(c * (r - _1n) * D_MINUS_ONE_SQ - D); // 9\n    const s2 = s * s;\n    const W0 = mod((s + s) * D); // 10\n    const W1 = mod(Nt * SQRT_AD_MINUS_ONE); // 11\n    const W2 = mod(_1n - s2); // 12\n    const W3 = mod(_1n + s2); // 13\n    return new ed25519.ExtendedPoint(mod(W0 * W3), mod(W2 * W1), mod(W1 * W3), mod(W0 * W2));\n}\n/**\n * Each ed25519/ExtendedPoint has 8 different equivalent points. This can be\n * a source of bugs for protocols like ring signatures. Ristretto was created to solve this.\n * Ristretto point operates in X:Y:Z:T extended coordinates like ExtendedPoint,\n * but it should work in its own namespace: do not combine those two.\n * https://datatracker.ietf.org/doc/html/draft-irtf-cfrg-ristretto255-decaf448\n */\nclass RistPoint {\n    // Private property to discourage combining ExtendedPoint + RistrettoPoint\n    // Always use Ristretto encoding/decoding instead.\n    constructor(ep) {\n        this.ep = ep;\n    }\n    static fromAffine(ap) {\n        return new RistPoint(ed25519.ExtendedPoint.fromAffine(ap));\n    }\n    /**\n     * Takes uniform output of 64-byte hash function like sha512 and converts it to `RistrettoPoint`.\n     * The hash-to-group operation applies Elligator twice and adds the results.\n     * **Note:** this is one-way map, there is no conversion from point to hash.\n     * https://ristretto.group/formulas/elligator.html\n     * @param hex 64-byte output of a hash function\n     */\n    static hashToCurve(hex) {\n        hex = ensureBytes('ristrettoHash', hex, 64);\n        const r1 = bytes255ToNumberLE(hex.slice(0, 32));\n        const R1 = calcElligatorRistrettoMap(r1);\n        const r2 = bytes255ToNumberLE(hex.slice(32, 64));\n        const R2 = calcElligatorRistrettoMap(r2);\n        return new RistPoint(R1.add(R2));\n    }\n    /**\n     * Converts ristretto-encoded string to ristretto point.\n     * https://ristretto.group/formulas/decoding.html\n     * @param hex Ristretto-encoded 32 bytes. Not every 32-byte string is valid ristretto encoding\n     */\n    static fromHex(hex) {\n        hex = ensureBytes('ristrettoHex', hex, 32);\n        const { a, d } = ed25519.CURVE;\n        const P = ed25519.CURVE.Fp.ORDER;\n        const mod = ed25519.CURVE.Fp.create;\n        const emsg = 'RistrettoPoint.fromHex: the hex is not valid encoding of RistrettoPoint';\n        const s = bytes255ToNumberLE(hex);\n        // 1. Check that s_bytes is the canonical encoding of a field element, or else abort.\n        // 3. Check that s is non-negative, or else abort\n        if (!equalBytes(numberToBytesLE(s, 32), hex) || isNegativeLE(s, P))\n            throw new Error(emsg);\n        const s2 = mod(s * s);\n        const u1 = mod(_1n + a * s2); // 4 (a is -1)\n        const u2 = mod(_1n - a * s2); // 5\n        const u1_2 = mod(u1 * u1);\n        const u2_2 = mod(u2 * u2);\n        const v = mod(a * d * u1_2 - u2_2); // 6\n        const { isValid, value: I } = invertSqrt(mod(v * u2_2)); // 7\n        const Dx = mod(I * u2); // 8\n        const Dy = mod(I * Dx * v); // 9\n        let x = mod((s + s) * Dx); // 10\n        if (isNegativeLE(x, P))\n            x = mod(-x); // 10\n        const y = mod(u1 * Dy); // 11\n        const t = mod(x * y); // 12\n        if (!isValid || isNegativeLE(t, P) || y === _0n)\n            throw new Error(emsg);\n        return new RistPoint(new ed25519.ExtendedPoint(x, y, _1n, t));\n    }\n    static msm(points, scalars) {\n        const Fn = Field(ed25519.CURVE.n, ed25519.CURVE.nBitLength);\n        return pippenger(RistPoint, Fn, points, scalars);\n    }\n    /**\n     * Encodes ristretto point to Uint8Array.\n     * https://ristretto.group/formulas/encoding.html\n     */\n    toRawBytes() {\n        let { ex: x, ey: y, ez: z, et: t } = this.ep;\n        const P = ed25519.CURVE.Fp.ORDER;\n        const mod = ed25519.CURVE.Fp.create;\n        const u1 = mod(mod(z + y) * mod(z - y)); // 1\n        const u2 = mod(x * y); // 2\n        // Square root always exists\n        const u2sq = mod(u2 * u2);\n        const { value: invsqrt } = invertSqrt(mod(u1 * u2sq)); // 3\n        const D1 = mod(invsqrt * u1); // 4\n        const D2 = mod(invsqrt * u2); // 5\n        const zInv = mod(D1 * D2 * t); // 6\n        let D; // 7\n        if (isNegativeLE(t * zInv, P)) {\n            let _x = mod(y * SQRT_M1);\n            let _y = mod(x * SQRT_M1);\n            x = _x;\n            y = _y;\n            D = mod(D1 * INVSQRT_A_MINUS_D);\n        }\n        else {\n            D = D2; // 8\n        }\n        if (isNegativeLE(x * zInv, P))\n            y = mod(-y); // 9\n        let s = mod((z - y) * D); // 10 (check footer's note, no sqrt(-a))\n        if (isNegativeLE(s, P))\n            s = mod(-s);\n        return numberToBytesLE(s, 32); // 11\n    }\n    toHex() {\n        return bytesToHex(this.toRawBytes());\n    }\n    toString() {\n        return this.toHex();\n    }\n    // Compare one point to another.\n    equals(other) {\n        assertRstPoint(other);\n        const { ex: X1, ey: Y1 } = this.ep;\n        const { ex: X2, ey: Y2 } = other.ep;\n        const mod = ed25519.CURVE.Fp.create;\n        // (x1 * y2 == y1 * x2) | (y1 * y2 == x1 * x2)\n        const one = mod(X1 * Y2) === mod(Y1 * X2);\n        const two = mod(Y1 * Y2) === mod(X1 * X2);\n        return one || two;\n    }\n    add(other) {\n        assertRstPoint(other);\n        return new RistPoint(this.ep.add(other.ep));\n    }\n    subtract(other) {\n        assertRstPoint(other);\n        return new RistPoint(this.ep.subtract(other.ep));\n    }\n    multiply(scalar) {\n        return new RistPoint(this.ep.multiply(scalar));\n    }\n    multiplyUnsafe(scalar) {\n        return new RistPoint(this.ep.multiplyUnsafe(scalar));\n    }\n    double() {\n        return new RistPoint(this.ep.double());\n    }\n    negate() {\n        return new RistPoint(this.ep.negate());\n    }\n}\nexport const RistrettoPoint = /* @__PURE__ */ (() => {\n    if (!RistPoint.BASE)\n        RistPoint.BASE = new RistPoint(ed25519.ExtendedPoint.BASE);\n    if (!RistPoint.ZERO)\n        RistPoint.ZERO = new RistPoint(ed25519.ExtendedPoint.ZERO);\n    return RistPoint;\n})();\n// Hashing to ristretto255. https://www.rfc-editor.org/rfc/rfc9380#appendix-B\nexport const hashToRistretto255 = (msg, options) => {\n    const d = options.DST;\n    const DST = typeof d === 'string' ? utf8ToBytes(d) : d;\n    const uniform_bytes = expand_message_xmd(msg, DST, 64, sha512);\n    const P = RistPoint.hashToCurve(uniform_bytes);\n    return P;\n};\nexport const hash_to_ristretto255 = hashToRistretto255; // legacy\n//# sourceMappingURL=ed25519.js.map", "/**\n * Short <PERSON> curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { pippenger, validateBasic, wNAF, } from './curve.js';\nimport { Field, getMinHashLength, invert, mapHashToField, mod, validateField, } from './modular.js';\nimport * as ut from './utils.js';\nimport { abool, ensureBytes, memoized } from './utils.js';\nfunction validateSigVerOpts(opts) {\n    if (opts.lowS !== undefined)\n        abool('lowS', opts.lowS);\n    if (opts.prehash !== undefined)\n        abool('prehash', opts.prehash);\n}\nfunction validatePointOpts(curve) {\n    const opts = validateBasic(curve);\n    ut.validateObject(opts, {\n        a: 'field',\n        b: 'field',\n    }, {\n        allowedPrivateKeyLengths: 'array',\n        wrapPrivateKey: 'boolean',\n        isTorsionFree: 'function',\n        clearCofactor: 'function',\n        allowInfinityPoint: 'boolean',\n        fromBytes: 'function',\n        toBytes: 'function',\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error('invalid endomorphism, can only be defined for Koblitz curves that have a=0');\n        }\n        if (typeof endo !== 'object' ||\n            typeof endo.beta !== 'bigint' ||\n            typeof endo.splitScalar !== 'function') {\n            throw new Error('invalid endomorphism, expected beta: bigint and splitScalar: function');\n        }\n    }\n    return Object.freeze({ ...opts });\n}\nconst { bytesToNumberBE: b2n, hexToBytes: h2b } = ut;\nexport class DERErr extends Error {\n    constructor(m = '') {\n        super(m);\n    }\n}\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nexport const DER = {\n    // asn.1 DER encoding utils\n    Err: DERErr,\n    // Basic building block is TLV (Tag-Length-Value)\n    _tlv: {\n        encode: (tag, data) => {\n            const { Err: E } = DER;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length & 1)\n                throw new E('tlv.encode: unpadded data');\n            const dataLen = data.length / 2;\n            const len = ut.numberToHexUnpadded(dataLen);\n            if ((len.length / 2) & 128)\n                throw new E('tlv.encode: long form length too big');\n            // length of length with long form flag\n            const lenLen = dataLen > 127 ? ut.numberToHexUnpadded((len.length / 2) | 128) : '';\n            const t = ut.numberToHexUnpadded(tag);\n            return t + lenLen + len + data;\n        },\n        // v - value, l - left bytes (unparsed)\n        decode(tag, data) {\n            const { Err: E } = DER;\n            let pos = 0;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length < 2 || data[pos++] !== tag)\n                throw new E('tlv.decode: wrong tlv');\n            const first = data[pos++];\n            const isLong = !!(first & 128); // First bit of first length byte is flag for short/long form\n            let length = 0;\n            if (!isLong)\n                length = first;\n            else {\n                // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n                const lenLen = first & 127;\n                if (!lenLen)\n                    throw new E('tlv.decode(long): indefinite length not supported');\n                if (lenLen > 4)\n                    throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n                const lengthBytes = data.subarray(pos, pos + lenLen);\n                if (lengthBytes.length !== lenLen)\n                    throw new E('tlv.decode: length bytes not complete');\n                if (lengthBytes[0] === 0)\n                    throw new E('tlv.decode(long): zero leftmost byte');\n                for (const b of lengthBytes)\n                    length = (length << 8) | b;\n                pos += lenLen;\n                if (length < 128)\n                    throw new E('tlv.decode(long): not minimal encoding');\n            }\n            const v = data.subarray(pos, pos + length);\n            if (v.length !== length)\n                throw new E('tlv.decode: wrong value length');\n            return { v, l: data.subarray(pos + length) };\n        },\n    },\n    // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n    // since we always use positive integers here. It must always be empty:\n    // - add zero byte if exists\n    // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n    _int: {\n        encode(num) {\n            const { Err: E } = DER;\n            if (num < _0n)\n                throw new E('integer: negative integers are not allowed');\n            let hex = ut.numberToHexUnpadded(num);\n            // Pad with zero byte if negative flag is present\n            if (Number.parseInt(hex[0], 16) & 0b1000)\n                hex = '00' + hex;\n            if (hex.length & 1)\n                throw new E('unexpected DER parsing assertion: unpadded hex');\n            return hex;\n        },\n        decode(data) {\n            const { Err: E } = DER;\n            if (data[0] & 128)\n                throw new E('invalid signature integer: negative');\n            if (data[0] === 0x00 && !(data[1] & 128))\n                throw new E('invalid signature integer: unnecessary leading zero');\n            return b2n(data);\n        },\n    },\n    toSig(hex) {\n        // parse DER signature\n        const { Err: E, _int: int, _tlv: tlv } = DER;\n        const data = typeof hex === 'string' ? h2b(hex) : hex;\n        ut.abytes(data);\n        const { v: seqBytes, l: seqLeftBytes } = tlv.decode(0x30, data);\n        if (seqLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        const { v: rBytes, l: rLeftBytes } = tlv.decode(0x02, seqBytes);\n        const { v: sBytes, l: sLeftBytes } = tlv.decode(0x02, rLeftBytes);\n        if (sLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        return { r: int.decode(rBytes), s: int.decode(sBytes) };\n    },\n    hexFromSig(sig) {\n        const { _tlv: tlv, _int: int } = DER;\n        const rs = tlv.encode(0x02, int.encode(sig.r));\n        const ss = tlv.encode(0x02, int.encode(sig.s));\n        const seq = rs + ss;\n        return tlv.encode(0x30, seq);\n    },\n};\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nexport function weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const Fn = Field(CURVE.n, CURVE.nBitLength);\n    const toBytes = CURVE.toBytes ||\n        ((_c, point, _isCompressed) => {\n            const a = point.toAffine();\n            return ut.concatBytes(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n        });\n    const fromBytes = CURVE.fromBytes ||\n        ((bytes) => {\n            // const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n            const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n            const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n            return { x, y };\n        });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula\n     * @returns y²\n     */\n    function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x2 * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n    }\n    // Validate whether the passed curve params are valid.\n    // We check if curve equation works for generator point.\n    // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n    // ProjectivePoint class has not been initialized yet.\n    if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx)))\n        throw new Error('bad generator point: equation left != right');\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return ut.inRange(num, _1n, CURVE.n);\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n: N } = CURVE;\n        if (lengths && typeof key !== 'bigint') {\n            if (ut.isBytes(key))\n                key = ut.bytesToHex(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== 'string' || !lengths.includes(key.length))\n                throw new Error('invalid private key');\n            key = key.padStart(nByteLength * 2, '0');\n        }\n        let num;\n        try {\n            num =\n                typeof key === 'bigint'\n                    ? key\n                    : ut.bytesToNumberBE(ensureBytes('private key', key, nByteLength));\n        }\n        catch (error) {\n            throw new Error('invalid private key, expected hex or ' + nByteLength + ' bytes, got ' + typeof key);\n        }\n        if (wrapPrivateKey)\n            num = mod(num, N); // disabled by default, enabled for BLS\n        ut.aInRange('private key', num, _1n, N); // num in range [1..N-1]\n        return num;\n    }\n    function assertPrjPoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ProjectivePoint expected');\n    }\n    // Memoized toAffine / validity check. They are heavy. Points are immutable.\n    // Converts Projective point to affine (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    // (x, y, z) ∋ (x=x/z, y=y/z)\n    const toAffineMemo = memoized((p, iz) => {\n        const { px: x, py: y, pz: z } = p;\n        // Fast-path for normalized points\n        if (Fp.eql(z, Fp.ONE))\n            return { x, y };\n        const is0 = p.is0();\n        // If invZ was 0, we return zero point. However we still want to execute\n        // all operations, so we replace invZ with a random number, 1.\n        if (iz == null)\n            iz = is0 ? Fp.ONE : Fp.inv(z);\n        const ax = Fp.mul(x, iz);\n        const ay = Fp.mul(y, iz);\n        const zz = Fp.mul(z, iz);\n        if (is0)\n            return { x: Fp.ZERO, y: Fp.ZERO };\n        if (!Fp.eql(zz, Fp.ONE))\n            throw new Error('invZ was invalid');\n        return { x: ax, y: ay };\n    });\n    // NOTE: on exception this will crash 'cached' and no value will be set.\n    // Otherwise true will be return\n    const assertValidMemo = memoized((p) => {\n        if (p.is0()) {\n            // (0, 1, 0) aka ZERO is invalid in most contexts.\n            // In BLS, ZERO can be serialized, so we allow it.\n            // (0, 0, 0) is invalid representation of ZERO.\n            if (CURVE.allowInfinityPoint && !Fp.is0(p.py))\n                return;\n            throw new Error('bad point: ZERO');\n        }\n        // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n        const { x, y } = p.toAffine();\n        // Check if x, y are valid field elements\n        if (!Fp.isValid(x) || !Fp.isValid(y))\n            throw new Error('bad point: x or y not FE');\n        const left = Fp.sqr(y); // y²\n        const right = weierstrassEquation(x); // x³ + ax + b\n        if (!Fp.eql(left, right))\n            throw new Error('bad point: equation left != right');\n        if (!p.isTorsionFree())\n            throw new Error('bad point: not in prime-order subgroup');\n        return true;\n    });\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */\n    class Point {\n        constructor(px, py, pz) {\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            if (px == null || !Fp.isValid(px))\n                throw new Error('x required');\n            if (py == null || !Fp.isValid(py))\n                throw new Error('y required');\n            if (pz == null || !Fp.isValid(pz))\n                throw new Error('z required');\n            Object.freeze(this);\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('invalid affine point');\n            if (p instanceof Point)\n                throw new Error('projective point not allowed');\n            const is0 = (i) => Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y))\n                return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */\n        static normalizeZ(points) {\n            const toInv = Fp.invertBatch(points.map((p) => p.pz));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */\n        static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes(ensureBytes('pointHex', hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // Multiscalar Multiplication\n        static msm(points, scalars) {\n            return pippenger(Point, Fn, points, scalars);\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            wnaf.setWindowSize(this, windowSize);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            assertValidMemo(this);\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd)\n                return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */\n        equals(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */\n        negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, n, Point.normalizeZ);\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */\n        multiplyUnsafe(sc) {\n            const { endo, n: N } = CURVE;\n            ut.aInRange('scalar', sc, _0n, N);\n            const I = Point.ZERO;\n            if (sc === _0n)\n                return I;\n            if (this.is0() || sc === _1n)\n                return this;\n            // Case a: no endomorphism. Case b: has precomputes.\n            if (!endo || wnaf.hasPrecomputes(this))\n                return wnaf.wNAFCachedUnsafe(this, sc, Point.normalizeZ);\n            // Case c: endomorphism\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(sc);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while (k1 > _0n || k2 > _0n) {\n                if (k1 & _1n)\n                    k1p = k1p.add(d);\n                if (k2 & _1n)\n                    k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg)\n                k1p = k1p.negate();\n            if (k2neg)\n                k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */\n        multiply(scalar) {\n            const { endo, n: N } = CURVE;\n            ut.aInRange('scalar', scalar, _1n, N);\n            let point, fake; // Fake point is used to const-time mult\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(scalar);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            }\n            else {\n                const { p, f } = this.wNAF(scalar);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([point, fake])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */\n        multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            return toAffineMemo(this, iz);\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n)\n                return true; // No subgroups, always torsion-free\n            if (isTorsionFree)\n                return isTorsionFree(Point, this);\n            throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n)\n                return this; // Fast-path\n            if (clearCofactor)\n                return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            abool('isCompressed', isCompressed);\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            abool('isCompressed', isCompressed);\n            return ut.bytesToHex(this.toRawBytes(isCompressed));\n        }\n    }\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n    const _bits = CURVE.nBitLength;\n    const wnaf = wNAF(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n    // Validate if generator point is on curve\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder,\n    };\n}\nfunction validateOpts(curve) {\n    const opts = validateBasic(curve);\n    ut.validateObject(opts, {\n        hash: 'hash',\n        hmac: 'function',\n        randomBytes: 'function',\n    }, {\n        bits2int: 'function',\n        bits2int_modN: 'function',\n        lowS: 'boolean',\n    });\n    return Object.freeze({ lowS: true, ...opts });\n}\n/**\n * Creates short weierstrass curve and ECDSA signature methods for it.\n * @example\n * import { Field } from '@noble/curves/abstract/modular';\n * // Before that, define BigInt-s: a, b, p, n, Gx, Gy\n * const curve = weierstrass({ a, b, Fp: Field(p), n, Gx, Gy, h: 1n })\n */\nexport function weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function modN(a) {\n        return mod(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return invert(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder, } = weierstrassPoints({\n        ...CURVE,\n        toBytes(_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = ut.concatBytes;\n            abool('isCompressed', isCompressed);\n            if (isCompressed) {\n                return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n            }\n            else {\n                return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes(bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = ut.bytesToNumberBE(tail);\n                if (!ut.inRange(x, _1n, Fp.ORDER))\n                    throw new Error('Point is not on curve');\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y;\n                try {\n                    y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                }\n                catch (sqrtError) {\n                    const suffix = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n                    throw new Error('Point is not on curve' + suffix);\n                }\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd)\n                    y = Fp.neg(y);\n                return { x, y };\n            }\n            else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return { x, y };\n            }\n            else {\n                const cl = compressedLen;\n                const ul = uncompressedLen;\n                throw new Error('invalid Point, expected length of ' + cl + ', or uncompressed ' + ul + ', got ' + len);\n            }\n        },\n    });\n    const numToNByteStr = (num) => ut.bytesToHex(ut.numberToBytesBE(num, CURVE.nByteLength));\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to) => ut.bytesToNumberBE(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */\n    class Signature {\n        constructor(r, s, recovery) {\n            this.r = r;\n            this.s = s;\n            this.recovery = recovery;\n            this.assertValidity();\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = CURVE.nByteLength;\n            hex = ensureBytes('compactSignature', hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig(ensureBytes('DER', hex));\n            return new Signature(r, s);\n        }\n        assertValidity() {\n            ut.aInRange('r', this.r, _1n, CURVE_ORDER); // r in [1..N]\n            ut.aInRange('s', this.s, _1n, CURVE_ORDER); // s in [1..N]\n        }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN(ensureBytes('msgHash', msgHash)); // Truncate hash\n            if (rec == null || ![0, 1, 2, 3].includes(rec))\n                throw new Error('recovery id invalid');\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER)\n                throw new Error('recovery id 2 or 3 invalid');\n            const prefix = (rec & 1) === 0 ? '02' : '03';\n            const R = Point.fromHex(prefix + numToNByteStr(radj));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q)\n                throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return ut.hexToBytes(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig({ r: this.r, s: this.s });\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return ut.hexToBytes(this.toCompactHex());\n        }\n        toCompactHex() {\n            return numToNByteStr(this.r) + numToNByteStr(this.s);\n        }\n    }\n    const utils = {\n        isValidPrivateKey(privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            }\n            catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */\n        randomPrivateKey: () => {\n            const length = getMinHashLength(CURVE.n);\n            return mapHashToField(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        },\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */\n    function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */\n    function isProbPub(item) {\n        const arr = ut.isBytes(item);\n        const str = typeof item === 'string';\n        const len = (arr || str) && item.length;\n        if (arr)\n            return len === compressedLen || len === uncompressedLen;\n        if (str)\n            return len === 2 * compressedLen || len === 2 * uncompressedLen;\n        if (item instanceof Point)\n            return true;\n        return false;\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */\n    function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA))\n            throw new Error('first arg must be private key');\n        if (!isProbPub(publicB))\n            throw new Error('second arg must be public key');\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int ||\n        function (bytes) {\n            // Our custom check \"just in case\"\n            if (bytes.length > 8192)\n                throw new Error('input is too large');\n            // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n            // for some cases, since bytes.length * 8 is not actual bitLength.\n            const num = ut.bytesToNumberBE(bytes); // check for == u8 done here\n            const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n            return delta > 0 ? num >> BigInt(delta) : num;\n        };\n    const bits2int_modN = CURVE.bits2int_modN ||\n        function (bytes) {\n            return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n        };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = ut.bitMask(CURVE.nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */\n    function int2octets(num) {\n        ut.aInRange('num < 2^' + CURVE.nBitLength, num, _0n, ORDER_MASK);\n        // works with order, can have different size than numToField!\n        return ut.numberToBytesBE(num, CURVE.nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order,\n    // this will be invalid at least for P521. Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if (['recovered', 'canonical'].some((k) => k in opts))\n            throw new Error('sign() legacy options not supported');\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null)\n            lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = ensureBytes('msgHash', msgHash);\n        validateSigVerOpts(opts);\n        if (prehash)\n            msgHash = ensureBytes('prehashed msgHash', hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [int2octets(d), int2octets(h1int)];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null && ent !== false) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push(ensureBytes('extraEntropy', e)); // check for being bytes\n        }\n        const seed = ut.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k))\n                return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n)\n                return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n)\n                return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return { seed, k2sig };\n    }\n    const defaultSigOpts = { lowS: CURVE.lowS, prehash: false };\n    const defaultVerOpts = { lowS: CURVE.lowS, prehash: false };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */\n    function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = ut.createHmacDrbg(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */\n    function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = ensureBytes('msgHash', msgHash);\n        publicKey = ensureBytes('publicKey', publicKey);\n        const { lowS, prehash, format } = opts;\n        // Verify opts, deduce signature format\n        validateSigVerOpts(opts);\n        if ('strict' in opts)\n            throw new Error('options.strict was renamed to lowS');\n        if (format !== undefined && format !== 'compact' && format !== 'der')\n            throw new Error('format must be compact or der');\n        const isHex = typeof sg === 'string' || ut.isBytes(sg);\n        const isObj = !isHex &&\n            !format &&\n            typeof sg === 'object' &&\n            sg !== null &&\n            typeof sg.r === 'bigint' &&\n            typeof sg.s === 'bigint';\n        if (!isHex && !isObj)\n            throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n        let _sig = undefined;\n        let P;\n        try {\n            if (isObj)\n                _sig = new Signature(sg.r, sg.s);\n            if (isHex) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    if (format !== 'compact')\n                        _sig = Signature.fromDER(sg);\n                }\n                catch (derError) {\n                    if (!(derError instanceof DER.Err))\n                        throw derError;\n                }\n                if (!_sig && format !== 'der')\n                    _sig = Signature.fromCompact(sg);\n            }\n            P = Point.fromHex(publicKey);\n        }\n        catch (error) {\n            return false;\n        }\n        if (!_sig)\n            return false;\n        if (lowS && _sig.hasHighS())\n            return false;\n        if (prehash)\n            msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R)\n            return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils,\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nexport function SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for (let o = q - _1n; o % _2n === _0n; o /= _2n)\n        l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v) => {\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for (let i = c1; i > _1n; i--) {\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return { isValid: isQR, value: tv3 };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v) => {\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nexport function mapToCurveSimpleSWU(Fp, opts) {\n    validateField(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n        throw new Error('mapToCurveSimpleSWU: invalid opts');\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd)\n        throw new Error('Fp.isOdd is not implemented!');\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u) => {\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        x = Fp.div(x, tv4); // 25.   x = x / tv4\n        return { x, y };\n    };\n}\n//# sourceMappingURL=weierstrass.js.map", "/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { hmac } from '@noble/hashes/hmac';\nimport { concatBytes, randomBytes } from '@noble/hashes/utils';\nimport { weierstrass } from './abstract/weierstrass.js';\n/** connects noble-curves to noble-hashes */\nexport function getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs) => hmac(hash, key, concatBytes(...msgs)),\n        randomBytes,\n    };\n}\nexport function createCurve(curveDef, defHash) {\n    const create = (hash) => weierstrass({ ...curveDef, ...getHash(hash) });\n    return { ...create(defHash), create };\n}\n//# sourceMappingURL=_shortw_utils.js.map", "/**\n * NIST secp256r1 aka p256.\n * https://www.secg.org/sec2-v2.pdf, https://neuromancer.sk/std/nist/P-256\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha256 } from '@noble/hashes/sha256';\nimport { createCurve } from './_shortw_utils.js';\nimport { createHasher } from './abstract/hash-to-curve.js';\nimport { Field } from './abstract/modular.js';\nimport { mapToCurveSimpleSWU } from './abstract/weierstrass.js';\nconst Fp256 = Field(BigInt('0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff'));\nconst CURVE_A = Fp256.create(BigInt('-3'));\nconst CURVE_B = BigInt('0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b');\n/** secp256r1 curve, ECDSA and ECDH methods. */\n// prettier-ignore\nexport const p256 = createCurve({\n    a: CURVE_A, // Equation params: a, b\n    b: CURVE_B,\n    Fp: Fp256, // Field: 2n**224n * (2n**32n-1n) + 2n**192n + 2n**96n-1n\n    // Curve order, total count of valid points in the field\n    n: BigInt('0xffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551'),\n    // Base (generator) point (x, y)\n    Gx: BigInt('0x6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296'),\n    Gy: BigInt('0x4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5'),\n    h: BigInt(1),\n    lowS: false,\n}, sha256);\n/** Alias to p256. */\nexport const secp256r1 = p256;\nconst mapSWU = /* @__PURE__ */ (() => mapToCurveSimpleSWU(Fp256, {\n    A: CURVE_A,\n    B: CURVE_B,\n    Z: Fp256.create(BigInt('-10')),\n}))();\nconst htf = /* @__PURE__ */ (() => createHasher(secp256r1.ProjectivePoint, (scalars) => mapSWU(scalars[0]), {\n    DST: 'P256_XMD:SHA-256_SSWU_RO_',\n    encodeDST: 'P256_XMD:SHA-256_SSWU_NU_',\n    p: Fp256.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: sha256,\n}))();\n/** secp256r1 hash-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nexport const hashToCurve = /* @__PURE__ */ (() => htf.hashToCurve)();\n/** secp256r1 encode-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nexport const encodeToCurve = /* @__PURE__ */ (() => htf.encodeToCurve)();\n//# sourceMappingURL=p256.js.map", "import { chacha20poly1305 } from \"@noble/ciphers/chacha\";\nimport { hkdf } from \"@noble/hashes/hkdf\";\nimport { randomBytes } from \"@noble/hashes/utils\";\nimport { sha256 } from \"@noble/hashes/sha256\";\nimport { x25519 } from \"@noble/curves/ed25519\";\nimport { p256 } from \"@noble/curves/p256\";\nimport { CryptoTypes } from \"@walletconnect/types\";\nimport { decodeJWT } from \"@walletconnect/relay-auth\";\nimport { concat, fromString, toString } from \"uint8arrays\";\n\nexport const BASE10 = \"base10\";\nexport const BASE16 = \"base16\";\nexport const BASE64 = \"base64pad\";\nexport const BASE64URL = \"base64url\";\nexport const UTF8 = \"utf8\";\n\nexport const TYPE_0 = 0;\nexport const TYPE_1 = 1;\nexport const TYPE_2 = 2;\n\nexport type P256KeyDataType = {\n  crv: \"P-256\" | string;\n  ext: true | boolean;\n  key_ops: [\"verify\"] | string[];\n  kty: string;\n  x: string;\n  y: string;\n};\n\nconst ZERO_INDEX = 0;\nconst TYPE_LENGTH = 1;\nconst IV_LENGTH = 12;\nconst KEY_LENGTH = 32;\n\nexport function generateKeyPair(): CryptoTypes.KeyPair {\n  const privateKey = x25519.utils.randomPrivateKey();\n  const publicKey = x25519.getPublicKey(privateKey);\n  return {\n    privateKey: toString(privateKey, BASE16),\n    publicKey: toString(publicKey, BASE16),\n  };\n}\n\nexport function generateRandomBytes32(): string {\n  const random = randomBytes(KEY_LENGTH);\n  return toString(random, BASE16);\n}\n\nexport function deriveSymKey(privateKeyA: string, publicKeyB: string): string {\n  const sharedKey = x25519.getSharedSecret(\n    fromString(privateKeyA, BASE16),\n    fromString(publicKeyB, BASE16),\n  );\n  const symKey = hkdf(sha256, sharedKey, undefined, undefined, KEY_LENGTH);\n  return toString(symKey, BASE16);\n}\n\nexport function hashKey(key: string): string {\n  const result = sha256(fromString(key, BASE16));\n  return toString(result, BASE16);\n}\n\nexport function hashMessage(message: string): string {\n  const result = sha256(fromString(message, UTF8));\n  return toString(result, BASE16);\n}\n\nexport function encodeTypeByte(type: number): Uint8Array {\n  return fromString(`${type}`, BASE10);\n}\n\nexport function decodeTypeByte(byte: Uint8Array): number {\n  return Number(toString(byte, BASE10));\n}\n\nfunction toBase64URL(base64: string): string {\n  return base64.replace(/\\+/g, \"-\").replace(/\\//g, \"_\").replace(/=/g, \"\");\n}\n\nfunction fromBase64URL(base64url: string): string {\n  const base64 = base64url.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  const padding = (4 - (base64.length % 4)) % 4;\n  return base64 + \"=\".repeat(padding);\n}\n\nexport function encrypt(params: CryptoTypes.EncryptParams): string {\n  const type = encodeTypeByte(typeof params.type !== \"undefined\" ? params.type : TYPE_0);\n  if (decodeTypeByte(type) === TYPE_1 && typeof params.senderPublicKey === \"undefined\") {\n    throw new Error(\"Missing sender public key for type 1 envelope\");\n  }\n  const senderPublicKey =\n    typeof params.senderPublicKey !== \"undefined\"\n      ? fromString(params.senderPublicKey, BASE16)\n      : undefined;\n\n  const iv =\n    typeof params.iv !== \"undefined\" ? fromString(params.iv, BASE16) : randomBytes(IV_LENGTH);\n  const key = fromString(params.symKey, BASE16);\n  const box = chacha20poly1305(key, iv);\n  const sealed = box.encrypt(fromString(params.message, UTF8));\n  const result = serialize({ type, sealed, iv, senderPublicKey });\n  return params.encoding === BASE64URL ? toBase64URL(result) : result;\n}\n\nexport function decrypt(params: CryptoTypes.DecryptParams): string {\n  const key = fromString(params.symKey, BASE16);\n  const { sealed, iv } = deserialize({ encoded: params.encoded, encoding: params.encoding });\n  const box = chacha20poly1305(key, iv);\n  const message = box.decrypt(sealed);\n  if (message === null) throw new Error(\"Failed to decrypt\");\n  return toString(message, UTF8);\n}\n\nexport function encodeTypeTwoEnvelope(\n  message: string,\n  encoding?: CryptoTypes.EncodingType,\n): string {\n  const type = encodeTypeByte(TYPE_2);\n  // iv is not used in type 2 envelopes\n  const iv = randomBytes(IV_LENGTH);\n  const sealed = fromString(message, UTF8);\n  const result = serialize({ type, sealed, iv });\n  return encoding === BASE64URL ? toBase64URL(result) : result;\n}\n\nexport function decodeTypeTwoEnvelope(\n  encoded: string,\n  encoding?: CryptoTypes.EncodingType,\n): string {\n  const { sealed } = deserialize({ encoded, encoding });\n  return toString(sealed, UTF8);\n}\n\nexport function serialize(params: CryptoTypes.EncodingParams): string {\n  if (decodeTypeByte(params.type) === TYPE_2) {\n    return toString(concat([params.type, params.sealed]), BASE64);\n  }\n  if (decodeTypeByte(params.type) === TYPE_1) {\n    if (typeof params.senderPublicKey === \"undefined\") {\n      throw new Error(\"Missing sender public key for type 1 envelope\");\n    }\n    return toString(\n      concat([params.type, params.senderPublicKey, params.iv, params.sealed]),\n      BASE64,\n    );\n  }\n  // default to type 0 envelope\n  return toString(concat([params.type, params.iv, params.sealed]), BASE64);\n}\n\nexport function deserialize(params: CryptoTypes.DecodingParams): CryptoTypes.EncodingParams {\n  const encoding = params.encoding || BASE64;\n  const normalizedEncoded = encoding === BASE64URL ? fromBase64URL(params.encoded) : params.encoded;\n  const bytes = fromString(normalizedEncoded, BASE64);\n  const type = bytes.slice(ZERO_INDEX, TYPE_LENGTH);\n  const slice1 = TYPE_LENGTH;\n  if (decodeTypeByte(type) === TYPE_1) {\n    const slice2 = slice1 + KEY_LENGTH;\n    const slice3 = slice2 + IV_LENGTH;\n    const senderPublicKey = bytes.slice(slice1, slice2);\n    const iv = bytes.slice(slice2, slice3);\n    const sealed = bytes.slice(slice3);\n    return { type, sealed, iv, senderPublicKey };\n  }\n  if (decodeTypeByte(type) === TYPE_2) {\n    const sealed = bytes.slice(slice1);\n    // iv is not used in type 2 envelopes\n    const iv = randomBytes(IV_LENGTH);\n    return { type, sealed, iv };\n  }\n  // default to type 0 envelope\n  const slice2 = slice1 + IV_LENGTH;\n  const iv = bytes.slice(slice1, slice2);\n  const sealed = bytes.slice(slice2);\n  return { type, sealed, iv };\n}\n\nexport function validateDecoding(\n  encoded: string,\n  opts?: CryptoTypes.DecodeOptions,\n): CryptoTypes.EncodingValidation {\n  const deserialized = deserialize({ encoded, encoding: opts?.encoding });\n  return validateEncoding({\n    type: decodeTypeByte(deserialized.type),\n    senderPublicKey:\n      typeof deserialized.senderPublicKey !== \"undefined\"\n        ? toString(deserialized.senderPublicKey, BASE16)\n        : undefined,\n    receiverPublicKey: opts?.receiverPublicKey,\n  });\n}\n\nexport function validateEncoding(opts?: CryptoTypes.EncodeOptions): CryptoTypes.EncodingValidation {\n  const type = opts?.type || TYPE_0;\n  if (type === TYPE_1) {\n    if (typeof opts?.senderPublicKey === \"undefined\") {\n      throw new Error(\"missing sender public key\");\n    }\n    if (typeof opts?.receiverPublicKey === \"undefined\") {\n      throw new Error(\"missing receiver public key\");\n    }\n  }\n  return {\n    type,\n    senderPublicKey: opts?.senderPublicKey,\n    receiverPublicKey: opts?.receiverPublicKey,\n  };\n}\n\nexport function isTypeOneEnvelope(\n  result: CryptoTypes.EncodingValidation,\n): result is CryptoTypes.TypeOneParams {\n  return (\n    result.type === TYPE_1 &&\n    typeof result.senderPublicKey === \"string\" &&\n    typeof result.receiverPublicKey === \"string\"\n  );\n}\n\nexport function isTypeTwoEnvelope(\n  result: CryptoTypes.EncodingValidation,\n): result is CryptoTypes.TypeOneParams {\n  return result.type === TYPE_2;\n}\n\nexport function getCryptoKeyFromKeyData(keyData: P256KeyDataType): Uint8Array {\n  const xBuffer = Buffer.from(keyData.x, \"base64\");\n  const yBuffer = Buffer.from(keyData.y, \"base64\");\n\n  // Concatenate x and y coordinates with 0x04 prefix (uncompressed point format)\n  return concat([new Uint8Array([0x04]), xBuffer, yBuffer]);\n}\n\nexport function verifyP256Jwt<T>(token: string, keyData: P256KeyDataType) {\n  const [headerBase64Url, payloadBase64Url, signatureBase64Url] = token.split(\".\");\n\n  // Decode the signature\n  const signatureBuffer = Buffer.from(fromBase64URL(signatureBase64Url), \"base64\");\n\n  // Check if signature length is correct (64 bytes for P-256)\n  if (signatureBuffer.length !== 64) {\n    throw new Error(\"Invalid signature length\");\n  }\n\n  // Extract r and s from the signature\n  const r = signatureBuffer.slice(0, 32);\n  const s = signatureBuffer.slice(32, 64);\n\n  // Create the signing input\n  const signingInput = `${headerBase64Url}.${payloadBase64Url}`;\n\n  // Hash the signing input\n  const messageHash = sha256(signingInput);\n\n  // Get the public key in uncompressed point format\n  const publicKey = getCryptoKeyFromKeyData(keyData);\n\n  // Verify the signature using noble/curves p256\n  const isValid = p256.verify(\n    concat([r, s]), // signature bytes\n    messageHash, // message hash\n    publicKey, // public key in uncompressed format\n  );\n\n  if (!isValid) {\n    throw new Error(\"Invalid signature\");\n  }\n\n  const data = decodeJWT(token) as unknown as { payload: T };\n  return data.payload;\n}\n", "import { RELAY_JSONRPC } from \"@walletconnect/relay-api\";\nimport { RelayerTypes } from \"@walletconnect/types\";\n\nexport const RELAYER_DEFAULT_PROTOCOL = \"irn\";\n\nexport function getRelayProtocolName(\n  opts?: RelayerTypes.RequestOptions,\n): RelayerTypes.ProtocolOptions {\n  return opts?.relay || { protocol: RELAYER_DEFAULT_PROTOCOL };\n}\n\nexport function getRelayProtocolApi(protocol: string) {\n  const jsonrpc = RELAY_JSONRPC[protocol];\n  if (typeof jsonrpc === \"undefined\") {\n    throw new Error(`Relay Protocol not supported: ${protocol}`);\n  }\n  return jsonrpc;\n}\n", "import { EngineTypes, RelayerTypes } from \"@walletconnect/types\";\nimport { fromBase64 } from \"./misc\";\n\n// -- uri -------------------------------------------------- //\n\nexport function parseRelayParams(params: any, delimiter = \"-\"): RelayerTypes.ProtocolOptions {\n  const relay: any = {};\n  const prefix = \"relay\" + delimiter;\n  Object.keys(params).forEach((key) => {\n    if (key.startsWith(prefix)) {\n      const name = key.replace(prefix, \"\");\n      const value = params[key];\n      relay[name] = value;\n    }\n  });\n  return relay;\n}\n\nexport function parseUri(str: string): EngineTypes.UriParameters {\n  if (!str.includes(\"wc:\")) {\n    const parsed = fromBase64(str);\n    if (parsed?.includes(\"wc:\")) {\n      str = parsed;\n    }\n  }\n\n  // remove android schema prefix\n  str = str.includes(\"wc://\") ? str.replace(\"wc://\", \"\") : str;\n  // remove ios schema prefix\n  str = str.includes(\"wc:\") ? str.replace(\"wc:\", \"\") : str;\n  const pathStart: number = str.indexOf(\":\");\n  const pathEnd: number | undefined = str.indexOf(\"?\") !== -1 ? str.indexOf(\"?\") : undefined;\n  const protocol: string = str.substring(0, pathStart);\n  const path: string = str.substring(pathStart + 1, pathEnd);\n  const requiredValues = path.split(\"@\");\n  const queryString: string = typeof pathEnd !== \"undefined\" ? str.substring(pathEnd) : \"\";\n  const urlSearchParams = new URLSearchParams(queryString);\n  const queryParams: Record<string, string> = {};\n  urlSearchParams.forEach((value, key) => {\n    queryParams[key] = value;\n  });\n  const methods =\n    typeof queryParams.methods === \"string\" ? queryParams.methods.split(\",\") : undefined;\n  const result = {\n    protocol,\n    topic: parseTopic(requiredValues[0]),\n    version: parseInt(requiredValues[1], 10),\n    symKey: queryParams.symKey as string,\n    relay: parseRelayParams(queryParams),\n    methods,\n    expiryTimestamp: queryParams.expiryTimestamp\n      ? parseInt(queryParams.expiryTimestamp as string, 10)\n      : undefined,\n  };\n  return result;\n}\n\nexport function parseTopic(topic: string): string {\n  return topic.startsWith(\"//\") ? topic.substring(2) : topic;\n}\n\nexport function formatRelayParams(relay: RelayerTypes.ProtocolOptions, delimiter = \"-\") {\n  const prefix = \"relay\";\n  const params: any = {};\n  Object.keys(relay).forEach((key) => {\n    const typedKey = key as keyof typeof relay;\n    const k = prefix + delimiter + typedKey;\n    if (relay[typedKey]) {\n      params[k] = relay[typedKey];\n    }\n  });\n  return params;\n}\n\nexport function formatUri(params: EngineTypes.UriParameters): string {\n  const urlSearchParams = new URLSearchParams();\n\n  const relayParams = formatRelayParams(params.relay);\n  Object.keys(relayParams)\n    .sort()\n    .forEach((key) => {\n      urlSearchParams.set(key, relayParams[key]);\n    });\n\n  urlSearchParams.set(\"symKey\", params.symKey);\n  if (params.expiryTimestamp)\n    urlSearchParams.set(\"expiryTimestamp\", params.expiryTimestamp.toString());\n\n  if (params.methods) {\n    urlSearchParams.set(\"methods\", params.methods.join(\",\"));\n  }\n\n  const queryString = urlSearchParams.toString();\n  return `${params.protocol}:${params.topic}@${params.version}?${queryString}`;\n}\n\nexport function getLinkModeURL(\n  universalLink: string,\n  topic: string,\n  encodedEnvelope: string,\n): string {\n  return `${universalLink}?wc_ev=${encodedEnvelope}&topic=${topic}`;\n}\n", "import { ProposalTypes, SessionTypes } from \"@walletconnect/types\";\nimport { mergeArrays } from \"./misc\";\nimport { isConformingNamespaces, isValidNamespaces, isValidObject } from \"./validators\";\n\nexport function getAccountsChains(accounts: SessionTypes.Namespace[\"accounts\"]) {\n  const chains: string[] = [];\n  accounts.forEach((account) => {\n    const [chain, chainId] = account.split(\":\");\n    chains.push(`${chain}:${chainId}`);\n  });\n\n  return chains;\n}\n\nexport function getNamespacesChains(namespaces: SessionTypes.Namespaces) {\n  const chains: string[] = [];\n  Object.values(namespaces).forEach((namespace) => {\n    chains.push(...getAccountsChains(namespace.accounts));\n  });\n\n  return chains;\n}\n\nexport function getNamespacesMethodsForChainId(\n  namespaces: SessionTypes.Namespaces,\n  chainId: string,\n) {\n  const methods: SessionTypes.Namespace[\"methods\"] = [];\n  Object.values(namespaces).forEach((namespace) => {\n    const chains = getAccountsChains(namespace.accounts);\n    if (chains.includes(chainId)) methods.push(...namespace.methods);\n  });\n\n  return methods;\n}\n\nexport function getNamespacesEventsForChainId(\n  namespaces: SessionTypes.Namespaces,\n  chainId: string,\n) {\n  const events: SessionTypes.Namespace[\"events\"] = [];\n  Object.values(namespaces).forEach((namespace) => {\n    const chains = getAccountsChains(namespace.accounts);\n    if (chains.includes(chainId)) events.push(...namespace.events);\n  });\n\n  return events;\n}\n\nexport function getRequiredNamespacesFromNamespaces(\n  namespaces: SessionTypes.Namespaces,\n  caller: string,\n): ProposalTypes.RequiredNamespaces {\n  const validNamespacesError = isValidNamespaces(namespaces, caller);\n  if (validNamespacesError) throw new Error(validNamespacesError.message);\n\n  const required: ProposalTypes.RequiredNamespaces = {};\n  for (const [namespace, values] of Object.entries(namespaces)) {\n    required[namespace] = {\n      methods: values.methods,\n      events: values.events,\n      chains: values.accounts.map((account) => `${account.split(\":\")[0]}:${account.split(\":\")[1]}`),\n    };\n  }\n  return required;\n}\n\nexport type BuildApprovedNamespacesParams = {\n  proposal: ProposalTypes.Struct;\n  supportedNamespaces: Record<\n    string,\n    { chains: string[]; methods: string[]; events: string[]; accounts: string[] }\n  >;\n};\n\n/**\n * util designed for Wallets that builds namespaces structure by provided supported chains, methods, events & accounts.\n * It takes required & optional namespaces provided in the session proposal\n * along with the supported chains/methods/events/accounts by the wallet and returns a structured namespaces object\n * @param {BuildApprovedNamespacesParams} params\n * @returns {SessionTypes.Namespaces}\n */\nexport function buildApprovedNamespaces(\n  params: BuildApprovedNamespacesParams,\n): SessionTypes.Namespaces {\n  const {\n    proposal: { requiredNamespaces, optionalNamespaces = {} },\n    supportedNamespaces,\n  } = params;\n  const normalizedRequired = normalizeNamespaces(requiredNamespaces);\n  const normalizedOptional = normalizeNamespaces(optionalNamespaces);\n\n  // build approved namespaces\n  const namespaces: SessionTypes.Namespaces = {};\n  Object.keys(supportedNamespaces).forEach((namespace) => {\n    const supportedChains = supportedNamespaces[namespace].chains;\n    const supportedMethods = supportedNamespaces[namespace].methods;\n    const supportedEvents = supportedNamespaces[namespace].events;\n    const supportedAccounts = supportedNamespaces[namespace].accounts;\n\n    supportedChains.forEach((chain) => {\n      if (!supportedAccounts.some((account) => account.includes(chain))) {\n        throw new Error(`No accounts provided for chain ${chain} in namespace ${namespace}`);\n      }\n    });\n\n    namespaces[namespace] = {\n      chains: supportedChains,\n      methods: supportedMethods,\n      events: supportedEvents,\n      accounts: supportedAccounts,\n    };\n  });\n\n  // verify all required namespaces are supported\n  const err = isConformingNamespaces(requiredNamespaces, namespaces, \"approve()\");\n  if (err) throw new Error(err.message);\n\n  const approvedNamespaces: SessionTypes.Namespaces = {};\n\n  // if both required & optional namespaces are empty, return all supported namespaces by the wallet\n  if (!Object.keys(requiredNamespaces).length && !Object.keys(optionalNamespaces).length)\n    return namespaces;\n\n  // assign accounts for the required namespaces\n  Object.keys(normalizedRequired).forEach((requiredNamespace) => {\n    const chains = supportedNamespaces[requiredNamespace].chains.filter((chain) =>\n      normalizedRequired[requiredNamespace]?.chains?.includes(chain),\n    );\n    const methods = supportedNamespaces[requiredNamespace].methods.filter((method) =>\n      normalizedRequired[requiredNamespace]?.methods?.includes(method),\n    );\n    const events = supportedNamespaces[requiredNamespace].events.filter((event) =>\n      normalizedRequired[requiredNamespace]?.events?.includes(event),\n    );\n\n    const accounts = chains\n      .map((chain: string) =>\n        supportedNamespaces[requiredNamespace].accounts.filter((account: string) =>\n          account.includes(`${chain}:`),\n        ),\n      )\n      .flat();\n\n    approvedNamespaces[requiredNamespace] = {\n      chains,\n      methods,\n      events,\n      accounts,\n    };\n  });\n\n  // add optional namespaces\n  Object.keys(normalizedOptional).forEach((optionalNamespace) => {\n    if (!supportedNamespaces[optionalNamespace]) return;\n\n    const chainsToAdd = normalizedOptional[optionalNamespace]?.chains?.filter((chain) =>\n      supportedNamespaces[optionalNamespace].chains.includes(chain),\n    );\n    const methodsToAdd = supportedNamespaces[optionalNamespace].methods.filter((method) =>\n      normalizedOptional[optionalNamespace]?.methods?.includes(method),\n    );\n    const eventsToAdd = supportedNamespaces[optionalNamespace].events.filter((event) =>\n      normalizedOptional[optionalNamespace]?.events?.includes(event),\n    );\n\n    const accountsToAdd = chainsToAdd\n      ?.map((chain: string) =>\n        supportedNamespaces[optionalNamespace].accounts.filter((account: string) =>\n          account.includes(`${chain}:`),\n        ),\n      )\n      .flat();\n\n    approvedNamespaces[optionalNamespace] = {\n      chains: mergeArrays(approvedNamespaces[optionalNamespace]?.chains, chainsToAdd),\n      methods: mergeArrays(approvedNamespaces[optionalNamespace]?.methods, methodsToAdd),\n      events: mergeArrays(approvedNamespaces[optionalNamespace]?.events, eventsToAdd),\n      accounts: mergeArrays(approvedNamespaces[optionalNamespace]?.accounts, accountsToAdd),\n    };\n  });\n\n  return approvedNamespaces;\n}\n\nexport function isCaipNamespace(namespace: string): boolean {\n  return namespace.includes(\":\");\n}\n\nexport function parseNamespaceKey(namespace: string) {\n  return isCaipNamespace(namespace) ? namespace.split(\":\")[0] : namespace;\n}\n\n/**\n * Converts\n * ```\n * {\n *  \"eip155:1\": {...},\n *  \"eip155:2\": {...},\n * }\n * ```\n * into\n * ```\n * {\n *  \"eip155\": {\n *      chains: [\"eip155:1\", \"eip155:2\"],\n *      ...\n *    }\n * }\n *```\n */\nexport function normalizeNamespaces(\n  namespaces: ProposalTypes.RequiredNamespaces,\n): ProposalTypes.RequiredNamespaces {\n  const normalizedNamespaces = {} as ProposalTypes.RequiredNamespaces;\n  if (!isValidObject(namespaces)) return normalizedNamespaces;\n  for (const [key, values] of Object.entries(namespaces)) {\n    const chains = isCaipNamespace(key) ? [key] : values.chains;\n    const methods = values.methods || [];\n    const events = values.events || [];\n    const normalizedKey = parseNamespaceKey(key);\n    normalizedNamespaces[normalizedKey] = {\n      ...normalizedNamespaces[normalizedKey],\n      chains: mergeArrays(chains, normalizedNamespaces[normalizedKey]?.chains),\n      methods: mergeArrays(methods, normalizedNamespaces[normalizedKey]?.methods),\n      events: mergeArrays(events, normalizedNamespaces[normalizedKey]?.events),\n    };\n  }\n  return normalizedNamespaces;\n}\n\nexport function getNamespacesFromAccounts(accounts: string[]) {\n  const namespaces: SessionTypes.Namespaces = {};\n  accounts?.forEach((account) => {\n    const [namespace, chainId] = account.split(\":\");\n    if (!namespaces[namespace]) {\n      namespaces[namespace] = {\n        accounts: [],\n        chains: [],\n        events: [],\n        methods: [],\n      };\n    }\n    namespaces[namespace].accounts.push(account);\n    namespaces[namespace].chains?.push(`${namespace}:${chainId}`);\n  });\n\n  return namespaces;\n}\n\nexport function buildNamespacesFromAuth(methods: string[], accounts: string[]) {\n  accounts = accounts.map((account) => account.replace(\"did:pkh:\", \"\"));\n\n  const namespaces = getNamespacesFromAccounts(accounts);\n\n  for (const [_, values] of Object.entries(namespaces) as [string, SessionTypes.Namespace][]) {\n    if (!values.methods) {\n      values.methods = methods;\n    } else {\n      values.methods = mergeArrays(values.methods, methods);\n    }\n    values.events = [\"chainChanged\", \"accountsChanged\"];\n  }\n  return namespaces;\n}\n\nexport function mergeRequiredAndOptionalNamespaces(\n  requiredNamespaces: ProposalTypes.RequiredNamespaces,\n  optionalNamespaces: ProposalTypes.OptionalNamespaces,\n) {\n  const normalizedRequired = normalizeNamespaces(requiredNamespaces);\n  const normalizedOptional = normalizeNamespaces(optionalNamespaces);\n\n  const mergedNamespaces: ProposalTypes.OptionalNamespaces = {};\n\n  const combinedNamespaces = Object.keys(normalizedRequired).concat(\n    Object.keys(normalizedOptional),\n  );\n\n  for (const namespace of combinedNamespaces) {\n    mergedNamespaces[namespace] = {\n      chains: mergeArrays(\n        normalizedRequired[namespace]?.chains,\n        normalizedOptional[namespace]?.chains,\n      ),\n      methods: mergeArrays(\n        normalizedRequired[namespace]?.methods,\n        normalizedOptional[namespace]?.methods,\n      ),\n      events: mergeArrays(\n        normalizedRequired[namespace]?.events,\n        normalizedOptional[namespace]?.events,\n      ),\n    };\n  }\n\n  return mergedNamespaces;\n}\n", "/**\n * Types\n */\nexport type SdkErrorKey = keyof typeof SDK_ERRORS;\nexport type InternalErrorKey = keyof typeof INTERNAL_ERRORS;\n\n/**\n * Constants\n */\nexport const SDK_ERRORS = {\n  /* ----- INVALID (1xxx) ----- */\n  INVALID_METHOD: {\n    message: \"Invalid method.\",\n    code: 1001,\n  },\n  INVALID_EVENT: {\n    message: \"Invalid event.\",\n    code: 1002,\n  },\n  INVALID_UPDATE_REQUEST: {\n    message: \"Invalid update request.\",\n    code: 1003,\n  },\n  INVALID_EXTEND_REQUEST: {\n    message: \"Invalid extend request.\",\n    code: 1004,\n  },\n  INVALID_SESSION_SETTLE_REQUEST: {\n    message: \"Invalid session settle request.\",\n    code: 1005,\n  },\n  /* ----- UNAUTHORIZED (3xxx) ----- */\n  UNAUTHORIZED_METHOD: {\n    message: \"Unauthorized method.\",\n    code: 3001,\n  },\n  UNAUTHORIZED_EVENT: {\n    message: \"Unauthorized event.\",\n    code: 3002,\n  },\n  UNAUTHORIZED_UPDATE_REQUEST: {\n    message: \"Unauthorized update request.\",\n    code: 3003,\n  },\n  UNAUTHORIZED_EXTEND_REQUEST: {\n    message: \"Unauthorized extend request.\",\n    code: 3004,\n  },\n  /* ----- REJECTED (5xxx) ----- */\n  USER_REJECTED: {\n    message: \"User rejected.\",\n    code: 5000,\n  },\n  USER_REJECTED_CHAINS: {\n    message: \"User rejected chains.\",\n    code: 5001,\n  },\n  USER_REJECTED_METHODS: {\n    message: \"User rejected methods.\",\n    code: 5002,\n  },\n  USER_REJECTED_EVENTS: {\n    message: \"User rejected events.\",\n    code: 5003,\n  },\n  UNSUPPORTED_CHAINS: {\n    message: \"Unsupported chains.\",\n    code: 5100,\n  },\n  UNSUPPORTED_METHODS: {\n    message: \"Unsupported methods.\",\n    code: 5101,\n  },\n  UNSUPPORTED_EVENTS: {\n    message: \"Unsupported events.\",\n    code: 5102,\n  },\n  UNSUPPORTED_ACCOUNTS: {\n    message: \"Unsupported accounts.\",\n    code: 5103,\n  },\n  UNSUPPORTED_NAMESPACE_KEY: {\n    message: \"Unsupported namespace key.\",\n    code: 5104,\n  },\n  /* ----- REASON (6xxx) ----- */\n  USER_DISCONNECTED: {\n    message: \"User disconnected.\",\n    code: 6000,\n  },\n  /* ----- FAILURE (7xxx) ----- */\n  SESSION_SETTLEMENT_FAILED: {\n    message: \"Session settlement failed.\",\n    code: 7000,\n  },\n  /* ----- PAIRING (10xxx) ----- */\n  WC_METHOD_UNSUPPORTED: {\n    message: \"Unsupported wc_ method.\",\n    code: 10001,\n  },\n};\n\nexport const INTERNAL_ERRORS = {\n  NOT_INITIALIZED: {\n    message: \"Not initialized.\",\n    code: 1,\n  },\n  NO_MATCHING_KEY: {\n    message: \"No matching key.\",\n    code: 2,\n  },\n  RESTORE_WILL_OVERRIDE: {\n    message: \"Restore will override.\",\n    code: 3,\n  },\n  RESUBSCRIBED: {\n    message: \"Resubscribed.\",\n    code: 4,\n  },\n  MISSING_OR_INVALID: {\n    message: \"Missing or invalid.\",\n    code: 5,\n  },\n  EXPIRED: {\n    message: \"Expired.\",\n    code: 6,\n  },\n  UNKNOWN_TYPE: {\n    message: \"Unknown type.\",\n    code: 7,\n  },\n  MISMATCHED_TOPIC: {\n    message: \"Mismatched topic.\",\n    code: 8,\n  },\n  NON_CONFORMING_NAMESPACES: {\n    message: \"Non conforming namespaces.\",\n    code: 9,\n  },\n};\n\n/**\n * Utilities\n */\nexport function getInternalError(key: InternalErrorKey, context?: string | number) {\n  const { message, code } = INTERNAL_ERRORS[key];\n  return {\n    message: context ? `${message} ${context}` : message,\n    code,\n  };\n}\n\nexport function getSdkError(key: SdkErrorKey, context?: string | number) {\n  const { message, code } = SDK_ERRORS[key];\n  return {\n    message: context ? `${message} ${context}` : message,\n    code,\n  };\n}\n", "import { SessionTypes, ProposalTypes, RelayerTypes, EngineTypes } from \"@walletconnect/types\";\nimport { ErrorResponse } from \"@walletconnect/jsonrpc-types\";\nimport {\n  getNamespacesChains,\n  getNamespacesMethodsForChainId,\n  getNamespacesEventsForChainId,\n  getAccountsChains,\n} from \"./namespaces\";\nimport { getSdkError, getInternalError } from \"./errors\";\nimport { fromBase64, hasOverlap } from \"./misc\";\nimport { getChainsFromNamespace } from \"./caip\";\n\nexport type ErrorObject = { message: string; code: number } | null;\n\n// -- types validation ----------------------------------------------------- //\n\nexport function isValidArray(arr: any, itemCondition?: (item: any) => boolean) {\n  if (Array.isArray(arr)) {\n    if (typeof itemCondition !== \"undefined\" && arr.length) {\n      return arr.every(itemCondition);\n    } else {\n      return true;\n    }\n  }\n  return false;\n}\n\nexport function isValidObject(obj: any) {\n  return Object.getPrototypeOf(obj) === Object.prototype && Object.keys(obj).length;\n}\n\nexport function isUndefined(input: any): input is undefined {\n  return typeof input === \"undefined\";\n}\n\nexport function isValidString(input: any, optional: boolean): input is string {\n  if (optional && isUndefined(input)) return true;\n\n  return typeof input === \"string\" && Boolean(input.trim().length);\n}\n\nexport function isValidNumber(input: any, optional: boolean) {\n  if (optional && isUndefined(input)) return true;\n\n  return typeof input === \"number\" && !isNaN(input);\n}\n\n// -- protocol validation -------------------------------------------------- //\n\nexport function isSessionCompatible(session: SessionTypes.Struct, params: EngineTypes.FindParams) {\n  const { requiredNamespaces } = params;\n  const sessionKeys = Object.keys(session.namespaces);\n  const paramsKeys = Object.keys(requiredNamespaces);\n  let compatible = true;\n\n  if (!hasOverlap(paramsKeys, sessionKeys)) return false;\n\n  sessionKeys.forEach((key) => {\n    const { accounts, methods, events } = session.namespaces[key];\n    const chains = getAccountsChains(accounts);\n    const requiredNamespace = requiredNamespaces[key];\n    if (\n      !hasOverlap(getChainsFromNamespace(key, requiredNamespace), chains) ||\n      !hasOverlap(requiredNamespace.methods, methods) ||\n      !hasOverlap(requiredNamespace.events, events)\n    ) {\n      compatible = false;\n    }\n  });\n\n  return compatible;\n}\n\nexport function isValidChainId(value: any) {\n  if (isValidString(value, false) && value.includes(\":\")) {\n    const split = value.split(\":\");\n    return split.length === 2;\n  }\n  return false;\n}\n\nexport function isValidAccountId(value: any) {\n  if (isValidString(value, false) && value.includes(\":\")) {\n    const split = value.split(\":\");\n    if (split.length === 3) {\n      const chainId = split[0] + \":\" + split[1];\n      return !!split[2] && isValidChainId(chainId);\n    }\n  }\n  return false;\n}\n\nexport function isValidUrl(value: any) {\n  function validateUrl(blob: string) {\n    try {\n      const url = new URL(blob);\n      return typeof url !== \"undefined\";\n    } catch (e) {\n      return false;\n    }\n  }\n  try {\n    if (isValidString(value, false)) {\n      const isValid = validateUrl(value);\n      if (isValid) return true;\n\n      const decoded = fromBase64(value);\n      return validateUrl(decoded);\n    }\n  } catch (e) {}\n  return false;\n}\n\nexport function isProposalStruct(input: any): input is ProposalTypes.Struct {\n  return input?.proposer?.publicKey;\n}\n\nexport function isSessionStruct(input: any): input is SessionTypes.Struct {\n  return input?.topic;\n}\n\nexport function isValidController(input: any, method: string) {\n  let error: ErrorObject = null;\n  if (!isValidString(input?.publicKey, false)) {\n    error = getInternalError(\n      \"MISSING_OR_INVALID\",\n      `${method} controller public key should be a string`,\n    );\n  }\n\n  return error;\n}\n\nexport function isValidNamespaceMethodsOrEvents(input: any): input is string {\n  let valid = true;\n  if (isValidArray(input)) {\n    if (input.length) {\n      valid = input.every((item: any) => isValidString(item, false));\n    }\n  } else {\n    valid = false;\n  }\n\n  return valid;\n}\n\nexport function isValidChains(key: string, chains: any, context: string) {\n  let error: ErrorObject = null;\n\n  if (isValidArray(chains) && chains.length) {\n    chains.forEach((chain: any) => {\n      if (error) return;\n      if (!isValidChainId(chain)) {\n        error = getSdkError(\n          \"UNSUPPORTED_CHAINS\",\n          `${context}, chain ${chain} should be a string and conform to \"namespace:chainId\" format`,\n        );\n      }\n    });\n  } else if (!isValidChainId(key)) {\n    error = getSdkError(\n      \"UNSUPPORTED_CHAINS\",\n      `${context}, chains must be defined as \"namespace:chainId\" e.g. \"eip155:1\": {...} in the namespace key OR as an array of CAIP-2 chainIds e.g. eip155: { chains: [\"eip155:1\", \"eip155:5\"] }`,\n    );\n  }\n\n  return error;\n}\n\nexport function isValidNamespaceChains(namespaces: any, method: string, type: string) {\n  let error: ErrorObject = null;\n  Object.entries(namespaces).forEach(([key, namespace]: [string, any]) => {\n    if (error) return;\n    const validChainsError = isValidChains(\n      key,\n      getChainsFromNamespace(key, namespace),\n      `${method} ${type}`,\n    );\n    if (validChainsError) {\n      error = validChainsError;\n    }\n  });\n\n  return error;\n}\n\nexport function isValidAccounts(accounts: any, context: string) {\n  let error: ErrorObject = null;\n  if (isValidArray(accounts)) {\n    accounts.forEach((account: any) => {\n      if (error) return;\n      if (!isValidAccountId(account)) {\n        error = getSdkError(\n          \"UNSUPPORTED_ACCOUNTS\",\n          `${context}, account ${account} should be a string and conform to \"namespace:chainId:address\" format`,\n        );\n      }\n    });\n  } else {\n    error = getSdkError(\n      \"UNSUPPORTED_ACCOUNTS\",\n      `${context}, accounts should be an array of strings conforming to \"namespace:chainId:address\" format`,\n    );\n  }\n\n  return error;\n}\n\nexport function isValidNamespaceAccounts(input: any, method: string) {\n  let error: ErrorObject = null;\n  Object.values(input).forEach((namespace: any) => {\n    if (error) return;\n    const validAccountsError = isValidAccounts(namespace?.accounts, `${method} namespace`);\n    if (validAccountsError) {\n      error = validAccountsError;\n    }\n  });\n\n  return error;\n}\n\nexport function isValidActions(namespace: any, context: string) {\n  let error: ErrorObject = null;\n  if (!isValidNamespaceMethodsOrEvents(namespace?.methods)) {\n    error = getSdkError(\n      \"UNSUPPORTED_METHODS\",\n      `${context}, methods should be an array of strings or empty array for no methods`,\n    );\n  } else if (!isValidNamespaceMethodsOrEvents(namespace?.events)) {\n    error = getSdkError(\n      \"UNSUPPORTED_EVENTS\",\n      `${context}, events should be an array of strings or empty array for no events`,\n    );\n  }\n\n  return error;\n}\n\nexport function isValidNamespaceActions(input: any, method: string) {\n  let error: ErrorObject = null;\n  Object.values(input).forEach((namespace: any) => {\n    if (error) return;\n    const validActionsError = isValidActions(namespace, `${method}, namespace`);\n    if (validActionsError) {\n      error = validActionsError;\n    }\n  });\n\n  return error;\n}\n\nexport function isValidRequiredNamespaces(input: any, method: string, type: string) {\n  let error: ErrorObject = null;\n  if (input && isValidObject(input)) {\n    const validActionsError = isValidNamespaceActions(input, method);\n    if (validActionsError) {\n      error = validActionsError;\n    }\n    const validChainsError = isValidNamespaceChains(input, method, type);\n    if (validChainsError) {\n      error = validChainsError;\n    }\n  } else {\n    error = getInternalError(\n      \"MISSING_OR_INVALID\",\n      `${method}, ${type} should be an object with data`,\n    );\n  }\n\n  return error;\n}\n\nexport function isValidNamespaces(input: any, method: string) {\n  let error: ErrorObject = null;\n  if (input && isValidObject(input)) {\n    const validActionsError = isValidNamespaceActions(input, method);\n    if (validActionsError) {\n      error = validActionsError;\n    }\n    const validAccountsError = isValidNamespaceAccounts(input, method);\n    if (validAccountsError) {\n      error = validAccountsError;\n    }\n  } else {\n    error = getInternalError(\n      \"MISSING_OR_INVALID\",\n      `${method}, namespaces should be an object with data`,\n    );\n  }\n\n  return error;\n}\n\nexport function isValidRelay(input: any): input is RelayerTypes.ProtocolOptions {\n  return isValidString(input.protocol, true);\n}\n\nexport function isValidRelays(\n  input: any,\n  optional: boolean,\n): input is RelayerTypes.ProtocolOptions[] {\n  let valid = false;\n\n  if (optional && !input) valid = true;\n  else if (input && isValidArray(input) && input.length) {\n    input.forEach((relay: RelayerTypes.ProtocolOptions) => {\n      valid = isValidRelay(relay);\n    });\n  }\n\n  return valid;\n}\n\nexport function isValidId(input: any) {\n  return typeof input === \"number\";\n}\n\nexport function isValidParams(input: any) {\n  // eslint-disable-next-line valid-typeof\n  return typeof input !== \"undefined\" && typeof input !== null;\n}\n\nexport function isValidErrorReason(input: any): input is ErrorResponse {\n  if (!input) return false;\n  if (typeof input !== \"object\") return false;\n  if (!input.code || !isValidNumber(input.code, false)) return false;\n  if (!input.message || !isValidString(input.message, false)) return false;\n\n  return true;\n}\n\nexport function isValidRequest(request: any) {\n  if (isUndefined(request)) return false;\n  if (!isValidString(request.method, false)) return false;\n  return true;\n}\n\nexport function isValidResponse(response: any) {\n  if (isUndefined(response)) return false;\n  if (isUndefined(response.result) && isUndefined(response.error)) return false;\n  if (!isValidNumber(response.id, false)) return false;\n  if (!isValidString(response.jsonrpc, false)) return false;\n  return true;\n}\n\nexport function isValidEvent(event: any) {\n  if (isUndefined(event)) return false;\n  if (!isValidString(event.name, false)) return false;\n  return true;\n}\n\nexport function isValidNamespacesChainId(namespaces: SessionTypes.Namespaces, chainId: string) {\n  if (!isValidChainId(chainId)) return false;\n  const chains = getNamespacesChains(namespaces);\n  if (!chains.includes(chainId)) return false;\n\n  return true;\n}\n\nexport function isValidNamespacesRequest(\n  namespaces: SessionTypes.Namespaces,\n  chainId: string,\n  method: string,\n) {\n  if (!isValidString(method, false)) return false;\n  const methods = getNamespacesMethodsForChainId(namespaces, chainId);\n  return methods.includes(method);\n}\n\nexport function isValidNamespacesEvent(\n  namespaces: SessionTypes.Namespaces,\n  chainId: string,\n  eventName: string,\n) {\n  if (!isValidString(eventName, false)) return false;\n  const events = getNamespacesEventsForChainId(namespaces, chainId);\n  return events.includes(eventName);\n}\n\nexport function isConformingNamespaces(\n  requiredNamespaces: ProposalTypes.RequiredNamespaces,\n  namespaces: SessionTypes.Namespaces,\n  context: string,\n) {\n  let error: ErrorObject = null;\n\n  const parsedRequired = parseNamespaces(requiredNamespaces);\n  const parsedApproved = parseApprovedNamespaces(namespaces);\n  const requiredChains = Object.keys(parsedRequired);\n  const approvedChains = Object.keys(parsedApproved);\n\n  const uniqueRequired = filterDuplicateNamespaces(Object.keys(requiredNamespaces));\n  const uniqueApproved = filterDuplicateNamespaces(Object.keys(namespaces));\n  const missingRequiredNamespaces = uniqueRequired.filter(\n    (namespace) => !uniqueApproved.includes(namespace),\n  );\n\n  if (missingRequiredNamespaces.length) {\n    error = getInternalError(\n      \"NON_CONFORMING_NAMESPACES\",\n      `${context} namespaces keys don't satisfy requiredNamespaces.\n      Required: ${missingRequiredNamespaces.toString()}\n      Received: ${Object.keys(namespaces).toString()}`,\n    );\n  }\n\n  if (!hasOverlap(requiredChains, approvedChains)) {\n    error = getInternalError(\n      \"NON_CONFORMING_NAMESPACES\",\n      `${context} namespaces chains don't satisfy required namespaces.\n      Required: ${requiredChains.toString()}\n      Approved: ${approvedChains.toString()}`,\n    );\n  }\n\n  // validate inline defined chains with approved accounts\n  Object.keys(namespaces).forEach((chain) => {\n    if (!chain.includes(\":\")) return;\n    if (error) return;\n    const chains = getAccountsChains(namespaces[chain].accounts);\n    if (!chains.includes(chain)) {\n      error = getInternalError(\n        \"NON_CONFORMING_NAMESPACES\",\n        `${context} namespaces accounts don't satisfy namespace accounts for ${chain}\n        Required: ${chain}\n        Approved: ${chains.toString()}`,\n      );\n    }\n  });\n\n  requiredChains.forEach((chain) => {\n    if (error) return;\n\n    if (!hasOverlap(parsedRequired[chain].methods, parsedApproved[chain].methods)) {\n      error = getInternalError(\n        \"NON_CONFORMING_NAMESPACES\",\n        `${context} namespaces methods don't satisfy namespace methods for ${chain}`,\n      );\n    } else if (!hasOverlap(parsedRequired[chain].events, parsedApproved[chain].events)) {\n      error = getInternalError(\n        \"NON_CONFORMING_NAMESPACES\",\n        `${context} namespaces events don't satisfy namespace events for ${chain}`,\n      );\n    }\n  });\n\n  return error;\n}\n\nfunction parseNamespaces(namespaces: ProposalTypes.RequiredNamespaces) {\n  const parsed: ProposalTypes.RequiredNamespaces = {};\n  Object.keys(namespaces).forEach((key) => {\n    // e.g. `eip155:1`\n    const isInlineChainDefinition = key.includes(\":\");\n\n    if (isInlineChainDefinition) {\n      parsed[key] = namespaces[key];\n    } else {\n      namespaces[key].chains?.forEach((chain) => {\n        parsed[chain] = {\n          methods: namespaces[key].methods,\n          events: namespaces[key].events,\n        };\n      });\n    }\n  });\n  return parsed;\n}\n\nfunction filterDuplicateNamespaces(namespaces: string[]) {\n  return [\n    ...new Set(\n      namespaces.map((namespace) =>\n        namespace.includes(\":\") ? namespace.split(\":\")[0] : namespace,\n      ),\n    ),\n  ];\n}\n\nfunction parseApprovedNamespaces(namespaces: SessionTypes.Namespaces) {\n  const parsed: SessionTypes.Namespaces = {};\n  Object.keys(namespaces).forEach((key) => {\n    const isInlineChainDefinition = key.includes(\":\");\n    if (isInlineChainDefinition) {\n      parsed[key] = namespaces[key];\n    } else {\n      const chains = getAccountsChains(namespaces[key].accounts);\n      chains?.forEach((chain) => {\n        parsed[chain] = {\n          accounts: namespaces[key].accounts.filter((account: string) =>\n            account.includes(`${chain}:`),\n          ),\n          methods: namespaces[key].methods,\n          events: namespaces[key].events,\n        };\n      });\n    }\n  });\n  return parsed;\n}\n\nexport function isValidRequestExpiry(expiry: number, boundaries: { min: number; max: number }) {\n  return isValidNumber(expiry, false) && expiry <= boundaries.max && expiry >= boundaries.min;\n}\n", "import { getDocument } from \"@walletconnect/window-getters\";\nimport { getEnvironment, ENV_MAP, isBrowser, isReactNative } from \"./misc\";\n\nexport function isOnline(): Promise<boolean> {\n  const env = getEnvironment();\n  return new Promise((resolve) => {\n    switch (env) {\n      case ENV_MAP.browser:\n        resolve(getBrowserOnlineStatus());\n        break;\n      case ENV_MAP.reactNative:\n        resolve(getReactNativeOnlineStatus());\n        break;\n      case ENV_MAP.node:\n        resolve(getNodeOnlineStatus());\n        break;\n      default:\n        resolve(true);\n    }\n  });\n}\n\nexport function getBrowserOnlineStatus() {\n  return isBrowser() && navigator?.onLine;\n}\n\nexport async function getReactNativeOnlineStatus(): Promise<boolean> {\n  // global.NetInfo is set in react-native-compat\n  if (isReactNative() && typeof global !== \"undefined\" && (global as any)?.NetInfo) {\n    const state = await (global as any)?.NetInfo.fetch();\n    return state?.isConnected;\n  }\n  // fallback to true if global.NetInfo is undefined, meaning an older version of react-native-compat is used\n  return true;\n}\n\nexport function getNodeOnlineStatus() {\n  /**\n   * TODO: need to implement\n   */\n  return true;\n}\n\nexport function subscribeToNetworkChange(callbackHandler: (connected: boolean) => void) {\n  const env = getEnvironment();\n  switch (env) {\n    case ENV_MAP.browser:\n      subscribeToBrowserNetworkChange(callbackHandler);\n      break;\n    case ENV_MAP.reactNative:\n      subscribeToReactNativeNetworkChange(callbackHandler);\n      break;\n    case ENV_MAP.node:\n      // wip: need to implement\n      break;\n    default:\n      break;\n  }\n}\n\nexport function subscribeToBrowserNetworkChange(callbackHandler: (connected: boolean) => void) {\n  if (!isReactNative() && isBrowser()) {\n    window.addEventListener(\"online\", () => callbackHandler(true));\n    window.addEventListener(\"offline\", () => callbackHandler(false));\n  }\n}\n\n// global.NetInfo is set in react-native-compat\nexport function subscribeToReactNativeNetworkChange(callbackHandler: (connected: boolean) => void) {\n  if (isReactNative() && typeof global !== \"undefined\" && (global as any)?.NetInfo) {\n    (global as any)?.NetInfo.addEventListener((state: any) => callbackHandler(state?.isConnected));\n  }\n}\n\nexport function isAppVisible(): boolean {\n  if (isBrowser() && getDocument()) {\n    return getDocument()?.visibilityState === \"visible\";\n  }\n  // TODO: implement reliable visibility check for react-native\n  // node.js does not have a visibilityState\n  return true;\n}\n", "const memoryStore: Record<string, any> = {};\n\nexport abstract class MemoryStore {\n  static get<T = unknown>(key: string) {\n    return memoryStore[key] as T | undefined;\n  }\n\n  static set(key: string, value: unknown) {\n    memoryStore[key] = value;\n  }\n\n  static delete(key: string) {\n    delete memoryStore[key];\n  }\n}\n"], "names": ["CAIP_DELIMITER", "chain", "namespace", "reference", "params", "account", "address", "array", "parser", "unique", "str", "value", "accounts", "namespaces", "keys", "key", "ns", "chains", "requiredNamespaces", "namespaceProps", "getDocument", "getNavigator", "_a", "e", "queryString", "newQueryParams", "urlSearchParams", "metadata", "_b", "appMetadata", "icon", "__spreadProps", "__spreadValues", "error", "getWindowMetadata", "protocol", "version", "env", "getLocation", "OS", "Version", "info", "detect", "os", "sdkVersion", "id", "relayUrl", "auth", "projectId", "useOnCloseEvent", "bundleId", "packageName", "splitUrl", "ua", "url", "domain", "obj", "type", "context", "depth", "a", "b", "x", "arr", "map", "cb", "res", "word", "c", "w", "expiry", "FIVE_MINUTES", "expireErrorMessage", "timeout", "toMiliseconds", "cacheResolve", "cacheReject", "cacheTimeout", "result", "promiseResolve", "promiseReject", "err", "promise", "resolve", "reject", "topic", "target", "parsed", "ttl", "now", "fromMiliseconds", "event", "wcDeepLink", "json", "deeplink", "link", "requestId", "sessionTopic", "payload", "startApp", "storage", "arr1", "arr2", "param", "index", "r", "input", "removePadding", "encoded", "encodedString", "ms", "anumber", "isBytes", "abytes", "aexists", "aoutput", "crypto", "u32", "createView", "isLE", "utf8ToBytes", "toBytes", "concatBytes", "_0n", "_1n", "_2n", "DEFAULT_RPC_URL", "message", "prefix", "prefixedMessage", "keccak_256", "reconstructedMessage", "cacaoSignature", "chainId", "baseRpcUrl", "signature", "recoverAddress", "parsed<PERSON><PERSON><PERSON>", "parseChainId", "eip1271MagicValue", "dynamicTypeOffset", "dynamicTypeLength", "nonPrefixedSignature", "nonPrefixedHashedMessage", "data", "response", "generateJsonRpcId", "solanaTransaction", "binary", "bytes", "i", "signatureCount", "signatureEndPos", "signature<PERSON><PERSON>er", "bs58", "didPrefix", "iss", "segments", "cacao", "reconstructed", "wallet<PERSON>ddress", "verifySignature", "header", "statement", "uri", "nonce", "issuedAt", "expirationTime", "notBefore", "resources", "resource", "recap", "decoded", "val", "requestPayload", "authPayload", "methods", "requested", "<PERSON><PERSON><PERSON><PERSON>", "getCommonValuesInArrays", "requestedRecaps", "updatedResources", "actions", "supportedActions", "formattedActions", "updatedRecap", "action", "abilities", "ability", "resourceAbilities", "limits", "limit", "baseRecap", "recap1", "recap2", "decoded1", "decoded2", "merged", "mergedRecap", "base", "statementForRecap", "currentCounter", "uniqueAbilities", "recapStatemet", "recapStatement", "abool", "equalBytes", "isAligned32", "setBigUint64", "_3n", "_5n", "validateOpts", "ut.validateObject", "ut.numberToHexUnpadded", "ut.abytes", "ut.concatBytes", "ut.inRange", "ut.isBytes", "ut.bytesToHex", "ut.bytesToNumberBE", "ut.aInRange", "ut.numberToBytesBE", "ut.hexToBytes", "ut.bitMask", "ut.createHmacDrbg", "ZERO_INDEX", "TYPE_LENGTH", "IV_LENGTH", "KEY_LENGTH", "privateKey", "x25519", "public<PERSON>ey", "toString", "random", "randomBytes", "privateKeyA", "publicKeyB", "sharedKey", "fromString", "sym<PERSON>ey", "hkdf", "sha256", "byte", "toBase64URL", "base64", "fromBase64URL", "base64url", "padding", "sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "iv", "sealed", "chacha20poly1305", "encoding", "concat", "normalizedEncoded", "slice1", "slice2", "slice3", "opts", "deserialized", "keyData", "xBuffer", "<PERSON><PERSON><PERSON><PERSON>", "token", "headerBase64Url", "payloadBase64Url", "signatureBase64Url", "s", "signingInput", "messageHash", "p256", "decodeJWT", "jsonrpc", "RELAY_JSONRPC", "delimiter", "relay", "name", "fromBase64", "pathStart", "pathEnd", "requiredValues", "queryParams", "<PERSON><PERSON><PERSON>", "k", "relayParams", "universalLink", "encodedEnvelope", "events", "caller", "validNamespacesError", "isValidNamespaces", "required", "values", "optionalNamespaces", "supportedNamespaces", "normalizedRequired", "normalizedOptional", "supportedMethods", "supportedEvents", "supportedAccounts", "isConformingNamespaces", "approvedNamespaces", "requiredNamespace", "method", "optionalNamespace", "_c", "_d", "_e", "_f", "chainsToAdd", "methodsToAdd", "eventsToAdd", "accountsToAdd", "mergeArrays", "normalizedNamespaces", "isValidObject", "normalizedKey", "_", "mergedNamespaces", "combinedNamespaces", "code", "itemCondition", "optional", "session", "<PERSON><PERSON><PERSON><PERSON>", "params<PERSON><PERSON><PERSON>", "compatible", "hasOverlap", "getAccountsChains", "getChainsFromNamespace", "split", "validateUrl", "blob", "getInternalError", "valid", "item", "getSdkError", "valid<PERSON><PERSON>nsError", "validAccountsError", "validActionsError", "request", "getNamespacesChains", "getNamespacesMethodsForChainId", "eventName", "getNamespacesEventsForChainId", "parsedRequired", "parseNamespaces", "parsedApproved", "parseApprovedNamespaces", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "uniqueRequired", "filterDuplicateNamespaces", "uniqueApproved", "missingRequiredNamespaces", "boundaries", "getEnvironment", "ENV_MAP", "<PERSON><PERSON><PERSON><PERSON>", "isReactNative", "state", "call<PERSON><PERSON><PERSON><PERSON>", "memoryStore"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAWA,MAAMA,EAAiB,GAEhB,CAAA,SAAS,aAAaC,CAA8B,CAAA,CACzD,KAAM,CAACC,CAAAA,CAAWC,CAAS,CAAA,CAAIF,EAAM,KAAMD,CAAAA,CAAc,EACzD,OAAO,CAAE,UAAAE,CAAW,CAAA,SAAA,CAAAC,CAAU,CAChC,CAEgB,SAAA,aAAA,CAAcC,EAA+B,CAC3D,KAAM,CAAE,SAAAF,CAAAA,CAAAA,CAAW,SAAAC,CAAAA,CAAU,EAAIC,CACjC,CAAA,OAAO,CAACF,CAAWC,CAAAA,CAAS,EAAE,IAAKH,CAAAA,CAAc,CACnD,UAEgB,cAAeK,CAAAA,CAAAA,CAAkC,CAC/D,KAAM,CAACH,EAAWC,CAAWG,CAAAA,CAAO,CAAID,CAAAA,CAAAA,CAAQ,MAAML,CAAc,CAAA,CACpE,OAAO,CAAE,SAAA,CAAAE,EAAW,SAAAC,CAAAA,CAAAA,CAAW,OAAAG,CAAAA,CAAQ,CACzC,CAEO,SAAS,gBAAgBF,CAAiC,CAAA,CAC/D,KAAM,CAAE,SAAA,CAAAF,EAAW,SAAAC,CAAAA,CAAAA,CAAW,QAAAG,CAAQ,CAAA,CAAIF,EAC1C,OAAO,CAACF,EAAWC,CAAWG,CAAAA,CAAO,CAAE,CAAA,IAAA,CAAKN,CAAc,CAC5D,UAEgB,eAAgBO,CAAAA,CAAAA,CAAiBC,EAA2C,CAC1F,MAAMC,CAAmB,CAAA,GACzB,OAAAF,CAAAA,CAAM,QAASG,CAAQ,EAAA,CACrB,MAAMC,CAAQH,CAAAA,CAAAA,CAAOE,CAAG,CAAA,CACnBD,EAAO,QAASE,CAAAA,CAAK,GAAGF,CAAO,CAAA,IAAA,CAAKE,CAAK,EAChD,CAAC,EACMF,CACT,UAEgB,qBAAsBJ,CAAAA,CAAAA,CAAiB,CACrD,KAAM,CAAE,QAAAC,CAAQ,CAAA,CAAI,cAAeD,CAAAA,CAAO,EAC1C,OAAOC,CACT,CAEgB,SAAA,mBAAA,CAAoBD,EAAiB,CACnD,KAAM,CAAE,SAAA,CAAAH,EAAW,SAAAC,CAAAA,CAAU,EAAI,cAAeE,CAAAA,CAAO,EAEvD,OADc,aAAA,CAAc,CAAE,SAAA,CAAAH,EAAW,SAAAC,CAAAA,CAAU,CAAC,CAEtD,UAEgB,sBAAuBG,CAAAA,CAAAA,CAAiBL,EAAe,CACrE,KAAM,CAAE,SAAAC,CAAAA,CAAAA,CAAW,UAAAC,CAAU,CAAA,CAAI,aAAaF,CAAK,CAAA,CAEnD,OADgB,eAAA,CAAgB,CAAE,SAAAC,CAAAA,CAAAA,CAAW,UAAAC,CAAW,CAAA,OAAA,CAAAG,CAAQ,CAAC,CAEnE,CAEO,SAAS,yBAAyBM,CAAoB,CAAA,CAC3D,OAAO,eAAgBA,CAAAA,CAAAA,CAAU,qBAAqB,CACxD,CAEgB,SAAA,qBAAA,CAAsBA,EAAoB,CACxD,OAAO,gBAAgBA,CAAU,CAAA,mBAAmB,CACtD,CAEO,SAAS,0BACdC,CACAC,CAAAA,CAAAA,CAAiB,EACP,CAAA,CACV,MAAMF,CAAqB,CAAA,GAC3B,OAAO,MAAA,CAAA,IAAA,CAAKC,CAAU,CAAA,CAAE,QAASE,CAAQ,EAAA,CACvC,GAAID,CAAK,CAAA,MAAA,EAAU,CAACA,CAAK,CAAA,QAAA,CAASC,CAAG,CAAA,CAAG,OACxC,MAAMC,CAAAA,CAAKH,EAAWE,CAAG,CAAA,CACzBH,EAAS,IAAK,CAAA,GAAGI,CAAG,CAAA,QAAQ,EAC9B,CAAC,CAAA,CACMJ,CACT,CAEO,SAAS,wBACdC,CACAC,CAAAA,CAAAA,CAAiB,EACP,CAAA,CACV,MAAMG,CAAmB,CAAA,GACzB,OAAO,MAAA,CAAA,IAAA,CAAKJ,CAAU,CAAE,CAAA,OAAA,CAASE,CAAQ,EAAA,CACvC,GAAID,CAAK,CAAA,MAAA,EAAU,CAACA,CAAK,CAAA,QAAA,CAASC,CAAG,CAAG,CAAA,OACxC,MAAMC,CAAAA,CAAKH,EAAWE,CAAG,CAAA,CACzBE,EAAO,IAAK,CAAA,GAAG,sBAAsBD,CAAG,CAAA,QAAQ,CAAC,EACnD,CAAC,CACMC,CAAAA,CACT,CAEgB,SAAA,+BAAA,CACdC,EACAJ,CAAiB,CAAA,GACP,CACV,MAAMG,EAAmB,EAAC,CAC1B,cAAO,IAAKC,CAAAA,CAAkB,EAAE,OAASH,CAAAA,CAAAA,EAAQ,CAC/C,GAAID,EAAK,MAAU,EAAA,CAACA,EAAK,QAASC,CAAAA,CAAG,EAAG,OACxC,MAAMC,CAAKE,CAAAA,CAAAA,CAAmBH,CAAG,CACjCE,CAAAA,CAAAA,CAAO,KAAK,GAAG,sBAAA,CAAuBF,EAAKC,CAAE,CAAC,EAChD,CAAC,EACMC,CACT,UAEgB,sBACdf,CAAAA,CAAAA,CACAiB,EACA,CAEA,OAAOjB,EAAU,QAAS,CAAA,GAAG,EAAI,CAACA,CAAS,EAAIiB,CAAe,CAAA,MAAA,EAAU,EAC1E", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34], "debugId": null}}]}