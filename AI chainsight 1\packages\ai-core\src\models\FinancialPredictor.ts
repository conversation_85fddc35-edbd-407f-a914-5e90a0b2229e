import * as tf from '@tensorflow/tfjs-node';
import { LSTMModel } from './LSTMModel';
import { TransformerModel } from './TransformerModel';
import { DataProcessor } from '../utils/DataProcessor';
import { ModelEvaluator } from '../evaluation/ModelEvaluator';
import {
  MarketData,
  PredictionResult,
  ModelConfig,
  PredictionOptions,
  ModelMetrics,
  ModelState
} from '../types';
import { DEFAULT_MODEL_CONFIG } from '../constants';

export class FinancialPredictor {
  private lstmModel: LSTMModel;
  private transformerModel: TransformerModel;
  private dataProcessor: DataProcessor;
  private evaluator: ModelEvaluator;
  private modelStates: Map<string, ModelState>;

  constructor() {
    this.lstmModel = new LSTMModel();
    this.transformerModel = new TransformerModel();
    this.dataProcessor = new DataProcessor();
    this.evaluator = new ModelEvaluator();
    this.modelStates = new Map();
  }

  async initialize(): Promise<void> {
    console.log('Initializing Financial Predictor...');
    
    // Initialize TensorFlow backend
    await tf.ready();
    
    // Load pre-trained models if available
    await this.loadPretrainedModels();
    
    console.log('Financial Predictor initialized successfully');
  }

  async predict(
    symbol: string,
    marketData: MarketData[],
    options: PredictionOptions = {}
  ): Promise<PredictionResult> {
    try {
      console.log(`Generating prediction for ${symbol}`);

      // Validate input data
      if (!marketData || marketData.length < 60) {
        throw new Error('Insufficient market data for prediction');
      }

      // Process market data
      const processedData = await this.dataProcessor.processMarketData(
        marketData,
        options.include_technical_indicators ?? true
      );

      // Select model based on options
      const modelType = options.model_type || 'ensemble';
      
      let predictions: any[];
      let confidence: number;
      let modelUsed: string;

      switch (modelType) {
        case 'lstm':
          const lstmResult = await this.lstmModel.predict(processedData);
          predictions = lstmResult.predictions;
          confidence = lstmResult.confidence;
          modelUsed = 'LSTM';
          break;

        case 'transformer':
          const transformerResult = await this.transformerModel.predict(processedData);
          predictions = transformerResult.predictions;
          confidence = transformerResult.confidence;
          modelUsed = 'Transformer';
          break;

        case 'ensemble':
        default:
          const ensembleResult = await this.ensemblePredict(processedData);
          predictions = ensembleResult.predictions;
          confidence = ensembleResult.confidence;
          modelUsed = 'LSTM-Transformer Ensemble';
          break;
      }

      // Add confidence intervals
      const confidenceInterval = options.confidence_interval || 0.95;
      const enhancedPredictions = this.addConfidenceIntervals(
        predictions,
        confidence,
        confidenceInterval
      );

      return {
        symbol,
        predictions: enhancedPredictions,
        model_used: modelUsed,
        confidence,
        metadata: {
          training_data_points: processedData.features.length,
          features_used: processedData.feature_names || [],
          prediction_horizon: predictions.length,
          model_version: '1.0.0'
        }
      };

    } catch (error) {
      console.error('Prediction failed:', error);
      throw new Error(`Prediction failed: ${error.message}`);
    }
  }

  async trainModel(
    symbol: string,
    marketData: MarketData[],
    config: Partial<ModelConfig> = {}
  ): Promise<ModelMetrics> {
    try {
      console.log(`Training model for ${symbol}`);

      // Merge with default config
      const modelConfig: ModelConfig = {
        ...DEFAULT_MODEL_CONFIG.lstm,
        ...config
      };

      // Process training data
      const trainingData = await this.dataProcessor.prepareTrainingData(
        marketData,
        modelConfig
      );

      // Split data for training and validation
      const { trainData, valData } = this.dataProcessor.splitData(
        trainingData,
        0.8
      );

      let metrics: ModelMetrics;

      // Train based on model type
      switch (modelConfig.model_type) {
        case 'lstm':
          await this.lstmModel.train(trainData, valData, modelConfig);
          metrics = await this.evaluator.evaluate(this.lstmModel, valData);
          break;

        case 'transformer':
          await this.transformerModel.train(trainData, valData, modelConfig);
          metrics = await this.evaluator.evaluate(this.transformerModel, valData);
          break;

        default:
          // Train both models for ensemble
          await this.lstmModel.train(trainData, valData, {
            ...modelConfig,
            model_type: 'lstm'
          });
          await this.transformerModel.train(trainData, valData, {
            ...modelConfig,
            model_type: 'transformer'
          });
          
          // Evaluate ensemble
          metrics = await this.evaluateEnsemble(valData);
          break;
      }

      // Save model state
      this.modelStates.set(symbol, {
        model_id: `${symbol}_${Date.now()}`,
        model_type: modelConfig.model_type,
        is_trained: true,
        training_date: new Date(),
        performance_metrics: metrics,
        config: modelConfig
      });

      console.log(`Model training completed for ${symbol}`);
      return metrics;

    } catch (error) {
      console.error('Model training failed:', error);
      throw new Error(`Training failed: ${error.message}`);
    }
  }

  async saveModel(symbol: string, path: string): Promise<void> {
    try {
      const modelState = this.modelStates.get(symbol);
      if (!modelState) {
        throw new Error(`No trained model found for ${symbol}`);
      }

      // Save LSTM model
      await this.lstmModel.save(`${path}/lstm_${symbol}`);
      
      // Save Transformer model
      await this.transformerModel.save(`${path}/transformer_${symbol}`);

      // Save model metadata
      const metadata = {
        ...modelState,
        saved_at: new Date().toISOString()
      };

      // In a real implementation, save metadata to file
      console.log(`Model saved for ${symbol}:`, metadata);

    } catch (error) {
      console.error('Model saving failed:', error);
      throw new Error(`Failed to save model: ${error.message}`);
    }
  }

  async loadModel(symbol: string, path: string): Promise<void> {
    try {
      // Load LSTM model
      await this.lstmModel.load(`${path}/lstm_${symbol}`);
      
      // Load Transformer model
      await this.transformerModel.load(`${path}/transformer_${symbol}`);

      console.log(`Model loaded for ${symbol}`);

    } catch (error) {
      console.error('Model loading failed:', error);
      throw new Error(`Failed to load model: ${error.message}`);
    }
  }

  getModelState(symbol: string): ModelState | undefined {
    return this.modelStates.get(symbol);
  }

  getAllModelStates(): ModelState[] {
    return Array.from(this.modelStates.values());
  }

  private async ensemblePredict(processedData: any): Promise<{
    predictions: any[];
    confidence: number;
  }> {
    // Get predictions from both models
    const lstmResult = await this.lstmModel.predict(processedData);
    const transformerResult = await this.transformerModel.predict(processedData);

    // Combine predictions (weighted average)
    const lstmWeight = 0.6;
    const transformerWeight = 0.4;

    const ensemblePredictions = lstmResult.predictions.map((lstmPred: any, index: number) => {
      const transformerPred = transformerResult.predictions[index];
      
      return {
        timestamp: lstmPred.timestamp,
        predicted_price: (lstmPred.predicted_price * lstmWeight) + 
                        (transformerPred.predicted_price * transformerWeight),
        confidence: Math.min(lstmPred.confidence, transformerPred.confidence)
      };
    });

    const avgConfidence = (lstmResult.confidence * lstmWeight) + 
                         (transformerResult.confidence * transformerWeight);

    return {
      predictions: ensemblePredictions,
      confidence: avgConfidence
    };
  }

  private addConfidenceIntervals(
    predictions: any[],
    confidence: number,
    confidenceInterval: number
  ): any[] {
    return predictions.map(pred => {
      const margin = pred.predicted_price * (1 - confidence) * 2;
      
      return {
        ...pred,
        lower_bound: pred.predicted_price - margin,
        upper_bound: pred.predicted_price + margin
      };
    });
  }

  private async loadPretrainedModels(): Promise<void> {
    try {
      // In a real implementation, load pre-trained models from storage
      console.log('Loading pre-trained models...');
      
      // For now, just initialize with default configurations
      await this.lstmModel.initialize();
      await this.transformerModel.initialize();
      
    } catch (error) {
      console.warn('No pre-trained models found, will train from scratch');
    }
  }

  private async evaluateEnsemble(valData: any): Promise<ModelMetrics> {
    // Evaluate ensemble performance
    const lstmMetrics = await this.evaluator.evaluate(this.lstmModel, valData);
    const transformerMetrics = await this.evaluator.evaluate(this.transformerModel, valData);

    // Combine metrics (take best of both)
    return {
      mse: Math.min(lstmMetrics.mse, transformerMetrics.mse),
      mae: Math.min(lstmMetrics.mae, transformerMetrics.mae),
      rmse: Math.min(lstmMetrics.rmse, transformerMetrics.rmse),
      mape: Math.min(lstmMetrics.mape, transformerMetrics.mape),
      r2_score: Math.max(lstmMetrics.r2_score, transformerMetrics.r2_score),
      sharpe_ratio: Math.max(lstmMetrics.sharpe_ratio || 0, transformerMetrics.sharpe_ratio || 0),
      max_drawdown: Math.min(lstmMetrics.max_drawdown || 0, transformerMetrics.max_drawdown || 0),
      accuracy: Math.max(lstmMetrics.accuracy || 0, transformerMetrics.accuracy || 0)
    };
  }

  dispose(): void {
    // Clean up TensorFlow resources
    this.lstmModel.dispose();
    this.transformerModel.dispose();
    this.modelStates.clear();
  }
}
