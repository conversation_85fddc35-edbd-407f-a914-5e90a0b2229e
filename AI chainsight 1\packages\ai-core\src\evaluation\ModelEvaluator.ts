import { ModelMetrics, TrainingData } from '../types';
import { LSTMModel } from '../models/LSTMModel';
import { TransformerModel } from '../models/TransformerModel';

export class ModelEvaluator {

  async evaluate(
    model: LSTMModel | TransformerModel,
    testData: TrainingData
  ): Promise<ModelMetrics> {
    try {
      console.log('Evaluating model performance...');

      // Get model predictions
      const predictions = await this.getPredictions(model, testData);
      const actual = testData.targets.flat();

      // Calculate regression metrics
      const mse = this.calculateMSE(actual, predictions);
      const mae = this.calculateMAE(actual, predictions);
      const rmse = Math.sqrt(mse);
      const mape = this.calculateMAPE(actual, predictions);
      const r2Score = this.calculateR2Score(actual, predictions);

      // Calculate financial metrics
      const sharpeRatio = this.calculateSharpeRatio(actual, predictions);
      const maxDrawdown = this.calculateMaxDrawdown(predictions);
      const accuracy = this.calculateDirectionalAccuracy(actual, predictions);

      const metrics: ModelMetrics = {
        mse,
        mae,
        rmse,
        mape,
        r2_score: r2Score,
        sharpe_ratio: sharpeRatio,
        max_drawdown: maxDrawdown,
        accuracy
      };

      console.log('Model evaluation completed:', metrics);
      return metrics;

    } catch (error) {
      console.error('Model evaluation failed:', error);
      throw new Error(`Evaluation failed: ${error.message}`);
    }
  }

  async compareModels(
    models: Array<{ name: string; model: LSTMModel | TransformerModel }>,
    testData: TrainingData
  ): Promise<Array<{ name: string; metrics: ModelMetrics; rank: number }>> {
    const results = [];

    // Evaluate each model
    for (const { name, model } of models) {
      try {
        const metrics = await this.evaluate(model, testData);
        results.push({ name, metrics, rank: 0 });
      } catch (error) {
        console.error(`Failed to evaluate model ${name}:`, error);
      }
    }

    // Rank models based on composite score
    results.forEach(result => {
      result.rank = this.calculateCompositeScore(result.metrics);
    });

    // Sort by rank (higher is better)
    results.sort((a, b) => b.rank - a.rank);

    // Assign final ranks
    results.forEach((result, index) => {
      result.rank = index + 1;
    });

    return results;
  }

  async backtestModel(
    model: LSTMModel | TransformerModel,
    historicalData: TrainingData,
    windowSize: number = 252 // Trading days in a year
  ): Promise<{
    metrics: ModelMetrics;
    dailyReturns: number[];
    cumulativeReturns: number[];
    drawdowns: number[];
  }> {
    console.log('Starting model backtest...');

    const predictions: number[] = [];
    const actual: number[] = [];
    const dailyReturns: number[] = [];

    // Rolling window backtest
    for (let i = windowSize; i < historicalData.features.length; i++) {
      try {
        // Use data up to current point for prediction
        const trainWindow = {
          features: historicalData.features.slice(i - windowSize, i),
          targets: historicalData.targets.slice(i - windowSize, i),
          timestamps: historicalData.timestamps.slice(i - windowSize, i),
          symbols: historicalData.symbols.slice(i - windowSize, i)
        };

        // Get prediction for next period
        const prediction = await this.getSinglePrediction(model, trainWindow);
        const actualValue = historicalData.targets[i][0]; // First target value

        predictions.push(prediction);
        actual.push(actualValue);

        // Calculate daily return
        if (i > windowSize) {
          const prevActual = actual[actual.length - 2];
          const dailyReturn = (actualValue - prevActual) / prevActual;
          dailyReturns.push(dailyReturn);
        }

      } catch (error) {
        console.warn(`Backtest failed at step ${i}:`, error);
      }
    }

    // Calculate cumulative returns and drawdowns
    const cumulativeReturns = this.calculateCumulativeReturns(dailyReturns);
    const drawdowns = this.calculateDrawdownSeries(cumulativeReturns);

    // Calculate overall metrics
    const metrics = await this.evaluate(model, {
      features: [],
      targets: actual.map(val => [val]),
      timestamps: [],
      symbols: []
    });

    console.log('Backtest completed');
    return {
      metrics,
      dailyReturns,
      cumulativeReturns,
      drawdowns
    };
  }

  private async getPredictions(
    model: LSTMModel | TransformerModel,
    testData: TrainingData
  ): Promise<number[]> {
    const predictions: number[] = [];

    for (let i = 0; i < testData.features.length; i++) {
      try {
        const processedData = {
          features: testData.features[i]
        };

        const result = await model.predict(processedData);
        
        // Extract first prediction value
        if (result.predictions.length > 0) {
          predictions.push(result.predictions[0].predicted_price);
        }

      } catch (error) {
        console.warn(`Prediction failed for sample ${i}:`, error);
        predictions.push(0); // Default value for failed predictions
      }
    }

    return predictions;
  }

  private async getSinglePrediction(
    model: LSTMModel | TransformerModel,
    data: TrainingData
  ): Promise<number> {
    const processedData = {
      features: data.features[data.features.length - 1]
    };

    const result = await model.predict(processedData);
    return result.predictions[0]?.predicted_price || 0;
  }

  private calculateMSE(actual: number[], predicted: number[]): number {
    if (actual.length !== predicted.length) {
      throw new Error('Arrays must have the same length');
    }

    const sumSquaredErrors = actual.reduce((sum, actualVal, i) => {
      const error = actualVal - predicted[i];
      return sum + (error * error);
    }, 0);

    return sumSquaredErrors / actual.length;
  }

  private calculateMAE(actual: number[], predicted: number[]): number {
    if (actual.length !== predicted.length) {
      throw new Error('Arrays must have the same length');
    }

    const sumAbsoluteErrors = actual.reduce((sum, actualVal, i) => {
      return sum + Math.abs(actualVal - predicted[i]);
    }, 0);

    return sumAbsoluteErrors / actual.length;
  }

  private calculateMAPE(actual: number[], predicted: number[]): number {
    if (actual.length !== predicted.length) {
      throw new Error('Arrays must have the same length');
    }

    const sumPercentageErrors = actual.reduce((sum, actualVal, i) => {
      if (actualVal === 0) return sum; // Avoid division by zero
      const percentageError = Math.abs((actualVal - predicted[i]) / actualVal);
      return sum + percentageError;
    }, 0);

    return (sumPercentageErrors / actual.length) * 100;
  }

  private calculateR2Score(actual: number[], predicted: number[]): number {
    const actualMean = actual.reduce((sum, val) => sum + val, 0) / actual.length;
    
    const totalSumSquares = actual.reduce((sum, val) => {
      return sum + Math.pow(val - actualMean, 2);
    }, 0);

    const residualSumSquares = actual.reduce((sum, actualVal, i) => {
      return sum + Math.pow(actualVal - predicted[i], 2);
    }, 0);

    return 1 - (residualSumSquares / totalSumSquares);
  }

  private calculateSharpeRatio(actual: number[], predicted: number[]): number {
    // Calculate returns based on predictions
    const returns: number[] = [];
    
    for (let i = 1; i < predicted.length; i++) {
      const returnRate = (predicted[i] - predicted[i - 1]) / predicted[i - 1];
      returns.push(returnRate);
    }

    if (returns.length === 0) return 0;

    const meanReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - meanReturn, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);

    // Assume risk-free rate of 2% annually (0.02/252 daily)
    const riskFreeRate = 0.02 / 252;

    return stdDev === 0 ? 0 : (meanReturn - riskFreeRate) / stdDev;
  }

  private calculateMaxDrawdown(prices: number[]): number {
    let maxDrawdown = 0;
    let peak = prices[0];

    for (let i = 1; i < prices.length; i++) {
      if (prices[i] > peak) {
        peak = prices[i];
      }

      const drawdown = (peak - prices[i]) / peak;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }

    return maxDrawdown;
  }

  private calculateDirectionalAccuracy(actual: number[], predicted: number[]): number {
    if (actual.length < 2 || predicted.length < 2) return 0;

    let correctDirections = 0;
    let totalDirections = 0;

    for (let i = 1; i < Math.min(actual.length, predicted.length); i++) {
      const actualDirection = actual[i] > actual[i - 1];
      const predictedDirection = predicted[i] > predicted[i - 1];

      if (actualDirection === predictedDirection) {
        correctDirections++;
      }
      totalDirections++;
    }

    return totalDirections === 0 ? 0 : correctDirections / totalDirections;
  }

  private calculateCompositeScore(metrics: ModelMetrics): number {
    // Weighted composite score (higher is better)
    const weights = {
      r2_score: 0.3,
      mape: -0.2, // Negative because lower is better
      sharpe_ratio: 0.25,
      max_drawdown: -0.15, // Negative because lower is better
      accuracy: 0.2
    };

    let score = 0;
    score += (metrics.r2_score || 0) * weights.r2_score;
    score += (100 - (metrics.mape || 100)) / 100 * Math.abs(weights.mape);
    score += (metrics.sharpe_ratio || 0) * weights.sharpe_ratio;
    score += (1 - Math.abs(metrics.max_drawdown || 0)) * Math.abs(weights.max_drawdown);
    score += (metrics.accuracy || 0) * weights.accuracy;

    return score;
  }

  private calculateCumulativeReturns(dailyReturns: number[]): number[] {
    const cumulative: number[] = [1]; // Start with 1 (100%)

    for (const dailyReturn of dailyReturns) {
      const newValue = cumulative[cumulative.length - 1] * (1 + dailyReturn);
      cumulative.push(newValue);
    }

    return cumulative;
  }

  private calculateDrawdownSeries(cumulativeReturns: number[]): number[] {
    const drawdowns: number[] = [];
    let peak = cumulativeReturns[0];

    for (const value of cumulativeReturns) {
      if (value > peak) {
        peak = value;
      }

      const drawdown = (peak - value) / peak;
      drawdowns.push(drawdown);
    }

    return drawdowns;
  }
}
