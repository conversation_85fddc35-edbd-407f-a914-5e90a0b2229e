import { <PERSON>Dete<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>annerConfig, RecognitionConfig } from '../types';
import { FaceDetector } from '../detection/FaceDetector';
import { FaceRecognizer } from '../recognition/FaceRecognizer';
import { FacialAnalyzer } from '../analysis/FacialAnalyzer';
import { LivenessDetector } from '../liveness/LivenessDetector';
import { FaceDatabase } from '../database/FaceDatabase';
import { v4 as uuidv4 } from 'uuid';

export class FaceScanner {
  private detector: FaceDetector;
  private recognizer: FaceRecognizer;
  private analyzer: FacialAnalyzer;
  private livenessDetector: LivenessDetector;
  private database: FaceDatabase;
  private config: ScannerConfig;
  private isInitialized: boolean = false;

  constructor(config: ScannerConfig) {
    this.config = config;
    this.detector = new FaceDetector(config.detection_model);
    this.recognizer = new FaceRecognizer(config.recognition_model);
    this.analyzer = new FacialAnalyzer({
      emotion_model: config.emotion_model,
      age_gender_model: config.age_gender_model
    });
    this.livenessDetector = new LivenessDetector();
    this.database = new FaceDatabase();
  }

  async initialize(): Promise<void> {
    try {
      console.log('Initializing Face Scanner...');

      // Initialize all components
      await Promise.all([
        this.detector.initialize(),
        this.recognizer.initialize(),
        this.analyzer.initialize(),
        this.livenessDetector.initialize(),
        this.database.initialize()
      ]);

      this.isInitialized = true;
      console.log('Face Scanner initialized successfully');

    } catch (error) {
      console.error('Failed to initialize Face Scanner:', error);
      throw new Error(`Initialization failed: ${error.message}`);
    }
  }

  async scanImage(
    imageData: Buffer | string,
    options: {
      enable_recognition?: boolean;
      enable_analysis?: boolean;
      enable_liveness?: boolean;
      recognition_config?: RecognitionConfig;
    } = {}
  ): Promise<ScanResult> {
    if (!this.isInitialized) {
      throw new Error('Face Scanner not initialized. Call initialize() first.');
    }

    const startTime = Date.now();
    const scanId = uuidv4();

    try {
      console.log(`Starting face scan: ${scanId}`);

      // Step 1: Detect faces
      const detections = await this.detector.detectFaces(imageData);
      
      if (detections.length === 0) {
        return this.createEmptyResult(scanId, startTime, imageData);
      }

      console.log(`Detected ${detections.length} face(s)`);

      // Step 2: Recognition (if enabled)
      let recognitions: FaceMatch[] = [];
      if (options.enable_recognition !== false) {
        recognitions = await this.performRecognition(
          imageData,
          detections,
          options.recognition_config
        );
      }

      // Step 3: Facial analysis (if enabled)
      let analysis = null;
      if (options.enable_analysis !== false) {
        analysis = await this.analyzer.analyzeAll(imageData, detections[0]);
      }

      // Step 4: Liveness detection (if enabled)
      if (options.enable_liveness && analysis) {
        const livenessResult = await this.livenessDetector.checkLiveness(
          imageData,
          detections[0]
        );
        analysis.liveness = livenessResult;
      }

      // Step 5: Quality assessment
      const qualityAssessment = await this.assessQuality(imageData, detections[0]);

      const processingTime = Date.now() - startTime;

      const result: ScanResult = {
        scan_id: scanId,
        detections,
        recognitions,
        analysis,
        quality_assessment: qualityAssessment,
        processing_time: processingTime,
        timestamp: new Date(),
        metadata: {
          image_width: 0, // Would be extracted from image
          image_height: 0,
          image_format: 'unknown',
          model_versions: {
            detection: this.config.detection_model,
            recognition: this.config.recognition_model,
            emotion: this.config.emotion_model,
            age_gender: this.config.age_gender_model
          },
          processing_pipeline: this.getProcessingPipeline(options)
        }
      };

      console.log(`Face scan completed: ${scanId} (${processingTime}ms)`);
      return result;

    } catch (error) {
      console.error(`Face scan failed: ${scanId}`, error);
      throw new Error(`Scan failed: ${error.message}`);
    }
  }

  async enrollPerson(
    personId: string,
    images: (Buffer | string)[],
    metadata?: any
  ): Promise<boolean> {
    try {
      console.log(`Enrolling person: ${personId} with ${images.length} images`);

      const embeddings = [];
      let totalQuality = 0;

      for (const imageData of images) {
        // Detect face
        const detections = await this.detector.detectFaces(imageData);
        
        if (detections.length === 0) {
          console.warn('No face detected in enrollment image, skipping');
          continue;
        }

        if (detections.length > 1) {
          console.warn('Multiple faces detected in enrollment image, using first');
        }

        const detection = detections[0];

        // Check quality
        const quality = await this.assessQuality(imageData, detection);
        if (quality.overall_quality < 0.7) {
          console.warn('Low quality image in enrollment, skipping');
          continue;
        }

        // Extract embedding
        const embedding = await this.recognizer.extractEmbedding(imageData, detection);
        embeddings.push(embedding);
        totalQuality += quality.overall_quality;
      }

      if (embeddings.length < 2) {
        throw new Error('Insufficient quality images for enrollment (minimum 2 required)');
      }

      // Create biometric template
      const template = {
        id: uuidv4(),
        person_id: personId,
        embeddings,
        created_at: new Date(),
        updated_at: new Date(),
        quality_threshold: totalQuality / embeddings.length,
        enrollment_images: embeddings.length
      };

      // Store in database
      const success = await this.database.storePerson({
        id: personId,
        biometric_template: template,
        enrollment_date: new Date(),
        metadata: metadata || {}
      });

      if (success) {
        console.log(`Person enrolled successfully: ${personId}`);
      }

      return success;

    } catch (error) {
      console.error(`Enrollment failed for person ${personId}:`, error);
      return false;
    }
  }

  async recognizePerson(
    imageData: Buffer | string,
    config?: RecognitionConfig
  ): Promise<FaceMatch[]> {
    try {
      const detections = await this.detector.detectFaces(imageData);
      
      if (detections.length === 0) {
        return [];
      }

      return await this.performRecognition(imageData, detections, config);

    } catch (error) {
      console.error('Recognition failed:', error);
      return [];
    }
  }

  async compareFaces(
    image1: Buffer | string,
    image2: Buffer | string
  ): Promise<{
    similarity_score: number;
    is_same_person: boolean;
    confidence: number;
  }> {
    try {
      // Detect faces in both images
      const [detections1, detections2] = await Promise.all([
        this.detector.detectFaces(image1),
        this.detector.detectFaces(image2)
      ]);

      if (detections1.length === 0 || detections2.length === 0) {
        throw new Error('No face detected in one or both images');
      }

      // Extract embeddings
      const [embedding1, embedding2] = await Promise.all([
        this.recognizer.extractEmbedding(image1, detections1[0]),
        this.recognizer.extractEmbedding(image2, detections2[0])
      ]);

      // Compare embeddings
      const similarity = await this.recognizer.compareEmbeddings(
        embedding1.vector,
        embedding2.vector
      );

      const isSamePerson = similarity.distance < 0.6; // Threshold
      const confidence = Math.max(0, 1 - similarity.distance);

      return {
        similarity_score: similarity.similarity,
        is_same_person: isSamePerson,
        confidence
      };

    } catch (error) {
      console.error('Face comparison failed:', error);
      throw new Error(`Comparison failed: ${error.message}`);
    }
  }

  async deletePerson(personId: string): Promise<boolean> {
    try {
      return await this.database.deletePerson(personId);
    } catch (error) {
      console.error(`Failed to delete person ${personId}:`, error);
      return false;
    }
  }

  async listPersons(): Promise<any[]> {
    try {
      return await this.database.listPersons();
    } catch (error) {
      console.error('Failed to list persons:', error);
      return [];
    }
  }

  async getPersonInfo(personId: string): Promise<any | null> {
    try {
      return await this.database.getPerson(personId);
    } catch (error) {
      console.error(`Failed to get person info for ${personId}:`, error);
      return null;
    }
  }

  private async performRecognition(
    imageData: Buffer | string,
    detections: FaceDetection[],
    config?: RecognitionConfig
  ): Promise<FaceMatch[]> {
    const matches: FaceMatch[] = [];

    for (const detection of detections) {
      try {
        // Extract embedding
        const embedding = await this.recognizer.extractEmbedding(imageData, detection);
        
        // Search in database
        const searchResults = await this.database.searchByEmbedding(
          embedding.vector,
          config?.confidence_threshold || 0.8,
          config?.max_distance_threshold || 0.6
        );

        matches.push(...searchResults);

      } catch (error) {
        console.warn('Recognition failed for detection:', error);
      }
    }

    return matches;
  }

  private async assessQuality(
    imageData: Buffer | string,
    detection: FaceDetection
  ): Promise<any> {
    // Simplified quality assessment
    const faceSize = detection.bounding_box.width * detection.bounding_box.height;
    const imageSizeEstimate = 640 * 480; // Assume standard size
    const faceSizeRatio = faceSize / imageSizeEstimate;

    const poseQuality = this.calculatePoseQuality(detection.pose);
    const sharpnessScore = detection.quality_score; // Use detection quality as proxy

    const overallQuality = (
      faceSizeRatio * 0.3 +
      poseQuality * 0.3 +
      sharpnessScore * 0.4
    );

    return {
      overall_quality: Math.min(1, overallQuality),
      sharpness: sharpnessScore,
      brightness: 0.8, // Would be calculated from image
      contrast: 0.8,
      face_size: faceSizeRatio,
      pose_quality: poseQuality,
      occlusion_score: 0.9, // Would be calculated
      is_suitable_for_recognition: overallQuality > 0.7
    };
  }

  private calculatePoseQuality(pose: any): number {
    const maxAngle = 30; // degrees
    const yawPenalty = Math.abs(pose.yaw) / maxAngle;
    const pitchPenalty = Math.abs(pose.pitch) / maxAngle;
    const rollPenalty = Math.abs(pose.roll) / maxAngle;

    const totalPenalty = (yawPenalty + pitchPenalty + rollPenalty) / 3;
    return Math.max(0, 1 - totalPenalty);
  }

  private createEmptyResult(
    scanId: string,
    startTime: number,
    imageData: Buffer | string
  ): ScanResult {
    return {
      scan_id: scanId,
      detections: [],
      recognitions: [],
      analysis: null,
      quality_assessment: {
        overall_quality: 0,
        sharpness: 0,
        brightness: 0,
        contrast: 0,
        face_size: 0,
        pose_quality: 0,
        occlusion_score: 0,
        is_suitable_for_recognition: false
      },
      processing_time: Date.now() - startTime,
      timestamp: new Date(),
      metadata: {
        image_width: 0,
        image_height: 0,
        image_format: 'unknown',
        model_versions: {},
        processing_pipeline: []
      }
    };
  }

  private getProcessingPipeline(options: any): string[] {
    const pipeline = ['detection'];
    
    if (options.enable_recognition !== false) {
      pipeline.push('recognition');
    }
    
    if (options.enable_analysis !== false) {
      pipeline.push('emotion_analysis', 'demographic_analysis');
    }
    
    if (options.enable_liveness) {
      pipeline.push('liveness_detection');
    }
    
    pipeline.push('quality_assessment');
    
    return pipeline;
  }

  async dispose(): Promise<void> {
    try {
      await Promise.all([
        this.detector.dispose(),
        this.recognizer.dispose(),
        this.analyzer.dispose(),
        this.livenessDetector.dispose(),
        this.database.dispose()
      ]);

      this.isInitialized = false;
      console.log('Face Scanner disposed');

    } catch (error) {
      console.error('Error disposing Face Scanner:', error);
    }
  }

  getStatus(): {
    initialized: boolean;
    models_loaded: Record<string, boolean>;
    database_connected: boolean;
  } {
    return {
      initialized: this.isInitialized,
      models_loaded: {
        detection: this.detector.isLoaded(),
        recognition: this.recognizer.isLoaded(),
        emotion: this.analyzer.isEmotionModelLoaded(),
        age_gender: this.analyzer.isAgeGenderModelLoaded()
      },
      database_connected: this.database.isConnected()
    };
  }
}
