{"name": "@chainsight/chatbot-engine", "version": "1.0.0", "description": "Advanced AI chatbot engine with OpenAI integration", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"openai": "^4.0.0", "axios": "^1.5.0", "lodash": "^4.17.21", "uuid": "^9.0.0", "moment": "^2.29.4", "node-cache": "^5.1.2", "rate-limiter-flexible": "^2.4.2"}, "devDependencies": {"@types/lodash": "^4.14.195", "@types/node": "^20.5.0", "@types/uuid": "^9.0.2", "typescript": "^5.1.6", "jest": "^29.6.2", "@types/jest": "^29.5.3", "ts-jest": "^29.1.1", "eslint": "^8.46.0", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1"}, "keywords": ["chatbot", "ai", "openai", "conversation", "nlp", "assistant"], "author": "Chainsight Team", "license": "MIT"}