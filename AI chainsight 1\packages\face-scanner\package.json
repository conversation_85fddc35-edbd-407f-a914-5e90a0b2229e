{"name": "@chainsight/face-scanner", "version": "1.0.0", "description": "Advanced face recognition and analysis engine using MediaPipe and OpenCV", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"@mediapipe/face_detection": "^0.4.1646425229", "@mediapipe/face_mesh": "^0.4.1633559619", "@mediapipe/face_landmark_detection": "^0.1.1646425229", "opencv4nodejs": "^5.6.0", "canvas": "^2.11.2", "sharp": "^0.32.5", "uuid": "^9.0.0", "lodash": "^4.17.21", "ml-matrix": "^6.10.4", "face-api.js": "^0.22.2"}, "devDependencies": {"@types/lodash": "^4.14.195", "@types/node": "^20.5.0", "@types/uuid": "^9.0.2", "typescript": "^5.1.6", "jest": "^29.6.2", "@types/jest": "^29.5.3", "ts-jest": "^29.1.1", "eslint": "^8.46.0", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1"}, "keywords": ["face-recognition", "computer-vision", "mediapipe", "opencv", "biometrics", "facial-analysis"], "author": "Chainsight Team", "license": "MIT"}