import { FinancialPredictor } from '../models/FinancialPredictor';
import { MarketData, ModelConfig, ModelMetrics, TrainingProgress } from '../types';
import { DEFAULT_MODEL_CONFIG } from '../constants';

export class ModelTrainer {
  private predictor: FinancialPredictor;
  private trainingProgress: Map<string, TrainingProgress>;

  constructor() {
    this.predictor = new FinancialPredictor();
    this.trainingProgress = new Map();
  }

  async initialize(): Promise<void> {
    await this.predictor.initialize();
  }

  async trainModel(
    symbol: string,
    marketData: MarketData[],
    config: Partial<ModelConfig> = {},
    onProgress?: (progress: TrainingProgress) => void
  ): Promise<ModelMetrics> {
    try {
      console.log(`Starting training for ${symbol}`);

      // Validate input data
      this.validateTrainingData(marketData);

      // Merge with default configuration
      const fullConfig: ModelConfig = {
        ...DEFAULT_MODEL_CONFIG.lstm,
        ...config
      };

      // Initialize progress tracking
      const progressId = `${symbol}_${Date.now()}`;
      this.trainingProgress.set(progressId, {
        epoch: 0,
        total_epochs: fullConfig.hyperparameters.epochs,
        loss: 0,
        val_loss: 0,
        metrics: {},
        estimated_time_remaining: 0
      });

      // Set up progress callback
      const progressCallback = (progress: TrainingProgress) => {
        this.trainingProgress.set(progressId, progress);
        if (onProgress) {
          onProgress(progress);
        }
      };

      // Train the model
      const metrics = await this.predictor.trainModel(symbol, marketData, fullConfig);

      // Clean up progress tracking
      this.trainingProgress.delete(progressId);

      console.log(`Training completed for ${symbol}:`, metrics);
      return metrics;

    } catch (error) {
      console.error(`Training failed for ${symbol}:`, error);
      throw new Error(`Training failed: ${error.message}`);
    }
  }

  async trainMultipleModels(
    symbols: string[],
    marketDataMap: Map<string, MarketData[]>,
    config: Partial<ModelConfig> = {}
  ): Promise<Map<string, ModelMetrics>> {
    const results = new Map<string, ModelMetrics>();

    for (const symbol of symbols) {
      try {
        const marketData = marketDataMap.get(symbol);
        if (!marketData) {
          console.warn(`No market data found for ${symbol}`);
          continue;
        }

        const metrics = await this.trainModel(symbol, marketData, config);
        results.set(symbol, metrics);

        console.log(`Completed training for ${symbol}`);

      } catch (error) {
        console.error(`Failed to train model for ${symbol}:`, error);
        // Continue with other symbols
      }
    }

    return results;
  }

  async hyperparameterTuning(
    symbol: string,
    marketData: MarketData[],
    parameterGrid: Partial<ModelConfig>[]
  ): Promise<{ bestConfig: ModelConfig; bestMetrics: ModelMetrics }> {
    console.log(`Starting hyperparameter tuning for ${symbol}`);

    let bestConfig: ModelConfig | null = null;
    let bestMetrics: ModelMetrics | null = null;
    let bestScore = -Infinity;

    for (let i = 0; i < parameterGrid.length; i++) {
      const config = parameterGrid[i];
      console.log(`Testing configuration ${i + 1}/${parameterGrid.length}`);

      try {
        const metrics = await this.trainModel(symbol, marketData, config);
        
        // Calculate composite score (higher is better)
        const score = this.calculateCompositeScore(metrics);

        if (score > bestScore) {
          bestScore = score;
          bestConfig = { ...DEFAULT_MODEL_CONFIG.lstm, ...config };
          bestMetrics = metrics;
        }

        console.log(`Configuration ${i + 1} score: ${score.toFixed(4)}`);

      } catch (error) {
        console.error(`Configuration ${i + 1} failed:`, error);
      }
    }

    if (!bestConfig || !bestMetrics) {
      throw new Error('Hyperparameter tuning failed - no valid configuration found');
    }

    console.log(`Best configuration found with score: ${bestScore.toFixed(4)}`);
    return { bestConfig, bestMetrics };
  }

  async crossValidation(
    symbol: string,
    marketData: MarketData[],
    config: Partial<ModelConfig> = {},
    folds: number = 5
  ): Promise<{
    meanMetrics: ModelMetrics;
    stdMetrics: ModelMetrics;
    foldResults: ModelMetrics[];
  }> {
    console.log(`Starting ${folds}-fold cross-validation for ${symbol}`);

    const foldSize = Math.floor(marketData.length / folds);
    const foldResults: ModelMetrics[] = [];

    for (let fold = 0; fold < folds; fold++) {
      console.log(`Training fold ${fold + 1}/${folds}`);

      // Create train/validation split for this fold
      const valStart = fold * foldSize;
      const valEnd = valStart + foldSize;

      const trainData = [
        ...marketData.slice(0, valStart),
        ...marketData.slice(valEnd)
      ];

      try {
        const metrics = await this.trainModel(`${symbol}_fold_${fold}`, trainData, config);
        foldResults.push(metrics);

      } catch (error) {
        console.error(`Fold ${fold + 1} failed:`, error);
        // Use default metrics for failed folds
        foldResults.push(this.getDefaultMetrics());
      }
    }

    // Calculate mean and standard deviation
    const meanMetrics = this.calculateMeanMetrics(foldResults);
    const stdMetrics = this.calculateStdMetrics(foldResults, meanMetrics);

    console.log(`Cross-validation completed for ${symbol}`);
    return { meanMetrics, stdMetrics, foldResults };
  }

  getTrainingProgress(progressId: string): TrainingProgress | undefined {
    return this.trainingProgress.get(progressId);
  }

  getAllTrainingProgress(): Map<string, TrainingProgress> {
    return new Map(this.trainingProgress);
  }

  async saveModel(symbol: string, path: string): Promise<void> {
    await this.predictor.saveModel(symbol, path);
  }

  async loadModel(symbol: string, path: string): Promise<void> {
    await this.predictor.loadModel(symbol, path);
  }

  private validateTrainingData(marketData: MarketData[]): void {
    if (!marketData || marketData.length === 0) {
      throw new Error('Market data is empty');
    }

    if (marketData.length < 100) {
      throw new Error('Insufficient market data for training (minimum 100 data points required)');
    }

    // Check for required fields
    const requiredFields = ['timestamp', 'open', 'high', 'low', 'close', 'volume'];
    const firstDataPoint = marketData[0];

    for (const field of requiredFields) {
      if (!(field in firstDataPoint) || firstDataPoint[field as keyof MarketData] == null) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Check for data consistency
    for (let i = 0; i < marketData.length; i++) {
      const data = marketData[i];
      
      if (data.high < data.low) {
        throw new Error(`Invalid data at index ${i}: high < low`);
      }
      
      if (data.open < 0 || data.close < 0 || data.volume < 0) {
        throw new Error(`Invalid data at index ${i}: negative values`);
      }
    }
  }

  private calculateCompositeScore(metrics: ModelMetrics): number {
    // Weighted composite score (higher is better)
    const weights = {
      r2_score: 0.4,
      mape: -0.3, // Negative because lower MAPE is better
      sharpe_ratio: 0.2,
      max_drawdown: -0.1 // Negative because lower drawdown is better
    };

    let score = 0;
    score += (metrics.r2_score || 0) * weights.r2_score;
    score += (100 - (metrics.mape || 100)) / 100 * Math.abs(weights.mape); // Normalize MAPE
    score += (metrics.sharpe_ratio || 0) * weights.sharpe_ratio;
    score += (1 - Math.abs(metrics.max_drawdown || 0)) * Math.abs(weights.max_drawdown);

    return score;
  }

  private calculateMeanMetrics(foldResults: ModelMetrics[]): ModelMetrics {
    const sum = foldResults.reduce((acc, metrics) => ({
      mse: acc.mse + metrics.mse,
      mae: acc.mae + metrics.mae,
      rmse: acc.rmse + metrics.rmse,
      mape: acc.mape + metrics.mape,
      r2_score: acc.r2_score + metrics.r2_score,
      sharpe_ratio: acc.sharpe_ratio + (metrics.sharpe_ratio || 0),
      max_drawdown: acc.max_drawdown + (metrics.max_drawdown || 0),
      accuracy: acc.accuracy + (metrics.accuracy || 0)
    }), this.getDefaultMetrics());

    const count = foldResults.length;
    return {
      mse: sum.mse / count,
      mae: sum.mae / count,
      rmse: sum.rmse / count,
      mape: sum.mape / count,
      r2_score: sum.r2_score / count,
      sharpe_ratio: sum.sharpe_ratio / count,
      max_drawdown: sum.max_drawdown / count,
      accuracy: sum.accuracy / count
    };
  }

  private calculateStdMetrics(foldResults: ModelMetrics[], meanMetrics: ModelMetrics): ModelMetrics {
    const variance = foldResults.reduce((acc, metrics) => ({
      mse: acc.mse + Math.pow(metrics.mse - meanMetrics.mse, 2),
      mae: acc.mae + Math.pow(metrics.mae - meanMetrics.mae, 2),
      rmse: acc.rmse + Math.pow(metrics.rmse - meanMetrics.rmse, 2),
      mape: acc.mape + Math.pow(metrics.mape - meanMetrics.mape, 2),
      r2_score: acc.r2_score + Math.pow(metrics.r2_score - meanMetrics.r2_score, 2),
      sharpe_ratio: acc.sharpe_ratio + Math.pow((metrics.sharpe_ratio || 0) - meanMetrics.sharpe_ratio!, 2),
      max_drawdown: acc.max_drawdown + Math.pow((metrics.max_drawdown || 0) - meanMetrics.max_drawdown!, 2),
      accuracy: acc.accuracy + Math.pow((metrics.accuracy || 0) - meanMetrics.accuracy!, 2)
    }), this.getDefaultMetrics());

    const count = foldResults.length;
    return {
      mse: Math.sqrt(variance.mse / count),
      mae: Math.sqrt(variance.mae / count),
      rmse: Math.sqrt(variance.rmse / count),
      mape: Math.sqrt(variance.mape / count),
      r2_score: Math.sqrt(variance.r2_score / count),
      sharpe_ratio: Math.sqrt(variance.sharpe_ratio / count),
      max_drawdown: Math.sqrt(variance.max_drawdown / count),
      accuracy: Math.sqrt(variance.accuracy / count)
    };
  }

  private getDefaultMetrics(): ModelMetrics {
    return {
      mse: 0,
      mae: 0,
      rmse: 0,
      mape: 0,
      r2_score: 0,
      sharpe_ratio: 0,
      max_drawdown: 0,
      accuracy: 0
    };
  }

  dispose(): void {
    this.predictor.dispose();
    this.trainingProgress.clear();
  }
}
