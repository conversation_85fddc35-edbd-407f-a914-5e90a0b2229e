export { AnimationDesigner } from './core/AnimationDesigner';
export { SVGGenerator } from './generators/SVGGenerator';
export { LottieGenerator } from './generators/LottieGenerator';
export { AnimationExporter } from './export/AnimationExporter';
export { MotionEngine } from './motion/MotionEngine';
export { ShapeLibrary } from './shapes/ShapeLibrary';
export { ColorPalette } from './colors/ColorPalette';
export { EasingLibrary } from './easing/EasingLibrary';
export { TemplateManager } from './templates/TemplateManager';

// Types
export type {
  Animation,
  AnimationFrame,
  Shape,
  Motion,
  Easing,
  ColorScheme,
  ExportOptions,
  Template,
  AnimationConfig
} from './types';

// Constants
export {
  ANIMATION_TYPES,
  SHAPE_TYPES,
  MOTION_TYPES,
  EASING_TYPES,
  EXPORT_FORMATS
} from './constants';
