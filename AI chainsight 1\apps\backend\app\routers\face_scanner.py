from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
import logging
import os

from ..core.database import get_db, FaceScanResult
from ..core.security import get_current_user_optional
from ..services.face_service import FaceService

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize face service
face_service = FaceService()

# Pydantic models
class FaceScanResponse(BaseModel):
    faces_detected: int
    confidence_scores: List[float]
    landmarks: List[Dict[str, Any]]
    emotions: List[Dict[str, Any]]
    file_path: str
    processing_time: float

class FaceComparisonRequest(BaseModel):
    image1_path: str
    image2_path: str

class FaceComparisonResponse(BaseModel):
    similarity_score: float
    is_match: bool
    confidence: float

@router.post("/scan", response_model=FaceScanResponse)
async def scan_faces(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Scan faces in uploaded image"""
    try:
        logger.info(f"Scanning faces in uploaded file: {file.filename}")
        
        # Validate file type
        if not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Save uploaded file
        upload_dir = "uploads/faces"
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Perform face scanning
        scan_result = await face_service.scan_faces(file_path)
        
        # Save result to database
        face_scan = FaceScanResult(
            file_path=file_path,
            faces_detected=scan_result["faces_detected"],
            confidence_scores=str(scan_result["confidence_scores"]),
            landmarks=str(scan_result["landmarks"]),
            emotions=str(scan_result["emotions"]),
            user_id=current_user.id if current_user else None
        )
        
        db.add(face_scan)
        db.commit()
        
        logger.info(f"Face scan completed: {scan_result['faces_detected']} faces detected")
        
        return FaceScanResponse(
            faces_detected=scan_result["faces_detected"],
            confidence_scores=scan_result["confidence_scores"],
            landmarks=scan_result["landmarks"],
            emotions=scan_result["emotions"],
            file_path=file_path,
            processing_time=scan_result["processing_time"]
        )
        
    except Exception as e:
        logger.error(f"Face scanning failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/compare", response_model=FaceComparisonResponse)
async def compare_faces(
    request: FaceComparisonRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Compare two faces for similarity"""
    try:
        logger.info(f"Comparing faces: {request.image1_path} vs {request.image2_path}")
        
        # Validate file paths
        if not os.path.exists(request.image1_path):
            raise HTTPException(status_code=404, detail="First image not found")
        
        if not os.path.exists(request.image2_path):
            raise HTTPException(status_code=404, detail="Second image not found")
        
        # Perform face comparison
        comparison_result = await face_service.compare_faces(
            request.image1_path,
            request.image2_path
        )
        
        return FaceComparisonResponse(
            similarity_score=comparison_result["similarity_score"],
            is_match=comparison_result["is_match"],
            confidence=comparison_result["confidence"]
        )
        
    except Exception as e:
        logger.error(f"Face comparison failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/detect-emotions")
async def detect_emotions(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Detect emotions in faces"""
    try:
        logger.info(f"Detecting emotions in uploaded file: {file.filename}")
        
        # Validate file type
        if not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Save uploaded file
        upload_dir = "uploads/emotions"
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Detect emotions
        emotion_result = await face_service.detect_emotions(file_path)
        
        return {
            "faces": emotion_result["faces"],
            "emotions": emotion_result["emotions"],
            "dominant_emotion": emotion_result["dominant_emotion"],
            "confidence": emotion_result["confidence"],
            "file_path": file_path
        }
        
    except Exception as e:
        logger.error(f"Emotion detection failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/extract-features")
async def extract_features(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Extract facial features and landmarks"""
    try:
        logger.info(f"Extracting features from uploaded file: {file.filename}")
        
        # Validate file type
        if not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Save uploaded file
        upload_dir = "uploads/features"
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Extract features
        features_result = await face_service.extract_features(file_path)
        
        return {
            "faces_detected": features_result["faces_detected"],
            "landmarks": features_result["landmarks"],
            "face_encodings": features_result["face_encodings"],
            "bounding_boxes": features_result["bounding_boxes"],
            "file_path": file_path
        }
        
    except Exception as e:
        logger.error(f"Feature extraction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/scan-history")
async def get_scan_history(
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Get face scan history"""
    try:
        query = db.query(FaceScanResult)
        
        if current_user:
            query = query.filter(FaceScanResult.user_id == current_user.id)
        
        scans = query.order_by(FaceScanResult.created_at.desc())\
                    .offset(offset)\
                    .limit(limit)\
                    .all()
        
        return {
            "scans": [
                {
                    "id": scan.id,
                    "file_path": scan.file_path,
                    "faces_detected": scan.faces_detected,
                    "created_at": scan.created_at.isoformat()
                }
                for scan in scans
            ],
            "total": len(scans)
        }
        
    except Exception as e:
        logger.error(f"Get scan history failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/scan/{scan_id}")
async def delete_scan(
    scan_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Delete a face scan record"""
    try:
        scan = db.query(FaceScanResult).filter(FaceScanResult.id == scan_id).first()
        
        if not scan:
            raise HTTPException(status_code=404, detail="Scan not found")
        
        # Check ownership if user is authenticated
        if current_user and scan.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="Not authorized to delete this scan")
        
        # Delete file if it exists
        if os.path.exists(scan.file_path):
            os.remove(scan.file_path)
        
        # Delete database record
        db.delete(scan)
        db.commit()
        
        return {"message": "Scan deleted successfully"}
        
    except Exception as e:
        logger.error(f"Delete scan failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/supported-formats")
async def get_supported_formats():
    """Get supported image formats"""
    return {
        "supported_formats": [
            "image/jpeg",
            "image/jpg", 
            "image/png",
            "image/bmp",
            "image/tiff"
        ],
        "max_file_size": "10MB",
        "recommended_resolution": "640x480 or higher"
    }
