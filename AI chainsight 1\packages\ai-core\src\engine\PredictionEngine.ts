import { FinancialPredictor } from '../models/FinancialPredictor';
import { MarketData, PredictionResult, PredictionRequest, PredictionOptions } from '../types';

export class PredictionEngine {
  private predictor: FinancialPredictor;
  private cache: Map<string, { result: PredictionResult; timestamp: number }>;
  private cacheTimeout: number = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.predictor = new FinancialPredictor();
    this.cache = new Map();
  }

  async initialize(): Promise<void> {
    await this.predictor.initialize();
    console.log('Prediction Engine initialized');
  }

  async predict(request: PredictionRequest, marketData: MarketData[]): Promise<PredictionResult> {
    try {
      // Check cache first
      const cacheKey = this.generateCacheKey(request);
      const cached = this.cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        console.log(`Returning cached prediction for ${request.symbol}`);
        return cached.result;
      }

      // Generate new prediction
      const result = await this.predictor.predict(
        request.symbol,
        marketData,
        request.options
      );

      // Cache the result
      this.cache.set(cacheKey, {
        result,
        timestamp: Date.now()
      });

      return result;

    } catch (error) {
      console.error('Prediction failed:', error);
      throw new Error(`Prediction failed: ${error.message}`);
    }
  }

  async batchPredict(
    requests: PredictionRequest[],
    marketDataMap: Map<string, MarketData[]>
  ): Promise<Map<string, PredictionResult>> {
    const results = new Map<string, PredictionResult>();

    // Process predictions in parallel
    const promises = requests.map(async (request) => {
      try {
        const marketData = marketDataMap.get(request.symbol);
        if (!marketData) {
          throw new Error(`No market data found for ${request.symbol}`);
        }

        const result = await this.predict(request, marketData);
        return { symbol: request.symbol, result };

      } catch (error) {
        console.error(`Batch prediction failed for ${request.symbol}:`, error);
        return null;
      }
    });

    const batchResults = await Promise.all(promises);

    // Collect successful results
    batchResults.forEach(item => {
      if (item) {
        results.set(item.symbol, item.result);
      }
    });

    return results;
  }

  async predictWithConfidenceInterval(
    symbol: string,
    marketData: MarketData[],
    confidenceLevel: number = 0.95,
    options: PredictionOptions = {}
  ): Promise<PredictionResult> {
    const enhancedOptions: PredictionOptions = {
      ...options,
      confidence_interval: confidenceLevel
    };

    return this.predictor.predict(symbol, marketData, enhancedOptions);
  }

  async predictMultipleTimeframes(
    symbol: string,
    marketData: MarketData[],
    timeframes: string[]
  ): Promise<Map<string, PredictionResult>> {
    const results = new Map<string, PredictionResult>();

    for (const timeframe of timeframes) {
      try {
        const options: PredictionOptions = {
          // Adjust prediction horizon based on timeframe
          model_type: this.selectModelForTimeframe(timeframe)
        };

        const result = await this.predictor.predict(symbol, marketData, options);
        results.set(timeframe, result);

      } catch (error) {
        console.error(`Prediction failed for ${symbol} ${timeframe}:`, error);
      }
    }

    return results;
  }

  async getEnsemblePrediction(
    symbol: string,
    marketData: MarketData[],
    models: ('lstm' | 'transformer')[] = ['lstm', 'transformer']
  ): Promise<PredictionResult> {
    const predictions: PredictionResult[] = [];

    // Get predictions from each model
    for (const modelType of models) {
      try {
        const result = await this.predictor.predict(symbol, marketData, {
          model_type: modelType
        });
        predictions.push(result);

      } catch (error) {
        console.error(`${modelType} prediction failed:`, error);
      }
    }

    if (predictions.length === 0) {
      throw new Error('All model predictions failed');
    }

    // Combine predictions using weighted average
    return this.combineEnsemblePredictions(symbol, predictions);
  }

  async getRealTimePrediction(
    symbol: string,
    marketData: MarketData[],
    options: PredictionOptions = {}
  ): Promise<PredictionResult> {
    // For real-time predictions, use faster model with lower latency
    const realTimeOptions: PredictionOptions = {
      ...options,
      model_type: 'lstm' // LSTM is typically faster than transformer
    };

    return this.predictor.predict(symbol, marketData, realTimeOptions);
  }

  clearCache(): void {
    this.cache.clear();
    console.log('Prediction cache cleared');
  }

  getCacheStats(): {
    size: number;
    hitRate: number;
    oldestEntry: number;
  } {
    const now = Date.now();
    let oldestTimestamp = now;

    this.cache.forEach(entry => {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
      }
    });

    return {
      size: this.cache.size,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
      oldestEntry: now - oldestTimestamp
    };
  }

  private generateCacheKey(request: PredictionRequest): string {
    const optionsStr = JSON.stringify(request.options || {});
    return `${request.symbol}_${request.timeframe}_${request.periods}_${optionsStr}`;
  }

  private selectModelForTimeframe(timeframe: string): 'lstm' | 'transformer' {
    // Select model based on timeframe characteristics
    switch (timeframe) {
      case '1m':
      case '5m':
      case '15m':
        return 'lstm'; // LSTM for short-term predictions
      case '1h':
      case '4h':
      case '1d':
        return 'transformer'; // Transformer for medium-term
      case '1w':
      case '1M':
        return 'transformer'; // Transformer for long-term
      default:
        return 'lstm';
    }
  }

  private combineEnsemblePredictions(
    symbol: string,
    predictions: PredictionResult[]
  ): PredictionResult {
    if (predictions.length === 1) {
      return predictions[0];
    }

    // Calculate weights based on model confidence
    const totalConfidence = predictions.reduce((sum, pred) => sum + pred.confidence, 0);
    const weights = predictions.map(pred => pred.confidence / totalConfidence);

    // Combine predictions using weighted average
    const combinedPredictions = [];
    const maxLength = Math.max(...predictions.map(p => p.predictions.length));

    for (let i = 0; i < maxLength; i++) {
      let weightedPrice = 0;
      let weightedConfidence = 0;
      let weightedLowerBound = 0;
      let weightedUpperBound = 0;
      let timestamp = 0;

      predictions.forEach((pred, modelIndex) => {
        if (i < pred.predictions.length) {
          const prediction = pred.predictions[i];
          const weight = weights[modelIndex];

          weightedPrice += prediction.predicted_price * weight;
          weightedConfidence += prediction.confidence * weight;
          weightedLowerBound += prediction.lower_bound * weight;
          weightedUpperBound += prediction.upper_bound * weight;
          timestamp = prediction.timestamp; // Use timestamp from any model
        }
      });

      combinedPredictions.push({
        timestamp,
        predicted_price: weightedPrice,
        confidence: weightedConfidence,
        lower_bound: weightedLowerBound,
        upper_bound: weightedUpperBound
      });
    }

    // Calculate ensemble confidence
    const ensembleConfidence = predictions.reduce((sum, pred) => sum + pred.confidence, 0) / predictions.length;

    // Combine metadata
    const allFeatures = new Set<string>();
    let totalDataPoints = 0;

    predictions.forEach(pred => {
      pred.metadata.features_used.forEach(feature => allFeatures.add(feature));
      totalDataPoints += pred.metadata.training_data_points;
    });

    return {
      symbol,
      predictions: combinedPredictions,
      model_used: `Ensemble (${predictions.map(p => p.model_used).join(', ')})`,
      confidence: ensembleConfidence,
      metadata: {
        training_data_points: Math.floor(totalDataPoints / predictions.length),
        features_used: Array.from(allFeatures),
        prediction_horizon: combinedPredictions.length,
        model_version: 'ensemble-1.0.0'
      }
    };
  }

  dispose(): void {
    this.predictor.dispose();
    this.cache.clear();
  }
}
