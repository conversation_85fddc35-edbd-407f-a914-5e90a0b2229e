(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@rainbow-me/rainbowkit/dist/kaia-65D2U3PU.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>kaia_default)
});
"use client";
// src/components/RainbowKitProvider/chainIcons/kaia.svg
var kaia_default = "data:image/svg+xml,%3Csvg%20width%3D%22256%22%20height%3D%22256%22%20viewBox%3D%220%200%20256%20256%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Cpath%20d%3D%22M0%20128C0%2057.3076%2057.3076%200%20128%200C198.692%200%20256%2057.3076%20256%20128C256%20198.692%20198.692%20256%20128%20256C57.3076%20256%200%20198.692%200%20128Z%22%20fill%3D%22%23040404%22%2F%3E%0A%3Cg%20clip-path%3D%22url(%23clip0_544_687)%22%3E%0A%3Cpath%20d%3D%22M124.421%2089.9797C124.421%2084.302%20129.053%2079.6765%20134.74%2079.6765H153.204V54.6091H143.972H134.74C115.178%2054.6091%2099.3152%2070.4475%2099.3152%2089.9797C99.3152%2095.0766%20100.402%2099.9214%20102.345%20104.294C87.1521%20110.707%2077.0638%20124.331%2074.7146%20141.572C71.9702%20160.502%2079.5666%20180.922%2096.1097%20190.787C110.874%20200.07%20133.96%20199.457%20146.431%20186.577V195.127H172.129V100.283H134.75C129.064%20100.283%20124.421%2095.6576%20124.421%2089.9797ZM147.002%20125.361V149.168C147.002%20162.321%20136.32%20172.975%20123.158%20172.975C109.996%20172.975%2099.3152%20162.31%2099.3152%20149.168C99.3152%20136.026%20109.996%20125.361%20123.158%20125.361H147.002Z%22%20fill%3D%22%23BFF009%22%2F%3E%0A%3C%2Fg%3E%0A%3Cdefs%3E%0A%3CclipPath%20id%3D%22clip0_544_687%22%3E%0A%3Crect%20width%3D%2298.304%22%20height%3D%22142.848%22%20fill%3D%22white%22%20transform%3D%22translate(73.9844%2054.272)%22%2F%3E%0A%3C%2FclipPath%3E%0A%3C%2Fdefs%3E%0A%3C%2Fsvg%3E%0A";
;
}}),
}]);

//# sourceMappingURL=node_modules_%40rainbow-me_rainbowkit_dist_kaia-65D2U3PU_70aff253.js.map