module.exports = {

"[project]/node_modules/@rainbow-me/rainbowkit/dist/unichain-C5BWO2ZY.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>unichain_default)
});
"use client";
// src/components/RainbowKitProvider/chainIcons/unichain.svg
var unichain_default = "data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2028%2028%22%3E%3Cg%20transform%3D%22translate(0%2C0)%20scale(0.7)%22%3E%3Cg%20clip-path%3D%22url(%23a)%22%3E%3Cpath%20fill%3D%22url(%23b)%22%20d%3D%22M0%200h40v40H0z%22%2F%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M36%2019.696c-8.672%200-15.696-7.03-15.696-15.696h-.608v15.696H4v.608c8.673%200%2015.696%207.03%2015.696%2015.696h.608V20.304H36v-.608Z%22%2F%3E%3C%2Fg%3E%3Cdefs%3E%3CradialGradient%20id%3D%22b%22%20cx%3D%220%22%20cy%3D%220%22%20r%3D%221%22%20gradientTransform%3D%22matrix(0%2020%20-20%200%2020%2020)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23FC74FE%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23F50DB4%22%2F%3E%3C%2FradialGradient%3E%3CclipPath%20id%3D%22a%22%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M0%200h40v40H0z%22%2F%3E%3C%2FclipPath%3E%3C%2Fdefs%3E%3C%2Fg%3E%3C%2Fsvg%3E";
;
}}),

};

//# sourceMappingURL=node_modules_%40rainbow-me_rainbowkit_dist_unichain-C5BWO2ZY_a568b34c.js.map