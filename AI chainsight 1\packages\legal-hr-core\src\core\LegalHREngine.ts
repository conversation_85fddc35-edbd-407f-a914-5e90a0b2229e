import { ParsedResume, LegalDocument, ContractAnalysis, ProcessingResult, CandidateMatch, JobRequirement } from '../types';
import { ResumeParser } from '../resume/ResumeParser';
import { ContractAnalyzer } from '../legal/ContractAnalyzer';
import { DocumentProcessor } from '../processing/DocumentProcessor';
import { SkillExtractor } from '../skills/SkillExtractor';
import { ComplianceChecker } from '../compliance/ComplianceChecker';
import { v4 as uuidv4 } from 'uuid';

export class LegalHREngine {
  private resumeParser: ResumeParser;
  private contractAnalyzer: ContractAnalyzer;
  private documentProcessor: DocumentProcessor;
  private skillExtractor: SkillExtractor;
  private complianceChecker: ComplianceChecker;
  private isInitialized: boolean = false;

  constructor() {
    this.resumeParser = new ResumeParser();
    this.contractAnalyzer = new ContractAnalyzer();
    this.documentProcessor = new DocumentProcessor();
    this.skillExtractor = new SkillExtractor();
    this.complianceChecker = new ComplianceChecker();
  }

  async initialize(): Promise<void> {
    try {
      console.log('Initializing Legal HR Engine...');

      await Promise.all([
        this.resumeParser.initialize(),
        this.contractAnalyzer.initialize(),
        this.documentProcessor.initialize(),
        this.skillExtractor.initialize(),
        this.complianceChecker.initialize()
      ]);

      this.isInitialized = true;
      console.log('Legal HR Engine initialized successfully');

    } catch (error) {
      console.error('Failed to initialize Legal HR Engine:', error);
      throw new Error(`Initialization failed: ${error.message}`);
    }
  }

  async parseResume(
    fileBuffer: Buffer,
    fileName: string,
    options: {
      extract_skills?: boolean;
      analyze_experience?: boolean;
      validate_contact_info?: boolean;
    } = {}
  ): Promise<ProcessingResult> {
    if (!this.isInitialized) {
      throw new Error('Legal HR Engine not initialized. Call initialize() first.');
    }

    const startTime = Date.now();

    try {
      console.log(`Parsing resume: ${fileName}`);

      // Step 1: Extract text from document
      const extractedText = await this.documentProcessor.extractText(fileBuffer, fileName);
      
      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error('No text could be extracted from the document');
      }

      // Step 2: Parse resume structure
      const parsedResume = await this.resumeParser.parseResume(extractedText, {
        file_name: fileName,
        file_size: fileBuffer.length
      });

      // Step 3: Enhanced skill extraction (if enabled)
      if (options.extract_skills !== false) {
        const enhancedSkills = await this.skillExtractor.extractSkills(extractedText);
        parsedResume.skills = this.mergeSkills(parsedResume.skills, enhancedSkills);
      }

      // Step 4: Experience analysis (if enabled)
      if (options.analyze_experience !== false) {
        parsedResume.experience = await this.analyzeExperience(parsedResume.experience);
      }

      // Step 5: Contact info validation (if enabled)
      if (options.validate_contact_info !== false) {
        parsedResume.contact_info = await this.validateContactInfo(parsedResume.contact_info);
      }

      const processingTime = Date.now() - startTime;

      console.log(`Resume parsed successfully: ${fileName} (${processingTime}ms)`);

      return {
        success: true,
        document_id: parsedResume.id,
        processing_time: processingTime,
        confidence_score: parsedResume.metadata.parsing_confidence,
        extracted_data: parsedResume
      };

    } catch (error) {
      console.error(`Resume parsing failed: ${fileName}`, error);
      
      return {
        success: false,
        processing_time: Date.now() - startTime,
        confidence_score: 0,
        errors: [{
          type: 'parsing_error',
          message: error.message,
          severity: 'critical'
        }]
      };
    }
  }

  async analyzeContract(
    fileBuffer: Buffer,
    fileName: string,
    options: {
      check_compliance?: boolean;
      extract_risks?: boolean;
      compare_with?: string;
    } = {}
  ): Promise<ProcessingResult> {
    if (!this.isInitialized) {
      throw new Error('Legal HR Engine not initialized. Call initialize() first.');
    }

    const startTime = Date.now();

    try {
      console.log(`Analyzing contract: ${fileName}`);

      // Step 1: Extract text from document
      const extractedText = await this.documentProcessor.extractText(fileBuffer, fileName);
      
      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error('No text could be extracted from the document');
      }

      // Step 2: Parse legal document structure
      const legalDocument = await this.contractAnalyzer.parseDocument(extractedText, {
        file_name: fileName,
        file_size: fileBuffer.length
      });

      // Step 3: Perform contract analysis
      const analysis = await this.contractAnalyzer.analyzeContract(legalDocument);

      // Step 4: Compliance check (if enabled)
      if (options.check_compliance !== false) {
        analysis.compliance_check = await this.complianceChecker.checkCompliance(
          legalDocument,
          ['GDPR', 'SOX', 'HIPAA'] // Default standards
        );
      }

      // Step 5: Risk extraction (if enabled)
      if (options.extract_risks !== false) {
        analysis.risk_assessment = await this.contractAnalyzer.assessRisks(legalDocument);
      }

      // Step 6: Contract comparison (if requested)
      if (options.compare_with) {
        analysis.comparison = await this.contractAnalyzer.compareContracts(
          legalDocument.id,
          options.compare_with
        );
      }

      const processingTime = Date.now() - startTime;

      console.log(`Contract analyzed successfully: ${fileName} (${processingTime}ms)`);

      return {
        success: true,
        document_id: legalDocument.id,
        processing_time: processingTime,
        confidence_score: legalDocument.metadata.confidence_score,
        extracted_data: {
          document: legalDocument,
          analysis: analysis
        }
      };

    } catch (error) {
      console.error(`Contract analysis failed: ${fileName}`, error);
      
      return {
        success: false,
        processing_time: Date.now() - startTime,
        confidence_score: 0,
        errors: [{
          type: 'analysis_error',
          message: error.message,
          severity: 'critical'
        }]
      };
    }
  }

  async matchCandidateToJob(
    candidateId: string,
    jobRequirement: JobRequirement
  ): Promise<CandidateMatch> {
    try {
      console.log(`Matching candidate ${candidateId} to job ${jobRequirement.id}`);

      // Get candidate resume (would typically come from database)
      const candidate = await this.getCandidateProfile(candidateId);
      if (!candidate) {
        throw new Error(`Candidate ${candidateId} not found`);
      }

      // Perform skill matching
      const skillMatch = await this.matchSkills(candidate.resume.skills, jobRequirement);

      // Perform experience matching
      const experienceMatch = await this.matchExperience(candidate.resume.experience, jobRequirement);

      // Perform education matching
      const educationMatch = await this.matchEducation(candidate.resume.education, jobRequirement);

      // Calculate overall match score
      const overallScore = this.calculateOverallMatchScore(skillMatch, experienceMatch, educationMatch);

      // Generate recommendations
      const recommendations = await this.generateMatchRecommendations(
        candidate.resume,
        jobRequirement,
        skillMatch,
        experienceMatch,
        educationMatch
      );

      const match: CandidateMatch = {
        candidate_id: candidateId,
        job_id: jobRequirement.id,
        overall_match_score: overallScore,
        skill_match: skillMatch,
        experience_match: experienceMatch,
        education_match: educationMatch,
        recommendations
      };

      console.log(`Candidate matching completed: ${overallScore}% match`);
      return match;

    } catch (error) {
      console.error(`Candidate matching failed:`, error);
      throw new Error(`Matching failed: ${error.message}`);
    }
  }

  async extractSkillsFromText(text: string): Promise<any[]> {
    try {
      return await this.skillExtractor.extractSkills(text);
    } catch (error) {
      console.error('Skill extraction failed:', error);
      return [];
    }
  }

  async validateDocument(
    fileBuffer: Buffer,
    fileName: string,
    documentType: 'resume' | 'contract' | 'legal'
  ): Promise<{
    is_valid: boolean;
    confidence: number;
    issues: string[];
    recommendations: string[];
  }> {
    try {
      const extractedText = await this.documentProcessor.extractText(fileBuffer, fileName);
      
      if (!extractedText || extractedText.trim().length < 100) {
        return {
          is_valid: false,
          confidence: 0,
          issues: ['Document appears to be empty or too short'],
          recommendations: ['Please provide a valid document with sufficient content']
        };
      }

      // Document type specific validation
      let validation;
      switch (documentType) {
        case 'resume':
          validation = await this.resumeParser.validateResume(extractedText);
          break;
        case 'contract':
        case 'legal':
          validation = await this.contractAnalyzer.validateDocument(extractedText);
          break;
        default:
          throw new Error(`Unsupported document type: ${documentType}`);
      }

      return validation;

    } catch (error) {
      console.error('Document validation failed:', error);
      return {
        is_valid: false,
        confidence: 0,
        issues: [`Validation error: ${error.message}`],
        recommendations: ['Please check the document format and try again']
      };
    }
  }

  async batchProcessDocuments(
    documents: Array<{
      buffer: Buffer;
      fileName: string;
      type: 'resume' | 'contract';
    }>,
    options: any = {}
  ): Promise<ProcessingResult[]> {
    try {
      console.log(`Processing ${documents.length} documents in batch`);

      const results = await Promise.allSettled(
        documents.map(async (doc) => {
          if (doc.type === 'resume') {
            return await this.parseResume(doc.buffer, doc.fileName, options);
          } else {
            return await this.analyzeContract(doc.buffer, doc.fileName, options);
          }
        })
      );

      return results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return {
            success: false,
            processing_time: 0,
            confidence_score: 0,
            errors: [{
              type: 'batch_processing_error',
              message: result.reason?.message || 'Unknown error',
              severity: 'critical'
            }]
          };
        }
      });

    } catch (error) {
      console.error('Batch processing failed:', error);
      throw new Error(`Batch processing failed: ${error.message}`);
    }
  }

  private async getCandidateProfile(candidateId: string): Promise<any> {
    // In a real implementation, this would fetch from database
    // For now, return a mock candidate
    return {
      id: candidateId,
      resume: {
        id: candidateId,
        skills: [],
        experience: [],
        education: []
      }
    };
  }

  private async matchSkills(candidateSkills: any[], jobRequirement: JobRequirement): Promise<any> {
    // Simplified skill matching logic
    const requiredSkills = jobRequirement.required_skills || [];
    const matchedSkills = [];
    const missingSkills = [];

    for (const required of requiredSkills) {
      const match = candidateSkills.find(skill => 
        skill.name.toLowerCase().includes(required.skill.toLowerCase())
      );
      
      if (match) {
        matchedSkills.push({
          skill: required.skill,
          match_score: 0.8,
          required_level: required.level,
          candidate_level: match.level
        });
      } else {
        missingSkills.push(required.skill);
      }
    }

    const score = matchedSkills.length / Math.max(requiredSkills.length, 1) * 100;

    return {
      score,
      matched_skills: matchedSkills,
      missing_skills: missingSkills,
      additional_skills: candidateSkills.map(s => s.name)
    };
  }

  private async matchExperience(candidateExperience: any[], jobRequirement: JobRequirement): Promise<any> {
    // Simplified experience matching logic
    const totalYears = candidateExperience.reduce((sum, exp) => sum + (exp.duration_months || 0), 0) / 12;
    const minRequired = jobRequirement.experience_requirements?.[0]?.minimum_years || 0;

    return {
      score: Math.min(100, (totalYears / Math.max(minRequired, 1)) * 100),
      years_match: totalYears >= minRequired,
      industry_match: true, // Simplified
      role_match: true, // Simplified
      gaps: []
    };
  }

  private async matchEducation(candidateEducation: any[], jobRequirement: JobRequirement): Promise<any> {
    // Simplified education matching logic
    const hasRequiredDegree = candidateEducation.some(edu => 
      edu.degree && edu.degree.toLowerCase().includes('bachelor')
    );

    return {
      score: hasRequiredDegree ? 100 : 50,
      degree_match: hasRequiredDegree,
      field_match: true, // Simplified
      alternatives_available: false
    };
  }

  private calculateOverallMatchScore(skillMatch: any, experienceMatch: any, educationMatch: any): number {
    return Math.round(
      (skillMatch.score * 0.5) +
      (experienceMatch.score * 0.3) +
      (educationMatch.score * 0.2)
    );
  }

  private async generateMatchRecommendations(
    resume: any,
    jobRequirement: JobRequirement,
    skillMatch: any,
    experienceMatch: any,
    educationMatch: any
  ): Promise<string[]> {
    const recommendations = [];

    if (skillMatch.missing_skills.length > 0) {
      recommendations.push(`Consider developing skills in: ${skillMatch.missing_skills.join(', ')}`);
    }

    if (experienceMatch.score < 80) {
      recommendations.push('Gain more relevant work experience in the target industry');
    }

    if (educationMatch.score < 80) {
      recommendations.push('Consider pursuing additional education or certifications');
    }

    return recommendations;
  }

  private mergeSkills(existingSkills: any[], extractedSkills: any[]): any[] {
    const skillMap = new Map();

    // Add existing skills
    existingSkills.forEach(skill => {
      skillMap.set(skill.name.toLowerCase(), skill);
    });

    // Merge with extracted skills
    extractedSkills.forEach(skill => {
      const key = skill.name.toLowerCase();
      if (skillMap.has(key)) {
        // Update confidence if higher
        const existing = skillMap.get(key);
        if (skill.confidence_score > existing.confidence_score) {
          skillMap.set(key, { ...existing, ...skill });
        }
      } else {
        skillMap.set(key, skill);
      }
    });

    return Array.from(skillMap.values());
  }

  private async analyzeExperience(experience: any[]): Promise<any[]> {
    // Enhanced experience analysis
    return experience.map(exp => ({
      ...exp,
      // Add calculated fields
      duration_months: exp.duration_months || this.calculateDuration(exp.start_date, exp.end_date),
      seniority_level: this.determineSeniorityLevel(exp.position),
      industry_category: this.categorizeIndustry(exp.company)
    }));
  }

  private async validateContactInfo(contactInfo: any): Promise<any> {
    // Basic contact info validation
    const validated = { ...contactInfo };

    if (contactInfo.email) {
      validated.email_valid = this.isValidEmail(contactInfo.email);
    }

    if (contactInfo.phone) {
      validated.phone_valid = this.isValidPhone(contactInfo.phone);
    }

    return validated;
  }

  private calculateDuration(startDate: Date, endDate: Date): number {
    if (!startDate) return 0;
    const end = endDate || new Date();
    const start = new Date(startDate);
    return Math.max(0, Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 30)));
  }

  private determineSeniorityLevel(position: string): string {
    const title = position.toLowerCase();
    if (title.includes('senior') || title.includes('lead') || title.includes('principal')) {
      return 'senior';
    } else if (title.includes('junior') || title.includes('intern') || title.includes('entry')) {
      return 'junior';
    }
    return 'mid';
  }

  private categorizeIndustry(company: string): string {
    // Simplified industry categorization
    const name = company.toLowerCase();
    if (name.includes('tech') || name.includes('software')) return 'technology';
    if (name.includes('bank') || name.includes('finance')) return 'finance';
    if (name.includes('health') || name.includes('medical')) return 'healthcare';
    return 'other';
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  async dispose(): Promise<void> {
    try {
      await Promise.all([
        this.resumeParser.dispose(),
        this.contractAnalyzer.dispose(),
        this.documentProcessor.dispose(),
        this.skillExtractor.dispose(),
        this.complianceChecker.dispose()
      ]);

      this.isInitialized = false;
      console.log('Legal HR Engine disposed');

    } catch (error) {
      console.error('Error disposing Legal HR Engine:', error);
    }
  }

  getStatus(): {
    initialized: boolean;
    components: Record<string, boolean>;
  } {
    return {
      initialized: this.isInitialized,
      components: {
        resume_parser: this.resumeParser.isInitialized(),
        contract_analyzer: this.contractAnalyzer.isInitialized(),
        document_processor: this.documentProcessor.isInitialized(),
        skill_extractor: this.skillExtractor.isInitialized(),
        compliance_checker: this.complianceChecker.isInitialized()
      }
    };
  }
}
