'use client'

import { motion } from 'framer-motion'
import { Hero } from '@/components/Hero'
import { FeatureGrid } from '@/components/FeatureGrid'
import { LiveChart } from '@/components/LiveChart'
import { AIAssistant } from '@/components/AIAssistant'

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <Hero />
      
      {/* Live Market Data */}
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-luxury-gold to-yellow-400 bg-clip-text text-transparent">
              Real-Time Market Intelligence
            </h2>
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              AI-powered financial analysis with live cryptocurrency and stock market data
            </p>
          </motion.div>
          <LiveChart />
        </div>
      </section>

      {/* Feature Grid */}
      <section className="py-20 px-4 bg-gradient-to-b from-transparent to-luxury-charcoal/50">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-luxury-gold to-yellow-400 bg-clip-text text-transparent">
              Modular AI Platform
            </h2>
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              Comprehensive suite of AI-powered tools for Web3, finance, design, and enterprise
            </p>
          </motion.div>
          <FeatureGrid />
        </div>
      </section>

      {/* AI Assistant */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-luxury-gold to-yellow-400 bg-clip-text text-transparent">
              Connectouch AI Assistant
            </h2>
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              Natural language interface for all platform features with real-time responses
            </p>
          </motion.div>
          <AIAssistant />
        </div>
      </section>
    </div>
  )
}
