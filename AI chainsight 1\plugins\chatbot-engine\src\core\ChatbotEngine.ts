import OpenAI from 'openai';
import { ChatMessage, Conversation, ChatContext, Response, ChatbotConfig, Intent } from '../types';
import { ConversationManager } from '../conversation/ConversationManager';
import { ContextManager } from '../context/ContextManager';
import { IntentClassifier } from '../nlp/IntentClassifier';
import { ResponseGenerator } from '../generation/ResponseGenerator';
import { MemoryManager } from '../memory/MemoryManager';
import { PersonalityEngine } from '../personality/PersonalityEngine';
import { DEFAULT_OPENAI_CONFIG, SYSTEM_PROMPTS } from '../constants';
import { RateLimiterMemory } from 'rate-limiter-flexible';

export class ChatbotEngine {
  private openai: OpenAI;
  private conversationManager: ConversationManager;
  private contextManager: ContextManager;
  private intentClassifier: IntentClassifier;
  private responseGenerator: ResponseGenerator;
  private memoryManager: MemoryManager;
  private personalityEngine: PersonalityEngine;
  private rateLimiter: RateLimiterMemory;
  private config: ChatbotConfig;

  constructor(config: ChatbotConfig) {
    this.config = config;
    
    // Initialize OpenAI
    this.openai = new OpenAI({
      apiKey: config.openai_config.api_key
    });

    // Initialize components
    this.conversationManager = new ConversationManager();
    this.contextManager = new ContextManager();
    this.intentClassifier = new IntentClassifier();
    this.responseGenerator = new ResponseGenerator(this.openai, config);
    this.memoryManager = new MemoryManager(config.memory_config);
    this.personalityEngine = new PersonalityEngine(config.personality);

    // Initialize rate limiter
    this.rateLimiter = new RateLimiterMemory({
      points: 60, // Number of requests
      duration: 60, // Per 60 seconds
    });
  }

  async processMessage(
    userId: string,
    message: string,
    conversationId?: string
  ): Promise<{
    response: Response;
    conversation: Conversation;
    metadata: {
      intent: Intent;
      processing_time: number;
      tokens_used: number;
    };
  }> {
    const startTime = Date.now();

    try {
      // Check rate limits
      await this.rateLimiter.consume(userId);

      // Get or create conversation
      let conversation = conversationId 
        ? await this.conversationManager.getConversation(conversationId)
        : await this.conversationManager.createConversation(userId);

      if (!conversation) {
        conversation = await this.conversationManager.createConversation(userId);
      }

      // Build context
      const context = await this.contextManager.buildContext(userId, conversation);

      // Classify intent
      const intent = await this.intentClassifier.classifyIntent(message, context);

      // Update memory with new message
      await this.memoryManager.addMemory({
        id: `msg_${Date.now()}`,
        type: 'episodic',
        content: message,
        context: `User message in conversation ${conversation.id}`,
        importance: 0.7,
        created_at: new Date(),
        last_accessed: new Date(),
        access_count: 1,
        decay_rate: 0.1
      });

      // Generate response
      const response = await this.generateResponse(message, intent, context, conversation);

      // Create user message
      const userMessage: ChatMessage = {
        id: `msg_${Date.now()}_user`,
        conversation_id: conversation.id,
        role: 'user',
        content: message,
        timestamp: new Date(),
        metadata: {
          intent: intent.name,
          entities: intent.entities,
          confidence: intent.confidence
        }
      };

      // Create assistant message
      const assistantMessage: ChatMessage = {
        id: `msg_${Date.now()}_assistant`,
        conversation_id: conversation.id,
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
        metadata: {
          confidence: response.confidence,
          processing_time: Date.now() - startTime,
          model_used: this.config.openai_config.model
        }
      };

      // Update conversation
      conversation.messages.push(userMessage, assistantMessage);
      conversation.updated_at = new Date();
      
      // Update context
      await this.contextManager.updateContext(userId, conversation, intent);

      // Save conversation
      await this.conversationManager.updateConversation(conversation);

      const processingTime = Date.now() - startTime;

      return {
        response,
        conversation,
        metadata: {
          intent,
          processing_time: processingTime,
          tokens_used: this.estimateTokens(message + response.content)
        }
      };

    } catch (error) {
      console.error('Error processing message:', error);
      
      // Return error response
      const errorResponse: Response = {
        content: "I apologize, but I'm experiencing some technical difficulties. Please try again in a moment.",
        type: 'error',
        confidence: 1.0,
        metadata: {
          reasoning: 'System error occurred during message processing'
        }
      };

      // Create minimal conversation for error case
      const errorConversation = conversationId 
        ? await this.conversationManager.getConversation(conversationId) || await this.conversationManager.createConversation(userId)
        : await this.conversationManager.createConversation(userId);

      return {
        response: errorResponse,
        conversation: errorConversation,
        metadata: {
          intent: { name: 'error', confidence: 1.0, parameters: {}, entities: [] },
          processing_time: Date.now() - startTime,
          tokens_used: 0
        }
      };
    }
  }

  private async generateResponse(
    message: string,
    intent: Intent,
    context: ChatContext,
    conversation: Conversation
  ): Promise<Response> {
    try {
      // Get relevant memories
      const relevantMemories = await this.memoryManager.getRelevantMemories(message, 5);
      
      // Apply personality
      const personalityContext = this.personalityEngine.getPersonalityContext(intent, context);
      
      // Generate response using OpenAI
      const response = await this.responseGenerator.generateResponse(
        message,
        intent,
        context,
        conversation,
        relevantMemories,
        personalityContext
      );

      // Store response in memory
      await this.memoryManager.addMemory({
        id: `response_${Date.now()}`,
        type: 'semantic',
        content: response.content,
        context: `Assistant response to: ${message}`,
        importance: 0.6,
        created_at: new Date(),
        last_accessed: new Date(),
        access_count: 1,
        decay_rate: 0.1
      });

      return response;

    } catch (error) {
      console.error('Error generating response:', error);
      
      // Return fallback response
      return {
        content: this.getFallbackResponse(intent.name),
        type: 'text',
        confidence: 0.5,
        metadata: {
          reasoning: 'Fallback response due to generation error'
        }
      };
    }
  }

  async getConversationHistory(
    userId: string,
    conversationId: string,
    limit: number = 50
  ): Promise<ChatMessage[]> {
    try {
      const conversation = await this.conversationManager.getConversation(conversationId);
      
      if (!conversation || conversation.user_id !== userId) {
        throw new Error('Conversation not found or access denied');
      }

      return conversation.messages.slice(-limit);

    } catch (error) {
      console.error('Error getting conversation history:', error);
      return [];
    }
  }

  async getUserConversations(userId: string, limit: number = 20): Promise<Conversation[]> {
    try {
      return await this.conversationManager.getUserConversations(userId, limit);
    } catch (error) {
      console.error('Error getting user conversations:', error);
      return [];
    }
  }

  async deleteConversation(userId: string, conversationId: string): Promise<boolean> {
    try {
      const conversation = await this.conversationManager.getConversation(conversationId);
      
      if (!conversation || conversation.user_id !== userId) {
        throw new Error('Conversation not found or access denied');
      }

      return await this.conversationManager.deleteConversation(conversationId);

    } catch (error) {
      console.error('Error deleting conversation:', error);
      return false;
    }
  }

  async updateUserPreferences(userId: string, preferences: any): Promise<boolean> {
    try {
      return await this.contextManager.updateUserPreferences(userId, preferences);
    } catch (error) {
      console.error('Error updating user preferences:', error);
      return false;
    }
  }

  async getAnalytics(userId?: string): Promise<any> {
    try {
      if (userId) {
        return await this.conversationManager.getUserAnalytics(userId);
      } else {
        return await this.conversationManager.getGlobalAnalytics();
      }
    } catch (error) {
      console.error('Error getting analytics:', error);
      return null;
    }
  }

  async trainFromFeedback(
    messageId: string,
    feedback: {
      rating: number;
      feedback_text?: string;
      feedback_type: string;
    }
  ): Promise<boolean> {
    try {
      // Store feedback for future training
      await this.memoryManager.addMemory({
        id: `feedback_${Date.now()}`,
        type: 'semantic',
        content: `Feedback for message ${messageId}: ${feedback.feedback_text || feedback.feedback_type}`,
        context: `User feedback with rating ${feedback.rating}`,
        importance: 0.8,
        created_at: new Date(),
        last_accessed: new Date(),
        access_count: 1,
        decay_rate: 0.05
      });

      // Update personality engine with feedback
      this.personalityEngine.processFeedback(feedback);

      return true;

    } catch (error) {
      console.error('Error processing feedback:', error);
      return false;
    }
  }

  private getFallbackResponse(intentName: string): string {
    const fallbacks = this.config.response_config.fallback_responses;
    
    if (fallbacks && fallbacks.length > 0) {
      return fallbacks[Math.floor(Math.random() * fallbacks.length)];
    }

    // Default fallbacks based on intent
    switch (intentName) {
      case 'greeting':
        return "Hello! I'm Connectouch, your AI market assistant. How can I help you today?";
      case 'price_inquiry':
        return "I'd be happy to help you with price information. Could you specify which asset you're interested in?";
      case 'market_analysis':
        return "I can provide market analysis for you. Which market or asset would you like me to analyze?";
      default:
        return "I'm here to help with your financial market questions. What would you like to know?";
    }
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  async shutdown(): Promise<void> {
    try {
      // Cleanup resources
      await this.memoryManager.cleanup();
      console.log('Chatbot engine shutdown complete');
    } catch (error) {
      console.error('Error during shutdown:', error);
    }
  }

  // Health check method
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    components: Record<string, boolean>;
    timestamp: Date;
  }> {
    const components = {
      openai: false,
      memory: false,
      conversation: false,
      context: false
    };

    try {
      // Test OpenAI connection
      await this.openai.models.list();
      components.openai = true;
    } catch (error) {
      console.warn('OpenAI health check failed:', error);
    }

    try {
      // Test memory manager
      await this.memoryManager.getRelevantMemories('test', 1);
      components.memory = true;
    } catch (error) {
      console.warn('Memory manager health check failed:', error);
    }

    try {
      // Test conversation manager
      components.conversation = true;
    } catch (error) {
      console.warn('Conversation manager health check failed:', error);
    }

    try {
      // Test context manager
      components.context = true;
    } catch (error) {
      console.warn('Context manager health check failed:', error);
    }

    const healthyComponents = Object.values(components).filter(Boolean).length;
    const totalComponents = Object.keys(components).length;

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (healthyComponents === totalComponents) {
      status = 'healthy';
    } else if (healthyComponents >= totalComponents / 2) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      status,
      components,
      timestamp: new Date()
    };
  }
}
