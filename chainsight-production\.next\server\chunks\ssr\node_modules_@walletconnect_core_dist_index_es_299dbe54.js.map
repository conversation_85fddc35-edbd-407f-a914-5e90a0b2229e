{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.es.js", "sources": ["file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/core.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/crypto.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/keychain.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/messages.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/publisher.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/relayer.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/store.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/subscriber.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/pairing.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/history.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/expirer.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/verify.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/echo.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/constants/events.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/vendor/base-x.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bytes.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bases/base.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bases/identity.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bases/base2.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bases/base8.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bases/base10.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bases/base16.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bases/base32.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bases/base36.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bases/base58.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bases/base64.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/bases/base256emoji.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/vendor/varint.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/varint.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/hashes/digest.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/hashes/hasher.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/hashes/sha2-browser.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/hashes/identity.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/codecs/json.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/multiformats/esm/src/basics.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/uint8arrays/esm/src/alloc.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/uint8arrays/esm/src/util/bases.js", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/uint8arrays/esm/src/from-string.js", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/keychain.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/crypto.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/messages.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/publisher.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/topicmap.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/subscriber.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/relayer.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/es-toolkit/dist/function/noop.mjs", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/es-toolkit/dist/predicate/isPlainObject.mjs", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/es-toolkit/dist/compat/_internal/getSymbols.mjs", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/es-toolkit/dist/compat/_internal/getTag.mjs", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/es-toolkit/dist/compat/_internal/tags.mjs", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/es-toolkit/dist/compat/util/eq.mjs", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/es-toolkit/dist/predicate/isEqualWith.mjs", "file:///F:/AI%20chain/chainsight-production/node_modules/node_modules/es-toolkit/dist/predicate/isEqual.mjs", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/store.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/pairing.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/history.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/expirer.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/verify.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/echo.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/controllers/events.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/core.ts", "file:///F:/AI%20chain/chainsight-production/node_modules/%40walletconnect/core/src/index.ts"], "sourcesContent": ["export const CORE_PROTOCOL = \"wc\";\nexport const CORE_VERSION = 2;\nexport const CORE_CONTEXT = \"core\";\n\nexport const CORE_STORAGE_PREFIX = `${CORE_PROTOCOL}@${CORE_VERSION}:${CORE_CONTEXT}:`;\n\nexport const CORE_DEFAULT = {\n  name: CORE_CONTEXT,\n  logger: \"error\",\n};\n\nexport const CORE_STORAGE_OPTIONS = {\n  database: \":memory:\",\n};\n", "import { ONE_DAY } from \"@walletconnect/time\";\n\nexport const CRYPTO_CONTEXT = \"crypto\";\n\nexport const CRYPTO_CLIENT_SEED = \"client_ed25519_seed\";\n\nexport const CRYPTO_JWT_TTL = ONE_DAY;\n", "export const KEYCHAIN_CONTEXT = \"keychain\";\n\nexport const KEYCHAIN_STORAGE_VERSION = \"0.3\";\n", "export const MESSAGES_CONTEXT = \"messages\";\n\nexport const MESSAGES_STORAGE_VERSION = \"0.3\";\n", "import { SIX_HOURS } from \"@walletconnect/time\";\n\nexport const PUBLISHER_DEFAULT_TTL = SIX_HOURS;\n\nexport const PUBLISHER_CONTEXT = \"publisher\";\n", "export const RELAYER_DEFAULT_PROTOCOL = \"irn\";\n\nexport const RELAYER_DEFAULT_LOGGER = \"error\";\n\nexport const RELAYER_DEFAULT_RELAY_URL = \"wss://relay.walletconnect.org\";\n\nexport const RELAYER_CONTEXT = \"relayer\";\n\nexport const RELAYER_EVENTS = {\n  message: \"relayer_message\",\n  message_ack: \"relayer_message_ack\",\n  connect: \"relayer_connect\",\n  disconnect: \"relayer_disconnect\",\n  error: \"relayer_error\",\n  connection_stalled: \"relayer_connection_stalled\",\n  transport_closed: \"relayer_transport_closed\",\n  publish: \"relayer_publish\",\n};\n\nexport const RELAYER_SUBSCRIBER_SUFFIX = \"_subscription\";\n\nexport const RELAYER_PROVIDER_EVENTS = {\n  payload: \"payload\",\n  connect: \"connect\",\n  disconnect: \"disconnect\",\n  error: \"error\",\n};\n\nexport const RELAYER_RECONNECT_TIMEOUT = 0.1;\n\nexport const RELAYER_STORAGE_OPTIONS = {\n  database: \":memory:\",\n};\n\n// Updated automatically via `new-version` npm script.\n\nexport const RELAYER_SDK_VERSION = \"2.21.1\";\n\n// delay to wait before closing the transport connection after init if not active\nexport const RELAYER_TRANSPORT_CUTOFF = 10_000;\n\nexport const TRANSPORT_TYPES = {\n  link_mode: \"link_mode\",\n  relay: \"relay\",\n} as const;\n\nexport const MESSAGE_DIRECTION = {\n  inbound: \"inbound\",\n  outbound: \"outbound\",\n} as const;\n", "export const STORE_STORAGE_VERSION = \"0.3\";\n\nexport const WALLETCONNECT_CLIENT_ID = \"WALLETCONNECT_CLIENT_ID\";\nexport const WALLETCONNECT_LINK_MODE_APPS = \"WALLETCONNECT_LINK_MODE_APPS\";\n", "import { THIRTY_DAYS, FIVE_SECONDS } from \"@walletconnect/time\";\n\nexport const SUBSCRIBER_EVENTS = {\n  created: \"subscription_created\",\n  deleted: \"subscription_deleted\",\n  expired: \"subscription_expired\",\n  disabled: \"subscription_disabled\",\n  sync: \"subscription_sync\",\n  resubscribed: \"subscription_resubscribed\",\n};\n\nexport const SUBSCRIBER_DEFAULT_TTL = THIRTY_DAYS;\n\nexport const SUBSCRIBER_CONTEXT = \"subscription\";\n\nexport const SUBSCRIBER_STORAGE_VERSION = \"0.3\";\n\nexport const PENDING_SUB_RESOLUTION_TIMEOUT = FIVE_SECONDS * 1000;\n", "import { THIRTY_DAYS, ONE_DAY, THIRTY_SECONDS } from \"@walletconnect/time\";\nimport { RelayerTypes, PairingJsonRpcTypes } from \"@walletconnect/types\";\n\nexport const PAIRING_CONTEXT = \"pairing\";\n\nexport const PAIRING_STORAGE_VERSION = \"0.3\";\n\nexport const PAIRING_DEFAULT_TTL = THIRTY_DAYS;\n\nexport const PAIRING_RPC_OPTS: Record<\n  PairingJsonRpcTypes.WcMethod | \"unregistered_method\",\n  {\n    req: RelayerTypes.PublishOptions;\n    res: RelayerTypes.PublishOptions;\n  }\n> = {\n  wc_pairingDelete: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1000,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 1001,\n    },\n  },\n  wc_pairingPing: {\n    req: {\n      ttl: THIRTY_SECONDS,\n      prompt: false,\n      tag: 1002,\n    },\n    res: {\n      ttl: THIRTY_SECONDS,\n      prompt: false,\n      tag: 1003,\n    },\n  },\n  unregistered_method: {\n    req: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 0,\n    },\n    res: {\n      ttl: ONE_DAY,\n      prompt: false,\n      tag: 0,\n    },\n  },\n};\n\nexport const PAIRING_EVENTS = {\n  create: \"pairing_create\",\n  expire: \"pairing_expire\",\n  delete: \"pairing_delete\",\n  ping: \"pairing_ping\",\n};\n", "export const HISTORY_EVENTS = {\n  created: \"history_created\",\n  updated: \"history_updated\",\n  deleted: \"history_deleted\",\n  sync: \"history_sync\",\n};\n\nexport const HISTORY_CONTEXT = \"history\";\n\nexport const HISTORY_STORAGE_VERSION = \"0.3\";\n", "import { ONE_DAY } from \"@walletconnect/time\";\n\nexport const EXPIRER_CONTEXT = \"expirer\";\n\nexport const EXPIRER_EVENTS = {\n  created: \"expirer_created\",\n  deleted: \"expirer_deleted\",\n  expired: \"expirer_expired\",\n  sync: \"expirer_sync\",\n};\n\nexport const EXPIRER_STORAGE_VERSION = \"0.3\";\n\nexport const EXPIRER_DEFAULT_TTL = ONE_DAY;\n", "export const VERIFY_CONTEXT = \"verify-api\";\n\nconst VERIFY_SERVER_COM = \"https://verify.walletconnect.com\";\nconst VERIFY_SERVER_ORG = \"https://verify.walletconnect.org\";\nexport const VERIFY_SERVER = VERIFY_SERVER_ORG;\nexport const VERIFY_SERVER_V3 = `${VERIFY_SERVER}/v3`;\n\nexport const TRUSTED_VERIFY_URLS = [VERIFY_SERVER_COM, VERIFY_SERVER_ORG];\n", "export const ECHO_CONTEXT = \"echo\";\n\nexport const ECHO_URL = \"https://echo.walletconnect.com\";\n", "export const EVENT_CLIENT_CONTEXT = \"event-client\";\n\nexport const EVENT_CLIENT_PAIRING_TRACES = {\n  pairing_started: \"pairing_started\",\n  pairing_uri_validation_success: \"pairing_uri_validation_success\",\n  pairing_uri_not_expired: \"pairing_uri_not_expired\",\n  store_new_pairing: \"store_new_pairing\",\n  subscribing_pairing_topic: \"subscribing_pairing_topic\",\n  subscribe_pairing_topic_success: \"subscribe_pairing_topic_success\",\n  existing_pairing: \"existing_pairing\",\n  pairing_not_expired: \"pairing_not_expired\",\n  emit_inactive_pairing: \"emit_inactive_pairing\",\n  emit_session_proposal: \"emit_session_proposal\",\n  subscribing_to_pairing_topic: \"subscribing_to_pairing_topic\",\n};\n\nexport const EVENT_CLIENT_PAIRING_ERRORS = {\n  no_wss_connection: \"no_wss_connection\",\n  no_internet_connection: \"no_internet_connection\",\n  malformed_pairing_uri: \"malformed_pairing_uri\",\n  active_pairing_already_exists: \"active_pairing_already_exists\",\n  subscribe_pairing_topic_failure: \"subscribe_pairing_topic_failure\",\n  pairing_expired: \"pairing_expired\",\n  proposal_expired: \"proposal_expired\",\n  proposal_listener_not_found: \"proposal_listener_not_found\",\n};\n\nexport const EVENT_CLIENT_SESSION_TRACES = {\n  session_approve_started: \"session_approve_started\",\n  proposal_not_expired: \"proposal_not_expired\",\n  session_namespaces_validation_success: \"session_namespaces_validation_success\",\n  create_session_topic: \"create_session_topic\",\n  subscribing_session_topic: \"subscribing_session_topic\",\n  subscribe_session_topic_success: \"subscribe_session_topic_success\",\n  publishing_session_approve: \"publishing_session_approve\",\n  session_approve_publish_success: \"session_approve_publish_success\",\n  store_session: \"store_session\",\n  publishing_session_settle: \"publishing_session_settle\",\n  session_settle_publish_success: \"session_settle_publish_success\",\n};\n\nexport const EVENT_CLIENT_SESSION_ERRORS = {\n  no_internet_connection: \"no_internet_connection\",\n  no_wss_connection: \"no_wss_connection\",\n  proposal_expired: \"proposal_expired\",\n  subscribe_session_topic_failure: \"subscribe_session_topic_failure\",\n  session_approve_publish_failure: \"session_approve_publish_failure\",\n  session_settle_publish_failure: \"session_settle_publish_failure\",\n  session_approve_namespace_validation_failure: \"session_approve_namespace_validation_failure\",\n  proposal_not_found: \"proposal_not_found\",\n};\n\nexport const EVENT_CLIENT_AUTHENTICATE_TRACES = {\n  authenticated_session_approve_started: \"authenticated_session_approve_started\",\n  authenticated_session_not_expired: \"authenticated_session_not_expired\",\n  chains_caip2_compliant: \"chains_caip2_compliant\",\n  chains_evm_compliant: \"chains_evm_compliant\",\n  create_authenticated_session_topic: \"create_authenticated_session_topic\",\n  cacaos_verified: \"cacaos_verified\",\n  store_authenticated_session: \"store_authenticated_session\",\n  subscribing_authenticated_session_topic: \"subscribing_authenticated_session_topic\",\n  subscribe_authenticated_session_topic_success: \"subscribe_authenticated_session_topic_success\",\n  publishing_authenticated_session_approve: \"publishing_authenticated_session_approve\",\n  authenticated_session_approve_publish_success: \"authenticated_session_approve_publish_success\",\n};\n\nexport const EVENT_CLIENT_AUTHENTICATE_ERRORS = {\n  no_internet_connection: \"no_internet_connection\",\n  no_wss_connection: \"no_wss_connection\",\n  missing_session_authenticate_request: \"missing_session_authenticate_request\",\n  session_authenticate_request_expired: \"session_authenticate_request_expired\",\n  chains_caip2_compliant_failure: \"chains_caip2_compliant_failure\",\n  chains_evm_compliant_failure: \"chains_evm_compliant_failure\",\n  invalid_cacao: \"invalid_cacao\",\n  subscribe_authenticated_session_topic_failure: \"subscribe_authenticated_session_topic_failure\",\n  authenticated_session_approve_publish_failure: \"authenticated_session_approve_publish_failure\",\n  authenticated_session_pending_request_not_found:\n    \"authenticated_session_pending_request_not_found\",\n};\n\nexport const EVENTS_STORAGE_VERSION = 0.1;\n\nexport const EVENTS_STORAGE_CONTEXT = \"event-client\";\n\nexport const EVENTS_STORAGE_CLEANUP_INTERVAL = 86400;\n\nexport const EVENTS_CLIENT_API_URL = \"https://pulse.walletconnect.org/batch\";\n", "function base(ALPHABET, name) {\n  if (ALPHABET.length >= 255) {\n    throw new TypeError('Alphabet too long');\n  }\n  var BASE_MAP = new Uint8Array(256);\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255;\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i);\n    var xc = x.charCodeAt(0);\n    if (BASE_MAP[xc] !== 255) {\n      throw new TypeError(x + ' is ambiguous');\n    }\n    BASE_MAP[xc] = i;\n  }\n  var BASE = ALPHABET.length;\n  var LEADER = ALPHABET.charAt(0);\n  var FACTOR = Math.log(BASE) / Math.log(256);\n  var iFACTOR = Math.log(256) / Math.log(BASE);\n  function encode(source) {\n    if (source instanceof Uint8Array);\n    else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength);\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source);\n    }\n    if (!(source instanceof Uint8Array)) {\n      throw new TypeError('Expected Uint8Array');\n    }\n    if (source.length === 0) {\n      return '';\n    }\n    var zeroes = 0;\n    var length = 0;\n    var pbegin = 0;\n    var pend = source.length;\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++;\n      zeroes++;\n    }\n    var size = (pend - pbegin) * iFACTOR + 1 >>> 0;\n    var b58 = new Uint8Array(size);\n    while (pbegin !== pend) {\n      var carry = source[pbegin];\n      var i = 0;\n      for (var it1 = size - 1; (carry !== 0 || i < length) && it1 !== -1; it1--, i++) {\n        carry += 256 * b58[it1] >>> 0;\n        b58[it1] = carry % BASE >>> 0;\n        carry = carry / BASE >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      pbegin++;\n    }\n    var it2 = size - length;\n    while (it2 !== size && b58[it2] === 0) {\n      it2++;\n    }\n    var str = LEADER.repeat(zeroes);\n    for (; it2 < size; ++it2) {\n      str += ALPHABET.charAt(b58[it2]);\n    }\n    return str;\n  }\n  function decodeUnsafe(source) {\n    if (typeof source !== 'string') {\n      throw new TypeError('Expected String');\n    }\n    if (source.length === 0) {\n      return new Uint8Array();\n    }\n    var psz = 0;\n    if (source[psz] === ' ') {\n      return;\n    }\n    var zeroes = 0;\n    var length = 0;\n    while (source[psz] === LEADER) {\n      zeroes++;\n      psz++;\n    }\n    var size = (source.length - psz) * FACTOR + 1 >>> 0;\n    var b256 = new Uint8Array(size);\n    while (source[psz]) {\n      var carry = BASE_MAP[source.charCodeAt(psz)];\n      if (carry === 255) {\n        return;\n      }\n      var i = 0;\n      for (var it3 = size - 1; (carry !== 0 || i < length) && it3 !== -1; it3--, i++) {\n        carry += BASE * b256[it3] >>> 0;\n        b256[it3] = carry % 256 >>> 0;\n        carry = carry / 256 >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      psz++;\n    }\n    if (source[psz] === ' ') {\n      return;\n    }\n    var it4 = size - length;\n    while (it4 !== size && b256[it4] === 0) {\n      it4++;\n    }\n    var vch = new Uint8Array(zeroes + (size - it4));\n    var j = zeroes;\n    while (it4 !== size) {\n      vch[j++] = b256[it4++];\n    }\n    return vch;\n  }\n  function decode(string) {\n    var buffer = decodeUnsafe(string);\n    if (buffer) {\n      return buffer;\n    }\n    throw new Error(`Non-${ name } character`);\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  };\n}\nvar src = base;\nvar _brrp__multiformats_scope_baseX = src;\nexport default _brrp__multiformats_scope_baseX;", "const empty = new Uint8Array(0);\nconst toHex = d => d.reduce((hex, byte) => hex + byte.toString(16).padStart(2, '0'), '');\nconst fromHex = hex => {\n  const hexes = hex.match(/../g);\n  return hexes ? new Uint8Array(hexes.map(b => parseInt(b, 16))) : empty;\n};\nconst equals = (aa, bb) => {\n  if (aa === bb)\n    return true;\n  if (aa.byteLength !== bb.byteLength) {\n    return false;\n  }\n  for (let ii = 0; ii < aa.byteLength; ii++) {\n    if (aa[ii] !== bb[ii]) {\n      return false;\n    }\n  }\n  return true;\n};\nconst coerce = o => {\n  if (o instanceof Uint8Array && o.constructor.name === 'Uint8Array')\n    return o;\n  if (o instanceof ArrayBuffer)\n    return new Uint8Array(o);\n  if (ArrayBuffer.isView(o)) {\n    return new Uint8Array(o.buffer, o.byteOffset, o.byteLength);\n  }\n  throw new Error('Unknown type, must be binary type');\n};\nconst isBinary = o => o instanceof ArrayBuffer || ArrayBuffer.isView(o);\nconst fromString = str => new TextEncoder().encode(str);\nconst toString = b => new TextDecoder().decode(b);\nexport {\n  equals,\n  coerce,\n  isBinary,\n  fromHex,\n  toHex,\n  fromString,\n  toString,\n  empty\n};", "import basex from '../../vendor/base-x.js';\nimport { coerce } from '../bytes.js';\nclass Encoder {\n  constructor(name, prefix, baseEncode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n  }\n  encode(bytes) {\n    if (bytes instanceof Uint8Array) {\n      return `${ this.prefix }${ this.baseEncode(bytes) }`;\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}\nclass Decoder {\n  constructor(name, prefix, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    if (prefix.codePointAt(0) === undefined) {\n      throw new Error('Invalid prefix character');\n    }\n    this.prefixCodePoint = prefix.codePointAt(0);\n    this.baseDecode = baseDecode;\n  }\n  decode(text) {\n    if (typeof text === 'string') {\n      if (text.codePointAt(0) !== this.prefixCodePoint) {\n        throw Error(`Unable to decode multibase string ${ JSON.stringify(text) }, ${ this.name } decoder only supports inputs prefixed with ${ this.prefix }`);\n      }\n      return this.baseDecode(text.slice(this.prefix.length));\n    } else {\n      throw Error('Can only multibase decode strings');\n    }\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n}\nclass ComposedDecoder {\n  constructor(decoders) {\n    this.decoders = decoders;\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n  decode(input) {\n    const prefix = input[0];\n    const decoder = this.decoders[prefix];\n    if (decoder) {\n      return decoder.decode(input);\n    } else {\n      throw RangeError(`Unable to decode multibase string ${ JSON.stringify(input) }, only inputs prefixed with ${ Object.keys(this.decoders) } are supported`);\n    }\n  }\n}\nexport const or = (left, right) => new ComposedDecoder({\n  ...left.decoders || { [left.prefix]: left },\n  ...right.decoders || { [right.prefix]: right }\n});\nexport class Codec {\n  constructor(name, prefix, baseEncode, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n    this.baseDecode = baseDecode;\n    this.encoder = new Encoder(name, prefix, baseEncode);\n    this.decoder = new Decoder(name, prefix, baseDecode);\n  }\n  encode(input) {\n    return this.encoder.encode(input);\n  }\n  decode(input) {\n    return this.decoder.decode(input);\n  }\n}\nexport const from = ({name, prefix, encode, decode}) => new Codec(name, prefix, encode, decode);\nexport const baseX = ({prefix, name, alphabet}) => {\n  const {encode, decode} = basex(alphabet, name);\n  return from({\n    prefix,\n    name,\n    encode,\n    decode: text => coerce(decode(text))\n  });\n};\nconst decode = (string, alphabet, bitsPerChar, name) => {\n  const codes = {};\n  for (let i = 0; i < alphabet.length; ++i) {\n    codes[alphabet[i]] = i;\n  }\n  let end = string.length;\n  while (string[end - 1] === '=') {\n    --end;\n  }\n  const out = new Uint8Array(end * bitsPerChar / 8 | 0);\n  let bits = 0;\n  let buffer = 0;\n  let written = 0;\n  for (let i = 0; i < end; ++i) {\n    const value = codes[string[i]];\n    if (value === undefined) {\n      throw new SyntaxError(`Non-${ name } character`);\n    }\n    buffer = buffer << bitsPerChar | value;\n    bits += bitsPerChar;\n    if (bits >= 8) {\n      bits -= 8;\n      out[written++] = 255 & buffer >> bits;\n    }\n  }\n  if (bits >= bitsPerChar || 255 & buffer << 8 - bits) {\n    throw new SyntaxError('Unexpected end of data');\n  }\n  return out;\n};\nconst encode = (data, alphabet, bitsPerChar) => {\n  const pad = alphabet[alphabet.length - 1] === '=';\n  const mask = (1 << bitsPerChar) - 1;\n  let out = '';\n  let bits = 0;\n  let buffer = 0;\n  for (let i = 0; i < data.length; ++i) {\n    buffer = buffer << 8 | data[i];\n    bits += 8;\n    while (bits > bitsPerChar) {\n      bits -= bitsPerChar;\n      out += alphabet[mask & buffer >> bits];\n    }\n  }\n  if (bits) {\n    out += alphabet[mask & buffer << bitsPerChar - bits];\n  }\n  if (pad) {\n    while (out.length * bitsPerChar & 7) {\n      out += '=';\n    }\n  }\n  return out;\n};\nexport const rfc4648 = ({name, prefix, bitsPerChar, alphabet}) => {\n  return from({\n    prefix,\n    name,\n    encode(input) {\n      return encode(input, alphabet, bitsPerChar);\n    },\n    decode(input) {\n      return decode(input, alphabet, bitsPerChar, name);\n    }\n  });\n};", "import { from } from './base.js';\nimport {\n  fromString,\n  toString\n} from '../bytes.js';\nexport const identity = from({\n  prefix: '\\0',\n  name: 'identity',\n  encode: buf => toString(buf),\n  decode: str => fromString(str)\n});", "import { rfc4648 } from './base.js';\nexport const base2 = rfc4648({\n  prefix: '0',\n  name: 'base2',\n  alphabet: '01',\n  bitsPerChar: 1\n});", "import { rfc4648 } from './base.js';\nexport const base8 = rfc4648({\n  prefix: '7',\n  name: 'base8',\n  alphabet: '01234567',\n  bitsPerChar: 3\n});", "import { baseX } from './base.js';\nexport const base10 = baseX({\n  prefix: '9',\n  name: 'base10',\n  alphabet: '0123456789'\n});", "import { rfc4648 } from './base.js';\nexport const base16 = rfc4648({\n  prefix: 'f',\n  name: 'base16',\n  alphabet: '0123456789abcdef',\n  bitsPerChar: 4\n});\nexport const base16upper = rfc4648({\n  prefix: 'F',\n  name: 'base16upper',\n  alphabet: '0123456789ABCDEF',\n  bitsPerChar: 4\n});", "import { rfc4648 } from './base.js';\nexport const base32 = rfc4648({\n  prefix: 'b',\n  name: 'base32',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567',\n  bitsPerChar: 5\n});\nexport const base32upper = rfc4648({\n  prefix: 'B',\n  name: 'base32upper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567',\n  bitsPerChar: 5\n});\nexport const base32pad = rfc4648({\n  prefix: 'c',\n  name: 'base32pad',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567=',\n  bitsPerChar: 5\n});\nexport const base32padupper = rfc4648({\n  prefix: 'C',\n  name: 'base32padupper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=',\n  bitsPerChar: 5\n});\nexport const base32hex = rfc4648({\n  prefix: 'v',\n  name: 'base32hex',\n  alphabet: '0123456789abcdefghijklmnopqrstuv',\n  bitsPerChar: 5\n});\nexport const base32hexupper = rfc4648({\n  prefix: 'V',\n  name: 'base32hexupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV',\n  bitsPerChar: 5\n});\nexport const base32hexpad = rfc4648({\n  prefix: 't',\n  name: 'base32hexpad',\n  alphabet: '0123456789abcdefghijklmnopqrstuv=',\n  bitsPerChar: 5\n});\nexport const base32hexpadupper = rfc4648({\n  prefix: 'T',\n  name: 'base32hexpadupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV=',\n  bitsPerChar: 5\n});\nexport const base32z = rfc4648({\n  prefix: 'h',\n  name: 'base32z',\n  alphabet: 'ybndrfg8ejkmcpqxot1uwisza345h769',\n  bitsPerChar: 5\n});", "import { baseX } from './base.js';\nexport const base36 = baseX({\n  prefix: 'k',\n  name: 'base36',\n  alphabet: '0123456789abcdefghijklmnopqrstuvwxyz'\n});\nexport const base36upper = baseX({\n  prefix: 'K',\n  name: 'base36upper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'\n});", "import { baseX } from './base.js';\nexport const base58btc = baseX({\n  name: 'base58btc',\n  prefix: 'z',\n  alphabet: '**********************************************************'\n});\nexport const base58flickr = baseX({\n  name: 'base58flickr',\n  prefix: 'Z',\n  alphabet: '**********************************************************'\n});", "import { rfc4648 } from './base.js';\nexport const base64 = rfc4648({\n  prefix: 'm',\n  name: 'base64',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',\n  bitsPerChar: 6\n});\nexport const base64pad = rfc4648({\n  prefix: 'M',\n  name: 'base64pad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',\n  bitsPerChar: 6\n});\nexport const base64url = rfc4648({\n  prefix: 'u',\n  name: 'base64url',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_',\n  bitsPerChar: 6\n});\nexport const base64urlpad = rfc4648({\n  prefix: 'U',\n  name: 'base64urlpad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=',\n  bitsPerChar: 6\n});", "import { from } from './base.js';\nconst alphabet = Array.from('\\uD83D\\uDE80\\uD83E\\uDE90\\u2604\\uD83D\\uDEF0\\uD83C\\uDF0C\\uD83C\\uDF11\\uD83C\\uDF12\\uD83C\\uDF13\\uD83C\\uDF14\\uD83C\\uDF15\\uD83C\\uDF16\\uD83C\\uDF17\\uD83C\\uDF18\\uD83C\\uDF0D\\uD83C\\uDF0F\\uD83C\\uDF0E\\uD83D\\uDC09\\u2600\\uD83D\\uDCBB\\uD83D\\uDDA5\\uD83D\\uDCBE\\uD83D\\uDCBF\\uD83D\\uDE02\\u2764\\uD83D\\uDE0D\\uD83E\\uDD23\\uD83D\\uDE0A\\uD83D\\uDE4F\\uD83D\\uDC95\\uD83D\\uDE2D\\uD83D\\uDE18\\uD83D\\uDC4D\\uD83D\\uDE05\\uD83D\\uDC4F\\uD83D\\uDE01\\uD83D\\uDD25\\uD83E\\uDD70\\uD83D\\uDC94\\uD83D\\uDC96\\uD83D\\uDC99\\uD83D\\uDE22\\uD83E\\uDD14\\uD83D\\uDE06\\uD83D\\uDE44\\uD83D\\uDCAA\\uD83D\\uDE09\\u263A\\uD83D\\uDC4C\\uD83E\\uDD17\\uD83D\\uDC9C\\uD83D\\uDE14\\uD83D\\uDE0E\\uD83D\\uDE07\\uD83C\\uDF39\\uD83E\\uDD26\\uD83C\\uDF89\\uD83D\\uDC9E\\u270C\\u2728\\uD83E\\uDD37\\uD83D\\uDE31\\uD83D\\uDE0C\\uD83C\\uDF38\\uD83D\\uDE4C\\uD83D\\uDE0B\\uD83D\\uDC97\\uD83D\\uDC9A\\uD83D\\uDE0F\\uD83D\\uDC9B\\uD83D\\uDE42\\uD83D\\uDC93\\uD83E\\uDD29\\uD83D\\uDE04\\uD83D\\uDE00\\uD83D\\uDDA4\\uD83D\\uDE03\\uD83D\\uDCAF\\uD83D\\uDE48\\uD83D\\uDC47\\uD83C\\uDFB6\\uD83D\\uDE12\\uD83E\\uDD2D\\u2763\\uD83D\\uDE1C\\uD83D\\uDC8B\\uD83D\\uDC40\\uD83D\\uDE2A\\uD83D\\uDE11\\uD83D\\uDCA5\\uD83D\\uDE4B\\uD83D\\uDE1E\\uD83D\\uDE29\\uD83D\\uDE21\\uD83E\\uDD2A\\uD83D\\uDC4A\\uD83E\\uDD73\\uD83D\\uDE25\\uD83E\\uDD24\\uD83D\\uDC49\\uD83D\\uDC83\\uD83D\\uDE33\\u270B\\uD83D\\uDE1A\\uD83D\\uDE1D\\uD83D\\uDE34\\uD83C\\uDF1F\\uD83D\\uDE2C\\uD83D\\uDE43\\uD83C\\uDF40\\uD83C\\uDF37\\uD83D\\uDE3B\\uD83D\\uDE13\\u2B50\\u2705\\uD83E\\uDD7A\\uD83C\\uDF08\\uD83D\\uDE08\\uD83E\\uDD18\\uD83D\\uDCA6\\u2714\\uD83D\\uDE23\\uD83C\\uDFC3\\uD83D\\uDC90\\u2639\\uD83C\\uDF8A\\uD83D\\uDC98\\uD83D\\uDE20\\u261D\\uD83D\\uDE15\\uD83C\\uDF3A\\uD83C\\uDF82\\uD83C\\uDF3B\\uD83D\\uDE10\\uD83D\\uDD95\\uD83D\\uDC9D\\uD83D\\uDE4A\\uD83D\\uDE39\\uD83D\\uDDE3\\uD83D\\uDCAB\\uD83D\\uDC80\\uD83D\\uDC51\\uD83C\\uDFB5\\uD83E\\uDD1E\\uD83D\\uDE1B\\uD83D\\uDD34\\uD83D\\uDE24\\uD83C\\uDF3C\\uD83D\\uDE2B\\u26BD\\uD83E\\uDD19\\u2615\\uD83C\\uDFC6\\uD83E\\uDD2B\\uD83D\\uDC48\\uD83D\\uDE2E\\uD83D\\uDE46\\uD83C\\uDF7B\\uD83C\\uDF43\\uD83D\\uDC36\\uD83D\\uDC81\\uD83D\\uDE32\\uD83C\\uDF3F\\uD83E\\uDDE1\\uD83C\\uDF81\\u26A1\\uD83C\\uDF1E\\uD83C\\uDF88\\u274C\\u270A\\uD83D\\uDC4B\\uD83D\\uDE30\\uD83E\\uDD28\\uD83D\\uDE36\\uD83E\\uDD1D\\uD83D\\uDEB6\\uD83D\\uDCB0\\uD83C\\uDF53\\uD83D\\uDCA2\\uD83E\\uDD1F\\uD83D\\uDE41\\uD83D\\uDEA8\\uD83D\\uDCA8\\uD83E\\uDD2C\\u2708\\uD83C\\uDF80\\uD83C\\uDF7A\\uD83E\\uDD13\\uD83D\\uDE19\\uD83D\\uDC9F\\uD83C\\uDF31\\uD83D\\uDE16\\uD83D\\uDC76\\uD83E\\uDD74\\u25B6\\u27A1\\u2753\\uD83D\\uDC8E\\uD83D\\uDCB8\\u2B07\\uD83D\\uDE28\\uD83C\\uDF1A\\uD83E\\uDD8B\\uD83D\\uDE37\\uD83D\\uDD7A\\u26A0\\uD83D\\uDE45\\uD83D\\uDE1F\\uD83D\\uDE35\\uD83D\\uDC4E\\uD83E\\uDD32\\uD83E\\uDD20\\uD83E\\uDD27\\uD83D\\uDCCC\\uD83D\\uDD35\\uD83D\\uDC85\\uD83E\\uDDD0\\uD83D\\uDC3E\\uD83C\\uDF52\\uD83D\\uDE17\\uD83E\\uDD11\\uD83C\\uDF0A\\uD83E\\uDD2F\\uD83D\\uDC37\\u260E\\uD83D\\uDCA7\\uD83D\\uDE2F\\uD83D\\uDC86\\uD83D\\uDC46\\uD83C\\uDFA4\\uD83D\\uDE47\\uD83C\\uDF51\\u2744\\uD83C\\uDF34\\uD83D\\uDCA3\\uD83D\\uDC38\\uD83D\\uDC8C\\uD83D\\uDCCD\\uD83E\\uDD40\\uD83E\\uDD22\\uD83D\\uDC45\\uD83D\\uDCA1\\uD83D\\uDCA9\\uD83D\\uDC50\\uD83D\\uDCF8\\uD83D\\uDC7B\\uD83E\\uDD10\\uD83E\\uDD2E\\uD83C\\uDFBC\\uD83E\\uDD75\\uD83D\\uDEA9\\uD83C\\uDF4E\\uD83C\\uDF4A\\uD83D\\uDC7C\\uD83D\\uDC8D\\uD83D\\uDCE3\\uD83E\\uDD42');\nconst alphabetBytesToChars = alphabet.reduce((p, c, i) => {\n  p[i] = c;\n  return p;\n}, []);\nconst alphabetCharsToBytes = alphabet.reduce((p, c, i) => {\n  p[c.codePointAt(0)] = i;\n  return p;\n}, []);\nfunction encode(data) {\n  return data.reduce((p, c) => {\n    p += alphabetBytesToChars[c];\n    return p;\n  }, '');\n}\nfunction decode(str) {\n  const byts = [];\n  for (const char of str) {\n    const byt = alphabetCharsToBytes[char.codePointAt(0)];\n    if (byt === undefined) {\n      throw new Error(`Non-base256emoji character: ${ char }`);\n    }\n    byts.push(byt);\n  }\n  return new Uint8Array(byts);\n}\nexport const base256emoji = from({\n  prefix: '\\uD83D\\uDE80',\n  name: 'base256emoji',\n  encode,\n  decode\n});", "var encode_1 = encode;\nvar MSB = 128, REST = 127, MSBALL = ~REST, INT = Math.pow(2, 31);\nfunction encode(num, out, offset) {\n  out = out || [];\n  offset = offset || 0;\n  var oldOffset = offset;\n  while (num >= INT) {\n    out[offset++] = num & 255 | MSB;\n    num /= 128;\n  }\n  while (num & MSBALL) {\n    out[offset++] = num & 255 | MSB;\n    num >>>= 7;\n  }\n  out[offset] = num | 0;\n  encode.bytes = offset - oldOffset + 1;\n  return out;\n}\nvar decode = read;\nvar MSB$1 = 128, REST$1 = 127;\nfunction read(buf, offset) {\n  var res = 0, offset = offset || 0, shift = 0, counter = offset, b, l = buf.length;\n  do {\n    if (counter >= l) {\n      read.bytes = 0;\n      throw new RangeError('Could not decode varint');\n    }\n    b = buf[counter++];\n    res += shift < 28 ? (b & REST$1) << shift : (b & REST$1) * Math.pow(2, shift);\n    shift += 7;\n  } while (b >= MSB$1);\n  read.bytes = counter - offset;\n  return res;\n}\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\nvar length = function (value) {\n  return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};\nvar varint = {\n  encode: encode_1,\n  decode: decode,\n  encodingLength: length\n};\nvar _brrp_varint = varint;\nexport default _brrp_varint;", "import varint from '../vendor/varint.js';\nexport const decode = (data, offset = 0) => {\n  const code = varint.decode(data, offset);\n  return [\n    code,\n    varint.decode.bytes\n  ];\n};\nexport const encodeTo = (int, target, offset = 0) => {\n  varint.encode(int, target, offset);\n  return target;\n};\nexport const encodingLength = int => {\n  return varint.encodingLength(int);\n};", "import {\n  coerce,\n  equals as equalBytes\n} from '../bytes.js';\nimport * as varint from '../varint.js';\nexport const create = (code, digest) => {\n  const size = digest.byteLength;\n  const sizeOffset = varint.encodingLength(code);\n  const digestOffset = sizeOffset + varint.encodingLength(size);\n  const bytes = new Uint8Array(digestOffset + size);\n  varint.encodeTo(code, bytes, 0);\n  varint.encodeTo(size, bytes, sizeOffset);\n  bytes.set(digest, digestOffset);\n  return new Digest(code, size, digest, bytes);\n};\nexport const decode = multihash => {\n  const bytes = coerce(multihash);\n  const [code, sizeOffset] = varint.decode(bytes);\n  const [size, digestOffset] = varint.decode(bytes.subarray(sizeOffset));\n  const digest = bytes.subarray(sizeOffset + digestOffset);\n  if (digest.byteLength !== size) {\n    throw new Error('Incorrect length');\n  }\n  return new Digest(code, size, digest, bytes);\n};\nexport const equals = (a, b) => {\n  if (a === b) {\n    return true;\n  } else {\n    return a.code === b.code && a.size === b.size && equalBytes(a.bytes, b.bytes);\n  }\n};\nexport class Digest {\n  constructor(code, size, digest, bytes) {\n    this.code = code;\n    this.size = size;\n    this.digest = digest;\n    this.bytes = bytes;\n  }\n}", "import * as Digest from './digest.js';\nexport const from = ({name, code, encode}) => new Hasher(name, code, encode);\nexport class Hasher {\n  constructor(name, code, encode) {\n    this.name = name;\n    this.code = code;\n    this.encode = encode;\n  }\n  digest(input) {\n    if (input instanceof Uint8Array) {\n      const result = this.encode(input);\n      return result instanceof Uint8Array ? Digest.create(this.code, result) : result.then(digest => Digest.create(this.code, digest));\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}", "import { from } from './hasher.js';\nconst sha = name => async data => new Uint8Array(await crypto.subtle.digest(name, data));\nexport const sha256 = from({\n  name: 'sha2-256',\n  code: 18,\n  encode: sha('SHA-256')\n});\nexport const sha512 = from({\n  name: 'sha2-512',\n  code: 19,\n  encode: sha('SHA-512')\n});", "import { coerce } from '../bytes.js';\nimport * as Digest from './digest.js';\nconst code = 0;\nconst name = 'identity';\nconst encode = coerce;\nconst digest = input => Digest.create(code, encode(input));\nexport const identity = {\n  code,\n  name,\n  encode,\n  digest\n};", "const textEncoder = new TextEncoder();\nconst textDecoder = new TextDecoder();\nexport const name = 'json';\nexport const code = 512;\nexport const encode = node => textEncoder.encode(JSON.stringify(node));\nexport const decode = data => JSON.parse(textDecoder.decode(data));", "import * as identityBase from './bases/identity.js';\nimport * as base2 from './bases/base2.js';\nimport * as base8 from './bases/base8.js';\nimport * as base10 from './bases/base10.js';\nimport * as base16 from './bases/base16.js';\nimport * as base32 from './bases/base32.js';\nimport * as base36 from './bases/base36.js';\nimport * as base58 from './bases/base58.js';\nimport * as base64 from './bases/base64.js';\nimport * as base256emoji from './bases/base256emoji.js';\nimport * as sha2 from './hashes/sha2.js';\nimport * as identity from './hashes/identity.js';\nimport * as raw from './codecs/raw.js';\nimport * as json from './codecs/json.js';\nimport {\n  CID,\n  hasher,\n  digest,\n  varint,\n  bytes\n} from './index.js';\nconst bases = {\n  ...identityBase,\n  ...base2,\n  ...base8,\n  ...base10,\n  ...base16,\n  ...base32,\n  ...base36,\n  ...base58,\n  ...base64,\n  ...base256emoji\n};\nconst hashes = {\n  ...sha2,\n  ...identity\n};\nconst codecs = {\n  raw,\n  json\n};\nexport {\n  CID,\n  hasher,\n  digest,\n  varint,\n  bytes,\n  hashes,\n  bases,\n  codecs\n};", "export function alloc(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.alloc != null) {\n    return globalThis.Buffer.alloc(size);\n  }\n  return new Uint8Array(size);\n}\nexport function allocUnsafe(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.allocUnsafe != null) {\n    return globalThis.Buffer.allocUnsafe(size);\n  }\n  return new Uint8Array(size);\n}", "import { bases } from 'multiformats/basics';\nimport { allocUnsafe } from '../alloc.js';\nfunction createCodec(name, prefix, encode, decode) {\n  return {\n    name,\n    prefix,\n    encoder: {\n      name,\n      prefix,\n      encode\n    },\n    decoder: { decode }\n  };\n}\nconst string = createCodec('utf8', 'u', buf => {\n  const decoder = new TextDecoder('utf8');\n  return 'u' + decoder.decode(buf);\n}, str => {\n  const encoder = new TextEncoder();\n  return encoder.encode(str.substring(1));\n});\nconst ascii = createCodec('ascii', 'a', buf => {\n  let string = 'a';\n  for (let i = 0; i < buf.length; i++) {\n    string += String.fromCharCode(buf[i]);\n  }\n  return string;\n}, str => {\n  str = str.substring(1);\n  const buf = allocUnsafe(str.length);\n  for (let i = 0; i < str.length; i++) {\n    buf[i] = str.charCodeAt(i);\n  }\n  return buf;\n});\nconst BASES = {\n  utf8: string,\n  'utf-8': string,\n  hex: bases.base16,\n  latin1: ascii,\n  ascii: ascii,\n  binary: ascii,\n  ...bases\n};\nexport default BASES;", "import bases from './util/bases.js';\nexport function fromString(string, encoding = 'utf8') {\n  const base = bases[encoding];\n  if (!base) {\n    throw new Error(`Unsupported encoding \"${ encoding }\"`);\n  }\n  if ((encoding === 'utf8' || encoding === 'utf-8') && globalThis.Buffer != null && globalThis.Buffer.from != null) {\n    return globalThis.Buffer.from(string, 'utf8');\n  }\n  return base.decoder.decode(`${ base.prefix }${ string }`);\n}", "import { generateChild<PERSON>ogger, getLog<PERSON><PERSON><PERSON>x<PERSON>, <PERSON><PERSON> } from \"@walletconnect/logger\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>hai<PERSON> } from \"@walletconnect/types\";\nimport { getInternalError, mapToObj, objToMap } from \"@walletconnect/utils\";\n\nimport { CORE_STORAGE_PREFIX, KEYCHAIN_CONTEXT, KEYCHAIN_STORAGE_VERSION } from \"../constants\";\n\nexport class Key<PERSON>hain implements IKeyChain {\n  public keychain = new Map<string, string>();\n  public name = KEYCHAIN_CONTEXT;\n  public version = KEYCHAIN_STORAGE_VERSION;\n\n  private initialized = false;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    this.core = core;\n    this.logger = generateChildLogger(logger, this.name);\n  }\n\n  public init: IKeyChain[\"init\"] = async () => {\n    if (!this.initialized) {\n      const keychain = await this.getKeyChain();\n      if (typeof keychain !== \"undefined\") {\n        this.keychain = keychain;\n      }\n      this.initialized = true;\n    }\n  };\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + this.name;\n  }\n\n  public has: IKeyChain[\"has\"] = (tag) => {\n    this.isInitialized();\n    return this.keychain.has(tag);\n  };\n\n  public set: IKeyChain[\"set\"] = async (tag, key) => {\n    this.isInitialized();\n    this.keychain.set(tag, key);\n    await this.persist();\n  };\n\n  public get: IKeyChain[\"get\"] = (tag) => {\n    this.isInitialized();\n    const key = this.keychain.get(tag);\n    if (typeof key === \"undefined\") {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `${this.name}: ${tag}`);\n      throw new Error(message);\n    }\n    return key;\n  };\n\n  public del: IKeyChain[\"del\"] = async (tag) => {\n    this.isInitialized();\n    this.keychain.delete(tag);\n    await this.persist();\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async setKeyChain(keychain: Map<string, string>) {\n    await this.core.storage.setItem<Record<string, string>>(this.storageKey, mapToObj(keychain));\n  }\n\n  private async getKeyChain() {\n    const keychain = await this.core.storage.getItem<Record<string, string>>(this.storageKey);\n    return typeof keychain !== \"undefined\" ? objToMap(keychain) : undefined;\n  }\n\n  private async persist() {\n    await this.setKeyChain(this.keychain);\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { generateChil<PERSON><PERSON>ogger, getLog<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from \"@walletconnect/logger\";\nimport { safeJsonParse, safeJsonStringify } from \"@walletconnect/safe-json\";\nimport { ICore, ICrypto, I<PERSON>ey<PERSON>hain } from \"@walletconnect/types\";\nimport * as relayAuth from \"@walletconnect/relay-auth\";\nimport { fromString } from \"uint8arrays/from-string\";\nimport {\n  decrypt,\n  deriveSymKey,\n  encrypt,\n  generateKeyPair as generateKeyPairUtil,\n  hashKey,\n  getInternalError,\n  generateRandomBytes32,\n  validateEncoding,\n  validateDecoding,\n  isTypeOneEnvelope,\n  isTypeTwoEnvelope,\n  encodeTypeTwoEnvelope,\n  decodeTypeTwoEnvelope,\n  deserialize,\n  decodeTypeByte,\n  BASE16,\n  BASE64,\n} from \"@walletconnect/utils\";\nimport { toString } from \"uint8arrays\";\n\nimport { CRYPTO_CONTEXT, CRYPTO_CLIENT_SEED, CRYPTO_JWT_TTL } from \"../constants\";\nimport { <PERSON><PERSON><PERSON><PERSON> } from \"./keychain\";\n\nexport class Crypto implements ICrypto {\n  public name = CRYPTO_CONTEXT;\n  public keychain: ICrypto[\"keychain\"];\n  public readonly randomSessionIdentifier = generateRandomBytes32();\n\n  private initialized = false;\n\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n    keychain?: IKeyChain,\n  ) {\n    this.core = core;\n    this.logger = generateChildLogger(logger, this.name);\n    this.keychain = keychain || new KeyChain(this.core, this.logger);\n  }\n\n  public init: ICrypto[\"init\"] = async () => {\n    if (!this.initialized) {\n      await this.keychain.init();\n      this.initialized = true;\n    }\n  };\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  public hasKeys: ICrypto[\"hasKeys\"] = (tag) => {\n    this.isInitialized();\n    return this.keychain.has(tag);\n  };\n\n  public getClientId: ICrypto[\"getClientId\"] = async () => {\n    this.isInitialized();\n    const seed = await this.getClientSeed();\n    const keyPair = relayAuth.generateKeyPair(seed);\n    const clientId = relayAuth.encodeIss(keyPair.publicKey);\n    return clientId;\n  };\n\n  public generateKeyPair: ICrypto[\"generateKeyPair\"] = () => {\n    this.isInitialized();\n    const keyPair = generateKeyPairUtil();\n    return this.setPrivateKey(keyPair.publicKey, keyPair.privateKey);\n  };\n\n  public signJWT: ICrypto[\"signJWT\"] = async (aud) => {\n    this.isInitialized();\n    const seed = await this.getClientSeed();\n    const keyPair = relayAuth.generateKeyPair(seed);\n    const sub = this.randomSessionIdentifier;\n    const ttl = CRYPTO_JWT_TTL;\n    const jwt = await relayAuth.signJWT(sub, aud, ttl, keyPair);\n    return jwt;\n  };\n\n  public generateSharedKey: ICrypto[\"generateSharedKey\"] = (\n    selfPublicKey,\n    peerPublicKey,\n    overrideTopic,\n  ) => {\n    this.isInitialized();\n    const selfPrivateKey = this.getPrivateKey(selfPublicKey);\n    const symKey = deriveSymKey(selfPrivateKey, peerPublicKey);\n    return this.setSymKey(symKey, overrideTopic);\n  };\n\n  public setSymKey: ICrypto[\"setSymKey\"] = async (symKey, overrideTopic) => {\n    this.isInitialized();\n    const topic = overrideTopic || hashKey(symKey);\n    await this.keychain.set(topic, symKey);\n    return topic;\n  };\n\n  public deleteKeyPair: ICrypto[\"deleteKeyPair\"] = async (publicKey: string) => {\n    this.isInitialized();\n    await this.keychain.del(publicKey);\n  };\n\n  public deleteSymKey: ICrypto[\"deleteSymKey\"] = async (topic: string) => {\n    this.isInitialized();\n    await this.keychain.del(topic);\n  };\n\n  public encode: ICrypto[\"encode\"] = async (topic, payload, opts) => {\n    this.isInitialized();\n    const params = validateEncoding(opts);\n    const message = safeJsonStringify(payload);\n\n    if (isTypeTwoEnvelope(params)) {\n      return encodeTypeTwoEnvelope(message, opts?.encoding);\n    }\n\n    if (isTypeOneEnvelope(params)) {\n      const selfPublicKey = params.senderPublicKey;\n      const peerPublicKey = params.receiverPublicKey;\n      topic = await this.generateSharedKey(selfPublicKey, peerPublicKey);\n    }\n    const symKey = this.getSymKey(topic);\n    const { type, senderPublicKey } = params;\n    const result = encrypt({ type, symKey, message, senderPublicKey, encoding: opts?.encoding });\n    return result;\n  };\n\n  public decode: ICrypto[\"decode\"] = async (topic, encoded, opts) => {\n    this.isInitialized();\n    const params = validateDecoding(encoded, opts);\n    if (isTypeTwoEnvelope(params)) {\n      const message = decodeTypeTwoEnvelope(encoded, opts?.encoding);\n      return safeJsonParse(message);\n    }\n    if (isTypeOneEnvelope(params)) {\n      const selfPublicKey = params.receiverPublicKey;\n      const peerPublicKey = params.senderPublicKey;\n      topic = await this.generateSharedKey(selfPublicKey, peerPublicKey);\n    }\n    try {\n      const symKey = this.getSymKey(topic);\n      const message = decrypt({ symKey, encoded, encoding: opts?.encoding });\n      const payload = safeJsonParse(message);\n      return payload;\n    } catch (error) {\n      this.logger.error(\n        `Failed to decode message from topic: '${topic}', clientId: '${await this.getClientId()}'`,\n      );\n      this.logger.error(error);\n    }\n  };\n\n  public getPayloadType: ICrypto[\"getPayloadType\"] = (encoded, encoding = BASE64) => {\n    const deserialized = deserialize({ encoded, encoding });\n    return decodeTypeByte(deserialized.type);\n  };\n\n  public getPayloadSenderPublicKey: ICrypto[\"getPayloadSenderPublicKey\"] = (\n    encoded,\n    encoding = BASE64,\n  ) => {\n    const deserialized = deserialize({ encoded, encoding });\n    return deserialized.senderPublicKey\n      ? toString(deserialized.senderPublicKey, BASE16)\n      : undefined;\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async setPrivateKey(publicKey: string, privateKey: string): Promise<string> {\n    await this.keychain.set(publicKey, privateKey);\n    return publicKey;\n  }\n\n  private getPrivateKey(publicKey: string) {\n    const privateKey = this.keychain.get(publicKey);\n    return privateKey;\n  }\n\n  private async getClientSeed(): Promise<Uint8Array> {\n    let seed = \"\";\n    try {\n      seed = this.keychain.get(CRYPTO_CLIENT_SEED);\n    } catch {\n      seed = generateRandomBytes32();\n      await this.keychain.set(CRYPTO_CLIENT_SEED, seed);\n    }\n    return fromString(seed, \"base16\");\n  }\n\n  private getSymKey(topic: string) {\n    const symKey = this.keychain.get(topic);\n    return symKey;\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { generateChildLogger, getLogger<PERSON><PERSON>x<PERSON>, Lo<PERSON> } from \"@walletconnect/logger\";\nimport { <PERSON><PERSON><PERSON>, IMessageTracker, MessageRecord } from \"@walletconnect/types\";\nimport { hashMessage, mapToObj, objToMap, getInternalError } from \"@walletconnect/utils\";\nimport {\n  CORE_STORAGE_PREFIX,\n  MESSAGE_DIRECTION,\n  MESSAGES_CONTEXT,\n  MESSAGES_STORAGE_VERSION,\n} from \"../constants\";\n\nexport class MessageTracker extends IMessageTracker {\n  public messages = new Map<string, MessageRecord>();\n  /**\n   * stores messages that have not been acknowledged by the implementing client\n   * this is used to prevent losing messages in race conditions such as\n   * when a message is received by the relayer before the implementing client is ready to receive it\n   */\n  public messagesWithoutClientAck = new Map<string, MessageRecord>();\n  public name = MESSAGES_CONTEXT;\n  public version = MESSAGES_STORAGE_VERSION;\n\n  private initialized = false;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n\n  constructor(\n    public logger: Logger,\n    public core: ICore,\n  ) {\n    super(logger, core);\n    this.logger = generateChildLogger(logger, this.name);\n    this.core = core;\n  }\n\n  public init: IMessageTracker[\"init\"] = async () => {\n    if (!this.initialized) {\n      this.logger.trace(`Initialized`);\n      try {\n        const messages = await this.getRelayerMessages();\n        if (typeof messages !== \"undefined\") {\n          this.messages = messages;\n        }\n        const messagesWithoutClientAck = await this.getRelayerMessagesWithoutClientAck();\n        if (typeof messagesWithoutClientAck !== \"undefined\") {\n          this.messagesWithoutClientAck = messagesWithoutClientAck;\n        }\n        this.logger.debug(`Successfully Restored records for ${this.name}`);\n        this.logger.trace({ type: \"method\", method: \"restore\", size: this.messages.size });\n      } catch (e) {\n        this.logger.debug(`Failed to Restore records for ${this.name}`);\n        this.logger.error(e as any);\n      } finally {\n        this.initialized = true;\n      }\n    }\n  };\n\n  get context(): string {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + this.name;\n  }\n\n  get storageKeyWithoutClientAck() {\n    return (\n      this.storagePrefix +\n      this.version +\n      this.core.customStoragePrefix +\n      \"//\" +\n      this.name +\n      \"_withoutClientAck\"\n    );\n  }\n\n  public set: IMessageTracker[\"set\"] = async (topic, message, direction) => {\n    this.isInitialized();\n    const hash = hashMessage(message);\n    let messages = this.messages.get(topic);\n    if (typeof messages === \"undefined\") {\n      messages = {};\n    }\n    if (typeof messages[hash] !== \"undefined\") {\n      return hash;\n    }\n    messages[hash] = message;\n    this.messages.set(topic, messages);\n    // Only store messages without client ack for inbound messages\n    if (direction === MESSAGE_DIRECTION.inbound) {\n      const messagesWithoutClientAck = this.messagesWithoutClientAck.get(topic) || {};\n      this.messagesWithoutClientAck.set(topic, {\n        ...messagesWithoutClientAck,\n        [hash]: message,\n      });\n    }\n\n    await this.persist();\n    return hash;\n  };\n\n  public get: IMessageTracker[\"get\"] = (topic) => {\n    this.isInitialized();\n    let messages = this.messages.get(topic);\n    if (typeof messages === \"undefined\") {\n      messages = {};\n    }\n    return messages;\n  };\n\n  public getWithoutAck: IMessageTracker[\"getWithoutAck\"] = (topics) => {\n    this.isInitialized();\n    const messages: Record<string, string[]> = {};\n    for (const topic of topics) {\n      const messagesWithoutClientAck = this.messagesWithoutClientAck.get(topic) || {};\n      messages[topic] = Object.values(messagesWithoutClientAck);\n    }\n    return messages;\n  };\n\n  public has: IMessageTracker[\"has\"] = (topic, message) => {\n    this.isInitialized();\n    const messages = this.get(topic);\n    const hash = hashMessage(message);\n    return typeof messages[hash] !== \"undefined\";\n  };\n\n  public ack: IMessageTracker[\"ack\"] = async (topic, message) => {\n    this.isInitialized();\n    const messages = this.messagesWithoutClientAck.get(topic);\n    if (typeof messages === \"undefined\") {\n      return;\n    }\n\n    const hash = hashMessage(message);\n\n    delete messages[hash];\n    if (Object.keys(messages).length === 0) {\n      this.messagesWithoutClientAck.delete(topic);\n    } else {\n      this.messagesWithoutClientAck.set(topic, messages);\n    }\n    await this.persist();\n  };\n\n  public del: IMessageTracker[\"del\"] = async (topic) => {\n    this.isInitialized();\n    this.messages.delete(topic);\n    this.messagesWithoutClientAck.delete(topic);\n    await this.persist();\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async setRelayerMessages(messages: Map<string, MessageRecord>): Promise<void> {\n    await this.core.storage.setItem<Record<string, MessageRecord>>(\n      this.storageKey,\n      mapToObj(messages),\n    );\n  }\n\n  private async setRelayerMessagesWithoutClientAck(\n    messages: Map<string, MessageRecord>,\n  ): Promise<void> {\n    await this.core.storage.setItem<Record<string, MessageRecord>>(\n      this.storageKeyWithoutClientAck,\n      mapToObj(messages),\n    );\n  }\n\n  private async getRelayerMessages(): Promise<Map<string, MessageRecord> | undefined> {\n    const messages = await this.core.storage.getItem<Record<string, MessageRecord>>(\n      this.storageKey,\n    );\n    return typeof messages !== \"undefined\" ? objToMap(messages) : undefined;\n  }\n\n  private async getRelayerMessagesWithoutClientAck(): Promise<\n    Map<string, MessageRecord> | undefined\n  > {\n    const messages = await this.core.storage.getItem<Record<string, MessageRecord>>(\n      this.storageKeyWithoutClientAck,\n    );\n    return typeof messages !== \"undefined\" ? objToMap(messages) : undefined;\n  }\n\n  private async persist() {\n    await this.setRelayerMessages(this.messages);\n    await this.setRelayerMessagesWithoutClientAck(this.messagesWithoutClientAck);\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { HEARTBEAT_EVENTS } from \"@walletconnect/heartbeat\";\nimport { JsonRpcPayload, RequestArguments } from \"@walletconnect/jsonrpc-types\";\nimport { generateChildLogger, getLoggerContext, Logger } from \"@walletconnect/logger\";\nimport { RelayJsonRpc } from \"@walletconnect/relay-api\";\nimport { IPublisher, I<PERSON><PERSON>yer, PublisherTypes, RelayerTypes } from \"@walletconnect/types\";\nimport {\n  getRelayProtocolApi,\n  getRelayProtocolName,\n  isUndefined,\n  createExpiringPromise,\n} from \"@walletconnect/utils\";\nimport { EventEmitter } from \"events\";\n\nimport { PUBLISHER_CONTEXT, PUBLISHER_DEFAULT_TTL, RELAYER_EVENTS } from \"../constants\";\nimport { getBigIntRpcId } from \"@walletconnect/jsonrpc-utils\";\nimport { ONE_MINUTE, ONE_SECOND, toMiliseconds } from \"@walletconnect/time\";\n\ntype IPublishType = PublisherTypes.Params & {\n  attestation?: string;\n  attempt: number;\n};\nexport class Publisher extends IPublisher {\n  public events = new EventEmitter();\n  public name = PUBLISHER_CONTEXT;\n  public queue = new Map<string, IPublishType>();\n\n  private publishTimeout = toMiliseconds(ONE_MINUTE);\n  private initialPublishTimeout = toMiliseconds(ONE_SECOND * 15);\n  private needsTransportRestart = false;\n\n  constructor(\n    public relayer: IRelayer,\n    public logger: Logger,\n  ) {\n    super(relayer, logger);\n    this.relayer = relayer;\n    this.logger = generateChildLogger(logger, this.name);\n    this.registerEventListeners();\n  }\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  public publish: IPublisher[\"publish\"] = async (topic, message, opts) => {\n    this.logger.debug(`Publishing Payload`);\n    this.logger.trace({ type: \"method\", method: \"publish\", params: { topic, message, opts } });\n\n    const ttl = opts?.ttl || PUBLISHER_DEFAULT_TTL;\n    const relay = getRelayProtocolName(opts);\n    const prompt = opts?.prompt || false;\n    const tag = opts?.tag || 0;\n    const id = opts?.id || (getBigIntRpcId().toString() as any);\n    const params = {\n      topic,\n      message,\n      opts: {\n        ttl,\n        relay,\n        prompt,\n        tag,\n        id,\n        attestation: opts?.attestation,\n        tvf: opts?.tvf,\n      },\n    };\n    const failedPublishMessage = `Failed to publish payload, please try again. id:${id} tag:${tag}`;\n\n    try {\n      /**\n       * attempt to publish the payload for <initialPublishTimeout> seconds,\n       * if the publish fails, add the payload to the queue and it will be retried on every pulse\n       * until it is successfully published or <publishTimeout> seconds have passed\n       */\n      const publishPromise = new Promise(async (resolve) => {\n        const onPublish = ({ id }: { id: string }) => {\n          if (params.opts.id === id) {\n            this.removeRequestFromQueue(id);\n            this.relayer.events.removeListener(RELAYER_EVENTS.publish, onPublish);\n            resolve(params);\n          }\n        };\n        this.relayer.events.on(RELAYER_EVENTS.publish, onPublish);\n        const initialPublish = createExpiringPromise(\n          new Promise((resolve, reject) => {\n            this.rpcPublish({\n              topic,\n              message,\n              ttl,\n              prompt,\n              tag,\n              id,\n              attestation: opts?.attestation,\n              tvf: opts?.tvf,\n            })\n              .then(resolve)\n              .catch((e) => {\n                this.logger.warn(e, e?.message);\n                reject(e);\n              });\n          }),\n          this.initialPublishTimeout,\n          `Failed initial publish, retrying.... id:${id} tag:${tag}`,\n        );\n        try {\n          await initialPublish;\n          this.events.removeListener(RELAYER_EVENTS.publish, onPublish);\n        } catch (e) {\n          this.queue.set(id, { ...params, attempt: 1 });\n          this.logger.warn(e, (e as Error)?.message);\n        }\n      });\n      this.logger.trace({\n        type: \"method\",\n        method: \"publish\",\n        params: { id, topic, message, opts },\n      });\n\n      await createExpiringPromise(publishPromise, this.publishTimeout, failedPublishMessage);\n    } catch (e) {\n      this.logger.debug(`Failed to Publish Payload`);\n      this.logger.error(e as any);\n      if (opts?.internal?.throwOnFailedPublish) {\n        throw e;\n      }\n    } finally {\n      this.queue.delete(id);\n    }\n  };\n\n  public on: IPublisher[\"on\"] = (event, listener) => {\n    this.events.on(event, listener);\n  };\n\n  public once: IPublisher[\"once\"] = (event, listener) => {\n    this.events.once(event, listener);\n  };\n\n  public off: IPublisher[\"off\"] = (event, listener) => {\n    this.events.off(event, listener);\n  };\n\n  public removeListener: IPublisher[\"removeListener\"] = (event, listener) => {\n    this.events.removeListener(event, listener);\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async rpcPublish(params: {\n    topic: string;\n    message: string;\n    ttl?: number;\n    prompt?: boolean;\n    tag?: number;\n    id?: number;\n    attestation?: string;\n    tvf?: RelayerTypes.ITVF;\n  }) {\n    const {\n      topic,\n      message,\n      ttl = PUBLISHER_DEFAULT_TTL,\n      prompt,\n      tag,\n      id,\n      attestation,\n      tvf,\n    } = params;\n    const api = getRelayProtocolApi(getRelayProtocolName().protocol);\n    const request: RequestArguments<RelayJsonRpc.PublishParams> = {\n      method: api.publish,\n      params: {\n        topic,\n        message,\n        ttl,\n        prompt,\n        tag,\n        attestation,\n        ...tvf,\n      },\n      id,\n    };\n    if (isUndefined(request.params?.prompt)) delete request.params?.prompt;\n    if (isUndefined(request.params?.tag)) delete request.params?.tag;\n    this.logger.debug(`Outgoing Relay Payload`);\n    this.logger.trace({ type: \"message\", direction: \"outgoing\", request });\n    const result = await this.relayer.request(request);\n    this.relayer.events.emit(RELAYER_EVENTS.publish, params);\n    this.logger.debug(`Successfully Published Payload`);\n    return result;\n  }\n\n  private removeRequestFromQueue(id: string) {\n    this.queue.delete(id);\n  }\n\n  private checkQueue() {\n    this.queue.forEach(async (params, id) => {\n      const attempt = params.attempt + 1;\n      this.queue.set(id, { ...params, attempt });\n      const { topic, message, opts, attestation } = params;\n      this.logger.warn(\n        {},\n        `Publisher: queue->publishing: ${params.opts.id}, tag: ${params.opts.tag}, attempt: ${attempt}`,\n      );\n      await this.rpcPublish({\n        ...params,\n        topic,\n        message,\n        ttl: opts.ttl,\n        prompt: opts.prompt,\n        tag: opts.tag,\n        id: opts.id,\n        attestation,\n        tvf: opts.tvf,\n      });\n      this.logger.warn({}, `Publisher: queue->published: ${params.opts.id}`);\n    });\n  }\n\n  private registerEventListeners() {\n    this.relayer.core.heartbeat.on(HEARTBEAT_EVENTS.pulse, () => {\n      // restart the transport if needed\n      // queue will be processed on the next pulse\n      if (this.needsTransportRestart) {\n        this.needsTransportRestart = false;\n        this.relayer.events.emit(RELAYER_EVENTS.connection_stalled);\n        return;\n      }\n      this.checkQueue();\n    });\n    this.relayer.on(RELAYER_EVENTS.message_ack, (event: JsonRpcPayload) => {\n      this.removeRequestFromQueue(event.id.toString());\n    });\n  }\n}\n", "import { ISubscriberTopicMap } from \"@walletconnect/types\";\n\nexport class SubscriberTopicMap implements ISubscriberTopicMap {\n  public map = new Map<string, string[]>();\n\n  get topics(): string[] {\n    return Array.from(this.map.keys());\n  }\n\n  public set: ISubscriberTopicMap[\"set\"] = (topic, id) => {\n    const ids = this.get(topic);\n    if (this.exists(topic, id)) return;\n    this.map.set(topic, [...ids, id]);\n  };\n\n  public get: ISubscriberTopicMap[\"get\"] = (topic) => {\n    const ids = this.map.get(topic);\n    return ids || [];\n  };\n\n  public exists: ISubscriberTopicMap[\"exists\"] = (topic, id) => {\n    const ids = this.get(topic);\n    return ids.includes(id);\n  };\n\n  public delete: ISubscriberTopicMap[\"delete\"] = (topic, id) => {\n    if (typeof id === \"undefined\") {\n      this.map.delete(topic);\n      return;\n    }\n    if (!this.map.has(topic)) return;\n    const ids = this.get(topic);\n    if (!this.exists(topic, id)) return;\n    const remaining = ids.filter((x) => x !== id);\n    if (!remaining.length) {\n      this.map.delete(topic);\n      return;\n    }\n    this.map.set(topic, remaining);\n  };\n\n  public clear: ISubscriberTopicMap[\"clear\"] = () => {\n    this.map.clear();\n  };\n}\n", "import { EventEmitter } from \"events\";\nimport { HEARTBEAT_EVENTS } from \"@walletconnect/heartbeat\";\nimport { ErrorResponse, RequestArguments } from \"@walletconnect/jsonrpc-types\";\nimport { generateChildLogger, getLoggerContext, Logger } from \"@walletconnect/logger\";\nimport { RelayJsonRpc } from \"@walletconnect/relay-api\";\nimport { ONE_SECOND, ONE_MINUTE, toMiliseconds } from \"@walletconnect/time\";\nimport {\n  IRelayer,\n  ISubscriber,\n  RelayerTypes,\n  SubscriberEvents,\n  SubscriberTypes,\n} from \"@walletconnect/types\";\nimport {\n  getSdkError,\n  getInternalError,\n  getRelayProtocolApi,\n  getRelayProtocolName,\n  createExpiringPromise,\n  hashMessage,\n  sleep,\n} from \"@walletconnect/utils\";\nimport {\n  CORE_STORAGE_PREFIX,\n  SUBSCRIBER_CONTEXT,\n  SUBSCRIBER_EVENTS,\n  SUBSCRIBER_STORAGE_VERSION,\n  REL<PERSON>YER_EVENTS,\n  TRANSPORT_TYPES,\n} from \"../constants\";\nimport { SubscriberTopicMap } from \"./topicmap\";\n\nexport class Subscriber extends ISubscriber {\n  public subscriptions = new Map<string, SubscriberTypes.Active>();\n  public topicMap = new SubscriberTopicMap();\n  public events = new EventEmitter();\n  public name = SUBSCRIBER_CONTEXT;\n  public version = SUBSCRIBER_STORAGE_VERSION;\n  public pending = new Map<string, SubscriberTypes.Params>();\n\n  private cached: SubscriberTypes.Active[] = [];\n  private initialized = false;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n  private subscribeTimeout = toMiliseconds(ONE_MINUTE);\n  private initialSubscribeTimeout = toMiliseconds(ONE_SECOND * 15);\n  private clientId: string;\n  private batchSubscribeTopicsLimit = 500;\n\n  constructor(\n    public relayer: IRelayer,\n    public logger: Logger,\n  ) {\n    super(relayer, logger);\n    this.relayer = relayer;\n    this.logger = generateChildLogger(logger, this.name);\n    this.clientId = \"\"; // assigned when calling this.getClientId()\n  }\n\n  public init: ISubscriber[\"init\"] = async () => {\n    if (!this.initialized) {\n      this.logger.trace(`Initialized`);\n      this.registerEventListeners();\n      await this.restore();\n    }\n    this.initialized = true;\n  };\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return (\n      this.storagePrefix + this.version + this.relayer.core.customStoragePrefix + \"//\" + this.name\n    );\n  }\n\n  get length() {\n    return this.subscriptions.size;\n  }\n\n  get ids() {\n    return Array.from(this.subscriptions.keys());\n  }\n\n  get values() {\n    return Array.from(this.subscriptions.values());\n  }\n\n  get topics() {\n    return this.topicMap.topics;\n  }\n\n  get hasAnyTopics() {\n    return (\n      this.topicMap.topics.length > 0 ||\n      this.pending.size > 0 ||\n      this.cached.length > 0 ||\n      this.subscriptions.size > 0\n    );\n  }\n\n  public subscribe: ISubscriber[\"subscribe\"] = async (topic, opts) => {\n    this.isInitialized();\n    this.logger.debug(`Subscribing Topic`);\n    this.logger.trace({ type: \"method\", method: \"subscribe\", params: { topic, opts } });\n    try {\n      const relay = getRelayProtocolName(opts);\n      const params = { topic, relay, transportType: opts?.transportType };\n      this.pending.set(topic, params);\n      const id = await this.rpcSubscribe(topic, relay, opts);\n      if (typeof id === \"string\") {\n        this.onSubscribe(id, params);\n        this.logger.debug(`Successfully Subscribed Topic`);\n        this.logger.trace({ type: \"method\", method: \"subscribe\", params: { topic, opts } });\n      }\n      return id;\n    } catch (e) {\n      this.logger.debug(`Failed to Subscribe Topic`);\n      this.logger.error(e as any);\n      throw e;\n    }\n  };\n\n  public unsubscribe: ISubscriber[\"unsubscribe\"] = async (topic, opts) => {\n    this.isInitialized();\n    if (typeof opts?.id !== \"undefined\") {\n      await this.unsubscribeById(topic, opts.id, opts);\n    } else {\n      await this.unsubscribeByTopic(topic, opts);\n    }\n  };\n\n  /**\n   * returns `true` only if the topic is actively subscribed to i.e. not pending or cached\n   */\n  public isSubscribed: ISubscriber[\"isSubscribed\"] = (topic: string) => {\n    return new Promise((resolve) => {\n      resolve(this.topicMap.topics.includes(topic));\n    });\n  };\n\n  /**\n   * returns `true` if the topic is known to the subscriber i.e. it is actively subscribed, pending, cached or in the topic map\n   */\n  public isKnownTopic: ISubscriber[\"isKnownTopic\"] = (topic: string) => {\n    return new Promise((resolve) => {\n      resolve(\n        this.topicMap.topics.includes(topic) ||\n          this.pending.has(topic) ||\n          this.cached.some((s) => s.topic === topic),\n      );\n    });\n  };\n\n  public on: ISubscriber[\"on\"] = (event, listener) => {\n    this.events.on(event, listener);\n  };\n\n  public once: ISubscriber[\"once\"] = (event, listener) => {\n    this.events.once(event, listener);\n  };\n\n  public off: ISubscriber[\"off\"] = (event, listener) => {\n    this.events.off(event, listener);\n  };\n\n  public removeListener: ISubscriber[\"removeListener\"] = (event, listener) => {\n    this.events.removeListener(event, listener);\n  };\n\n  public start: ISubscriber[\"start\"] = async () => {\n    await this.onConnect();\n  };\n\n  public stop: ISubscriber[\"stop\"] = async () => {\n    await this.onDisconnect();\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private hasSubscription(id: string, topic: string) {\n    let result = false;\n    try {\n      const subscription = this.getSubscription(id);\n      result = subscription.topic === topic;\n    } catch (e) {\n      // ignore error\n    }\n    return result;\n  }\n\n  private reset() {\n    this.cached = [];\n    this.initialized = true;\n  }\n\n  private onDisable() {\n    // only write to this.cached if there are active subscriptions\n    // as this.cached can be overridden if onDisable is called multiple times\n    if (this.values.length > 0) {\n      this.cached = this.values;\n    }\n    this.subscriptions.clear();\n    this.topicMap.clear();\n  }\n\n  private async unsubscribeByTopic(topic: string, opts?: RelayerTypes.UnsubscribeOptions) {\n    const ids = this.topicMap.get(topic);\n    await Promise.all(ids.map(async (id) => await this.unsubscribeById(topic, id, opts)));\n  }\n\n  private async unsubscribeById(topic: string, id: string, opts?: RelayerTypes.UnsubscribeOptions) {\n    this.logger.debug(`Unsubscribing Topic`);\n    this.logger.trace({ type: \"method\", method: \"unsubscribe\", params: { topic, id, opts } });\n\n    try {\n      const relay = getRelayProtocolName(opts);\n      await this.restartToComplete({ topic, id, relay });\n      await this.rpcUnsubscribe(topic, id, relay);\n      const reason = getSdkError(\"USER_DISCONNECTED\", `${this.name}, ${topic}`);\n      await this.onUnsubscribe(topic, id, reason);\n      this.logger.debug(`Successfully Unsubscribed Topic`);\n      this.logger.trace({ type: \"method\", method: \"unsubscribe\", params: { topic, id, opts } });\n    } catch (e) {\n      this.logger.debug(`Failed to Unsubscribe Topic`);\n      this.logger.error(e as any);\n      throw e;\n    }\n  }\n\n  private async rpcSubscribe(\n    topic: string,\n    relay: RelayerTypes.ProtocolOptions,\n    opts?: RelayerTypes.SubscribeOptions,\n  ) {\n    if (!opts || opts?.transportType === TRANSPORT_TYPES.relay) {\n      await this.restartToComplete({ topic, id: topic, relay });\n    }\n    const api = getRelayProtocolApi(relay.protocol);\n    const request: RequestArguments<RelayJsonRpc.SubscribeParams> = {\n      method: api.subscribe,\n      params: {\n        topic,\n      },\n    };\n    this.logger.debug(`Outgoing Relay Payload`);\n    this.logger.trace({ type: \"payload\", direction: \"outgoing\", request });\n    const shouldThrow = opts?.internal?.throwOnFailedPublish;\n    try {\n      const subId = await this.getSubscriptionId(topic);\n      // in link mode, allow the app to update its network state (i.e. active airplane mode) with small delay before attempting to subscribe\n      if (opts?.transportType === TRANSPORT_TYPES.link_mode) {\n        setTimeout(() => {\n          if (this.relayer.connected || this.relayer.connecting) {\n            this.relayer.request(request).catch((e) => this.logger.warn(e));\n          }\n        }, toMiliseconds(ONE_SECOND));\n        return subId;\n      }\n      const subscribePromise = new Promise(async (resolve) => {\n        const onSubscribe = (subscription: SubscriberEvents.Created) => {\n          if (subscription.topic === topic) {\n            this.events.removeListener(SUBSCRIBER_EVENTS.created, onSubscribe);\n            resolve(subscription.id);\n          }\n        };\n        this.events.on(SUBSCRIBER_EVENTS.created, onSubscribe);\n        try {\n          const result = await createExpiringPromise(\n            new Promise((resolve, reject) => {\n              this.relayer\n                .request(request)\n                .catch((e) => {\n                  this.logger.warn(e, e?.message);\n                  reject(e);\n                })\n                .then(resolve);\n            }),\n            this.initialSubscribeTimeout,\n            `Subscribing to ${topic} failed, please try again`,\n          );\n          this.events.removeListener(SUBSCRIBER_EVENTS.created, onSubscribe);\n          resolve(result);\n        } catch (err) {}\n      });\n\n      const subscribe = createExpiringPromise(\n        subscribePromise,\n        this.subscribeTimeout,\n        `Subscribing to ${topic} failed, please try again`,\n      );\n\n      const result = await subscribe;\n      if (!result && shouldThrow) {\n        throw new Error(`Subscribing to ${topic} failed, please try again`);\n      }\n      // return null to indicate that the subscription failed\n      return result ? subId : null;\n    } catch (err) {\n      this.logger.debug(`Outgoing Relay Subscribe Payload stalled`);\n      this.relayer.events.emit(RELAYER_EVENTS.connection_stalled);\n      if (shouldThrow) {\n        throw err;\n      }\n    }\n    return null;\n  }\n\n  private async rpcBatchSubscribe(subscriptions: SubscriberTypes.Params[]) {\n    if (!subscriptions.length) return;\n    const relay = subscriptions[0].relay;\n    const api = getRelayProtocolApi(relay!.protocol);\n    const request: RequestArguments<RelayJsonRpc.BatchSubscribeParams> = {\n      method: api.batchSubscribe,\n      params: {\n        topics: subscriptions.map((s) => s.topic),\n      },\n    };\n    this.logger.debug(`Outgoing Relay Payload`);\n    this.logger.trace({ type: \"payload\", direction: \"outgoing\", request });\n    try {\n      const subscribe = await createExpiringPromise(\n        new Promise((resolve) => {\n          this.relayer\n            .request(request)\n            .catch((e) => this.logger.warn(e))\n            .then(resolve);\n        }),\n        this.subscribeTimeout,\n        \"rpcBatchSubscribe failed, please try again\",\n      );\n      await subscribe;\n    } catch (err) {\n      this.relayer.events.emit(RELAYER_EVENTS.connection_stalled);\n    }\n  }\n\n  private async rpcBatchFetchMessages(subscriptions: SubscriberTypes.Params[]) {\n    if (!subscriptions.length) return;\n    const relay = subscriptions[0].relay;\n    const api = getRelayProtocolApi(relay!.protocol);\n    const request: RequestArguments<RelayJsonRpc.BatchFetchMessagesParams> = {\n      method: api.batchFetchMessages,\n      params: {\n        topics: subscriptions.map((s) => s.topic),\n      },\n    };\n    this.logger.debug(`Outgoing Relay Payload`);\n    this.logger.trace({ type: \"payload\", direction: \"outgoing\", request });\n    let result;\n    try {\n      const fetchMessagesPromise = await createExpiringPromise(\n        new Promise((resolve, reject) => {\n          this.relayer\n            .request(request)\n            .catch((e) => {\n              this.logger.warn(e);\n              reject(e);\n            })\n            .then(resolve);\n        }),\n        this.subscribeTimeout,\n        \"rpcBatchFetchMessages failed, please try again\",\n      );\n      result = (await fetchMessagesPromise) as {\n        messages: RelayerTypes.MessageEvent[];\n      };\n    } catch (err) {\n      this.relayer.events.emit(RELAYER_EVENTS.connection_stalled);\n    }\n    return result;\n  }\n\n  private rpcUnsubscribe(topic: string, id: string, relay: RelayerTypes.ProtocolOptions) {\n    const api = getRelayProtocolApi(relay.protocol);\n    const request: RequestArguments<RelayJsonRpc.UnsubscribeParams> = {\n      method: api.unsubscribe,\n      params: {\n        topic,\n        id,\n      },\n    };\n    this.logger.debug(`Outgoing Relay Payload`);\n    this.logger.trace({ type: \"payload\", direction: \"outgoing\", request });\n    return this.relayer.request(request);\n  }\n\n  private onSubscribe(id: string, params: SubscriberTypes.Params) {\n    this.setSubscription(id, { ...params, id });\n    this.pending.delete(params.topic);\n  }\n\n  private onBatchSubscribe(subscriptions: SubscriberTypes.Active[]) {\n    if (!subscriptions.length) return;\n    subscriptions.forEach((subscription) => {\n      this.setSubscription(subscription.id, { ...subscription });\n      this.pending.delete(subscription.topic);\n    });\n  }\n\n  private async onUnsubscribe(topic: string, id: string, reason: ErrorResponse) {\n    this.events.removeAllListeners(id);\n    if (this.hasSubscription(id, topic)) {\n      this.deleteSubscription(id, reason);\n    }\n    await this.relayer.messages.del(topic);\n  }\n\n  private async setRelayerSubscriptions(subscriptions: SubscriberTypes.Active[]) {\n    await this.relayer.core.storage.setItem<SubscriberTypes.Active[]>(\n      this.storageKey,\n      subscriptions,\n    );\n  }\n\n  private async getRelayerSubscriptions() {\n    const subscriptions = await this.relayer.core.storage.getItem<SubscriberTypes.Active[]>(\n      this.storageKey,\n    );\n    return subscriptions;\n  }\n\n  private setSubscription(id: string, subscription: SubscriberTypes.Active) {\n    this.logger.debug(`Setting subscription`);\n    this.logger.trace({ type: \"method\", method: \"setSubscription\", id, subscription });\n    this.addSubscription(id, subscription);\n  }\n\n  private addSubscription(id: string, subscription: SubscriberTypes.Active) {\n    this.subscriptions.set(id, { ...subscription });\n    this.topicMap.set(subscription.topic, id);\n    this.events.emit(SUBSCRIBER_EVENTS.created, subscription);\n  }\n\n  private getSubscription(id: string) {\n    this.logger.debug(`Getting subscription`);\n    this.logger.trace({ type: \"method\", method: \"getSubscription\", id });\n    const subscription = this.subscriptions.get(id);\n    if (!subscription) {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `${this.name}: ${id}`);\n      throw new Error(message);\n    }\n    return subscription;\n  }\n\n  private deleteSubscription(id: string, reason: ErrorResponse) {\n    this.logger.debug(`Deleting subscription`);\n    this.logger.trace({ type: \"method\", method: \"deleteSubscription\", id, reason });\n    const subscription = this.getSubscription(id);\n    this.subscriptions.delete(id);\n    this.topicMap.delete(subscription.topic, id);\n    this.events.emit(SUBSCRIBER_EVENTS.deleted, {\n      ...subscription,\n      reason,\n    } as SubscriberEvents.Deleted);\n  }\n\n  private restart = async () => {\n    await this.restore();\n    await this.onRestart();\n  };\n\n  private async persist() {\n    await this.setRelayerSubscriptions(this.values);\n    this.events.emit(SUBSCRIBER_EVENTS.sync);\n  }\n\n  private async onRestart() {\n    if (this.cached.length) {\n      const subs = [...this.cached];\n      const numOfBatches = Math.ceil(this.cached.length / this.batchSubscribeTopicsLimit);\n      for (let i = 0; i < numOfBatches; i++) {\n        const batch = subs.splice(0, this.batchSubscribeTopicsLimit);\n        await this.batchSubscribe(batch);\n      }\n    }\n    this.events.emit(SUBSCRIBER_EVENTS.resubscribed);\n  }\n\n  private async restore() {\n    try {\n      const persisted = await this.getRelayerSubscriptions();\n      if (typeof persisted === \"undefined\") return;\n      if (!persisted.length) return;\n      if (this.subscriptions.size) {\n        const { message } = getInternalError(\"RESTORE_WILL_OVERRIDE\", this.name);\n        this.logger.error(message);\n        this.logger.error(`${this.name}: ${JSON.stringify(this.values)}`);\n        throw new Error(message);\n      }\n      this.cached = persisted;\n      this.logger.debug(`Successfully Restored subscriptions for ${this.name}`);\n      this.logger.trace({ type: \"method\", method: \"restore\", subscriptions: this.values });\n    } catch (e) {\n      this.logger.debug(`Failed to Restore subscriptions for ${this.name}`);\n      this.logger.error(e as any);\n    }\n  }\n\n  private async batchSubscribe(subscriptions: SubscriberTypes.Params[]) {\n    if (!subscriptions.length) return;\n\n    await this.rpcBatchSubscribe(subscriptions);\n    this.onBatchSubscribe(\n      await Promise.all(\n        subscriptions.map(async (s) => {\n          return { ...s, id: await this.getSubscriptionId(s.topic) };\n        }),\n      ),\n    );\n  }\n\n  // @ts-ignore\n  private async batchFetchMessages(subscriptions: SubscriberTypes.Params[]) {\n    if (!subscriptions.length) return;\n    this.logger.trace(`Fetching batch messages for ${subscriptions.length} subscriptions`);\n    const response = await this.rpcBatchFetchMessages(subscriptions);\n    if (response && response.messages) {\n      await sleep(toMiliseconds(ONE_SECOND));\n      await this.relayer.handleBatchMessageEvents(response.messages);\n    }\n  }\n\n  private async onConnect() {\n    await this.restart();\n    this.reset();\n  }\n\n  private onDisconnect() {\n    this.onDisable();\n  }\n\n  private checkPending = async () => {\n    if (this.pending.size === 0 && (!this.initialized || !this.relayer.connected)) {\n      return;\n    }\n    const pendingSubscriptions: SubscriberTypes.Params[] = [];\n    this.pending.forEach((params) => {\n      pendingSubscriptions.push(params);\n    });\n\n    await this.batchSubscribe(pendingSubscriptions);\n  };\n\n  private registerEventListeners = () => {\n    this.relayer.core.heartbeat.on(HEARTBEAT_EVENTS.pulse, async () => {\n      await this.checkPending();\n    });\n    this.events.on(SUBSCRIBER_EVENTS.created, async (createdEvent: SubscriberEvents.Created) => {\n      const eventName = SUBSCRIBER_EVENTS.created;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, data: createdEvent });\n      await this.persist();\n    });\n    this.events.on(SUBSCRIBER_EVENTS.deleted, async (deletedEvent: SubscriberEvents.Deleted) => {\n      const eventName = SUBSCRIBER_EVENTS.deleted;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, data: deletedEvent });\n      await this.persist();\n    });\n  };\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n\n  private async restartToComplete(subscription: SubscriberTypes.Active) {\n    if (!this.relayer.connected && !this.relayer.connecting) {\n      this.cached.push(subscription);\n      await this.relayer.transportOpen();\n    }\n  }\n\n  private async getClientId() {\n    if (!this.clientId) {\n      this.clientId = await this.relayer.core.crypto.getClientId();\n    }\n    return this.clientId;\n  }\n\n  private async getSubscriptionId(topic: string) {\n    return hashMessage(topic + (await this.getClientId()));\n  }\n}\n", "import { EventEmitter } from \"events\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport {\n  formatJsonRpcR<PERSON>ult,\n  getBigIntRpcId,\n  IJsonRpcProvider,\n  isJsonRpcRequest,\n  isJsonRpcResponse,\n  JsonRpcPayload,\n  JsonRpcRequest,\n  RequestArguments,\n} from \"@walletconnect/jsonrpc-utils\";\nimport WsConnection from \"@walletconnect/jsonrpc-ws-connection\";\nimport {\n  generateChildLogger,\n  getDefaultLoggerOptions,\n  getLoggerContext,\n  pino,\n  Logger,\n} from \"@walletconnect/logger\";\nimport { RelayJsonRpc } from \"@walletconnect/relay-api\";\nimport {\n  FIVE_MINUTES,\n  ONE_SECOND,\n  FIVE_SECONDS,\n  THIRTY_SECONDS,\n  toMiliseconds,\n} from \"@walletconnect/time\";\nimport {\n  ICore,\n  IMessageTracker,\n  IPublisher,\n  IR<PERSON>yer,\n  ISubscriber,\n  RelayerOptions,\n  RelayerTypes,\n  SubscriberTypes,\n} from \"@walletconnect/types\";\nimport {\n  createExpiringPromise,\n  formatRelayRpcUrl,\n  isOnline,\n  subscribeToNetworkChange,\n  getAppId,\n  isAndroid,\n  isIos,\n  getInternalError,\n  isNode,\n  calcExpiry,\n  isAppVisible,\n} from \"@walletconnect/utils\";\n\nimport { HEARTBEAT_EVENTS } from \"@walletconnect/heartbeat\";\n\nimport {\n  RELAYER_SDK_VERSION,\n  RELAYER_CONTEXT,\n  RELAYER_DEFAULT_LOGGER,\n  RELAYER_EVENTS,\n  RELAYER_PROVIDER_EVENTS,\n  RELAYER_SUBSCRIBER_SUFFIX,\n  RELAYER_DEFAULT_RELAY_URL,\n  SUBSCRIBER_EVENTS,\n  RELAYER_RECONNECT_TIMEOUT,\n  TRANSPORT_TYPES,\n  MESSAGE_DIRECTION,\n} from \"../constants\";\nimport { MessageTracker } from \"./messages\";\nimport { Publisher } from \"./publisher\";\nimport { Subscriber } from \"./subscriber\";\n\nexport class Relayer extends IRelayer {\n  public protocol = \"wc\";\n  public version = 2;\n\n  public core: ICore;\n  public logger: Logger;\n  public events = new EventEmitter();\n  public provider: IJsonRpcProvider;\n  public messages: IMessageTracker;\n  public subscriber: ISubscriber;\n  public publisher: IPublisher;\n  public name = RELAYER_CONTEXT;\n  public transportExplicitlyClosed = false;\n\n  private initialized = false;\n  private connectionAttemptInProgress = false;\n\n  private relayUrl: string;\n  private projectId: string | undefined;\n  private packageName: string | undefined;\n  private bundleId: string | undefined;\n  private hasExperiencedNetworkDisruption = false;\n  private pingTimeout: NodeJS.Timeout | undefined;\n  /**\n   * the relay pings the client 30 seconds after the last message was received\n   * meaning if we don't receive a message in 30 seconds, the connection can be considered dead\n   */\n  private heartBeatTimeout = toMiliseconds(THIRTY_SECONDS + FIVE_SECONDS);\n  private reconnectTimeout: NodeJS.Timeout | undefined;\n  private connectPromise: Promise<void> | undefined;\n  private reconnectInProgress = false;\n  private requestsInFlight: string[] = [];\n  private connectTimeout = toMiliseconds(ONE_SECOND * 15);\n  constructor(opts: RelayerOptions) {\n    super(opts);\n    this.core = opts.core;\n    this.logger =\n      typeof opts.logger !== \"undefined\" && typeof opts.logger !== \"string\"\n        ? generateChildLogger(opts.logger, this.name)\n        : pino(getDefaultLoggerOptions({ level: opts.logger || RELAYER_DEFAULT_LOGGER }));\n    this.messages = new MessageTracker(this.logger, opts.core);\n    this.subscriber = new Subscriber(this, this.logger);\n    this.publisher = new Publisher(this, this.logger);\n\n    this.relayUrl = opts?.relayUrl || RELAYER_DEFAULT_RELAY_URL;\n    this.projectId = opts.projectId;\n\n    if (isAndroid()) {\n      this.packageName = getAppId();\n    } else if (isIos()) {\n      this.bundleId = getAppId();\n    }\n\n    // re-assigned during init()\n    this.provider = {} as IJsonRpcProvider;\n  }\n\n  public async init() {\n    this.logger.trace(`Initialized`);\n    this.registerEventListeners();\n    await Promise.all([this.messages.init(), this.subscriber.init()]);\n    this.initialized = true;\n    if (this.subscriber.hasAnyTopics) {\n      try {\n        await this.transportOpen();\n      } catch (e) {\n        this.logger.warn(e, (e as Error)?.message);\n      }\n    }\n  }\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  get connected() {\n    // @ts-expect-error\n    return this.provider?.connection?.socket?.readyState === 1 || false;\n  }\n\n  get connecting() {\n    return (\n      // @ts-expect-error\n      this.provider?.connection?.socket?.readyState === 0 ||\n      this.connectPromise !== undefined ||\n      false\n    );\n  }\n\n  public async publish(topic: string, message: string, opts?: RelayerTypes.PublishOptions) {\n    this.isInitialized();\n    await this.publisher.publish(topic, message, opts);\n    await this.recordMessageEvent(\n      {\n        topic,\n        message,\n        // We don't have `publishedAt` from the relay server on outgoing, so use current time to satisfy type.\n        publishedAt: Date.now(),\n        transportType: TRANSPORT_TYPES.relay,\n      },\n      MESSAGE_DIRECTION.outbound,\n    );\n  }\n\n  public async subscribe(topic: string, opts?: RelayerTypes.SubscribeOptions) {\n    this.isInitialized();\n    if (!opts?.transportType || opts?.transportType === \"relay\") {\n      await this.toEstablishConnection();\n    }\n    // throw unless explicitly set to false\n    const shouldThrowOnFailure =\n      typeof opts?.internal?.throwOnFailedPublish === \"undefined\"\n        ? true\n        : opts?.internal?.throwOnFailedPublish;\n\n    let id = this.subscriber.topicMap.get(topic)?.[0] || \"\";\n    let resolvePromise: () => void;\n    const onSubCreated = (subscription: SubscriberTypes.Active) => {\n      if (subscription.topic === topic) {\n        this.subscriber.off(SUBSCRIBER_EVENTS.created, onSubCreated);\n        resolvePromise();\n      }\n    };\n\n    await Promise.all([\n      new Promise<void>((resolve) => {\n        resolvePromise = resolve;\n        this.subscriber.on(SUBSCRIBER_EVENTS.created, onSubCreated);\n      }),\n      new Promise<void>(async (resolve, reject) => {\n        const result = await this.subscriber\n          .subscribe(topic, {\n            internal: {\n              throwOnFailedPublish: shouldThrowOnFailure,\n            },\n            ...opts,\n          })\n          .catch((error) => {\n            if (shouldThrowOnFailure) {\n              reject(error);\n            }\n          });\n        id = result || id;\n        resolve();\n      }),\n    ]);\n    return id;\n  }\n\n  public request = async (request: RequestArguments<RelayJsonRpc.SubscribeParams>) => {\n    this.logger.debug(`Publishing Request Payload`);\n    const id = request.id || (getBigIntRpcId().toString() as any);\n    await this.toEstablishConnection();\n    try {\n      this.logger.trace(\n        {\n          id,\n          method: request.method,\n          topic: request.params?.topic,\n        },\n        \"relayer.request - publishing...\",\n      );\n      const tag = `${id}:${(request.params as any)?.tag || \"\"}`;\n      this.requestsInFlight.push(tag);\n      const result = await this.provider.request(request);\n      this.requestsInFlight = this.requestsInFlight.filter((i) => i !== tag);\n      return result;\n    } catch (e) {\n      this.logger.debug(`Failed to Publish Request: ${id}`);\n      throw e;\n    }\n  };\n\n  public async unsubscribe(topic: string, opts?: RelayerTypes.UnsubscribeOptions) {\n    this.isInitialized();\n    await this.subscriber.unsubscribe(topic, opts);\n  }\n\n  public on(event: string, listener: any) {\n    this.events.on(event, listener);\n  }\n\n  public once(event: string, listener: any) {\n    this.events.once(event, listener);\n  }\n\n  public off(event: string, listener: any) {\n    this.events.off(event, listener);\n  }\n\n  public removeListener(event: string, listener: any) {\n    this.events.removeListener(event, listener);\n  }\n\n  public async transportDisconnect() {\n    if (this.provider.disconnect && (this.hasExperiencedNetworkDisruption || this.connected)) {\n      await createExpiringPromise(this.provider.disconnect(), 2000, \"provider.disconnect()\").catch(\n        () => this.onProviderDisconnect(),\n      );\n    } else {\n      this.onProviderDisconnect();\n    }\n  }\n\n  public async transportClose() {\n    this.transportExplicitlyClosed = true;\n    await this.transportDisconnect();\n  }\n\n  async transportOpen(relayUrl?: string) {\n    if (!this.subscriber.hasAnyTopics) {\n      this.logger.warn(\n        \"Starting WS connection skipped because the client has no topics to work with.\",\n      );\n      return;\n    }\n\n    if (this.connectPromise) {\n      this.logger.debug({}, `Waiting for existing connection attempt to resolve...`);\n      await this.connectPromise;\n      this.logger.debug({}, `Existing connection attempt resolved`);\n    } else {\n      this.connectPromise = new Promise(async (resolve, reject) => {\n        await this.connect(relayUrl)\n          .then(resolve)\n          .catch(reject)\n          .finally(() => {\n            this.connectPromise = undefined;\n          });\n      });\n      await this.connectPromise;\n    }\n    if (!this.connected) {\n      throw new Error(`Couldn't establish socket connection to the relay server: ${this.relayUrl}`);\n    }\n  }\n\n  public async restartTransport(relayUrl?: string) {\n    this.logger.debug({}, \"Restarting transport...\");\n    if (this.connectionAttemptInProgress) return;\n    this.relayUrl = relayUrl || this.relayUrl;\n    await this.confirmOnlineStateOrThrow();\n    await this.transportClose();\n    await this.transportOpen();\n  }\n\n  public async confirmOnlineStateOrThrow() {\n    if (await isOnline()) return;\n    throw new Error(\"No internet connection detected. Please restart your network and try again.\");\n  }\n\n  public async handleBatchMessageEvents(messages: RelayerTypes.MessageEvent[]) {\n    if (messages?.length === 0) {\n      this.logger.trace(\"Batch message events is empty. Ignoring...\");\n      return;\n    }\n    const sortedMessages = messages.sort((a, b) => a.publishedAt - b.publishedAt);\n    this.logger.debug(`Batch of ${sortedMessages.length} message events sorted`);\n    for (const message of sortedMessages) {\n      try {\n        await this.onMessageEvent(message);\n      } catch (e) {\n        this.logger.warn(e, \"Error while processing batch message event: \" + (e as Error)?.message);\n      }\n    }\n    this.logger.trace(`Batch of ${sortedMessages.length} message events processed`);\n  }\n\n  public async onLinkMessageEvent(\n    messageEvent: RelayerTypes.MessageEvent,\n    opts: { sessionExists: boolean },\n  ) {\n    const { topic } = messageEvent;\n\n    if (!opts.sessionExists) {\n      const expiry = calcExpiry(FIVE_MINUTES);\n      const pairing = { topic, expiry, relay: { protocol: \"irn\" }, active: false };\n      await this.core.pairing.pairings.set(topic, pairing);\n    }\n\n    this.events.emit(RELAYER_EVENTS.message, messageEvent);\n    await this.recordMessageEvent(messageEvent, MESSAGE_DIRECTION.inbound);\n  }\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async connect(relayUrl?: string) {\n    await this.confirmOnlineStateOrThrow();\n    if (relayUrl && relayUrl !== this.relayUrl) {\n      this.relayUrl = relayUrl;\n      await this.transportDisconnect();\n    }\n\n    this.connectionAttemptInProgress = true;\n    this.transportExplicitlyClosed = false;\n    let attempt = 1;\n    while (attempt < 6) {\n      try {\n        if (this.transportExplicitlyClosed) {\n          break;\n        }\n        this.logger.debug({}, `Connecting to ${this.relayUrl}, attempt: ${attempt}...`);\n        // Always create new socket instance when trying to connect because if the socket was dropped due to `socket hang up` exception\n        // It wont be able to reconnect\n        await this.createProvider();\n\n        await new Promise<void>(async (resolve, reject) => {\n          const onDisconnect = () => {\n            reject(new Error(`Connection interrupted while trying to subscribe`));\n          };\n          this.provider.once(RELAYER_PROVIDER_EVENTS.disconnect, onDisconnect);\n\n          await createExpiringPromise(\n            new Promise((resolve, reject) => {\n              this.provider.connect().then(resolve).catch(reject);\n            }),\n            this.connectTimeout,\n            `Socket stalled when trying to connect to ${this.relayUrl}`,\n          )\n            .catch((e) => {\n              reject(e);\n            })\n            .finally(() => {\n              this.provider.off(RELAYER_PROVIDER_EVENTS.disconnect, onDisconnect);\n              clearTimeout(this.reconnectTimeout);\n            });\n          await new Promise(async (resolve, reject) => {\n            const onDisconnect = () => {\n              reject(new Error(`Connection interrupted while trying to subscribe`));\n            };\n            this.provider.once(RELAYER_PROVIDER_EVENTS.disconnect, onDisconnect);\n            await this.subscriber\n              .start()\n              .then(resolve)\n              .catch(reject)\n              .finally(() => {\n                this.provider.off(RELAYER_PROVIDER_EVENTS.disconnect, onDisconnect);\n              });\n          });\n          this.hasExperiencedNetworkDisruption = false;\n          resolve();\n        });\n      } catch (e) {\n        await this.subscriber.stop();\n        const error = e as Error;\n        this.logger.warn({}, error.message);\n        this.hasExperiencedNetworkDisruption = true;\n      } finally {\n        this.connectionAttemptInProgress = false;\n      }\n\n      if (this.connected) {\n        this.logger.debug({}, `Connected to ${this.relayUrl} successfully on attempt: ${attempt}`);\n        break;\n      }\n\n      await new Promise((resolve) => setTimeout(resolve, toMiliseconds(attempt * 1)));\n      attempt++;\n    }\n  }\n\n  /*\n   * In Node, we must detect when the connection is stalled and terminate it.\n   * The logic is, if we don't receive ping from the relay within a certain time, we terminate the connection.\n   * The timer is refreshed on every message received from the relay.\n   *\n   * In the browser, ping/pong events are not exposed, so the above behaviour is handled by `subscribeToNetworkChange` and `isOnline` functions.\n   */\n  private startPingTimeout() {\n    if (!isNode()) return;\n    try {\n      //@ts-expect-error - Types are divergent between the node and browser WS API\n      if (this.provider?.connection?.socket) {\n        //@ts-expect-error\n        this.provider?.connection?.socket?.on(\"ping\", () => {\n          this.resetPingTimeout();\n        });\n      }\n      this.resetPingTimeout();\n    } catch (e) {\n      this.logger.warn(e, (e as Error)?.message);\n    }\n  }\n\n  private resetPingTimeout = () => {\n    if (!isNode()) return;\n    clearTimeout(this.pingTimeout);\n    this.pingTimeout = setTimeout(() => {\n      try {\n        this.logger.debug({}, \"pingTimeout: Connection stalled, terminating...\");\n        //@ts-expect-error\n        this.provider?.connection?.socket?.terminate?.();\n      } catch (e) {\n        this.logger.warn(e, (e as Error)?.message);\n      }\n    }, this.heartBeatTimeout);\n  };\n\n  private async createProvider() {\n    if (this.provider.connection) {\n      this.unregisterProviderListeners();\n    }\n    const auth = await this.core.crypto.signJWT(this.relayUrl);\n\n    this.provider = new JsonRpcProvider(\n      new WsConnection(\n        formatRelayRpcUrl({\n          sdkVersion: RELAYER_SDK_VERSION,\n          protocol: this.protocol,\n          version: this.version,\n          relayUrl: this.relayUrl,\n          projectId: this.projectId,\n          auth,\n          useOnCloseEvent: true,\n          bundleId: this.bundleId,\n          packageName: this.packageName,\n        }),\n      ),\n    );\n    this.registerProviderListeners();\n  }\n\n  private async recordMessageEvent(\n    messageEvent: RelayerTypes.MessageEvent,\n    direction?: RelayerTypes.MessageDirection,\n  ) {\n    const { topic, message } = messageEvent;\n    await this.messages.set(topic, message, direction);\n  }\n\n  private async shouldIgnoreMessageEvent(\n    messageEvent: RelayerTypes.MessageEvent,\n  ): Promise<boolean> {\n    const { topic, message } = messageEvent;\n\n    // Ignore if incoming `message` is clearly invalid.\n    if (!message || message.length === 0) {\n      this.logger.warn(`Ignoring invalid/empty message: ${message}`);\n      return true;\n    }\n\n    // Ignore if `topic` is not known to the subscriber.\n    if (!(await this.subscriber.isKnownTopic(topic))) {\n      this.logger.warn(`Ignoring message for unknown topic ${topic}`);\n      return true;\n    }\n\n    // Ignore if `message` is a duplicate.\n    const exists = this.messages.has(topic, message);\n    if (exists) {\n      this.logger.warn(`Ignoring duplicate message: ${message}`);\n    }\n    return exists;\n  }\n\n  private async onProviderPayload(payload: JsonRpcPayload) {\n    this.logger.debug(`Incoming Relay Payload`);\n    this.logger.trace({ type: \"payload\", direction: \"incoming\", payload });\n    if (isJsonRpcRequest(payload)) {\n      if (!payload.method.endsWith(RELAYER_SUBSCRIBER_SUFFIX)) return;\n      const event = (payload as JsonRpcRequest<RelayJsonRpc.SubscriptionParams>).params;\n      const { topic, message, publishedAt, attestation } = event.data;\n      const messageEvent: RelayerTypes.MessageEvent = {\n        topic,\n        message,\n        publishedAt,\n        transportType: TRANSPORT_TYPES.relay,\n        attestation,\n      };\n      this.logger.debug(`Emitting Relayer Payload`);\n      this.logger.trace({ type: \"event\", event: event.id, ...messageEvent });\n      this.events.emit(event.id, messageEvent);\n      await this.acknowledgePayload(payload);\n      await this.onMessageEvent(messageEvent);\n    } else if (isJsonRpcResponse(payload)) {\n      this.events.emit(RELAYER_EVENTS.message_ack, payload);\n    }\n  }\n\n  private async onMessageEvent(messageEvent: RelayerTypes.MessageEvent) {\n    if (await this.shouldIgnoreMessageEvent(messageEvent)) {\n      return;\n    }\n    await this.recordMessageEvent(messageEvent, MESSAGE_DIRECTION.inbound);\n    this.events.emit(RELAYER_EVENTS.message, messageEvent);\n  }\n\n  private async acknowledgePayload(payload: JsonRpcPayload) {\n    const response = formatJsonRpcResult(payload.id, true);\n    await this.provider.connection.send(response);\n  }\n\n  // ---------- Events Handlers ----------------------------------------------- //\n  private onPayloadHandler = (payload: JsonRpcPayload) => {\n    this.onProviderPayload(payload);\n    this.resetPingTimeout();\n  };\n\n  private onConnectHandler = () => {\n    this.logger.warn({}, \"Relayer connected 🛜\");\n    this.startPingTimeout();\n    this.events.emit(RELAYER_EVENTS.connect);\n  };\n\n  private onDisconnectHandler = () => {\n    this.logger.warn({}, `Relayer disconnected 🛑`);\n    this.requestsInFlight = [];\n    this.onProviderDisconnect();\n  };\n\n  private onProviderErrorHandler = (error: Error) => {\n    this.logger.fatal(`Fatal socket error: ${error.message}`);\n    this.events.emit(RELAYER_EVENTS.error, error);\n    // close the transport when a fatal error is received as there's no way to recover from it\n    // usual cases are missing/invalid projectId, expired jwt token, invalid origin etc\n    this.logger.fatal(\"Fatal socket error received, closing transport\");\n    this.transportClose();\n  };\n\n  private registerProviderListeners = () => {\n    this.provider.on(RELAYER_PROVIDER_EVENTS.payload, this.onPayloadHandler);\n    this.provider.on(RELAYER_PROVIDER_EVENTS.connect, this.onConnectHandler);\n    this.provider.on(RELAYER_PROVIDER_EVENTS.disconnect, this.onDisconnectHandler);\n    this.provider.on(RELAYER_PROVIDER_EVENTS.error, this.onProviderErrorHandler);\n  };\n\n  private unregisterProviderListeners() {\n    this.provider.off(RELAYER_PROVIDER_EVENTS.payload, this.onPayloadHandler);\n    this.provider.off(RELAYER_PROVIDER_EVENTS.connect, this.onConnectHandler);\n    this.provider.off(RELAYER_PROVIDER_EVENTS.disconnect, this.onDisconnectHandler);\n    this.provider.off(RELAYER_PROVIDER_EVENTS.error, this.onProviderErrorHandler);\n    clearTimeout(this.pingTimeout);\n  }\n\n  private async registerEventListeners() {\n    let lastConnectedState = await isOnline();\n    subscribeToNetworkChange(async (connected: boolean) => {\n      // sometimes the network change event is triggered multiple times so avoid reacting to the samFe value\n      if (lastConnectedState === connected) return;\n\n      lastConnectedState = connected;\n      if (!connected) {\n        // when the device network is restarted, the socket might stay in false `connected` state\n        this.hasExperiencedNetworkDisruption = true;\n        await this.transportDisconnect();\n        this.transportExplicitlyClosed = false;\n      } else {\n        await this.transportOpen().catch((error) =>\n          this.logger.error(error, (error as Error)?.message),\n        );\n      }\n    });\n\n    this.core.heartbeat.on(HEARTBEAT_EVENTS.pulse, async () => {\n      if (this.transportExplicitlyClosed) return;\n      if (!this.connected && isAppVisible()) {\n        try {\n          await this.confirmOnlineStateOrThrow();\n          await this.transportOpen();\n        } catch (error) {\n          this.logger.warn(error, (error as Error)?.message);\n        }\n      }\n    });\n  }\n\n  private async onProviderDisconnect() {\n    clearTimeout(this.pingTimeout);\n    this.events.emit(RELAYER_EVENTS.disconnect);\n    this.connectionAttemptInProgress = false;\n    if (this.reconnectInProgress) return;\n\n    this.reconnectInProgress = true;\n    await this.subscriber.stop();\n\n    if (!this.subscriber.hasAnyTopics) return;\n    if (this.transportExplicitlyClosed) return;\n\n    this.reconnectTimeout = setTimeout(async () => {\n      await this.transportOpen().catch((error) =>\n        this.logger.error(error, (error as Error)?.message),\n      );\n      this.reconnectTimeout = undefined;\n      this.reconnectInProgress = false;\n    }, toMiliseconds(RELAYER_RECONNECT_TIMEOUT));\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n\n  private async toEstablishConnection() {\n    await this.confirmOnlineStateOrThrow();\n    if (this.connected) return;\n    if (this.connectPromise) {\n      await this.connectPromise;\n      return;\n    }\n    await this.connect();\n  }\n}\n", "function noop() { }\n\nexport { noop };\n", "function isPlainObject(value) {\n    if (!value || typeof value !== 'object') {\n        return false;\n    }\n    const proto = Object.getPrototypeOf(value);\n    const hasObjectPrototype = proto === null ||\n        proto === Object.prototype ||\n        Object.getPrototypeOf(proto) === null;\n    if (!hasObjectPrototype) {\n        return false;\n    }\n    return Object.prototype.toString.call(value) === '[object Object]';\n}\n\nexport { isPlainObject };\n", "function getSymbols(object) {\n    return Object.getOwnPropertySymbols(object).filter(symbol => Object.prototype.propertyIsEnumerable.call(object, symbol));\n}\n\nexport { getSymbols };\n", "function getTag(value) {\n    if (value == null) {\n        return value === undefined ? '[object Undefined]' : '[object Null]';\n    }\n    return Object.prototype.toString.call(value);\n}\n\nexport { getTag };\n", "const regexpTag = '[object RegExp]';\nconst stringTag = '[object String]';\nconst numberTag = '[object Number]';\nconst booleanTag = '[object Boolean]';\nconst argumentsTag = '[object Arguments]';\nconst symbolTag = '[object Symbol]';\nconst dateTag = '[object Date]';\nconst mapTag = '[object Map]';\nconst setTag = '[object Set]';\nconst arrayTag = '[object Array]';\nconst functionTag = '[object Function]';\nconst arrayBufferTag = '[object ArrayBuffer]';\nconst objectTag = '[object Object]';\nconst errorTag = '[object Error]';\nconst dataViewTag = '[object DataView]';\nconst uint8ArrayTag = '[object Uint8Array]';\nconst uint8ClampedArrayTag = '[object Uint8ClampedArray]';\nconst uint16ArrayTag = '[object Uint16Array]';\nconst uint32ArrayTag = '[object Uint32Array]';\nconst bigUint64ArrayTag = '[object BigUint64Array]';\nconst int8ArrayTag = '[object Int8Array]';\nconst int16ArrayTag = '[object Int16Array]';\nconst int32ArrayTag = '[object Int32Array]';\nconst bigInt64ArrayTag = '[object BigInt64Array]';\nconst float32ArrayTag = '[object Float32Array]';\nconst float64ArrayTag = '[object Float64Array]';\n\nexport { argumentsTag, arrayBufferTag, arrayTag, bigInt64ArrayTag, bigUint64ArrayTag, booleanTag, dataViewTag, dateTag, errorTag, float32ArrayTag, float64ArrayTag, functionTag, int16ArrayTag, int32ArrayTag, int8ArrayTag, mapTag, numberTag, objectTag, regexpTag, setTag, stringTag, symbolTag, uint16ArrayTag, uint32ArrayTag, uint8ArrayTag, uint8ClampedArrayTag };\n", "function eq(value, other) {\n    return value === other || (Number.isNaN(value) && Number.isNaN(other));\n}\n\nexport { eq };\n", "import { isPlainObject } from './isPlainObject.mjs';\nimport { getSymbols } from '../compat/_internal/getSymbols.mjs';\nimport { getTag } from '../compat/_internal/getTag.mjs';\nimport { functionTag, regexpTag, symbolTag, dateTag, booleanTag, numberTag, stringTag, objectTag, errorTag, dataViewTag, arrayBufferTag, float64ArrayTag, float32ArrayTag, bigInt64ArrayTag, int32ArrayTag, int16ArrayTag, int8ArrayTag, bigUint64ArrayTag, uint32ArrayTag, uint16ArrayTag, uint8ClampedArrayTag, uint8ArrayTag, arrayTag, setTag, mapTag, argumentsTag } from '../compat/_internal/tags.mjs';\nimport { eq } from '../compat/util/eq.mjs';\n\nfunction isEqualWith(a, b, areValuesEqual) {\n    return isEqualWithImpl(a, b, undefined, undefined, undefined, undefined, areValuesEqual);\n}\nfunction isEqualWithImpl(a, b, property, aParent, bParent, stack, areValuesEqual) {\n    const result = areValuesEqual(a, b, property, aParent, bParent, stack);\n    if (result !== undefined) {\n        return result;\n    }\n    if (typeof a === typeof b) {\n        switch (typeof a) {\n            case 'bigint':\n            case 'string':\n            case 'boolean':\n            case 'symbol':\n            case 'undefined': {\n                return a === b;\n            }\n            case 'number': {\n                return a === b || Object.is(a, b);\n            }\n            case 'function': {\n                return a === b;\n            }\n            case 'object': {\n                return areObjectsEqual(a, b, stack, areValuesEqual);\n            }\n        }\n    }\n    return areObjectsEqual(a, b, stack, areValuesEqual);\n}\nfunction areObjectsEqual(a, b, stack, areValuesEqual) {\n    if (Object.is(a, b)) {\n        return true;\n    }\n    let aTag = getTag(a);\n    let bTag = getTag(b);\n    if (aTag === argumentsTag) {\n        aTag = objectTag;\n    }\n    if (bTag === argumentsTag) {\n        bTag = objectTag;\n    }\n    if (aTag !== bTag) {\n        return false;\n    }\n    switch (aTag) {\n        case stringTag:\n            return a.toString() === b.toString();\n        case numberTag: {\n            const x = a.valueOf();\n            const y = b.valueOf();\n            return eq(x, y);\n        }\n        case booleanTag:\n        case dateTag:\n        case symbolTag:\n            return Object.is(a.valueOf(), b.valueOf());\n        case regexpTag: {\n            return a.source === b.source && a.flags === b.flags;\n        }\n        case functionTag: {\n            return a === b;\n        }\n    }\n    stack = stack ?? new Map();\n    const aStack = stack.get(a);\n    const bStack = stack.get(b);\n    if (aStack != null && bStack != null) {\n        return aStack === b;\n    }\n    stack.set(a, b);\n    stack.set(b, a);\n    try {\n        switch (aTag) {\n            case mapTag: {\n                if (a.size !== b.size) {\n                    return false;\n                }\n                for (const [key, value] of a.entries()) {\n                    if (!b.has(key) || !isEqualWithImpl(value, b.get(key), key, a, b, stack, areValuesEqual)) {\n                        return false;\n                    }\n                }\n                return true;\n            }\n            case setTag: {\n                if (a.size !== b.size) {\n                    return false;\n                }\n                const aValues = Array.from(a.values());\n                const bValues = Array.from(b.values());\n                for (let i = 0; i < aValues.length; i++) {\n                    const aValue = aValues[i];\n                    const index = bValues.findIndex(bValue => {\n                        return isEqualWithImpl(aValue, bValue, undefined, a, b, stack, areValuesEqual);\n                    });\n                    if (index === -1) {\n                        return false;\n                    }\n                    bValues.splice(index, 1);\n                }\n                return true;\n            }\n            case arrayTag:\n            case uint8ArrayTag:\n            case uint8ClampedArrayTag:\n            case uint16ArrayTag:\n            case uint32ArrayTag:\n            case bigUint64ArrayTag:\n            case int8ArrayTag:\n            case int16ArrayTag:\n            case int32ArrayTag:\n            case bigInt64ArrayTag:\n            case float32ArrayTag:\n            case float64ArrayTag: {\n                if (typeof Buffer !== 'undefined' && Buffer.isBuffer(a) !== Buffer.isBuffer(b)) {\n                    return false;\n                }\n                if (a.length !== b.length) {\n                    return false;\n                }\n                for (let i = 0; i < a.length; i++) {\n                    if (!isEqualWithImpl(a[i], b[i], i, a, b, stack, areValuesEqual)) {\n                        return false;\n                    }\n                }\n                return true;\n            }\n            case arrayBufferTag: {\n                if (a.byteLength !== b.byteLength) {\n                    return false;\n                }\n                return areObjectsEqual(new Uint8Array(a), new Uint8Array(b), stack, areValuesEqual);\n            }\n            case dataViewTag: {\n                if (a.byteLength !== b.byteLength || a.byteOffset !== b.byteOffset) {\n                    return false;\n                }\n                return areObjectsEqual(new Uint8Array(a), new Uint8Array(b), stack, areValuesEqual);\n            }\n            case errorTag: {\n                return a.name === b.name && a.message === b.message;\n            }\n            case objectTag: {\n                const areEqualInstances = areObjectsEqual(a.constructor, b.constructor, stack, areValuesEqual) ||\n                    (isPlainObject(a) && isPlainObject(b));\n                if (!areEqualInstances) {\n                    return false;\n                }\n                const aKeys = [...Object.keys(a), ...getSymbols(a)];\n                const bKeys = [...Object.keys(b), ...getSymbols(b)];\n                if (aKeys.length !== bKeys.length) {\n                    return false;\n                }\n                for (let i = 0; i < aKeys.length; i++) {\n                    const propKey = aKeys[i];\n                    const aProp = a[propKey];\n                    if (!Object.hasOwn(b, propKey)) {\n                        return false;\n                    }\n                    const bProp = b[propKey];\n                    if (!isEqualWithImpl(aProp, bProp, propKey, a, b, stack, areValuesEqual)) {\n                        return false;\n                    }\n                }\n                return true;\n            }\n            default: {\n                return false;\n            }\n        }\n    }\n    finally {\n        stack.delete(a);\n        stack.delete(b);\n    }\n}\n\nexport { isEqualWith };\n", "import { isEqualWith } from './isEqualWith.mjs';\nimport { noop } from '../function/noop.mjs';\n\nfunction isEqual(a, b) {\n    return isEqualWith(a, b, noop);\n}\n\nexport { isEqual };\n", "import { generateChildLogger, getLoggerContext, Logger } from \"@walletconnect/logger\";\nimport { ICore, IStore } from \"@walletconnect/types\";\nimport {\n  getInternalError,\n  isProposalStruct,\n  isSessionStruct,\n  isUndefined,\n} from \"@walletconnect/utils\";\nimport { CORE_STORAGE_PREFIX, STORE_STORAGE_VERSION } from \"../constants\";\nimport { isEqual } from \"es-toolkit/compat\";\n\nexport class Store<Key, Data extends Record<string, any>> extends IStore<Key, Data> {\n  public map = new Map<Key, Data>();\n  public version = STORE_STORAGE_VERSION;\n\n  private cached: Data[] = [];\n  private initialized = false;\n\n  /**\n   * Regenerates the value key to retrieve it from cache\n   */\n  private getKey: ((data: Data) => Key) | undefined;\n\n  private storagePrefix = CORE_STORAGE_PREFIX;\n\n  // stores recently deleted key to return different rejection message when key is not found\n  private recentlyDeleted: Key[] = [];\n  private recentlyDeletedLimit = 200;\n\n  /**\n   * @param {ICore} core Core\n   * @param {Logger} logger Logger\n   * @param {string} name Store's name\n   * @param {Store<Key, Data>[\"getKey\"]} get<PERSON>ey Regenerates the value key to retrieve it from cache\n   * @param {string} storagePrefix Prefixes value keys\n   */\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n    public name: string,\n    storagePrefix: string = CORE_STORAGE_PREFIX,\n    getKey: Store<Key, Data>[\"getKey\"] = undefined,\n  ) {\n    super(core, logger, name, storagePrefix);\n    this.logger = generateChildLogger(logger, this.name);\n    this.storagePrefix = storagePrefix;\n    this.getKey = getKey;\n  }\n\n  public init: IStore<Key, Data>[\"init\"] = async () => {\n    if (!this.initialized) {\n      this.logger.trace(`Initialized`);\n\n      await this.restore();\n\n      this.cached.forEach((value) => {\n        if (this.getKey && value !== null && !isUndefined(value)) {\n          this.map.set(this.getKey(value), value);\n        } else if (isProposalStruct(value)) {\n          // TODO(pedro) revert type casting as any\n          this.map.set(value.id as any, value);\n        } else if (isSessionStruct(value)) {\n          // TODO(pedro) revert type casting as any\n          this.map.set(value.topic as any, value);\n        }\n      });\n\n      this.cached = [];\n      this.initialized = true;\n    }\n  };\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + this.name;\n  }\n\n  get length() {\n    return this.map.size;\n  }\n\n  get keys() {\n    return Array.from(this.map.keys());\n  }\n\n  get values() {\n    return Array.from(this.map.values());\n  }\n\n  public set: IStore<Key, Data>[\"set\"] = async (key, value) => {\n    this.isInitialized();\n    if (this.map.has(key)) {\n      await this.update(key, value);\n    } else {\n      this.logger.debug(`Setting value`);\n      this.logger.trace({ type: \"method\", method: \"set\", key, value });\n      this.map.set(key, value);\n      await this.persist();\n    }\n  };\n\n  public get: IStore<Key, Data>[\"get\"] = (key) => {\n    this.isInitialized();\n    this.logger.debug(`Getting value`);\n    this.logger.trace({ type: \"method\", method: \"get\", key });\n    const value = this.getData(key);\n    return value;\n  };\n\n  public getAll: IStore<Key, Data>[\"getAll\"] = (filter) => {\n    this.isInitialized();\n    if (!filter) return this.values;\n\n    return this.values.filter((value) =>\n      Object.keys(filter).every((key) => isEqual(value[key], filter[key])),\n    );\n  };\n\n  public update: IStore<Key, Data>[\"update\"] = async (key, update) => {\n    this.isInitialized();\n    this.logger.debug(`Updating value`);\n    this.logger.trace({ type: \"method\", method: \"update\", key, update });\n    const value = { ...this.getData(key), ...update };\n    this.map.set(key, value);\n    await this.persist();\n  };\n\n  public delete: IStore<Key, Data>[\"delete\"] = async (key, reason) => {\n    this.isInitialized();\n    if (!this.map.has(key)) return;\n    this.logger.debug(`Deleting value`);\n    this.logger.trace({ type: \"method\", method: \"delete\", key, reason });\n    this.map.delete(key);\n    this.addToRecentlyDeleted(key);\n    await this.persist();\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private addToRecentlyDeleted(key: Key) {\n    this.recentlyDeleted.push(key);\n    // limit the size of the recentlyDeleted array, truncate the 100 oldest entries.\n    if (this.recentlyDeleted.length >= this.recentlyDeletedLimit) {\n      this.recentlyDeleted.splice(0, this.recentlyDeletedLimit / 2);\n    }\n  }\n\n  private async setDataStore(value: Data[]) {\n    await this.core.storage.setItem<Data[]>(this.storageKey, value);\n  }\n\n  private async getDataStore() {\n    const value = await this.core.storage.getItem<Data[]>(this.storageKey);\n    return value;\n  }\n\n  private getData(key: Key) {\n    const value = this.map.get(key);\n    if (!value) {\n      if (this.recentlyDeleted.includes(key)) {\n        const { message } = getInternalError(\n          \"MISSING_OR_INVALID\",\n          `Record was recently deleted - ${this.name}: ${key}`,\n        );\n        this.logger.error(message);\n        throw new Error(message);\n      }\n\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `${this.name}: ${key}`);\n      this.logger.error(message);\n      throw new Error(message);\n    }\n    return value;\n  }\n\n  private async persist() {\n    await this.setDataStore(this.values);\n  }\n\n  private async restore() {\n    try {\n      const persisted = await this.getDataStore();\n      if (typeof persisted === \"undefined\") return;\n      if (!persisted.length) return;\n      if (this.map.size) {\n        const { message } = getInternalError(\"RESTORE_WILL_OVERRIDE\", this.name);\n        this.logger.error(message);\n        throw new Error(message);\n      }\n      this.cached = persisted;\n      this.logger.debug(`Successfully Restored value for ${this.name}`);\n      this.logger.trace({ type: \"method\", method: \"restore\", value: this.values });\n    } catch (e) {\n      this.logger.debug(`Failed to Restore value for ${this.name}`);\n      this.logger.error(e as any);\n    }\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { generateChil<PERSON><PERSON>ogger, getLog<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from \"@walletconnect/logger\";\nimport {\n  ICore,\n  PairingTypes,\n  IPairing,\n  IPairingPrivate,\n  IStore,\n  RelayerTypes,\n  PairingJsonRpcTypes,\n  ExpirerTypes,\n  EventClientTypes,\n} from \"@walletconnect/types\";\nimport {\n  getInternalError,\n  parseUri,\n  calcExpiry,\n  generateRandomBytes32,\n  formatUri,\n  getSdkError,\n  engineEvent,\n  createDelayedPromise,\n  isValidParams,\n  isValidUrl,\n  isValidString,\n  isExpired,\n  parseExpirerTarget,\n  TYPE_1,\n} from \"@walletconnect/utils\";\nimport {\n  formatJsonRpcRequest,\n  formatJsonRpcResult,\n  formatJsonRpcError,\n  isJsonRpcRequest,\n  isJsonRpcResponse,\n  isJsonRpcResult,\n  isJsonRpcError,\n} from \"@walletconnect/jsonrpc-utils\";\nimport { FIVE_MINUTES, toMiliseconds } from \"@walletconnect/time\";\nimport EventEmitter from \"events\";\nimport {\n  PAIRING_CONTEXT,\n  PAIRING_STORAGE_VERSION,\n  CORE_STORAGE_PREFIX,\n  RELAYER_DEFAULT_PROTOCOL,\n  PAIRING_RPC_OPTS,\n  RELAYER_EVENTS,\n  EXPIRER_EVENTS,\n  PAIRING_EVENTS,\n  EVENT_CLIENT_PAIRING_TRACES,\n  EVENT_CLIENT_PAIRING_ERRORS,\n  TRANSPORT_TYPES,\n} from \"../constants\";\nimport { Store } from \"../controllers/store\";\n\nexport class Pairing implements IPairing {\n  public name = PAIRING_CONTEXT;\n  public version = PAIRING_STORAGE_VERSION;\n\n  public events = new EventEmitter();\n  public pairings: IStore<string, PairingTypes.Struct>;\n\n  private initialized = false;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n  private ignoredPayloadTypes = [TYPE_1];\n  private registeredMethods: string[] = [];\n\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    this.core = core;\n    this.logger = generateChildLogger(logger, this.name);\n    this.pairings = new Store(this.core, this.logger, this.name, this.storagePrefix);\n  }\n\n  public init: IPairing[\"init\"] = async () => {\n    if (!this.initialized) {\n      await this.pairings.init();\n      await this.cleanup();\n      this.registerRelayerEvents();\n      this.registerExpirerEvents();\n      this.initialized = true;\n      this.logger.trace(`Initialized`);\n    }\n  };\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  public register: IPairing[\"register\"] = ({ methods }) => {\n    this.isInitialized();\n    this.registeredMethods = [...new Set([...this.registeredMethods, ...methods])];\n  };\n\n  public create: IPairing[\"create\"] = async (params) => {\n    this.isInitialized();\n    const symKey = generateRandomBytes32();\n    const topic = await this.core.crypto.setSymKey(symKey);\n    const expiry = calcExpiry(FIVE_MINUTES);\n    const relay = { protocol: RELAYER_DEFAULT_PROTOCOL };\n    const pairing = { topic, expiry, relay, active: false, methods: params?.methods };\n    const uri = formatUri({\n      protocol: this.core.protocol,\n      version: this.core.version,\n      topic,\n      symKey,\n      relay,\n      expiryTimestamp: expiry,\n      methods: params?.methods,\n    });\n    this.events.emit(PAIRING_EVENTS.create, pairing);\n    this.core.expirer.set(topic, expiry);\n    await this.pairings.set(topic, pairing);\n    await this.core.relayer.subscribe(topic, { transportType: params?.transportType });\n\n    return { topic, uri };\n  };\n\n  public pair: IPairing[\"pair\"] = async (params) => {\n    this.isInitialized();\n\n    const event = this.core.eventClient.createEvent({\n      properties: {\n        topic: params?.uri,\n        trace: [EVENT_CLIENT_PAIRING_TRACES.pairing_started],\n      },\n    });\n\n    this.isValidPair(params, event);\n\n    const { topic, symKey, relay, expiryTimestamp, methods } = parseUri(params.uri);\n\n    event.props.properties.topic = topic;\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.pairing_uri_validation_success);\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.pairing_uri_not_expired);\n\n    let existingPairing;\n    if (this.pairings.keys.includes(topic)) {\n      existingPairing = this.pairings.get(topic);\n      event.addTrace(EVENT_CLIENT_PAIRING_TRACES.existing_pairing);\n      if (existingPairing.active) {\n        event.setError(EVENT_CLIENT_PAIRING_ERRORS.active_pairing_already_exists);\n        throw new Error(\n          `Pairing already exists: ${topic}. Please try again with a new connection URI.`,\n        );\n      } else {\n        event.addTrace(EVENT_CLIENT_PAIRING_TRACES.pairing_not_expired);\n      }\n    }\n\n    const expiry = expiryTimestamp || calcExpiry(FIVE_MINUTES);\n    const pairing = { topic, relay, expiry, active: false, methods };\n    this.core.expirer.set(topic, expiry);\n    await this.pairings.set(topic, pairing);\n\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.store_new_pairing);\n\n    if (params.activatePairing) {\n      await this.activate({ topic });\n    }\n\n    this.events.emit(PAIRING_EVENTS.create, pairing);\n\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.emit_inactive_pairing);\n\n    // avoid overwriting keychain pairing already exists\n    if (!this.core.crypto.keychain.has(topic)) {\n      await this.core.crypto.setSymKey(symKey, topic);\n    }\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.subscribing_pairing_topic);\n\n    try {\n      await this.core.relayer.confirmOnlineStateOrThrow();\n    } catch (error) {\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.no_internet_connection);\n    }\n\n    try {\n      await this.core.relayer.subscribe(topic, { relay });\n    } catch (error) {\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.subscribe_pairing_topic_failure);\n      throw error;\n    }\n\n    event.addTrace(EVENT_CLIENT_PAIRING_TRACES.subscribe_pairing_topic_success);\n\n    return pairing;\n  };\n\n  public activate: IPairing[\"activate\"] = async ({ topic }) => {\n    this.isInitialized();\n    const expiry = calcExpiry(FIVE_MINUTES);\n    this.core.expirer.set(topic, expiry);\n    await this.pairings.update(topic, { active: true, expiry });\n  };\n\n  /**\n   * @deprecated Ping will be removed in the next major release.\n   */\n  public ping: IPairing[\"ping\"] = async (params) => {\n    this.isInitialized();\n    await this.isValidPing(params);\n    this.logger.warn(\"ping() is deprecated and will be removed in the next major release.\");\n    const { topic } = params;\n    if (this.pairings.keys.includes(topic)) {\n      const id = await this.sendRequest(topic, \"wc_pairingPing\", {});\n      const { done, resolve, reject } = createDelayedPromise<void>();\n      this.events.once(engineEvent(\"pairing_ping\", id), ({ error }) => {\n        if (error) reject(error);\n        else resolve();\n      });\n      await done();\n    }\n  };\n\n  public updateExpiry: IPairing[\"updateExpiry\"] = async ({ topic, expiry }) => {\n    this.isInitialized();\n    await this.pairings.update(topic, { expiry });\n  };\n\n  public updateMetadata: IPairing[\"updateMetadata\"] = async ({ topic, metadata }) => {\n    this.isInitialized();\n    await this.pairings.update(topic, { peerMetadata: metadata });\n  };\n\n  public getPairings: IPairing[\"getPairings\"] = () => {\n    this.isInitialized();\n    return this.pairings.values;\n  };\n\n  public disconnect: IPairing[\"disconnect\"] = async (params) => {\n    this.isInitialized();\n    await this.isValidDisconnect(params);\n    const { topic } = params;\n    if (this.pairings.keys.includes(topic)) {\n      await this.sendRequest(topic, \"wc_pairingDelete\", getSdkError(\"USER_DISCONNECTED\"));\n      await this.deletePairing(topic);\n    }\n  };\n\n  public formatUriFromPairing: IPairing[\"formatUriFromPairing\"] = (pairing) => {\n    this.isInitialized();\n    const { topic, relay, expiry, methods } = pairing;\n    const symKey = this.core.crypto.keychain.get(topic);\n    return formatUri({\n      protocol: this.core.protocol,\n      version: this.core.version,\n      topic,\n      symKey,\n      relay,\n      expiryTimestamp: expiry,\n      methods,\n    });\n  };\n\n  // ---------- Private Helpers ----------------------------------------------- //\n\n  private sendRequest: IPairingPrivate[\"sendRequest\"] = async (topic, method, params) => {\n    const payload = formatJsonRpcRequest(method, params);\n    const message = await this.core.crypto.encode(topic, payload);\n    const opts = PAIRING_RPC_OPTS[method].req;\n    this.core.history.set(topic, payload);\n    this.core.relayer.publish(topic, message, opts);\n    return payload.id;\n  };\n\n  private sendResult: IPairingPrivate[\"sendResult\"] = async (id, topic, result) => {\n    const payload = formatJsonRpcResult(id, result);\n    const message = await this.core.crypto.encode(topic, payload);\n    const record = await this.core.history.get(topic, id);\n    const method = record.request.method as PairingJsonRpcTypes.WcMethod;\n    const opts = PAIRING_RPC_OPTS[method].res;\n    await this.core.relayer.publish(topic, message, opts);\n    await this.core.history.resolve(payload);\n  };\n\n  private sendError: IPairingPrivate[\"sendError\"] = async (id, topic, error) => {\n    const payload = formatJsonRpcError(id, error);\n    const message = await this.core.crypto.encode(topic, payload);\n    const record = await this.core.history.get(topic, id);\n    const method = record.request.method as PairingJsonRpcTypes.WcMethod;\n\n    const opts = PAIRING_RPC_OPTS[method]\n      ? PAIRING_RPC_OPTS[method].res\n      : PAIRING_RPC_OPTS.unregistered_method.res;\n\n    await this.core.relayer.publish(topic, message, opts);\n    await this.core.history.resolve(payload);\n  };\n\n  private deletePairing: IPairingPrivate[\"deletePairing\"] = async (topic, expirerHasDeleted) => {\n    // Await the unsubscribe first to avoid deleting the symKey too early below.\n    await this.core.relayer.unsubscribe(topic);\n    await Promise.all([\n      this.pairings.delete(topic, getSdkError(\"USER_DISCONNECTED\")),\n      this.core.crypto.deleteSymKey(topic),\n      expirerHasDeleted ? Promise.resolve() : this.core.expirer.del(topic),\n    ]);\n  };\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n\n  private cleanup = async () => {\n    const expiredPairings = this.pairings.getAll().filter((pairing) => isExpired(pairing.expiry));\n    await Promise.all(expiredPairings.map((pairing) => this.deletePairing(pairing.topic)));\n  };\n\n  // ---------- Relay Events Router ----------------------------------- //\n\n  private registerRelayerEvents() {\n    this.core.relayer.on(RELAYER_EVENTS.message, async (event: RelayerTypes.MessageEvent) => {\n      const { topic, message, transportType } = event;\n\n      // Do not handle if the topic is not related to known pairing topics.\n      if (!this.pairings.keys.includes(topic)) return;\n\n      // Do not handle link-mode messages\n      if (transportType === TRANSPORT_TYPES.link_mode) return;\n\n      // messages of certain types should be ignored as they are handled by their respective SDKs\n      if (this.ignoredPayloadTypes.includes(this.core.crypto.getPayloadType(message))) return;\n\n      try {\n        const payload = await this.core.crypto.decode(topic, message);\n\n        if (isJsonRpcRequest(payload)) {\n          this.core.history.set(topic, payload);\n          await this.onRelayEventRequest({ topic, payload });\n        } else if (isJsonRpcResponse(payload)) {\n          await this.core.history.resolve(payload);\n          await this.onRelayEventResponse({ topic, payload });\n          this.core.history.delete(topic, payload.id);\n        }\n        await this.core.relayer.messages.ack(topic, message);\n      } catch (error) {\n        this.logger.error(error);\n      }\n    });\n  }\n\n  private onRelayEventRequest: IPairingPrivate[\"onRelayEventRequest\"] = async (event) => {\n    const { topic, payload } = event;\n    const reqMethod = payload.method as PairingJsonRpcTypes.WcMethod;\n\n    switch (reqMethod) {\n      case \"wc_pairingPing\":\n        return await this.onPairingPingRequest(topic, payload);\n      case \"wc_pairingDelete\":\n        return await this.onPairingDeleteRequest(topic, payload);\n      default:\n        return await this.onUnknownRpcMethodRequest(topic, payload);\n    }\n  };\n\n  private onRelayEventResponse: IPairingPrivate[\"onRelayEventResponse\"] = async (event) => {\n    const { topic, payload } = event;\n    const record = await this.core.history.get(topic, payload.id);\n    const resMethod = record.request.method as PairingJsonRpcTypes.WcMethod;\n\n    switch (resMethod) {\n      case \"wc_pairingPing\":\n        return this.onPairingPingResponse(topic, payload);\n      default:\n        return this.onUnknownRpcMethodResponse(resMethod);\n    }\n  };\n\n  private onPairingPingRequest: IPairingPrivate[\"onPairingPingRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    try {\n      this.isValidPing({ topic });\n      await this.sendResult<\"wc_pairingPing\">(id, topic, true);\n      this.events.emit(PAIRING_EVENTS.ping, { id, topic });\n    } catch (err: any) {\n      await this.sendError(id, topic, err);\n      this.logger.error(err);\n    }\n  };\n\n  private onPairingPingResponse: IPairingPrivate[\"onPairingPingResponse\"] = (_topic, payload) => {\n    const { id } = payload;\n    // put at the end of the stack to avoid a race condition\n    // where pairing_ping listener is not yet initialized\n    setTimeout(() => {\n      if (isJsonRpcResult(payload)) {\n        this.events.emit(engineEvent(\"pairing_ping\", id), {});\n      } else if (isJsonRpcError(payload)) {\n        this.events.emit(engineEvent(\"pairing_ping\", id), { error: payload.error });\n      }\n    }, 500);\n  };\n\n  private onPairingDeleteRequest: IPairingPrivate[\"onPairingDeleteRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id } = payload;\n    try {\n      this.isValidDisconnect({ topic });\n      await this.deletePairing(topic);\n      this.events.emit(PAIRING_EVENTS.delete, { id, topic });\n    } catch (err: any) {\n      await this.sendError(id, topic, err);\n      this.logger.error(err);\n    }\n  };\n\n  private onUnknownRpcMethodRequest: IPairingPrivate[\"onUnknownRpcMethodRequest\"] = async (\n    topic,\n    payload,\n  ) => {\n    const { id, method } = payload;\n\n    try {\n      // Ignore if the implementing client has registered this method as known.\n      if (this.registeredMethods.includes(method)) return;\n      const error = getSdkError(\"WC_METHOD_UNSUPPORTED\", method);\n      await this.sendError(id, topic, error);\n      this.logger.error(error);\n    } catch (err: any) {\n      await this.sendError(id, topic, err);\n      this.logger.error(err);\n    }\n  };\n\n  private onUnknownRpcMethodResponse: IPairingPrivate[\"onUnknownRpcMethodResponse\"] = (method) => {\n    // Ignore if the implementing client has registered this method as known.\n    if (this.registeredMethods.includes(method)) return;\n    this.logger.error(getSdkError(\"WC_METHOD_UNSUPPORTED\", method));\n  };\n\n  // ---------- Expirer Events ---------------------------------------- //\n\n  private registerExpirerEvents() {\n    this.core.expirer.on(EXPIRER_EVENTS.expired, async (event: ExpirerTypes.Expiration) => {\n      const { topic } = parseExpirerTarget(event.target);\n      if (!topic) return;\n      if (!this.pairings.keys.includes(topic)) return;\n      await this.deletePairing(topic, true);\n      this.events.emit(PAIRING_EVENTS.expire, { topic });\n    });\n  }\n\n  // ---------- Validation Helpers ----------------------------------- //\n\n  private isValidPair = (params: { uri: string }, event: EventClientTypes.Event) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `pair() params: ${params}`);\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.malformed_pairing_uri);\n      throw new Error(message);\n    }\n    if (!isValidUrl(params.uri)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `pair() uri: ${params.uri}`);\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.malformed_pairing_uri);\n      throw new Error(message);\n    }\n    const uri = parseUri(params?.uri);\n    if (!uri?.relay?.protocol) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `pair() uri#relay-protocol`);\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.malformed_pairing_uri);\n      throw new Error(message);\n    }\n    if (!uri?.symKey) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `pair() uri#symKey`);\n      event.setError(EVENT_CLIENT_PAIRING_ERRORS.malformed_pairing_uri);\n      throw new Error(message);\n    }\n    if (uri?.expiryTimestamp) {\n      const expiration = toMiliseconds(uri?.expiryTimestamp);\n      if (expiration < Date.now()) {\n        event.setError(EVENT_CLIENT_PAIRING_ERRORS.pairing_expired);\n        const { message } = getInternalError(\n          \"EXPIRED\",\n          `pair() URI has expired. Please try again with a new connection URI.`,\n        );\n        throw new Error(message);\n      }\n    }\n  };\n\n  private isValidPing = async (params: { topic: string }) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `ping() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic } = params;\n    await this.isValidPairingTopic(topic);\n  };\n\n  private isValidDisconnect = async (params: { topic: string }) => {\n    if (!isValidParams(params)) {\n      const { message } = getInternalError(\"MISSING_OR_INVALID\", `disconnect() params: ${params}`);\n      throw new Error(message);\n    }\n    const { topic } = params;\n    await this.isValidPairingTopic(topic);\n  };\n\n  private isValidPairingTopic = async (topic: any) => {\n    if (!isValidString(topic, false)) {\n      const { message } = getInternalError(\n        \"MISSING_OR_INVALID\",\n        `pairing topic should be a string: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    if (!this.pairings.keys.includes(topic)) {\n      const { message } = getInternalError(\n        \"NO_MATCHING_KEY\",\n        `pairing topic doesn't exist: ${topic}`,\n      );\n      throw new Error(message);\n    }\n    if (isExpired(this.pairings.get(topic).expiry)) {\n      await this.deletePairing(topic);\n      const { message } = getInternalError(\"EXPIRED\", `pairing topic: ${topic}`);\n      throw new Error(message);\n    }\n  };\n}\n", "import { formatJsonRpcRequest, isJsonRpcError } from \"@walletconnect/jsonrpc-utils\";\nimport { generateChildLogger, getLoggerContext, Logger } from \"@walletconnect/logger\";\nimport { IJsonRpcHistory, JsonRpcRecord, RequestEvent, ICore } from \"@walletconnect/types\";\nimport { calcExpiry, getInternalError } from \"@walletconnect/utils\";\nimport { EventEmitter } from \"events\";\nimport { THIRTY_DAYS, toMiliseconds } from \"@walletconnect/time\";\nimport { HEARTBEAT_EVENTS } from \"@walletconnect/heartbeat\";\nimport {\n  CORE_STORAGE_PREFIX,\n  HISTORY_CONTEXT,\n  HISTORY_EVENTS,\n  HISTORY_STORAGE_VERSION,\n} from \"../constants\";\n\nexport class JsonRpcHistory extends IJsonRpcHistory {\n  public records = new Map<number, JsonRpcRecord>();\n  public events = new EventEmitter();\n  public name = HISTORY_CONTEXT;\n  public version = HISTORY_STORAGE_VERSION;\n\n  private cached: JsonRpcRecord[] = [];\n  private initialized = false;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n  ) {\n    super(core, logger);\n    this.logger = generateChildLogger(logger, this.name);\n  }\n\n  public init: IJsonRpcHistory[\"init\"] = async () => {\n    if (!this.initialized) {\n      this.logger.trace(`Initialized`);\n      await this.restore();\n      this.cached.forEach((record) => this.records.set(record.id, record));\n      this.cached = [];\n      this.registerEventListeners();\n      this.initialized = true;\n    }\n  };\n\n  get context(): string {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + this.name;\n  }\n\n  get size(): number {\n    return this.records.size;\n  }\n\n  get keys(): number[] {\n    return Array.from(this.records.keys());\n  }\n\n  get values() {\n    return Array.from(this.records.values());\n  }\n\n  get pending(): RequestEvent[] {\n    const requests: RequestEvent[] = [];\n    this.values.forEach((record) => {\n      if (typeof record.response !== \"undefined\") return;\n      const requestEvent: RequestEvent = {\n        topic: record.topic,\n        request: formatJsonRpcRequest(record.request.method, record.request.params, record.id),\n        chainId: record.chainId,\n      };\n      return requests.push(requestEvent);\n    });\n    return requests;\n  }\n\n  public set: IJsonRpcHistory[\"set\"] = (topic, request, chainId) => {\n    this.isInitialized();\n    this.logger.debug(`Setting JSON-RPC request history record`);\n    this.logger.trace({ type: \"method\", method: \"set\", topic, request, chainId });\n    if (this.records.has(request.id)) return;\n    const record: JsonRpcRecord = {\n      id: request.id,\n      topic,\n      request: { method: request.method, params: request.params || null },\n      chainId,\n      expiry: calcExpiry(THIRTY_DAYS),\n    };\n    this.records.set(record.id, record);\n    this.persist();\n    this.events.emit(HISTORY_EVENTS.created, record);\n  };\n\n  public resolve: IJsonRpcHistory[\"resolve\"] = async (response) => {\n    this.isInitialized();\n    this.logger.debug(`Updating JSON-RPC response history record`);\n    this.logger.trace({ type: \"method\", method: \"update\", response });\n    if (!this.records.has(response.id)) return;\n    const record = await this.getRecord(response.id);\n    if (typeof record.response !== \"undefined\") return;\n    record.response = isJsonRpcError(response)\n      ? { error: response.error }\n      : { result: response.result };\n    this.records.set(record.id, record);\n    this.persist();\n    this.events.emit(HISTORY_EVENTS.updated, record);\n  };\n\n  public get: IJsonRpcHistory[\"get\"] = async (topic, id) => {\n    this.isInitialized();\n    this.logger.debug(`Getting record`);\n    this.logger.trace({ type: \"method\", method: \"get\", topic, id });\n    const record = await this.getRecord(id);\n    return record;\n  };\n\n  public delete: IJsonRpcHistory[\"delete\"] = (topic, id) => {\n    this.isInitialized();\n    this.logger.debug(`Deleting record`);\n    this.logger.trace({ type: \"method\", method: \"delete\", id });\n    this.values.forEach((record: JsonRpcRecord) => {\n      if (record.topic === topic) {\n        if (typeof id !== \"undefined\" && record.id !== id) return;\n        this.records.delete(record.id);\n        this.events.emit(HISTORY_EVENTS.deleted, record);\n      }\n    });\n    this.persist();\n  };\n\n  public exists: IJsonRpcHistory[\"exists\"] = async (topic, id) => {\n    this.isInitialized();\n    if (!this.records.has(id)) return false;\n    const record = await this.getRecord(id);\n    return record.topic === topic;\n  };\n\n  public on: IJsonRpcHistory[\"on\"] = (event, listener) => {\n    this.events.on(event, listener);\n  };\n\n  public once: IJsonRpcHistory[\"once\"] = (event, listener) => {\n    this.events.once(event, listener);\n  };\n\n  public off: IJsonRpcHistory[\"off\"] = (event, listener) => {\n    this.events.off(event, listener);\n  };\n\n  public removeListener: IJsonRpcHistory[\"removeListener\"] = (event, listener) => {\n    this.events.removeListener(event, listener);\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async setJsonRpcRecords(records: JsonRpcRecord[]): Promise<void> {\n    await this.core.storage.setItem<JsonRpcRecord[]>(this.storageKey, records);\n  }\n\n  private async getJsonRpcRecords(): Promise<JsonRpcRecord[] | undefined> {\n    const records = await this.core.storage.getItem<JsonRpcRecord[]>(this.storageKey);\n    return records;\n  }\n\n  private getRecord(id: number) {\n    this.isInitialized();\n    const record = this.records.get(id);\n    if (!record) {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `${this.name}: ${id}`);\n      throw new Error(message);\n    }\n    return record;\n  }\n\n  private async persist() {\n    await this.setJsonRpcRecords(this.values);\n    this.events.emit(HISTORY_EVENTS.sync);\n  }\n\n  private async restore() {\n    try {\n      const persisted = await this.getJsonRpcRecords();\n      if (typeof persisted === \"undefined\") return;\n      if (!persisted.length) return;\n      if (this.records.size) {\n        const { message } = getInternalError(\"RESTORE_WILL_OVERRIDE\", this.name);\n        this.logger.error(message);\n        throw new Error(message);\n      }\n      this.cached = persisted;\n      this.logger.debug(`Successfully Restored records for ${this.name}`);\n      this.logger.trace({ type: \"method\", method: \"restore\", records: this.values });\n    } catch (e) {\n      this.logger.debug(`Failed to Restore records for ${this.name}`);\n      this.logger.error(e as any);\n    }\n  }\n\n  private registerEventListeners(): void {\n    this.events.on(HISTORY_EVENTS.created, (record: JsonRpcRecord) => {\n      const eventName = HISTORY_EVENTS.created;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, record });\n    });\n    this.events.on(HISTORY_EVENTS.updated, (record: JsonRpcRecord) => {\n      const eventName = HISTORY_EVENTS.updated;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, record });\n    });\n\n    this.events.on(HISTORY_EVENTS.deleted, (record: JsonRpcRecord) => {\n      const eventName = HISTORY_EVENTS.deleted;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, record });\n    });\n\n    this.core.heartbeat.on(HEARTBEAT_EVENTS.pulse, () => {\n      this.cleanup();\n    });\n  }\n\n  private cleanup() {\n    try {\n      this.isInitialized();\n      let deleted = false;\n      this.records.forEach((record: JsonRpcRecord) => {\n        const msToExpiry = toMiliseconds(record.expiry || 0) - Date.now();\n        if (msToExpiry <= 0) {\n          this.logger.info(`Deleting expired history log: ${record.id}`);\n          this.records.delete(record.id);\n          this.events.emit(HISTORY_EVENTS.deleted, record, false);\n          deleted = true;\n        }\n      });\n      if (deleted) {\n        this.persist();\n      }\n    } catch (e) {\n      this.logger.warn(e);\n    }\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { HEARTBEAT_EVENTS } from \"@walletconnect/heartbeat\";\nimport { generateChildLogger, getLoggerContext, Logger } from \"@walletconnect/logger\";\nimport { toMiliseconds } from \"@walletconnect/time\";\nimport { ExpirerTypes, ICore, IExpirer } from \"@walletconnect/types\";\nimport { getInternalError, formatIdTarget, formatTopicTarget } from \"@walletconnect/utils\";\nimport { EventEmitter } from \"events\";\nimport {\n  CORE_STORAGE_PREFIX,\n  EXPIRER_CONTEXT,\n  EXPIRER_EVENTS,\n  EXPIRER_STORAGE_VERSION,\n} from \"../constants\";\n\nexport class Expirer extends IExpirer {\n  public expirations = new Map<string, ExpirerTypes.Expiration>();\n  public events = new EventEmitter();\n  public name = EXPIRER_CONTEXT;\n  public version = EXPIRER_STORAGE_VERSION;\n\n  private cached: ExpirerTypes.Expiration[] = [];\n  private initialized = false;\n\n  private storagePrefix = CORE_STORAGE_PREFIX;\n\n  constructor(\n    public core: <PERSON><PERSON>ore,\n    public logger: Logger,\n  ) {\n    super(core, logger);\n    this.logger = generateChildLogger(logger, this.name);\n  }\n\n  public init: IExpirer[\"init\"] = async () => {\n    if (!this.initialized) {\n      this.logger.trace(`Initialized`);\n      await this.restore();\n      this.cached.forEach((expiration) => this.expirations.set(expiration.target, expiration));\n      this.cached = [];\n      this.registerEventListeners();\n      this.initialized = true;\n    }\n  };\n\n  get context(): string {\n    return getLoggerContext(this.logger);\n  }\n\n  get storageKey() {\n    return this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + this.name;\n  }\n\n  get length(): number {\n    return this.expirations.size;\n  }\n\n  get keys(): string[] {\n    return Array.from(this.expirations.keys());\n  }\n\n  get values(): ExpirerTypes.Expiration[] {\n    return Array.from(this.expirations.values());\n  }\n\n  public has: IExpirer[\"has\"] = (key) => {\n    try {\n      const target = this.formatTarget(key);\n      const expiration = this.getExpiration(target);\n      return typeof expiration !== \"undefined\";\n    } catch (e) {\n      // ignore\n      return false;\n    }\n  };\n\n  public set: IExpirer[\"set\"] = (key, expiry) => {\n    this.isInitialized();\n    const target = this.formatTarget(key);\n    const expiration = { target, expiry };\n    this.expirations.set(target, expiration);\n    this.checkExpiry(target, expiration);\n    this.events.emit(EXPIRER_EVENTS.created, {\n      target,\n      expiration,\n    } as ExpirerTypes.Created);\n  };\n\n  public get: IExpirer[\"get\"] = (key) => {\n    this.isInitialized();\n    const target = this.formatTarget(key);\n    return this.getExpiration(target);\n  };\n\n  public del: IExpirer[\"del\"] = (key) => {\n    this.isInitialized();\n    const exists = this.has(key);\n    if (exists) {\n      const target = this.formatTarget(key);\n      const expiration = this.getExpiration(target);\n      this.expirations.delete(target);\n      this.events.emit(EXPIRER_EVENTS.deleted, {\n        target,\n        expiration,\n      } as ExpirerTypes.Deleted);\n    }\n  };\n\n  public on: IExpirer[\"on\"] = (event, listener) => {\n    this.events.on(event, listener);\n  };\n\n  public once: IExpirer[\"once\"] = (event, listener) => {\n    this.events.once(event, listener);\n  };\n\n  public off: IExpirer[\"off\"] = (event, listener) => {\n    this.events.off(event, listener);\n  };\n\n  public removeListener: IExpirer[\"removeListener\"] = (event, listener) => {\n    this.events.removeListener(event, listener);\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private formatTarget(key: string | number) {\n    if (typeof key === \"string\") {\n      return formatTopicTarget(key);\n    } else if (typeof key === \"number\") {\n      return formatIdTarget(key);\n    }\n    const { message } = getInternalError(\"UNKNOWN_TYPE\", `Target type: ${typeof key}`);\n    throw new Error(message);\n  }\n\n  private async setExpirations(expirations: ExpirerTypes.Expiration[]): Promise<void> {\n    await this.core.storage.setItem<ExpirerTypes.Expiration[]>(this.storageKey, expirations);\n  }\n\n  private async getExpirations(): Promise<ExpirerTypes.Expiration[] | undefined> {\n    const expirations = await this.core.storage.getItem<ExpirerTypes.Expiration[]>(this.storageKey);\n    return expirations;\n  }\n\n  private async persist() {\n    await this.setExpirations(this.values);\n    this.events.emit(EXPIRER_EVENTS.sync);\n  }\n\n  private async restore() {\n    try {\n      const persisted = await this.getExpirations();\n      if (typeof persisted === \"undefined\") return;\n      if (!persisted.length) return;\n      if (this.expirations.size) {\n        const { message } = getInternalError(\"RESTORE_WILL_OVERRIDE\", this.name);\n        this.logger.error(message);\n        throw new Error(message);\n      }\n      this.cached = persisted;\n      this.logger.debug(`Successfully Restored expirations for ${this.name}`);\n      this.logger.trace({ type: \"method\", method: \"restore\", expirations: this.values });\n    } catch (e) {\n      this.logger.debug(`Failed to Restore expirations for ${this.name}`);\n      this.logger.error(e as any);\n    }\n  }\n\n  private getExpiration(target: string): ExpirerTypes.Expiration {\n    const expiration = this.expirations.get(target);\n    if (!expiration) {\n      const { message } = getInternalError(\"NO_MATCHING_KEY\", `${this.name}: ${target}`);\n      this.logger.warn(message);\n      throw new Error(message);\n    }\n    return expiration;\n  }\n\n  private checkExpiry(target: string, expiration: ExpirerTypes.Expiration): void {\n    const { expiry } = expiration;\n    const msToTimeout = toMiliseconds(expiry) - Date.now();\n    if (msToTimeout <= 0) this.expire(target, expiration);\n  }\n\n  private expire(target: string, expiration: ExpirerTypes.Expiration): void {\n    this.expirations.delete(target);\n    this.events.emit(EXPIRER_EVENTS.expired, {\n      target,\n      expiration,\n    } as ExpirerTypes.Expired);\n  }\n\n  private checkExpirations(): void {\n    // avoid auto expiring if the relayer is not connected\n    if (!this.core.relayer.connected) return;\n    this.expirations.forEach((expiration, target) => this.checkExpiry(target, expiration));\n  }\n\n  private registerEventListeners(): void {\n    this.core.heartbeat.on(HEARTBEAT_EVENTS.pulse, () => this.checkExpirations());\n    this.events.on(EXPIRER_EVENTS.created, (createdEvent: ExpirerTypes.Created) => {\n      const eventName = EXPIRER_EVENTS.created;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, data: createdEvent });\n      this.persist();\n    });\n    this.events.on(EXPIRER_EVENTS.expired, (expiredEvent: ExpirerTypes.Expired) => {\n      const eventName = EXPIRER_EVENTS.expired;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, data: expiredEvent });\n      this.persist();\n    });\n    this.events.on(EXPIRER_EVENTS.deleted, (deletedEvent: ExpirerTypes.Deleted) => {\n      const eventName = EXPIRER_EVENTS.deleted;\n      this.logger.info(`Emitting ${eventName}`);\n      this.logger.debug({ type: \"event\", event: eventName, data: deletedEvent });\n      this.persist();\n    });\n  }\n\n  private isInitialized() {\n    if (!this.initialized) {\n      const { message } = getInternalError(\"NOT_INITIALIZED\", this.name);\n      throw new Error(message);\n    }\n  }\n}\n", "import { generateChildLogger, getLogger<PERSON>ontext, Logger } from \"@walletconnect/logger\";\nimport { ICore, IVerify } from \"@walletconnect/types\";\nimport { isBrowser, isTestRun, P256KeyDataType, verifyP256Jwt } from \"@walletconnect/utils\";\nimport { FIVE_SECONDS, ONE_SECOND, toMiliseconds } from \"@walletconnect/time\";\nimport { getDocument } from \"@walletconnect/window-getters\";\nimport { decodeJWT } from \"@walletconnect/relay-auth\";\n\nimport {\n  CORE_STORAGE_PREFIX,\n  CORE_VERSION,\n  TRUSTED_VERIFY_URLS,\n  VERIFY_CONTEXT,\n  VERIFY_SERVER,\n  VERIFY_SERVER_V3,\n} from \"../constants\";\nimport { IKeyValueStorage } from \"@walletconnect/keyvaluestorage\";\n\ntype Jwk = {\n  publicKey: P256KeyDataType;\n  expiresAt: number;\n};\ntype JwkPayload = {\n  exp: number;\n  id: string;\n  origin: string;\n  isScam: boolean;\n  isVerified: boolean;\n};\nexport class Verify extends IVerify {\n  public name = VERIFY_CONTEXT;\n  private abortController: AbortController;\n  private isDevEnv;\n  private verifyUrlV3 = VERIFY_SERVER_V3;\n  private storagePrefix = CORE_STORAGE_PREFIX;\n  private version = CORE_VERSION;\n  private publicKey?: Jwk;\n  private fetchPromise?: Promise<Jwk>;\n\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n    public store: IKeyValueStorage,\n  ) {\n    super(core, logger, store);\n    this.logger = generateChildLogger(logger, this.name);\n    this.abortController = new AbortController();\n    this.isDevEnv = isTestRun();\n    this.init();\n  }\n\n  get storeKey(): string {\n    return (\n      this.storagePrefix + this.version + this.core.customStoragePrefix + \"//\" + `verify:public:key`\n    );\n  }\n\n  public init = async () => {\n    if (this.isDevEnv) return;\n    this.publicKey = await this.store.getItem(this.storeKey);\n    if (this.publicKey && toMiliseconds(this.publicKey?.expiresAt) < Date.now()) {\n      this.logger.debug(\"verify v2 public key expired\");\n      await this.removePublicKey();\n    }\n  };\n\n  public register: IVerify[\"register\"] = async (params) => {\n    if (!isBrowser() || this.isDevEnv) return;\n    const origin = window.location.origin;\n    const { id, decryptedId } = params;\n    const src = `${this.verifyUrlV3}/attestation?projectId=${this.core.projectId}&origin=${origin}&id=${id}&decryptedId=${decryptedId}`;\n    try {\n      const document = getDocument() as Document;\n      const abortTimeout = this.startAbortTimer(ONE_SECOND * 5);\n      const attestationJwt = await new Promise((resolve, reject) => {\n        const abortListener = () => {\n          window.removeEventListener(\"message\", listener);\n          document.body.removeChild(iframe);\n          reject(\"attestation aborted\");\n        };\n        this.abortController.signal.addEventListener(\"abort\", abortListener);\n        const iframe = document.createElement(\"iframe\");\n        iframe.src = src;\n        iframe.style.display = \"none\";\n        iframe.addEventListener(\"error\", abortListener, { signal: this.abortController.signal });\n        const listener = (event: MessageEvent) => {\n          if (!event.data) return;\n          if (typeof event.data !== \"string\") return;\n          try {\n            const data = JSON.parse(event.data);\n            if (data.type === \"verify_attestation\") {\n              const decoded = decodeJWT(data.attestation) as unknown as { payload: JwkPayload };\n              if (decoded.payload.id !== id) return;\n\n              clearInterval(abortTimeout);\n              document.body.removeChild(iframe);\n              this.abortController.signal.removeEventListener(\"abort\", abortListener);\n              window.removeEventListener(\"message\", listener);\n              resolve(data.attestation === null ? \"\" : data.attestation);\n            }\n          } catch (e) {\n            this.logger.warn(e);\n          }\n        };\n        document.body.appendChild(iframe);\n        window.addEventListener(\"message\", listener, { signal: this.abortController.signal });\n      });\n      this.logger.debug(\"jwt attestation\", attestationJwt);\n      return attestationJwt as string;\n    } catch (e) {\n      this.logger.warn(e);\n    }\n    return \"\";\n  };\n\n  public resolve: IVerify[\"resolve\"] = async (params) => {\n    if (this.isDevEnv) return \"\";\n    const { attestationId, hash, encryptedId } = params;\n    if (attestationId === \"\") {\n      this.logger.debug(\"resolve: attestationId is empty, skipping\");\n      return;\n    }\n\n    if (attestationId) {\n      const decoded = decodeJWT(attestationId) as unknown as { payload: JwkPayload };\n      if (decoded.payload.id !== encryptedId) return;\n      const validation = await this.isValidJwtAttestation(attestationId);\n      if (validation) {\n        if (!validation.isVerified) {\n          this.logger.warn(\"resolve: jwt attestation: origin url not verified\");\n          return;\n        }\n        return validation;\n      }\n    }\n    if (!hash) return;\n    const verifyUrl = this.getVerifyUrl(params?.verifyUrl);\n    return this.fetchAttestation(hash, verifyUrl);\n  };\n\n  get context(): string {\n    return getLoggerContext(this.logger);\n  }\n\n  private fetchAttestation = async (attestationId: string, url: string) => {\n    this.logger.debug(`resolving attestation: ${attestationId} from url: ${url}`);\n    // set artificial timeout to prevent hanging\n    const timeout = this.startAbortTimer(ONE_SECOND * 5);\n    const result = await fetch(`${url}/attestation/${attestationId}?v2Supported=true`, {\n      signal: this.abortController.signal,\n    });\n    clearTimeout(timeout);\n    return result.status === 200 ? await result.json() : undefined;\n  };\n\n  private startAbortTimer(timer: number) {\n    this.abortController = new AbortController();\n    return setTimeout(() => this.abortController.abort(), toMiliseconds(timer));\n  }\n\n  private getVerifyUrl = (verifyUrl?: string) => {\n    let url = verifyUrl || VERIFY_SERVER;\n    if (!TRUSTED_VERIFY_URLS.includes(url)) {\n      this.logger.info(\n        `verify url: ${url}, not included in trusted list, assigning default: ${VERIFY_SERVER}`,\n      );\n      url = VERIFY_SERVER;\n    }\n    return url;\n  };\n\n  private fetchPublicKey = async () => {\n    try {\n      this.logger.debug(`fetching public key from: ${this.verifyUrlV3}`);\n      const timeout = this.startAbortTimer(FIVE_SECONDS);\n      const result = await fetch(`${this.verifyUrlV3}/public-key`, {\n        signal: this.abortController.signal,\n      });\n      clearTimeout(timeout);\n      return (await result.json()) as Jwk;\n    } catch (e) {\n      this.logger.warn(e);\n    }\n    return undefined;\n  };\n\n  private persistPublicKey = async (publicKey: Jwk) => {\n    this.logger.debug(`persisting public key to local storage`, publicKey);\n    await this.store.setItem(this.storeKey, publicKey);\n    this.publicKey = publicKey;\n  };\n\n  private removePublicKey = async () => {\n    this.logger.debug(`removing verify v2 public key from storage`);\n    await this.store.removeItem(this.storeKey);\n    this.publicKey = undefined;\n  };\n\n  private isValidJwtAttestation = async (attestation: string) => {\n    const key = await this.getPublicKey();\n    try {\n      if (key) {\n        const validation = this.validateAttestation(attestation, key);\n        return validation;\n      }\n    } catch (e) {\n      this.logger.error(e);\n      this.logger.warn(\"error validating attestation\");\n    }\n    const newKey = await this.fetchAndPersistPublicKey();\n    try {\n      if (newKey) {\n        const validation = this.validateAttestation(attestation, newKey);\n        return validation;\n      }\n    } catch (e) {\n      this.logger.error(e);\n      this.logger.warn(\"error validating attestation\");\n    }\n    return undefined;\n  };\n\n  private getPublicKey = async () => {\n    if (this.publicKey) return this.publicKey;\n    return await this.fetchAndPersistPublicKey();\n  };\n\n  private fetchAndPersistPublicKey = async () => {\n    if (this.fetchPromise) {\n      await this.fetchPromise;\n      return this.publicKey;\n    }\n    this.fetchPromise = new Promise(async (resolve) => {\n      const key = await this.fetchPublicKey();\n      if (!key) return;\n      await this.persistPublicKey(key);\n      resolve(key);\n    });\n    const key = await this.fetchPromise;\n    this.fetchPromise = undefined;\n    return key;\n  };\n\n  private validateAttestation = (attestation: string, key: Jwk) => {\n    const result = verifyP256Jwt<JwkPayload>(attestation, key.publicKey);\n    const validation = {\n      hasExpired: toMiliseconds(result.exp) < Date.now(),\n      payload: result,\n    };\n\n    if (validation.hasExpired) {\n      this.logger.warn(\"resolve: jwt attestation expired\");\n      throw new Error(\"JWT attestation expired\");\n    }\n\n    return {\n      origin: validation.payload.origin,\n      isScam: validation.payload.isScam,\n      isVerified: validation.payload.isVerified,\n    };\n  };\n}\n", "import { generateChild<PERSON>ogger, Logger } from \"@walletconnect/logger\";\nimport { IEchoClient } from \"@walletconnect/types\";\nimport { ECHO_CONTEXT, ECHO_URL } from \"../constants\";\n\nexport class EchoClient extends IEchoClient {\n  public readonly context = ECHO_CONTEXT;\n  constructor(\n    public projectId: string,\n    public logger: Logger,\n  ) {\n    super(projectId, logger);\n    this.logger = generateChildLogger(logger, this.context);\n  }\n\n  public registerDeviceToken: IEchoClient[\"registerDeviceToken\"] = async (params) => {\n    const { clientId, token, notificationType, enableEncrypted = false } = params;\n\n    const echoUrl = `${ECHO_URL}/${this.projectId}/clients`;\n\n    await fetch(echoUrl, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({\n        client_id: clientId,\n        type: notificationType,\n        token,\n        always_raw: enableEncrypted,\n      }),\n    });\n  };\n}\n", "import { generateChild<PERSON>ogger, Lo<PERSON> } from \"@walletconnect/logger\";\nimport { ICore, IEventClient, EventClientTypes } from \"@walletconnect/types\";\nimport { formatUA, isTestRun, uuidv4, getAppMetadata } from \"@walletconnect/utils\";\nimport {\n  CORE_STORAGE_PREFIX,\n  EVENTS_CLIENT_API_URL,\n  EVENTS_STORAGE_CLEANUP_INTERVAL,\n  EVENTS_STORAGE_CONTEXT,\n  EVENTS_STORAGE_VERSION,\n  RELAYER_SDK_VERSION,\n} from \"../constants\";\nimport { HEARTBEAT_EVENTS } from \"@walletconnect/heartbeat\";\nimport { fromMiliseconds } from \"@walletconnect/time\";\n\nexport class EventClient extends IEventClient {\n  public readonly context = EVENTS_STORAGE_CONTEXT;\n  private readonly storagePrefix = CORE_STORAGE_PREFIX;\n  private readonly storageVersion = EVENTS_STORAGE_VERSION;\n  private events = new Map<string, EventClientTypes.Event>();\n  private shouldPersist = false;\n  constructor(\n    public core: ICore,\n    public logger: Logger,\n    telemetryEnabled = true,\n  ) {\n    super(core, logger, telemetryEnabled);\n    this.logger = generateChildLogger(logger, this.context);\n    this.telemetryEnabled = telemetryEnabled;\n    if (telemetryEnabled) {\n      this.restore().then(async () => {\n        await this.submit();\n        this.setEventListeners();\n      });\n    } else {\n      // overwrite any persisted events with an empty array\n      this.persist();\n    }\n  }\n\n  get storageKey() {\n    return (\n      this.storagePrefix + this.storageVersion + this.core.customStoragePrefix + \"//\" + this.context\n    );\n  }\n\n  public init: IEventClient[\"init\"] = async () => {\n    if (isTestRun()) return;\n    try {\n      const initEvent = {\n        eventId: uuidv4(),\n        timestamp: Date.now(),\n        domain: this.getAppDomain(),\n        props: {\n          event: \"INIT\",\n          type: \"\",\n          properties: {\n            client_id: await this.core.crypto.getClientId(),\n            user_agent: formatUA(\n              this.core.relayer.protocol,\n              this.core.relayer.version,\n              RELAYER_SDK_VERSION,\n            ),\n          },\n        },\n      };\n      await this.sendEvent([initEvent] as unknown as EventClientTypes.Event[]);\n    } catch (error) {\n      this.logger.warn(error);\n    }\n  };\n\n  public createEvent: IEventClient[\"createEvent\"] = (params) => {\n    const {\n      event = \"ERROR\",\n      type = \"\",\n      properties: { topic, trace },\n    } = params;\n    const eventId = uuidv4();\n    const bundleId = this.core.projectId || \"\";\n    const timestamp = Date.now();\n    const props = {\n      event,\n      type,\n      properties: {\n        topic,\n        trace,\n      },\n    };\n    const eventObj = {\n      eventId,\n      timestamp,\n      props,\n      bundleId,\n      domain: this.getAppDomain(),\n      ...this.setMethods(eventId),\n    };\n    if (this.telemetryEnabled) {\n      this.events.set(eventId, eventObj);\n      this.shouldPersist = true;\n    }\n\n    return eventObj;\n  };\n\n  public getEvent: IEventClient[\"getEvent\"] = (params) => {\n    const { eventId, topic } = params;\n    if (eventId) {\n      return this.events.get(eventId);\n    }\n    const event = Array.from(this.events.values()).find(\n      (event) => event.props.properties.topic === topic,\n    );\n\n    if (!event) return;\n\n    return {\n      ...event,\n      ...this.setMethods(event.eventId),\n    };\n  };\n\n  public deleteEvent: IEventClient[\"deleteEvent\"] = (params) => {\n    const { eventId } = params;\n    this.events.delete(eventId);\n    this.shouldPersist = true;\n  };\n\n  private setEventListeners = () => {\n    this.core.heartbeat.on(HEARTBEAT_EVENTS.pulse, async () => {\n      if (this.shouldPersist) await this.persist();\n      // cleanup events older than EVENTS_STORAGE_CLEANUP_INTERVAL\n      this.events.forEach((event) => {\n        if (\n          fromMiliseconds(Date.now()) - fromMiliseconds(event.timestamp) >\n          EVENTS_STORAGE_CLEANUP_INTERVAL\n        ) {\n          this.events.delete(event.eventId);\n          this.shouldPersist = true;\n        }\n      });\n    });\n  };\n\n  private setMethods = (eventId: string) => {\n    return {\n      addTrace: (trace: string) => this.addTrace(eventId, trace),\n      setError: (errorType: string) => this.setError(eventId, errorType),\n    };\n  };\n\n  private addTrace = (eventId: string, trace: string) => {\n    const event = this.events.get(eventId);\n    if (!event) return;\n    event.props.properties.trace.push(trace);\n    this.events.set(eventId, event);\n    this.shouldPersist = true;\n  };\n\n  private setError = (eventId: string, errorType: string) => {\n    const event = this.events.get(eventId);\n    if (!event) return;\n    event.props.type = errorType;\n    event.timestamp = Date.now();\n    this.events.set(eventId, event);\n    this.shouldPersist = true;\n  };\n\n  private persist = async () => {\n    await this.core.storage.setItem(this.storageKey, Array.from(this.events.values()));\n    this.shouldPersist = false;\n  };\n\n  private restore = async () => {\n    try {\n      const events =\n        (await this.core.storage.getItem<EventClientTypes.Event[]>(this.storageKey)) || [];\n      if (!events.length) return;\n      events.forEach((event) => {\n        this.events.set(event.eventId, {\n          ...event,\n          ...this.setMethods(event.eventId),\n        });\n      });\n    } catch (error) {\n      this.logger.warn(error);\n    }\n  };\n\n  private submit = async () => {\n    if (!this.telemetryEnabled) return;\n\n    if (this.events.size === 0) return;\n\n    const eventsToSend: EventClientTypes.Event[] = [];\n    // exclude events without type as they can be considered `in progress`\n    for (const [_, event] of this.events) {\n      if (event.props.type) {\n        eventsToSend.push(event);\n      }\n    }\n\n    if (eventsToSend.length === 0) return;\n\n    try {\n      const response = await this.sendEvent(eventsToSend);\n      if (response.ok) {\n        for (const event of eventsToSend) {\n          this.events.delete(event.eventId);\n          this.shouldPersist = true;\n        }\n      }\n    } catch (error) {\n      this.logger.warn(error);\n    }\n  };\n\n  private sendEvent = async (events: EventClientTypes.Event[]) => {\n    // if domain isn't available, set `sp` as `desktop` so data would be extracted on api side\n    const platform = this.getAppDomain() ? \"\" : \"&sp=desktop\";\n    const response = await fetch(\n      `${EVENTS_CLIENT_API_URL}?projectId=${this.core.projectId}&st=events_sdk&sv=js-${RELAYER_SDK_VERSION}${platform}`,\n      {\n        method: \"POST\",\n        body: JSON.stringify(events),\n      },\n    );\n    return response;\n  };\n\n  private getAppDomain = () => {\n    return getAppMetadata().url;\n  };\n}\n", "import { EventEmitter } from \"events\";\n\nimport { HeartBeat } from \"@walletconnect/heartbeat\";\nimport KeyValueStorage from \"@walletconnect/keyvaluestorage\";\nimport {\n  Chunk<PERSON>oggerController,\n  generateChildLogger,\n  generatePlatformLogger,\n  getDefaultLoggerOptions,\n  getLoggerContext,\n} from \"@walletconnect/logger\";\nimport { CoreTypes, ICore } from \"@walletconnect/types\";\n\nimport {\n  CORE_CONTEXT,\n  CORE_DEFAULT,\n  CORE_PROTOCOL,\n  CORE_STORAGE_OPTIONS,\n  CORE_VERSION,\n  RELAYER_DEFAULT_RELAY_URL,\n  TRANSPORT_TYPES,\n  WALLETCONNECT_CLIENT_ID,\n  WALLETCONNECT_LINK_MODE_APPS,\n} from \"./constants\";\nimport {\n  Crypto,\n  EchoClient,\n  EventClient,\n  Expirer,\n  JsonRpcHistory,\n  Pairing,\n  Relayer,\n  Verify,\n} from \"./controllers\";\n\nexport class Core extends ICore {\n  public readonly protocol = CORE_PROTOCOL;\n  public readonly version = CORE_VERSION;\n\n  public readonly name: ICore[\"name\"] = CORE_CONTEXT;\n  public readonly relayUrl: ICore[\"relayUrl\"];\n  public readonly projectId: ICore[\"projectId\"];\n  public readonly customStoragePrefix: ICore[\"customStoragePrefix\"];\n  public events: ICore[\"events\"] = new EventEmitter();\n  public logger: ICore[\"logger\"];\n  public heartbeat: ICore[\"heartbeat\"];\n  public relayer: ICore[\"relayer\"];\n  public crypto: ICore[\"crypto\"];\n  public storage: ICore[\"storage\"];\n  public history: ICore[\"history\"];\n  public expirer: ICore[\"expirer\"];\n  public pairing: ICore[\"pairing\"];\n  public verify: ICore[\"verify\"];\n  public echoClient: ICore[\"echoClient\"];\n  public linkModeSupportedApps: ICore[\"linkModeSupportedApps\"];\n  public eventClient: ICore[\"eventClient\"];\n\n  private initialized = false;\n  private logChunkController: ChunkLoggerController | null;\n\n  static async init(opts?: CoreTypes.Options) {\n    const core = new Core(opts);\n    await core.initialize();\n    const clientId = await core.crypto.getClientId();\n    await core.storage.setItem(WALLETCONNECT_CLIENT_ID, clientId);\n\n    return core;\n  }\n\n  constructor(opts?: CoreTypes.Options) {\n    super(opts);\n\n    const globalCore = this.getGlobalCore(opts?.customStoragePrefix);\n    if (globalCore) {\n      try {\n        this.customStoragePrefix = globalCore.customStoragePrefix;\n        this.logger = globalCore.logger;\n        this.heartbeat = globalCore.heartbeat;\n        this.crypto = globalCore.crypto;\n        this.history = globalCore.history;\n        this.expirer = globalCore.expirer;\n        this.storage = globalCore.storage;\n        this.relayer = globalCore.relayer;\n        this.pairing = globalCore.pairing;\n        this.verify = globalCore.verify;\n        this.echoClient = globalCore.echoClient;\n        this.linkModeSupportedApps = globalCore.linkModeSupportedApps;\n        this.eventClient = globalCore.eventClient;\n        this.initialized = globalCore.initialized;\n        this.logChunkController = globalCore.logChunkController;\n        return globalCore;\n      } catch (error) {\n        console.warn(\"Failed to copy global core\", error);\n      }\n    }\n\n    this.projectId = opts?.projectId;\n    this.relayUrl = opts?.relayUrl || RELAYER_DEFAULT_RELAY_URL;\n    this.customStoragePrefix = opts?.customStoragePrefix ? `:${opts.customStoragePrefix}` : \"\";\n\n    const loggerOptions = getDefaultLoggerOptions({\n      level: typeof opts?.logger === \"string\" && opts.logger ? opts.logger : CORE_DEFAULT.logger,\n      name: CORE_CONTEXT,\n    });\n\n    const { logger, chunkLoggerController } = generatePlatformLogger({\n      opts: loggerOptions,\n      maxSizeInBytes: opts?.maxLogBlobSizeInBytes,\n      loggerOverride: opts?.logger,\n    });\n\n    this.logChunkController = chunkLoggerController;\n\n    if (this.logChunkController?.downloadLogsBlobInBrowser) {\n      // @ts-ignore\n      window.downloadLogsBlobInBrowser = async () => {\n        // Have to null check twice becquse there is no guarantee\n        // this.logChunkController.downloadLogsBlobInBrowser is always truthy\n        if (this.logChunkController?.downloadLogsBlobInBrowser) {\n          this.logChunkController?.downloadLogsBlobInBrowser({\n            clientId: await this.crypto.getClientId(),\n          });\n        }\n      };\n    }\n\n    this.logger = generateChildLogger(logger, this.name);\n    this.heartbeat = new HeartBeat();\n    this.crypto = new Crypto(this, this.logger, opts?.keychain);\n    this.history = new JsonRpcHistory(this, this.logger);\n    this.expirer = new Expirer(this, this.logger);\n    this.storage = opts?.storage\n      ? opts.storage\n      : new KeyValueStorage({ ...CORE_STORAGE_OPTIONS, ...opts?.storageOptions });\n    this.relayer = new Relayer({\n      core: this,\n      logger: this.logger,\n      relayUrl: this.relayUrl,\n      projectId: this.projectId,\n    });\n    this.pairing = new Pairing(this, this.logger);\n    this.verify = new Verify(this, this.logger, this.storage);\n    this.echoClient = new EchoClient(this.projectId || \"\", this.logger);\n    this.linkModeSupportedApps = [];\n    this.eventClient = new EventClient(this, this.logger, opts?.telemetryEnabled);\n    this.setGlobalCore(this);\n  }\n\n  get context() {\n    return getLoggerContext(this.logger);\n  }\n\n  // ---------- Public ----------------------------------------------- //\n\n  public async start() {\n    if (this.initialized) return;\n    await this.initialize();\n  }\n\n  public async getLogsBlob() {\n    return this.logChunkController?.logsToBlob({\n      clientId: await this.crypto.getClientId(),\n    });\n  }\n\n  public async addLinkModeSupportedApp(universalLink: string) {\n    if (this.linkModeSupportedApps.includes(universalLink)) return;\n    this.linkModeSupportedApps.push(universalLink);\n    await this.storage.setItem(WALLETCONNECT_LINK_MODE_APPS, this.linkModeSupportedApps);\n  }\n\n  // ---------- Events ----------------------------------------------- //\n\n  public on = (name: any, listener: any) => {\n    return this.events.on(name, listener);\n  };\n\n  public once = (name: any, listener: any) => {\n    return this.events.once(name, listener);\n  };\n\n  public off = (name: any, listener: any) => {\n    return this.events.off(name, listener);\n  };\n\n  public removeListener = (name: any, listener: any) => {\n    return this.events.removeListener(name, listener);\n  };\n\n  // ---------- Link-mode ----------------------------------------------- //\n\n  public dispatchEnvelope = ({\n    topic,\n    message,\n    sessionExists,\n  }: {\n    topic: string;\n    message: string;\n    sessionExists: boolean;\n  }) => {\n    if (!topic || !message) return;\n\n    const payload = {\n      topic,\n      message,\n      publishedAt: Date.now(),\n      transportType: TRANSPORT_TYPES.link_mode,\n    };\n\n    this.relayer.onLinkMessageEvent(payload, { sessionExists });\n  };\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async initialize() {\n    this.logger.trace(`Initialized`);\n    try {\n      await this.crypto.init();\n      await this.history.init();\n      await this.expirer.init();\n      await this.relayer.init();\n      await this.heartbeat.init();\n      await this.pairing.init();\n      this.linkModeSupportedApps = (await this.storage.getItem(WALLETCONNECT_LINK_MODE_APPS)) || [];\n\n      this.initialized = true;\n      this.logger.info(`Core Initialization Success`);\n    } catch (error) {\n      this.logger.warn(`Core Initialization Failure at epoch ${Date.now()}`, error);\n      this.logger.error((error as any).message);\n      throw error;\n    }\n  }\n\n  private getGlobalCore(customStoragePrefix = \"\"): Core | undefined {\n    try {\n      if (this.isGlobalCoreDisabled()) {\n        return undefined;\n      }\n      const globalCorePrefix = `_walletConnectCore_${customStoragePrefix}`;\n\n      const counterKey = `${globalCorePrefix}_count`;\n      globalThis[counterKey] = (globalThis[counterKey] || 0) + 1;\n      if (globalThis[counterKey] > 1) {\n        console.warn(\n          `WalletConnect Core is already initialized. This is probably a mistake and can lead to unexpected behavior. Init() was called ${globalThis[counterKey]} times.`,\n        );\n      }\n\n      return globalThis[globalCorePrefix];\n    } catch (error) {\n      console.warn(\"Failed to get global WalletConnect core\", error);\n      return undefined;\n    }\n  }\n\n  private setGlobalCore(core: Core) {\n    try {\n      if (this.isGlobalCoreDisabled()) {\n        return;\n      }\n      const customStoragePrefix = core.opts?.customStoragePrefix || \"\";\n      const globalCorePrefix = `_walletConnectCore_${customStoragePrefix}`;\n      globalThis[globalCorePrefix] = core;\n    } catch (error) {\n      console.warn(\"Failed to set global WalletConnect core\", error);\n    }\n  }\n\n  private isGlobalCoreDisabled() {\n    try {\n      return typeof process !== \"undefined\" && process.env.DISABLE_GLOBAL_CORE === \"true\";\n    } catch (error) {\n      return true;\n    }\n  }\n}\n", "import { Core as WalletConnectCore } from \"./core\";\n\nexport * from \"./constants\";\nexport * from \"./controllers\";\n\nexport const Core = WalletConnectCore;\nexport default WalletConnectCore;\n"], "names": ["ONE_DAY", "SIX_HOURS", "THIRTY_DAYS", "FIVE_SECONDS", "THIRTY_SECONDS", "VERIFY_SERVER_COM", "VERIFY_SERVER_ORG", "fromString", "from", "basex", "decode", "encode", "identity", "varint", "varint.encoding<PERSON>ength", "varint.encodeTo", "Digest.create", "base2", "base8", "base10", "base16", "base32", "base36", "base64", "base256emoji", "bases", "r", "h", "core", "logger", "__publicField", "KEYCHAIN_CONTEXT", "KEYCHAIN_STORAGE_VERSION", "CORE_STORAGE_PREFIX", "keychain", "tag", "key", "message", "getInternalError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getLoggerContext", "mapToObj", "objToMap", "C", "b", "CRYPTO_CONTEXT", "generateRandomBytes32", "seed", "keyPair", "relayAuth", "generateKeyPairUtil", "aud", "sub", "ttl", "CRYPTO_JWT_TTL", "selfPublicKey", "peerPublic<PERSON>ey", "overrideTopic", "selfPrivateKey", "sym<PERSON>ey", "derive<PERSON><PERSON><PERSON>ey", "topic", "hash<PERSON><PERSON>", "public<PERSON>ey", "payload", "opts", "params", "validateEncoding", "safeJsonStringify", "isTypeTwoEnvelope", "encodeTypeTwoEnvelope", "isTypeOneEnvelope", "type", "sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "encrypt", "encoded", "validateDecoding", "decodeTypeTwoEnvelope", "safeJsonParse", "decrypt", "error", "encoding", "BASE64", "deserialized", "deserialize", "decodeTypeByte", "toString", "BASE16", "<PERSON><PERSON><PERSON><PERSON>", "privateKey", "CRYPTO_CLIENT_SEED", "e", "p", "y", "k", "R", "g", "l", "i", "IMessageTracker", "MESSAGES_CONTEXT", "MESSAGES_STORAGE_VERSION", "messages", "messagesWithoutClientAck", "direction", "hash", "hashMessage", "MESSAGE_DIRECTION", "__spreadProps", "__spreadValues", "topics", "IPublisher", "relayer", "EventEmitter", "PUBLISHER_CONTEXT", "toMiliseconds", "ONE_MINUTE", "ONE_SECOND", "_a", "PUBLISHER_DEFAULT_TTL", "relay", "getRelayProtocolName", "prompt", "id", "getBigIntRpcId", "failedPublishMessage", "publishPromise", "resolve", "onPublish", "RELAYER_EVENTS", "initialPublish", "createExpiringPromise", "reject", "event", "listener", "_b", "_c", "_d", "attestation", "tvf", "request", "getRelayProtocolApi", "isUndefined", "result", "attempt", "HEARTBEAT_EVENTS", "a", "n", "ids", "remaining", "x", "ISubscriber", "SubscriberTopicMap", "SUBSCRIBER_CONTEXT", "SUBSCRIBER_STORAGE_VERSION", "pendingSubscriptions", "SUBSCRIBER_EVENTS", "createdEvent", "eventName", "deletedEvent", "reason", "getSdkError", "TRANSPORT_TYPES", "shouldThrow", "subId", "subscribePromise", "onSubscribe", "subscription", "err", "subscriptions", "s", "subs", "numOfBatches", "batch", "persisted", "response", "sleep", "m", "<PERSON><PERSON><PERSON><PERSON>", "RELAYER_CONTEXT", "isNode", "RELAYER_PROVIDER_EVENTS", "pino", "getDefaultLoggerOptions", "RELAYER_DEFAULT_LOGGER", "MessageTracker", "Subscriber", "Publisher", "RELAYER_DEFAULT_RELAY_URL", "isAndroid", "getAppId", "isIos", "shouldThrowOnFailure", "resolvePromise", "onSubCreated", "relayUrl", "isOnline", "sortedMessages", "messageEvent", "expiry", "calcExpiry", "FIVE_MINUTES", "pairing", "onDisconnect", "_e", "auth", "JsonRpcProvider", "WsConnection", "formatRelayRpcUrl", "RELAYER_SDK_VERSION", "exists", "isJsonRpcRequest", "RELAYER_SUBSCRIBER_SUFFIX", "publishedAt", "isJsonRpcResponse", "formatJsonRpcResult", "lastConnectedState", "subscribeToNetworkChange", "connected", "isAppVisible", "RELAYER_RECONNECT_TIMEOUT", "u", "IStore", "name", "storagePrefix", "<PERSON><PERSON><PERSON>", "STORE_STORAGE_VERSION", "value", "isProposalStruct", "isSessionStruct", "filter", "isEqual", "update", "PAIRING_CONTEXT", "PAIRING_STORAGE_VERSION", "TYPE_1", "methods", "RELAYER_DEFAULT_PROTOCOL", "uri", "formatUri", "PAIRING_EVENTS", "EVENT_CLIENT_PAIRING_TRACES", "expiryTimestamp", "parseUri", "existingPairing", "EVENT_CLIENT_PAIRING_ERRORS", "done", "createDelayedPromise", "engineEvent", "metadata", "method", "formatJsonRpcRequest", "PAIRING_RPC_OPTS", "formatJsonRpcError", "expirer<PERSON><PERSON><PERSON><PERSON><PERSON>", "expiredPairings", "isExpired", "resMethod", "_topic", "isJsonRpcResult", "isJsonRpcError", "isValidParams", "isValidUrl", "isValidString", "Store", "transportType", "EXPIRER_EVENTS", "parseExpirer<PERSON>arget", "IJsonRpcHistory", "HISTORY_CONTEXT", "HISTORY_STORAGE_VERSION", "record", "chainId", "HISTORY_EVENTS", "requests", "requestEvent", "records", "deleted", "IExpirer", "EXPIRER_CONTEXT", "EXPIRER_STORAGE_VERSION", "expiration", "target", "formatTopicTarget", "formatIdTarget", "expirations", "expiredEvent", "IVerify", "store", "VERIFY_CONTEXT", "VERIFY_SERVER_V3", "CORE_VERSION", "<PERSON><PERSON><PERSON><PERSON>", "origin", "decryptedId", "src", "document", "getDocument", "abortTimeout", "attestationJwt", "abortListener", "iframe", "data", "decodeJWT", "attestationId", "encryptedId", "validation", "verifyUrl", "url", "timeout", "VERIFY_SERVER", "TRUSTED_VERIFY_URLS", "new<PERSON>ey", "verifyP256Jwt", "isTestRun", "timer", "IEchoClient", "projectId", "ECHO_CONTEXT", "clientId", "token", "notificationType", "enableEncrypted", "echoUrl", "ECHO_URL", "I", "IEventClient", "telemetryEnabled", "EVENTS_STORAGE_CONTEXT", "EVENTS_STORAGE_VERSION", "initEvent", "uuidv4", "formatUA", "trace", "eventId", "bundleId", "timestamp", "eventObj", "fromMiliseconds", "EVENTS_STORAGE_CLEANUP_INTERVAL", "errorType", "events", "eventsToSend", "_", "platform", "EVENTS_CLIENT_API_URL", "getAppMetadata", "Core", "ICore", "CORE_PROTOCOL", "CORE_CONTEXT", "sessionExists", "globalCore", "loggerOptions", "CORE_DEFAULT", "chunkLoggerController", "generatePlatformLogger", "HeartBeat", "Crypto", "JsonRpcHistory", "Expirer", "KeyValueStorage", "CORE_STORAGE_OPTIONS", "Relayer", "Pairing", "Verify", "EchoClient", "EventClient", "WALLETCONNECT_CLIENT_ID", "universalLink", "WALLETCONNECT_LINK_MODE_APPS", "customStoragePrefix", "globalCorePrefix", "counterKey", "WalletConnectCore"], "mappings": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61], "debugId": null}}]}