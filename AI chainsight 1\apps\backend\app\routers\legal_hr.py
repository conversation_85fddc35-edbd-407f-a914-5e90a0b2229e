from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
import logging
import os

from ..core.database import get_db, DocumentAnalysis
from ..core.security import get_current_user_optional
from ..services.legal_hr_service import LegalHRService

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize legal HR service
legal_hr_service = LegalHRService()

# Pydantic models
class DocumentAnalysisResponse(BaseModel):
    document_type: str
    extracted_text: str
    analysis_result: Dict[str, Any]
    confidence: float
    processing_time: float

class ResumeAnalysisResponse(BaseModel):
    candidate_name: str
    contact_info: Dict[str, str]
    skills: List[str]
    experience: List[Dict[str, Any]]
    education: List[Dict[str, Any]]
    summary: str
    score: float

class ContractAnalysisResponse(BaseModel):
    contract_type: str
    parties: List[str]
    key_terms: Dict[str, Any]
    obligations: List[str]
    risks: List[str]
    recommendations: List[str]

@router.post("/analyze-resume", response_model=ResumeAnalysisResponse)
async def analyze_resume(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Analyze resume document"""
    try:
        logger.info(f"Analyzing resume: {file.filename}")
        
        # Validate file type
        allowed_types = ["application/pdf", "application/msword", 
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                        "text/plain"]
        
        if file.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail="Unsupported file type")
        
        # Save uploaded file
        upload_dir = "uploads/resumes"
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Analyze resume
        analysis_result = await legal_hr_service.analyze_resume(file_path)
        
        # Save to database
        document_analysis = DocumentAnalysis(
            file_path=file_path,
            document_type="resume",
            extracted_text=analysis_result["extracted_text"],
            analysis_result=str(analysis_result["analysis"]),
            confidence=analysis_result["confidence"],
            user_id=current_user.id if current_user else None
        )
        
        db.add(document_analysis)
        db.commit()
        
        logger.info(f"Resume analysis completed for: {analysis_result['analysis']['candidate_name']}")
        
        return ResumeAnalysisResponse(**analysis_result["analysis"])
        
    except Exception as e:
        logger.error(f"Resume analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyze-contract", response_model=ContractAnalysisResponse)
async def analyze_contract(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Analyze legal contract"""
    try:
        logger.info(f"Analyzing contract: {file.filename}")
        
        # Validate file type
        allowed_types = ["application/pdf", "application/msword", 
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                        "text/plain"]
        
        if file.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail="Unsupported file type")
        
        # Save uploaded file
        upload_dir = "uploads/contracts"
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Analyze contract
        analysis_result = await legal_hr_service.analyze_contract(file_path)
        
        # Save to database
        document_analysis = DocumentAnalysis(
            file_path=file_path,
            document_type="contract",
            extracted_text=analysis_result["extracted_text"],
            analysis_result=str(analysis_result["analysis"]),
            confidence=analysis_result["confidence"],
            user_id=current_user.id if current_user else None
        )
        
        db.add(document_analysis)
        db.commit()
        
        logger.info(f"Contract analysis completed: {analysis_result['analysis']['contract_type']}")
        
        return ContractAnalysisResponse(**analysis_result["analysis"])
        
    except Exception as e:
        logger.error(f"Contract analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/extract-text")
async def extract_text(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Extract text from document"""
    try:
        logger.info(f"Extracting text from: {file.filename}")
        
        # Save uploaded file
        upload_dir = "uploads/documents"
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Extract text
        extracted_text = await legal_hr_service.extract_text(file_path)
        
        return {
            "filename": file.filename,
            "extracted_text": extracted_text["text"],
            "word_count": extracted_text["word_count"],
            "character_count": extracted_text["character_count"],
            "file_path": file_path
        }
        
    except Exception as e:
        logger.error(f"Text extraction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/generate-job-description")
async def generate_job_description(
    position: str,
    department: str,
    experience_level: str,
    skills_required: List[str],
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Generate job description"""
    try:
        logger.info(f"Generating job description for: {position}")
        
        job_description = await legal_hr_service.generate_job_description(
            position=position,
            department=department,
            experience_level=experience_level,
            skills_required=skills_required
        )
        
        return {
            "position": position,
            "department": department,
            "job_description": job_description["description"],
            "responsibilities": job_description["responsibilities"],
            "requirements": job_description["requirements"],
            "qualifications": job_description["qualifications"]
        }
        
    except Exception as e:
        logger.error(f"Job description generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/score-resume")
async def score_resume(
    resume_text: str,
    job_description: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Score resume against job description"""
    try:
        logger.info("Scoring resume against job description")
        
        if not resume_text.strip():
            raise HTTPException(status_code=400, detail="Resume text is required")
        
        if not job_description.strip():
            raise HTTPException(status_code=400, detail="Job description is required")
        
        scoring_result = await legal_hr_service.score_resume(
            resume_text=resume_text,
            job_description=job_description
        )
        
        return {
            "overall_score": scoring_result["overall_score"],
            "skill_match": scoring_result["skill_match"],
            "experience_match": scoring_result["experience_match"],
            "education_match": scoring_result["education_match"],
            "keyword_matches": scoring_result["keyword_matches"],
            "recommendations": scoring_result["recommendations"],
            "strengths": scoring_result["strengths"],
            "gaps": scoring_result["gaps"]
        }
        
    except Exception as e:
        logger.error(f"Resume scoring failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analysis-history")
async def get_analysis_history(
    document_type: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Get document analysis history"""
    try:
        query = db.query(DocumentAnalysis)
        
        if document_type:
            query = query.filter(DocumentAnalysis.document_type == document_type)
        
        if current_user:
            query = query.filter(DocumentAnalysis.user_id == current_user.id)
        
        analyses = query.order_by(DocumentAnalysis.created_at.desc())\
                       .offset(offset)\
                       .limit(limit)\
                       .all()
        
        return {
            "analyses": [
                {
                    "id": a.id,
                    "file_path": a.file_path,
                    "document_type": a.document_type,
                    "confidence": a.confidence,
                    "created_at": a.created_at.isoformat()
                }
                for a in analyses
            ],
            "total": len(analyses)
        }
        
    except Exception as e:
        logger.error(f"Get analysis history failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/analysis/{analysis_id}")
async def delete_analysis(
    analysis_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Delete document analysis"""
    try:
        analysis = db.query(DocumentAnalysis).filter(DocumentAnalysis.id == analysis_id).first()
        
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        # Check ownership if user is authenticated
        if current_user and analysis.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="Not authorized to delete this analysis")
        
        # Delete file if it exists
        if os.path.exists(analysis.file_path):
            os.remove(analysis.file_path)
        
        # Delete database record
        db.delete(analysis)
        db.commit()
        
        return {"message": "Analysis deleted successfully"}
        
    except Exception as e:
        logger.error(f"Delete analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/supported-formats")
async def get_supported_formats():
    """Get supported document formats"""
    return {
        "supported_formats": [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/plain"
        ],
        "max_file_size": "10MB",
        "document_types": ["resume", "contract", "legal_document", "job_description"]
    }
