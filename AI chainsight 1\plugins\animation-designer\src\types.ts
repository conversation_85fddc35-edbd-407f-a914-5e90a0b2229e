export interface Animation {
  id: string;
  name: string;
  type: 'svg' | 'lottie' | 'gif' | 'video';
  duration: number; // in milliseconds
  fps: number;
  width: number;
  height: number;
  frames: AnimationFrame[];
  layers: Layer[];
  config: AnimationConfig;
  metadata: AnimationMetadata;
}

export interface AnimationFrame {
  id: string;
  timestamp: number;
  elements: FrameElement[];
  transitions: Transition[];
}

export interface Layer {
  id: string;
  name: string;
  type: 'shape' | 'text' | 'image' | 'group';
  visible: boolean;
  locked: boolean;
  opacity: number;
  blend_mode: BlendMode;
  transform: Transform;
  effects: Effect[];
  parent_id?: string;
}

export interface FrameElement {
  id: string;
  layer_id: string;
  shape: Shape;
  style: Style;
  transform: Transform;
  animations: PropertyAnimation[];
}

export interface Shape {
  type: 'rectangle' | 'circle' | 'ellipse' | 'polygon' | 'path' | 'text' | 'line';
  properties: ShapeProperties;
  path?: string; // SVG path data
}

export interface ShapeProperties {
  x: number;
  y: number;
  width?: number;
  height?: number;
  radius?: number;
  rx?: number;
  ry?: number;
  points?: Point[];
  text?: string;
  font_family?: string;
  font_size?: number;
  font_weight?: string;
}

export interface Point {
  x: number;
  y: number;
}

export interface Style {
  fill: Fill;
  stroke: Stroke;
  shadow: Shadow;
  filters: Filter[];
}

export interface Fill {
  type: 'solid' | 'gradient' | 'pattern';
  color?: string;
  gradient?: Gradient;
  pattern?: Pattern;
  opacity: number;
}

export interface Stroke {
  color: string;
  width: number;
  opacity: number;
  dash_array?: number[];
  line_cap: 'butt' | 'round' | 'square';
  line_join: 'miter' | 'round' | 'bevel';
}

export interface Shadow {
  color: string;
  offset_x: number;
  offset_y: number;
  blur: number;
  opacity: number;
}

export interface Gradient {
  type: 'linear' | 'radial';
  stops: ColorStop[];
  start_point: Point;
  end_point: Point;
  center_point?: Point;
  radius?: number;
}

export interface ColorStop {
  offset: number; // 0-1
  color: string;
  opacity: number;
}

export interface Pattern {
  type: 'image' | 'texture';
  url: string;
  repeat: 'repeat' | 'no-repeat' | 'repeat-x' | 'repeat-y';
  scale: number;
}

export interface Filter {
  type: 'blur' | 'brightness' | 'contrast' | 'saturate' | 'hue-rotate';
  value: number;
}

export interface Transform {
  translate_x: number;
  translate_y: number;
  scale_x: number;
  scale_y: number;
  rotation: number; // in degrees
  skew_x: number;
  skew_y: number;
  origin_x: number;
  origin_y: number;
}

export interface PropertyAnimation {
  property: string;
  keyframes: Keyframe[];
  easing: Easing;
  duration: number;
  delay: number;
  repeat: number;
  direction: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
}

export interface Keyframe {
  time: number; // 0-1
  value: any;
  easing?: Easing;
}

export interface Easing {
  type: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'cubic-bezier';
  values?: [number, number, number, number]; // for cubic-bezier
}

export interface Transition {
  from_frame: string;
  to_frame: string;
  duration: number;
  easing: Easing;
  properties: string[];
}

export interface Motion {
  type: 'translate' | 'rotate' | 'scale' | 'skew' | 'opacity' | 'color';
  start_value: any;
  end_value: any;
  duration: number;
  easing: Easing;
  delay: number;
}

export interface Effect {
  type: 'glow' | 'drop-shadow' | 'inner-shadow' | 'outline' | 'bevel';
  properties: EffectProperties;
  enabled: boolean;
}

export interface EffectProperties {
  color?: string;
  size?: number;
  opacity?: number;
  offset_x?: number;
  offset_y?: number;
  blur?: number;
  spread?: number;
}

export interface ColorScheme {
  id: string;
  name: string;
  colors: string[];
  type: 'monochromatic' | 'analogous' | 'complementary' | 'triadic' | 'custom';
}

export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  thumbnail: string;
  animation: Animation;
  customizable_properties: CustomizableProperty[];
}

export interface CustomizableProperty {
  name: string;
  type: 'color' | 'text' | 'number' | 'boolean' | 'select';
  default_value: any;
  options?: any[];
  min?: number;
  max?: number;
  step?: number;
}

export interface ExportOptions {
  format: 'svg' | 'lottie' | 'gif' | 'mp4' | 'webm' | 'png_sequence';
  quality: 'low' | 'medium' | 'high' | 'ultra';
  fps?: number;
  width?: number;
  height?: number;
  background_color?: string;
  transparent_background?: boolean;
  loop?: boolean;
  compression?: number;
  optimize?: boolean;
}

export interface AnimationConfig {
  auto_play: boolean;
  loop: boolean;
  reverse: boolean;
  bounce: boolean;
  speed: number;
  quality: 'low' | 'medium' | 'high';
  preload: boolean;
}

export interface AnimationMetadata {
  created_at: Date;
  updated_at: Date;
  author: string;
  version: string;
  tags: string[];
  description?: string;
  file_size?: number;
  complexity_score?: number;
}

export interface RenderOptions {
  width: number;
  height: number;
  background_color?: string;
  transparent: boolean;
  anti_alias: boolean;
  quality: number;
}

export interface ExportResult {
  success: boolean;
  file_path?: string;
  file_size?: number;
  format: string;
  duration?: number;
  error?: string;
  metadata: {
    export_time: number;
    compression_ratio?: number;
    quality_score?: number;
  };
}

export interface AnimationLibrary {
  id: string;
  name: string;
  animations: Animation[];
  templates: Template[];
  color_schemes: ColorScheme[];
  created_at: Date;
}

export interface PreviewOptions {
  width: number;
  height: number;
  background_color: string;
  show_controls: boolean;
  auto_play: boolean;
  loop: boolean;
}

export interface TimelineEvent {
  id: string;
  timestamp: number;
  type: 'keyframe' | 'transition' | 'effect';
  target_id: string;
  properties: Record<string, any>;
}

export interface BlendMode {
  type: 'normal' | 'multiply' | 'screen' | 'overlay' | 'soft-light' | 'hard-light' | 'color-dodge' | 'color-burn' | 'darken' | 'lighten' | 'difference' | 'exclusion';
}

export interface AnimationStats {
  total_frames: number;
  total_layers: number;
  total_elements: number;
  total_animations: number;
  complexity_score: number;
  estimated_file_size: number;
  render_time_estimate: number;
}
