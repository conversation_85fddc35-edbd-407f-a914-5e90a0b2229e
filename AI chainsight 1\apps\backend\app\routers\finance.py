from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
import logging

from ..core.database import get_db, FinanceAnalysis
from ..core.security import get_current_user_optional
from ..services.finance_service import FinanceService

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize finance service
finance_service = FinanceService()

# Pydantic models
class PredictionRequest(BaseModel):
    symbol: str
    timeframe: str = "1d"  # 1h, 1d, 1w, 1m
    periods: int = 30

class PredictionResponse(BaseModel):
    symbol: str
    predictions: List[Dict[str, Any]]
    confidence: float
    model_used: str

class MarketAnalysisRequest(BaseModel):
    symbols: List[str]
    analysis_type: str = "technical"  # technical, fundamental, sentiment

class PortfolioOptimizationRequest(BaseModel):
    assets: List[str]
    weights: Optional[List[float]] = None
    risk_tolerance: str = "moderate"  # conservative, moderate, aggressive

@router.post("/predict-price", response_model=PredictionResponse)
async def predict_price(
    request: PredictionRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Predict asset price using AI models"""
    try:
        logger.info(f"Predicting price for {request.symbol}")
        
        # Validate symbol
        if not request.symbol.strip():
            raise HTTPException(status_code=400, detail="Symbol is required")
        
        # Generate prediction
        prediction = await finance_service.predict_price(
            symbol=request.symbol,
            timeframe=request.timeframe,
            periods=request.periods
        )
        
        # Save analysis to database
        analysis = FinanceAnalysis(
            symbol=request.symbol,
            analysis_type="prediction",
            input_data=request.json(),
            result=prediction["predictions"].__str__(),
            confidence=prediction["confidence"],
            user_id=current_user.id if current_user else None
        )
        
        db.add(analysis)
        db.commit()
        
        return PredictionResponse(
            symbol=request.symbol,
            predictions=prediction["predictions"],
            confidence=prediction["confidence"],
            model_used=prediction["model_used"]
        )
        
    except Exception as e:
        logger.error(f"Price prediction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyze-market")
async def analyze_market(
    request: MarketAnalysisRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Perform market analysis on multiple assets"""
    try:
        logger.info(f"Analyzing market for symbols: {request.symbols}")
        
        if not request.symbols:
            raise HTTPException(status_code=400, detail="At least one symbol is required")
        
        analysis = await finance_service.analyze_market(
            symbols=request.symbols,
            analysis_type=request.analysis_type
        )
        
        return {
            "symbols": request.symbols,
            "analysisType": request.analysis_type,
            "results": analysis["results"],
            "summary": analysis["summary"],
            "timestamp": analysis["timestamp"]
        }
        
    except Exception as e:
        logger.error(f"Market analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/optimize-portfolio")
async def optimize_portfolio(
    request: PortfolioOptimizationRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Optimize portfolio allocation"""
    try:
        logger.info(f"Optimizing portfolio for assets: {request.assets}")
        
        if not request.assets:
            raise HTTPException(status_code=400, detail="At least one asset is required")
        
        optimization = await finance_service.optimize_portfolio(
            assets=request.assets,
            weights=request.weights,
            risk_tolerance=request.risk_tolerance
        )
        
        return {
            "assets": request.assets,
            "optimizedWeights": optimization["weights"],
            "expectedReturn": optimization["expected_return"],
            "risk": optimization["risk"],
            "sharpeRatio": optimization["sharpe_ratio"],
            "recommendations": optimization["recommendations"]
        }
        
    except Exception as e:
        logger.error(f"Portfolio optimization failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/market-data/{symbol}")
async def get_market_data(
    symbol: str,
    timeframe: str = "1d",
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get real-time market data"""
    try:
        if not symbol.strip():
            raise HTTPException(status_code=400, detail="Symbol is required")
        
        market_data = await finance_service.get_market_data(
            symbol=symbol,
            timeframe=timeframe,
            limit=limit
        )
        
        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "data": market_data["data"],
            "lastUpdate": market_data["last_update"],
            "source": market_data["source"]
        }
        
    except Exception as e:
        logger.error(f"Get market data failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/trending")
async def get_trending_assets(
    category: str = "crypto",  # crypto, stocks, forex
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """Get trending assets"""
    try:
        trending = await finance_service.get_trending_assets(
            category=category,
            limit=limit
        )
        
        return {
            "category": category,
            "trending": trending["assets"],
            "lastUpdate": trending["last_update"]
        }
        
    except Exception as e:
        logger.error(f"Get trending assets failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/risk-assessment/{symbol}")
async def assess_risk(
    symbol: str,
    timeframe: str = "1d",
    db: Session = Depends(get_db)
):
    """Assess investment risk for an asset"""
    try:
        if not symbol.strip():
            raise HTTPException(status_code=400, detail="Symbol is required")
        
        risk_assessment = await finance_service.assess_risk(
            symbol=symbol,
            timeframe=timeframe
        )
        
        return {
            "symbol": symbol,
            "riskScore": risk_assessment["risk_score"],
            "riskLevel": risk_assessment["risk_level"],
            "volatility": risk_assessment["volatility"],
            "factors": risk_assessment["factors"],
            "recommendations": risk_assessment["recommendations"]
        }
        
    except Exception as e:
        logger.error(f"Risk assessment failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analysis-history")
async def get_analysis_history(
    symbol: Optional[str] = None,
    analysis_type: Optional[str] = None,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Get analysis history"""
    try:
        query = db.query(FinanceAnalysis)
        
        if symbol:
            query = query.filter(FinanceAnalysis.symbol == symbol)
        
        if analysis_type:
            query = query.filter(FinanceAnalysis.analysis_type == analysis_type)
        
        if current_user:
            query = query.filter(FinanceAnalysis.user_id == current_user.id)
        
        analyses = query.order_by(FinanceAnalysis.created_at.desc()).limit(limit).all()
        
        return {
            "analyses": [
                {
                    "id": a.id,
                    "symbol": a.symbol,
                    "analysisType": a.analysis_type,
                    "confidence": a.confidence,
                    "createdAt": a.created_at.isoformat()
                }
                for a in analyses
            ],
            "total": len(analyses)
        }
        
    except Exception as e:
        logger.error(f"Get analysis history failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/indicators/{symbol}")
async def get_technical_indicators(
    symbol: str,
    indicators: str = "sma,ema,rsi,macd",  # Comma-separated list
    timeframe: str = "1d",
    db: Session = Depends(get_db)
):
    """Get technical indicators for an asset"""
    try:
        if not symbol.strip():
            raise HTTPException(status_code=400, detail="Symbol is required")
        
        indicator_list = [i.strip() for i in indicators.split(",")]
        
        technical_data = await finance_service.get_technical_indicators(
            symbol=symbol,
            indicators=indicator_list,
            timeframe=timeframe
        )
        
        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "indicators": technical_data["indicators"],
            "signals": technical_data["signals"],
            "lastUpdate": technical_data["last_update"]
        }
        
    except Exception as e:
        logger.error(f"Get technical indicators failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
