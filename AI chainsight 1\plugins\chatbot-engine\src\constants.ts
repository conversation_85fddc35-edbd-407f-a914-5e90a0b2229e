export const INTENT_TYPES = {
  // General conversation
  GREETING: 'greeting',
  GOODBYE: 'goodbye',
  HELP: 'help',
  CLARIFICATION: 'clarification',
  SMALL_TALK: 'small_talk',
  
  // Financial queries
  PRICE_INQUIRY: 'price_inquiry',
  MARKET_ANALYSIS: 'market_analysis',
  PORTFOLIO_QUERY: 'portfolio_query',
  INVESTMENT_ADVICE: 'investment_advice',
  RISK_ASSESSMENT: 'risk_assessment',
  
  // Trading actions
  BUY_ORDER: 'buy_order',
  SELL_ORDER: 'sell_order',
  SET_ALERT: 'set_alert',
  CANCEL_ORDER: 'cancel_order',
  
  // Information requests
  NEWS_REQUEST: 'news_request',
  EDUCATION: 'education',
  EXPLANATION: 'explanation',
  COMPARISON: 'comparison',
  
  // Account management
  ACCOUNT_INFO: 'account_info',
  SETTINGS: 'settings',
  PREFERENCES: 'preferences',
  
  // Technical support
  TECHNICAL_ISSUE: 'technical_issue',
  FEATURE_REQUEST: 'feature_request',
  BUG_REPORT: 'bug_report'
} as const;

export const ENTITY_TYPES = {
  // Financial entities
  CRYPTOCURRENCY: 'cryptocurrency',
  STOCK: 'stock',
  CURRENCY: 'currency',
  AMOUNT: 'amount',
  PERCENTAGE: 'percentage',
  
  // Time entities
  DATE: 'date',
  TIME: 'time',
  DURATION: 'duration',
  TIMEFRAME: 'timeframe',
  
  // Market entities
  EXCHANGE: 'exchange',
  MARKET_CAP: 'market_cap',
  VOLUME: 'volume',
  PRICE_LEVEL: 'price_level',
  
  // Technical analysis
  INDICATOR: 'indicator',
  PATTERN: 'pattern',
  SUPPORT_RESISTANCE: 'support_resistance',
  
  // User entities
  PORTFOLIO: 'portfolio',
  WATCHLIST: 'watchlist',
  ALERT: 'alert',
  
  // General entities
  PERSON: 'person',
  ORGANIZATION: 'organization',
  LOCATION: 'location',
  NUMBER: 'number'
} as const;

export const RESPONSE_TYPES = {
  TEXT: 'text',
  RICH: 'rich',
  ACTION: 'action',
  SUGGESTION: 'suggestion',
  ERROR: 'error',
  LOADING: 'loading'
} as const;

export const PERSONALITY_TRAITS = {
  HELPFULNESS: 'helpfulness',
  ENTHUSIASM: 'enthusiasm',
  PROFESSIONALISM: 'professionalism',
  EMPATHY: 'empathy',
  CONFIDENCE: 'confidence',
  PATIENCE: 'patience',
  HUMOR: 'humor',
  TECHNICAL_EXPERTISE: 'technical_expertise'
} as const;

export const MEMORY_TYPES = {
  SHORT_TERM: 'short_term',
  LONG_TERM: 'long_term',
  EPISODIC: 'episodic',
  SEMANTIC: 'semantic',
  PROCEDURAL: 'procedural'
} as const;

export const COMMUNICATION_STYLES = {
  FORMAL: 'formal',
  CASUAL: 'casual',
  TECHNICAL: 'technical',
  FRIENDLY: 'friendly',
  PROFESSIONAL: 'professional',
  EDUCATIONAL: 'educational'
} as const;

export const CONFIDENCE_THRESHOLDS = {
  HIGH: 0.8,
  MEDIUM: 0.6,
  LOW: 0.4,
  MINIMUM: 0.2
} as const;

export const DEFAULT_RESPONSES = {
  GREETING: [
    "Hello! I'm Connectouch, your AI market assistant. How can I help you today?",
    "Hi there! Ready to explore the markets together?",
    "Welcome back! What would you like to know about the markets today?"
  ],
  
  GOODBYE: [
    "Goodbye! Feel free to ask me anything about the markets anytime.",
    "See you later! Happy trading!",
    "Take care! I'll be here when you need market insights."
  ],
  
  HELP: [
    "I can help you with market analysis, price inquiries, portfolio management, and trading insights. What specific area interests you?",
    "I'm here to assist with all your financial market needs. Try asking about prices, trends, or analysis!",
    "You can ask me about cryptocurrency prices, stock analysis, market trends, or portfolio advice. What would you like to explore?"
  ],
  
  CLARIFICATION: [
    "Could you please provide more details about what you're looking for?",
    "I want to make sure I understand correctly. Could you rephrase that?",
    "Let me clarify - are you asking about...?"
  ],
  
  ERROR: [
    "I apologize, but I encountered an issue processing your request. Please try again.",
    "Something went wrong on my end. Could you please rephrase your question?",
    "I'm having trouble with that request. Let me know if you'd like to try something else."
  ],
  
  UNKNOWN_INTENT: [
    "I'm not sure I understand. Could you please rephrase that?",
    "That's an interesting question! Could you provide more context?",
    "I want to help, but I need a bit more information. Could you be more specific?"
  ]
} as const;

export const OPENAI_MODELS = {
  GPT_4: 'gpt-4',
  GPT_4_TURBO: 'gpt-4-1106-preview',
  GPT_3_5_TURBO: 'gpt-3.5-turbo',
  GPT_3_5_TURBO_16K: 'gpt-3.5-turbo-16k'
} as const;

export const DEFAULT_OPENAI_CONFIG = {
  model: OPENAI_MODELS.GPT_4_TURBO,
  temperature: 0.7,
  max_tokens: 1000,
  top_p: 1,
  frequency_penalty: 0,
  presence_penalty: 0
} as const;

export const RATE_LIMITS = {
  REQUESTS_PER_MINUTE: 60,
  REQUESTS_PER_HOUR: 1000,
  TOKENS_PER_MINUTE: 40000,
  TOKENS_PER_DAY: 1000000
} as const;

export const CACHE_DURATIONS = {
  PRICE_DATA: 30, // seconds
  MARKET_DATA: 60,
  NEWS_DATA: 300,
  ANALYSIS_RESULTS: 600,
  USER_PREFERENCES: 3600
} as const;

export const SYSTEM_PROMPTS = {
  DEFAULT: `You are Connectouch, an advanced AI assistant specializing in financial markets and cryptocurrency. You are knowledgeable, helpful, and provide accurate market insights. Always be professional yet approachable, and ensure your responses are informative and actionable.

Key traits:
- Expert knowledge in crypto, stocks, and financial markets
- Ability to analyze market trends and provide insights
- Helpful and patient with users of all experience levels
- Professional yet friendly communication style
- Always prioritize accuracy and user safety in financial advice

Remember to:
- Provide disclaimers for financial advice
- Encourage users to do their own research
- Be clear about risks in trading and investing
- Offer educational content when appropriate`,

  TECHNICAL_ANALYSIS: `You are a technical analysis expert. Focus on chart patterns, indicators, and market trends. Provide clear explanations of technical concepts and their implications.`,

  FUNDAMENTAL_ANALYSIS: `You are a fundamental analysis expert. Focus on company financials, market conditions, and economic factors that drive asset values.`,

  RISK_MANAGEMENT: `You are a risk management specialist. Always emphasize the importance of proper risk management, position sizing, and diversification in trading and investing.`,

  EDUCATIONAL: `You are an educational assistant focused on teaching financial concepts. Break down complex topics into easy-to-understand explanations with practical examples.`
} as const;

export const MARKET_HOURS = {
  NYSE: {
    open: '09:30',
    close: '16:00',
    timezone: 'America/New_York'
  },
  NASDAQ: {
    open: '09:30',
    close: '16:00',
    timezone: 'America/New_York'
  },
  CRYPTO: {
    open: '00:00',
    close: '23:59',
    timezone: 'UTC',
    always_open: true
  }
} as const;

export const SUPPORTED_LANGUAGES = {
  ENGLISH: 'en',
  SPANISH: 'es',
  FRENCH: 'fr',
  GERMAN: 'de',
  CHINESE: 'zh',
  JAPANESE: 'ja',
  KOREAN: 'ko'
} as const;

export const FEEDBACK_TYPES = {
  HELPFUL: 'helpful',
  NOT_HELPFUL: 'not_helpful',
  INCORRECT: 'incorrect',
  INCOMPLETE: 'incomplete',
  TOO_TECHNICAL: 'too_technical',
  TOO_SIMPLE: 'too_simple'
} as const;
