{"name": "@chainsight/legal-hr-core", "version": "1.0.0", "description": "Advanced legal document and HR resume processing engine", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "tesseract.js": "^4.1.1", "natural": "^6.5.0", "compromise": "^14.10.0", "uuid": "^9.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "sharp": "^0.32.5", "jszip": "^3.10.1"}, "devDependencies": {"@types/lodash": "^4.14.195", "@types/node": "^20.5.0", "@types/uuid": "^9.0.2", "@types/pdf-parse": "^1.1.1", "typescript": "^5.1.6", "jest": "^29.6.2", "@types/jest": "^29.5.3", "ts-jest": "^29.1.1", "eslint": "^8.46.0", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1"}, "keywords": ["legal", "hr", "resume-parsing", "document-analysis", "nlp", "contract-analysis"], "author": "Chainsight Team", "license": "MIT"}