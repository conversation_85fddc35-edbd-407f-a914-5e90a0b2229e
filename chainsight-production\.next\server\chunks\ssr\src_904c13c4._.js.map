{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/fpga-core/interfaces/signal-types.ts"], "sourcesContent": ["/**\n * FPGA Signal Type Definitions\n * Standardized signal types for inter-module communication\n */\n\nexport type SignalType = \n  | 'crypto.analysis'\n  | 'crypto.price'\n  | 'crypto.portfolio'\n  | 'finance.analysis'\n  | 'finance.recommendation'\n  | 'finance.risk'\n  | 'legal.contract'\n  | 'legal.compliance'\n  | 'legal.advice'\n  | 'design.generate'\n  | 'design.analyze'\n  | 'design.optimize'\n  | 'face.detect'\n  | 'face.recognize'\n  | 'face.verify'\n  | 'support.query'\n  | 'support.response'\n  | 'support.escalate'\n  | 'web3.transaction'\n  | 'web3.contract'\n  | 'web3.wallet'\n  | 'system.status'\n  | 'system.error'\n  | 'system.metric';\n\nexport interface CryptoSignal {\n  symbol: string;\n  price?: number;\n  volume?: number;\n  marketCap?: number;\n  analysis?: string;\n  recommendation?: 'buy' | 'sell' | 'hold';\n  confidence?: number;\n}\n\nexport interface FinanceSignal {\n  type: 'stock' | 'forex' | 'commodity' | 'crypto';\n  symbol: string;\n  data: any;\n  analysis?: string;\n  risk?: 'low' | 'medium' | 'high';\n  recommendation?: string;\n}\n\nexport interface LegalSignal {\n  documentType: 'contract' | 'policy' | 'regulation';\n  content: string;\n  analysis?: string;\n  compliance?: boolean;\n  risks?: string[];\n  recommendations?: string[];\n}\n\nexport interface DesignSignal {\n  type: 'logo' | 'ui' | 'banner' | 'illustration';\n  prompt?: string;\n  style?: string;\n  dimensions?: { width: number; height: number };\n  result?: string; // URL or base64\n  feedback?: string;\n}\n\nexport interface FaceSignal {\n  image: string; // base64 or URL\n  operation: 'detect' | 'recognize' | 'verify';\n  faces?: Array<{\n    id?: string;\n    confidence: number;\n    bbox: { x: number; y: number; width: number; height: number };\n    features?: any;\n  }>;\n  identity?: string;\n  verified?: boolean;\n}\n\nexport interface SupportSignal {\n  query: string;\n  context?: string;\n  user?: string;\n  priority: 'low' | 'normal' | 'high' | 'urgent';\n  response?: string;\n  sentiment?: 'positive' | 'neutral' | 'negative';\n  resolved?: boolean;\n}\n\nexport interface Web3Signal {\n  network: string;\n  address?: string;\n  transaction?: {\n    hash: string;\n    from: string;\n    to: string;\n    value: string;\n    gas: string;\n  };\n  contract?: {\n    address: string;\n    abi: any[];\n    method: string;\n    params: any[];\n  };\n  wallet?: {\n    connected: boolean;\n    address: string;\n    balance: string;\n  };\n}\n\nexport interface SystemSignal {\n  component: string;\n  status?: 'online' | 'offline' | 'degraded';\n  error?: {\n    code: string;\n    message: string;\n    stack?: string;\n  };\n  metrics?: {\n    cpu: number;\n    memory: number;\n    latency: number;\n    throughput: number;\n  };\n}\n\nexport type SignalData = \n  | CryptoSignal\n  | FinanceSignal\n  | LegalSignal\n  | DesignSignal\n  | FaceSignal\n  | SupportSignal\n  | Web3Signal\n  | SystemSignal;\n\nexport interface TypedSignal<T extends SignalData = SignalData> {\n  id: string;\n  type: SignalType;\n  data: T;\n  timestamp: number;\n  source: string;\n  priority: 'low' | 'normal' | 'high' | 'critical';\n  metadata?: Record<string, any>;\n}\n\n// Signal validation utilities\nexport function isValidSignalType(type: string): type is SignalType {\n  const validTypes: SignalType[] = [\n    'crypto.analysis', 'crypto.price', 'crypto.portfolio',\n    'finance.analysis', 'finance.recommendation', 'finance.risk',\n    'legal.contract', 'legal.compliance', 'legal.advice',\n    'design.generate', 'design.analyze', 'design.optimize',\n    'face.detect', 'face.recognize', 'face.verify',\n    'support.query', 'support.response', 'support.escalate',\n    'web3.transaction', 'web3.contract', 'web3.wallet',\n    'system.status', 'system.error', 'system.metric'\n  ];\n  return validTypes.includes(type as SignalType);\n}\n\nexport function createSignal<T extends SignalData>(\n  type: SignalType,\n  data: T,\n  source: string,\n  priority: 'low' | 'normal' | 'high' | 'critical' = 'normal'\n): TypedSignal<T> {\n  return {\n    id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n    type,\n    data,\n    timestamp: Date.now(),\n    source,\n    priority,\n    metadata: {}\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAoJM,SAAS,kBAAkB,IAAY;IAC5C,MAAM,aAA2B;QAC/B;QAAmB;QAAgB;QACnC;QAAoB;QAA0B;QAC9C;QAAkB;QAAoB;QACtC;QAAmB;QAAkB;QACrC;QAAe;QAAkB;QACjC;QAAiB;QAAoB;QACrC;QAAoB;QAAiB;QACrC;QAAiB;QAAgB;KAClC;IACD,OAAO,WAAW,QAAQ,CAAC;AAC7B;AAEO,SAAS,aACd,IAAgB,EAChB,IAAO,EACP,MAAc,EACd,WAAmD,QAAQ;IAE3D,OAAO;QACL,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAC9D;QACA;QACA,WAAW,KAAK,GAAG;QACnB;QACA;QACA,UAAU,CAAC;IACb;AACF", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/fpga-core/signal-routing/router.ts"], "sourcesContent": ["/**\n * FPGA Signal Router - Central data routing system\n * Manages signal flow between processing units with minimal latency\n */\n\nimport { TypedSignal, SignalType, SignalData } from '../interfaces/signal-types';\nimport { ProcessingUnitInterface } from '../interfaces/pu-interface';\n\nexport interface RouteConfig {\n  from: string | string[];\n  to: string | string[];\n  condition?: (signal: TypedSignal) => boolean;\n  transform?: (signal: TypedSignal) => TypedSignal;\n  priority?: number;\n}\n\nexport interface RouterMetrics {\n  totalSignals: number;\n  routedSignals: number;\n  droppedSignals: number;\n  averageLatency: number;\n  throughput: number;\n}\n\nexport class SignalRouter {\n  private routes: Map<string, RouteConfig[]> = new Map();\n  private processingUnits: Map<string, ProcessingUnitInterface> = new Map();\n  private signalQueue: TypedSignal[] = [];\n  private metrics: RouterMetrics = {\n    totalSignals: 0,\n    routedSignals: 0,\n    droppedSignals: 0,\n    averageLatency: 0,\n    throughput: 0\n  };\n  private isProcessing = false;\n  private subscribers: Map<string, Set<(signal: TypedSignal) => void>> = new Map();\n\n  constructor() {\n    this.startProcessingLoop();\n  }\n\n  // Processing Unit Management\n  registerProcessingUnit(pu: ProcessingUnitInterface): void {\n    this.processingUnits.set(pu.config.id, pu);\n    console.log(`🔧 Registered PU: ${pu.config.name} (${pu.config.id})`);\n  }\n\n  unregisterProcessingUnit(id: string): void {\n    this.processingUnits.delete(id);\n    console.log(`🔧 Unregistered PU: ${id}`);\n  }\n\n  // Route Management\n  addRoute(signalType: SignalType, config: RouteConfig): void {\n    if (!this.routes.has(signalType)) {\n      this.routes.set(signalType, []);\n    }\n    this.routes.get(signalType)!.push(config);\n    console.log(`🛤️ Added route for ${signalType}`);\n  }\n\n  removeRoute(signalType: SignalType, config: RouteConfig): void {\n    const routes = this.routes.get(signalType);\n    if (routes) {\n      const index = routes.indexOf(config);\n      if (index > -1) {\n        routes.splice(index, 1);\n      }\n    }\n  }\n\n  // Signal Processing\n  async routeSignal(signal: TypedSignal): Promise<void> {\n    const startTime = Date.now();\n    this.metrics.totalSignals++;\n\n    try {\n      // Add to queue for processing\n      this.signalQueue.push(signal);\n      \n      // Notify subscribers\n      this.notifySubscribers(signal);\n      \n      // Update metrics\n      const latency = Date.now() - startTime;\n      this.updateLatencyMetrics(latency);\n      \n    } catch (error) {\n      console.error('❌ Signal routing error:', error);\n      this.metrics.droppedSignals++;\n    }\n  }\n\n  private async processSignalQueue(): Promise<void> {\n    if (this.isProcessing || this.signalQueue.length === 0) return;\n    \n    this.isProcessing = true;\n    \n    try {\n      // Sort by priority\n      this.signalQueue.sort((a, b) => {\n        const priorityOrder = { critical: 4, high: 3, normal: 2, low: 1 };\n        return priorityOrder[b.priority] - priorityOrder[a.priority];\n      });\n\n      // Process signals in batches\n      const batchSize = 10;\n      const batch = this.signalQueue.splice(0, batchSize);\n      \n      await Promise.all(batch.map(signal => this.processSignal(signal)));\n      \n    } finally {\n      this.isProcessing = false;\n    }\n  }\n\n  private async processSignal(signal: TypedSignal): Promise<void> {\n    const routes = this.routes.get(signal.type) || [];\n    \n    for (const route of routes) {\n      try {\n        // Check condition\n        if (route.condition && !route.condition(signal)) continue;\n        \n        // Transform signal if needed\n        let processedSignal = signal;\n        if (route.transform) {\n          processedSignal = route.transform(signal);\n        }\n        \n        // Route to target processing units\n        await this.deliverSignal(processedSignal, route);\n        this.metrics.routedSignals++;\n        \n      } catch (error) {\n        console.error('❌ Route processing error:', error);\n        this.metrics.droppedSignals++;\n      }\n    }\n  }\n\n  private async deliverSignal(signal: TypedSignal, route: RouteConfig): Promise<void> {\n    const targets = Array.isArray(route.to) ? route.to : [route.to];\n    \n    for (const target of targets) {\n      const pu = this.processingUnits.get(target);\n      if (pu && pu.canProcess(signal)) {\n        try {\n          const result = await pu.process(signal);\n          if (result) {\n            // Route result signals\n            const results = Array.isArray(result) ? result : [result];\n            for (const resultSignal of results) {\n              await this.routeSignal(resultSignal);\n            }\n          }\n        } catch (error) {\n          console.error(`❌ PU ${target} processing error:`, error);\n        }\n      }\n    }\n  }\n\n  // Subscription Management\n  subscribe(signalType: SignalType, callback: (signal: TypedSignal) => void): () => void {\n    if (!this.subscribers.has(signalType)) {\n      this.subscribers.set(signalType, new Set());\n    }\n    this.subscribers.get(signalType)!.add(callback);\n    \n    // Return unsubscribe function\n    return () => {\n      this.subscribers.get(signalType)?.delete(callback);\n    };\n  }\n\n  private notifySubscribers(signal: TypedSignal): void {\n    const callbacks = this.subscribers.get(signal.type);\n    if (callbacks) {\n      callbacks.forEach(callback => {\n        try {\n          callback(signal);\n        } catch (error) {\n          console.error('❌ Subscriber callback error:', error);\n        }\n      });\n    }\n  }\n\n  // Processing Loop\n  private startProcessingLoop(): void {\n    setInterval(() => {\n      this.processSignalQueue();\n      this.updateThroughputMetrics();\n    }, 10); // 100Hz processing frequency\n  }\n\n  // Metrics\n  private updateLatencyMetrics(latency: number): void {\n    this.metrics.averageLatency = \n      (this.metrics.averageLatency * (this.metrics.totalSignals - 1) + latency) / \n      this.metrics.totalSignals;\n  }\n\n  private updateThroughputMetrics(): void {\n    // Calculate throughput over last second\n    this.metrics.throughput = this.metrics.routedSignals; // Simplified\n  }\n\n  getMetrics(): RouterMetrics {\n    return { ...this.metrics };\n  }\n\n  // Utility Methods\n  getRegisteredPUs(): string[] {\n    return Array.from(this.processingUnits.keys());\n  }\n\n  getRoutes(): Map<string, RouteConfig[]> {\n    return new Map(this.routes);\n  }\n\n  async shutdown(): Promise<void> {\n    // Stop all processing units\n    for (const pu of this.processingUnits.values()) {\n      await pu.stop();\n    }\n    \n    // Clear queues\n    this.signalQueue = [];\n    this.subscribers.clear();\n    \n    console.log('🔧 Signal Router shutdown complete');\n  }\n}\n\n// Global router instance\nexport const globalRouter = new SignalRouter();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAqBM,MAAM;IACH,SAAqC,IAAI,MAAM;IAC/C,kBAAwD,IAAI,MAAM;IAClE,cAA6B,EAAE,CAAC;IAChC,UAAyB;QAC/B,cAAc;QACd,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,YAAY;IACd,EAAE;IACM,eAAe,MAAM;IACrB,cAA+D,IAAI,MAAM;IAEjF,aAAc;QACZ,IAAI,CAAC,mBAAmB;IAC1B;IAEA,6BAA6B;IAC7B,uBAAuB,EAA2B,EAAQ;QACxD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,EAAE;QACvC,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IACrE;IAEA,yBAAyB,EAAU,EAAQ;QACzC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAC5B,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;IACzC;IAEA,mBAAmB;IACnB,SAAS,UAAsB,EAAE,MAAmB,EAAQ;QAC1D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa;YAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE;QAChC;QACA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAa,IAAI,CAAC;QAClC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY;IACjD;IAEA,YAAY,UAAsB,EAAE,MAAmB,EAAQ;QAC7D,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC/B,IAAI,QAAQ;YACV,MAAM,QAAQ,OAAO,OAAO,CAAC;YAC7B,IAAI,QAAQ,CAAC,GAAG;gBACd,OAAO,MAAM,CAAC,OAAO;YACvB;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAAmB,EAAiB;QACpD,MAAM,YAAY,KAAK,GAAG;QAC1B,IAAI,CAAC,OAAO,CAAC,YAAY;QAEzB,IAAI;YACF,8BAA8B;YAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YAEtB,qBAAqB;YACrB,IAAI,CAAC,iBAAiB,CAAC;YAEvB,iBAAiB;YACjB,MAAM,UAAU,KAAK,GAAG,KAAK;YAC7B,IAAI,CAAC,oBAAoB,CAAC;QAE5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,CAAC,OAAO,CAAC,cAAc;QAC7B;IACF;IAEA,MAAc,qBAAoC;QAChD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,GAAG;QAExD,IAAI,CAAC,YAAY,GAAG;QAEpB,IAAI;YACF,mBAAmB;YACnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG;gBACxB,MAAM,gBAAgB;oBAAE,UAAU;oBAAG,MAAM;oBAAG,QAAQ;oBAAG,KAAK;gBAAE;gBAChE,OAAO,aAAa,CAAC,EAAE,QAAQ,CAAC,GAAG,aAAa,CAAC,EAAE,QAAQ,CAAC;YAC9D;YAEA,6BAA6B;YAC7B,MAAM,YAAY;YAClB,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG;YAEzC,MAAM,QAAQ,GAAG,CAAC,MAAM,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,aAAa,CAAC;QAE3D,SAAU;YACR,IAAI,CAAC,YAAY,GAAG;QACtB;IACF;IAEA,MAAc,cAAc,MAAmB,EAAiB;QAC9D,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,IAAI,KAAK,EAAE;QAEjD,KAAK,MAAM,SAAS,OAAQ;YAC1B,IAAI;gBACF,kBAAkB;gBAClB,IAAI,MAAM,SAAS,IAAI,CAAC,MAAM,SAAS,CAAC,SAAS;gBAEjD,6BAA6B;gBAC7B,IAAI,kBAAkB;gBACtB,IAAI,MAAM,SAAS,EAAE;oBACnB,kBAAkB,MAAM,SAAS,CAAC;gBACpC;gBAEA,mCAAmC;gBACnC,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAC1C,IAAI,CAAC,OAAO,CAAC,aAAa;YAE5B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,IAAI,CAAC,OAAO,CAAC,cAAc;YAC7B;QACF;IACF;IAEA,MAAc,cAAc,MAAmB,EAAE,KAAkB,EAAiB;QAClF,MAAM,UAAU,MAAM,OAAO,CAAC,MAAM,EAAE,IAAI,MAAM,EAAE,GAAG;YAAC,MAAM,EAAE;SAAC;QAE/D,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;YACpC,IAAI,MAAM,GAAG,UAAU,CAAC,SAAS;gBAC/B,IAAI;oBACF,MAAM,SAAS,MAAM,GAAG,OAAO,CAAC;oBAChC,IAAI,QAAQ;wBACV,uBAAuB;wBACvB,MAAM,UAAU,MAAM,OAAO,CAAC,UAAU,SAAS;4BAAC;yBAAO;wBACzD,KAAK,MAAM,gBAAgB,QAAS;4BAClC,MAAM,IAAI,CAAC,WAAW,CAAC;wBACzB;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,kBAAkB,CAAC,EAAE;gBACpD;YACF;QACF;IACF;IAEA,0BAA0B;IAC1B,UAAU,UAAsB,EAAE,QAAuC,EAAc;QACrF,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa;YACrC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,IAAI;QACvC;QACA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAa,GAAG,CAAC;QAEtC,8BAA8B;QAC9B,OAAO;YACL,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,OAAO;QAC3C;IACF;IAEQ,kBAAkB,MAAmB,EAAQ;QACnD,MAAM,YAAY,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,IAAI;QAClD,IAAI,WAAW;YACb,UAAU,OAAO,CAAC,CAAA;gBAChB,IAAI;oBACF,SAAS;gBACX,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;YACF;QACF;IACF;IAEA,kBAAkB;IACV,sBAA4B;QAClC,YAAY;YACV,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,uBAAuB;QAC9B,GAAG,KAAK,6BAA6B;IACvC;IAEA,UAAU;IACF,qBAAqB,OAAe,EAAQ;QAClD,IAAI,CAAC,OAAO,CAAC,cAAc,GACzB,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,IAAI,OAAO,IACxE,IAAI,CAAC,OAAO,CAAC,YAAY;IAC7B;IAEQ,0BAAgC;QACtC,wCAAwC;QACxC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa;IACrE;IAEA,aAA4B;QAC1B,OAAO;YAAE,GAAG,IAAI,CAAC,OAAO;QAAC;IAC3B;IAEA,kBAAkB;IAClB,mBAA6B;QAC3B,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI;IAC7C;IAEA,YAAwC;QACtC,OAAO,IAAI,IAAI,IAAI,CAAC,MAAM;IAC5B;IAEA,MAAM,WAA0B;QAC9B,4BAA4B;QAC5B,KAAK,MAAM,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,GAAI;YAC9C,MAAM,GAAG,IAAI;QACf;QAEA,eAAe;QACf,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC,WAAW,CAAC,KAAK;QAEtB,QAAQ,GAAG,CAAC;IACd;AACF;AAGO,MAAM,eAAe,IAAI", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/fpga-core/interconnect/fabric.ts"], "sourcesContent": ["/**\n * FPGA Interconnect Fabric - Communication layer for all processing units\n */\n\nimport { ProcessingUnitInterface } from '../interfaces/pu-interface';\nimport { TypedSignal, SignalType, createSignal } from '../interfaces/signal-types';\nimport { SignalRouter, globalRouter } from '../signal-routing/router';\n\nexport interface FabricConfig {\n  maxProcessingUnits: number;\n  signalBufferSize: number;\n  healthCheckInterval: number;\n  autoRecovery: boolean;\n}\n\nexport interface FabricMetrics {\n  registeredPUs: number;\n  activePUs: number;\n  totalSignals: number;\n  signalsPerSecond: number;\n  averageLatency: number;\n  errorRate: number;\n}\n\nexport class InterconnectFabric {\n  private router: SignalRouter;\n  private processingUnits: Map<string, ProcessingUnitInterface> = new Map();\n  private config: FabricConfig;\n  private metrics: FabricMetrics = {\n    registeredPUs: 0,\n    activePUs: 0,\n    totalSignals: 0,\n    signalsPerSecond: 0,\n    averageLatency: 0,\n    errorRate: 0\n  };\n  private healthCheckTimer?: NodeJS.Timeout;\n  private isInitialized = false;\n\n  constructor(config: Partial<FabricConfig> = {}) {\n    this.config = {\n      maxProcessingUnits: 20,\n      signalBufferSize: 1000,\n      healthCheckInterval: 5000, // 5 seconds\n      autoRecovery: true,\n      ...config\n    };\n    \n    this.router = globalRouter;\n    this.setupDefaultRoutes();\n  }\n\n  async initialize(): Promise<void> {\n    if (this.isInitialized) return;\n    \n    console.log('🔧 Initializing FPGA Interconnect Fabric...');\n    \n    // Start health monitoring\n    this.startHealthMonitoring();\n    \n    // Setup error handling\n    this.setupErrorHandling();\n    \n    this.isInitialized = true;\n    console.log('✅ FPGA Interconnect Fabric initialized');\n  }\n\n  // Processing Unit Management\n  async registerProcessingUnit(pu: ProcessingUnitInterface): Promise<void> {\n    if (this.processingUnits.size >= this.config.maxProcessingUnits) {\n      throw new Error('Maximum processing units limit reached');\n    }\n\n    try {\n      // Initialize the processing unit\n      await pu.initialize();\n      await pu.start();\n      \n      // Register with router\n      this.router.registerProcessingUnit(pu);\n      this.processingUnits.set(pu.config.id, pu);\n      \n      // Update metrics\n      this.metrics.registeredPUs = this.processingUnits.size;\n      this.updateActivePUs();\n      \n      console.log(`✅ Registered PU: ${pu.config.name} (${pu.config.id})`);\n      \n      // Emit registration event\n      await this.emitSignal(createSignal('system.status', {\n        component: pu.config.id,\n        status: 'online'\n      }, 'fabric', 'normal'));\n      \n    } catch (error) {\n      console.error(`❌ Failed to register PU ${pu.config.id}:`, error);\n      throw error;\n    }\n  }\n\n  async unregisterProcessingUnit(id: string): Promise<void> {\n    const pu = this.processingUnits.get(id);\n    if (!pu) return;\n\n    try {\n      // Stop the processing unit\n      await pu.stop();\n      \n      // Unregister from router\n      this.router.unregisterProcessingUnit(id);\n      this.processingUnits.delete(id);\n      \n      // Update metrics\n      this.metrics.registeredPUs = this.processingUnits.size;\n      this.updateActivePUs();\n      \n      console.log(`🔄 Unregistered PU: ${id}`);\n      \n    } catch (error) {\n      console.error(`❌ Failed to unregister PU ${id}:`, error);\n    }\n  }\n\n  // Signal Management\n  async emitSignal(signal: TypedSignal): Promise<void> {\n    try {\n      this.metrics.totalSignals++;\n      await this.router.routeSignal(signal);\n    } catch (error) {\n      console.error('❌ Signal emission error:', error);\n      this.metrics.errorRate = (this.metrics.errorRate + 1) / this.metrics.totalSignals;\n    }\n  }\n\n  subscribeToSignals(signalType: SignalType, callback: (signal: TypedSignal) => void): () => void {\n    return this.router.subscribe(signalType, callback);\n  }\n\n  // Health Monitoring\n  private startHealthMonitoring(): void {\n    this.healthCheckTimer = setInterval(async () => {\n      await this.performHealthCheck();\n    }, this.config.healthCheckInterval);\n  }\n\n  private async performHealthCheck(): Promise<void> {\n    const unhealthyPUs: string[] = [];\n    \n    for (const [id, pu] of this.processingUnits.entries()) {\n      try {\n        const isHealthy = await pu.healthCheck();\n        if (!isHealthy) {\n          unhealthyPUs.push(id);\n        }\n      } catch (error) {\n        console.error(`❌ Health check failed for PU ${id}:`, error);\n        unhealthyPUs.push(id);\n      }\n    }\n    \n    // Handle unhealthy PUs\n    if (unhealthyPUs.length > 0 && this.config.autoRecovery) {\n      await this.recoverUnhealthyPUs(unhealthyPUs);\n    }\n    \n    this.updateActivePUs();\n    this.updateMetrics();\n  }\n\n  private async recoverUnhealthyPUs(unhealthyPUs: string[]): Promise<void> {\n    for (const id of unhealthyPUs) {\n      const pu = this.processingUnits.get(id);\n      if (!pu) continue;\n      \n      try {\n        console.log(`🔄 Attempting to recover PU: ${id}`);\n        await pu.reset();\n        await pu.start();\n        console.log(`✅ Recovered PU: ${id}`);\n      } catch (error) {\n        console.error(`❌ Failed to recover PU ${id}:`, error);\n        // Consider removing the PU if recovery fails multiple times\n      }\n    }\n  }\n\n  // Default Signal Routes\n  private setupDefaultRoutes(): void {\n    // Route crypto queries to crypto PU\n    this.router.addRoute('crypto.analysis', {\n      from: '*',\n      to: 'crypto-pu'\n    });\n    \n    this.router.addRoute('crypto.price', {\n      from: '*',\n      to: 'crypto-pu'\n    });\n    \n    // Route support queries to support PU\n    this.router.addRoute('support.query', {\n      from: '*',\n      to: 'support-pu'\n    });\n    \n    // Route system status to all PUs for monitoring\n    this.router.addRoute('system.status', {\n      from: '*',\n      to: ['crypto-pu', 'support-pu'],\n      condition: (signal) => signal.data.component === 'system'\n    });\n  }\n\n  // Error Handling\n  private setupErrorHandling(): void {\n    // Subscribe to error signals\n    this.router.subscribe('system.error', (signal) => {\n      console.error('🚨 System error received:', signal.data);\n      this.handleSystemError(signal);\n    });\n  }\n\n  private async handleSystemError(signal: TypedSignal): Promise<void> {\n    const errorData = signal.data as any;\n    \n    // Log error\n    console.error(`🚨 System Error in ${errorData.component}:`, errorData.error);\n    \n    // Attempt recovery if auto-recovery is enabled\n    if (this.config.autoRecovery && errorData.component) {\n      const pu = this.processingUnits.get(errorData.component);\n      if (pu) {\n        try {\n          await pu.reset();\n          console.log(`🔄 Auto-recovered PU: ${errorData.component}`);\n        } catch (error) {\n          console.error(`❌ Auto-recovery failed for ${errorData.component}:`, error);\n        }\n      }\n    }\n  }\n\n  // Metrics & Monitoring\n  private updateActivePUs(): void {\n    let activePUs = 0;\n    for (const pu of this.processingUnits.values()) {\n      if (pu.state.status === 'idle' || pu.state.status === 'processing') {\n        activePUs++;\n      }\n    }\n    this.metrics.activePUs = activePUs;\n  }\n\n  private updateMetrics(): void {\n    const routerMetrics = this.router.getMetrics();\n    this.metrics.signalsPerSecond = routerMetrics.throughput;\n    this.metrics.averageLatency = routerMetrics.averageLatency;\n    this.metrics.errorRate = routerMetrics.droppedSignals / Math.max(routerMetrics.totalSignals, 1);\n  }\n\n  getMetrics(): FabricMetrics {\n    return { ...this.metrics };\n  }\n\n  getProcessingUnits(): ProcessingUnitInterface[] {\n    return Array.from(this.processingUnits.values());\n  }\n\n  getProcessingUnit(id: string): ProcessingUnitInterface | undefined {\n    return this.processingUnits.get(id);\n  }\n\n  // Shutdown\n  async shutdown(): Promise<void> {\n    console.log('🔄 Shutting down FPGA Interconnect Fabric...');\n    \n    // Stop health monitoring\n    if (this.healthCheckTimer) {\n      clearInterval(this.healthCheckTimer);\n    }\n    \n    // Stop all processing units\n    for (const pu of this.processingUnits.values()) {\n      try {\n        await pu.stop();\n      } catch (error) {\n        console.error('❌ Error stopping PU:', error);\n      }\n    }\n    \n    // Shutdown router\n    await this.router.shutdown();\n    \n    this.isInitialized = false;\n    console.log('✅ FPGA Interconnect Fabric shutdown complete');\n  }\n}\n\n// Global fabric instance\nexport const globalFabric = new InterconnectFabric();\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAGD;AACA;;;AAkBO,MAAM;IACH,OAAqB;IACrB,kBAAwD,IAAI,MAAM;IAClE,OAAqB;IACrB,UAAyB;QAC/B,eAAe;QACf,WAAW;QACX,cAAc;QACd,kBAAkB;QAClB,gBAAgB;QAChB,WAAW;IACb,EAAE;IACM,iBAAkC;IAClC,gBAAgB,MAAM;IAE9B,YAAY,SAAgC,CAAC,CAAC,CAAE;QAC9C,IAAI,CAAC,MAAM,GAAG;YACZ,oBAAoB;YACpB,kBAAkB;YAClB,qBAAqB;YACrB,cAAc;YACd,GAAG,MAAM;QACX;QAEA,IAAI,CAAC,MAAM,GAAG,kJAAA,CAAA,eAAY;QAC1B,IAAI,CAAC,kBAAkB;IACzB;IAEA,MAAM,aAA4B;QAChC,IAAI,IAAI,CAAC,aAAa,EAAE;QAExB,QAAQ,GAAG,CAAC;QAEZ,0BAA0B;QAC1B,IAAI,CAAC,qBAAqB;QAE1B,uBAAuB;QACvB,IAAI,CAAC,kBAAkB;QAEvB,IAAI,CAAC,aAAa,GAAG;QACrB,QAAQ,GAAG,CAAC;IACd;IAEA,6BAA6B;IAC7B,MAAM,uBAAuB,EAA2B,EAAiB;QACvE,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;YAC/D,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,iCAAiC;YACjC,MAAM,GAAG,UAAU;YACnB,MAAM,GAAG,KAAK;YAEd,uBAAuB;YACvB,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;YACnC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,EAAE;YAEvC,iBAAiB;YACjB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI;YACtD,IAAI,CAAC,eAAe;YAEpB,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;YAElE,0BAA0B;YAC1B,MAAM,IAAI,CAAC,UAAU,CAAC,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;gBAClD,WAAW,GAAG,MAAM,CAAC,EAAE;gBACvB,QAAQ;YACV,GAAG,UAAU;QAEf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YAC1D,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB,EAAU,EAAiB;QACxD,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,IAAI;QAET,IAAI;YACF,2BAA2B;YAC3B,MAAM,GAAG,IAAI;YAEb,yBAAyB;YACzB,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC;YACrC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YAE5B,iBAAiB;YACjB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI;YACtD,IAAI,CAAC,eAAe;YAEpB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAEzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC,EAAE;QACpD;IACF;IAEA,oBAAoB;IACpB,MAAM,WAAW,MAAmB,EAAiB;QACnD,IAAI;YACF,IAAI,CAAC,OAAO,CAAC,YAAY;YACzB,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY;QACnF;IACF;IAEA,mBAAmB,UAAsB,EAAE,QAAuC,EAAc;QAC9F,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY;IAC3C;IAEA,oBAAoB;IACZ,wBAA8B;QACpC,IAAI,CAAC,gBAAgB,GAAG,YAAY;YAClC,MAAM,IAAI,CAAC,kBAAkB;QAC/B,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB;IACpC;IAEA,MAAc,qBAAoC;QAChD,MAAM,eAAyB,EAAE;QAEjC,KAAK,MAAM,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,GAAI;YACrD,IAAI;gBACF,MAAM,YAAY,MAAM,GAAG,WAAW;gBACtC,IAAI,CAAC,WAAW;oBACd,aAAa,IAAI,CAAC;gBACpB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC,EAAE;gBACrD,aAAa,IAAI,CAAC;YACpB;QACF;QAEA,uBAAuB;QACvB,IAAI,aAAa,MAAM,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YACvD,MAAM,IAAI,CAAC,mBAAmB,CAAC;QACjC;QAEA,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,aAAa;IACpB;IAEA,MAAc,oBAAoB,YAAsB,EAAiB;QACvE,KAAK,MAAM,MAAM,aAAc;YAC7B,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;YACpC,IAAI,CAAC,IAAI;YAET,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;gBAChD,MAAM,GAAG,KAAK;gBACd,MAAM,GAAG,KAAK;gBACd,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,IAAI;YACrC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC/C,4DAA4D;YAC9D;QACF;IACF;IAEA,wBAAwB;IAChB,qBAA2B;QACjC,oCAAoC;QACpC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,mBAAmB;YACtC,MAAM;YACN,IAAI;QACN;QAEA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB;YACnC,MAAM;YACN,IAAI;QACN;QAEA,sCAAsC;QACtC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB;YACpC,MAAM;YACN,IAAI;QACN;QAEA,gDAAgD;QAChD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB;YACpC,MAAM;YACN,IAAI;gBAAC;gBAAa;aAAa;YAC/B,WAAW,CAAC,SAAW,OAAO,IAAI,CAAC,SAAS,KAAK;QACnD;IACF;IAEA,iBAAiB;IACT,qBAA2B;QACjC,6BAA6B;QAC7B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YACrC,QAAQ,KAAK,CAAC,6BAA6B,OAAO,IAAI;YACtD,IAAI,CAAC,iBAAiB,CAAC;QACzB;IACF;IAEA,MAAc,kBAAkB,MAAmB,EAAiB;QAClE,MAAM,YAAY,OAAO,IAAI;QAE7B,YAAY;QACZ,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,UAAU,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,KAAK;QAE3E,+CAA+C;QAC/C,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,UAAU,SAAS,EAAE;YACnD,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,SAAS;YACvD,IAAI,IAAI;gBACN,IAAI;oBACF,MAAM,GAAG,KAAK;oBACd,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,UAAU,SAAS,EAAE;gBAC5D,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,UAAU,SAAS,CAAC,CAAC,CAAC,EAAE;gBACtE;YACF;QACF;IACF;IAEA,uBAAuB;IACf,kBAAwB;QAC9B,IAAI,YAAY;QAChB,KAAK,MAAM,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,GAAI;YAC9C,IAAI,GAAG,KAAK,CAAC,MAAM,KAAK,UAAU,GAAG,KAAK,CAAC,MAAM,KAAK,cAAc;gBAClE;YACF;QACF;QACA,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;IAC3B;IAEQ,gBAAsB;QAC5B,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,UAAU;QAC5C,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,cAAc,UAAU;QACxD,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,cAAc,cAAc;QAC1D,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,cAAc,cAAc,GAAG,KAAK,GAAG,CAAC,cAAc,YAAY,EAAE;IAC/F;IAEA,aAA4B;QAC1B,OAAO;YAAE,GAAG,IAAI,CAAC,OAAO;QAAC;IAC3B;IAEA,qBAAgD;QAC9C,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM;IAC/C;IAEA,kBAAkB,EAAU,EAAuC;QACjE,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;IAClC;IAEA,WAAW;IACX,MAAM,WAA0B;QAC9B,QAAQ,GAAG,CAAC;QAEZ,yBAAyB;QACzB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,cAAc,IAAI,CAAC,gBAAgB;QACrC;QAEA,4BAA4B;QAC5B,KAAK,MAAM,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,GAAI;YAC9C,IAAI;gBACF,MAAM,GAAG,IAAI;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;QAEA,kBAAkB;QAClB,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ;QAE1B,IAAI,CAAC,aAAa,GAAG;QACrB,QAAQ,GAAG,CAAC;IACd;AACF;AAGO,MAAM,eAAe,IAAI", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/fpga-core/interfaces/pu-interface.ts"], "sourcesContent": ["/**\n * FPGA-Inspired Processing Unit Interface\n * Standardized interface for all AI modules\n */\n\nexport interface Signal<T = any> {\n  id: string;\n  type: string;\n  data: T;\n  timestamp: number;\n  source: string;\n  priority: 'low' | 'normal' | 'high' | 'critical';\n}\n\nexport interface ProcessingUnitConfig {\n  id: string;\n  name: string;\n  version: string;\n  capabilities: string[];\n  resources: {\n    memory: number;\n    cpu: number;\n    gpu?: number;\n  };\n  dependencies: string[];\n}\n\nexport interface ProcessingUnitState {\n  status: 'idle' | 'processing' | 'error' | 'disabled';\n  load: number; // 0-100%\n  lastActivity: number;\n  errorCount: number;\n  processedSignals: number;\n}\n\nexport interface ProcessingUnitInterface {\n  // Core Properties\n  readonly config: ProcessingUnitConfig;\n  readonly state: ProcessingUnitState;\n  \n  // Lifecycle Methods\n  initialize(): Promise<void>;\n  start(): Promise<void>;\n  stop(): Promise<void>;\n  reset(): Promise<void>;\n  \n  // Signal Processing\n  process(signal: Signal): Promise<Signal | Signal[] | null>;\n  canProcess(signal: Signal): boolean;\n  \n  // Resource Management\n  getResourceUsage(): { memory: number; cpu: number; gpu?: number };\n  setResourceLimits(limits: Partial<ProcessingUnitConfig['resources']>): void;\n  \n  // Health & Monitoring\n  healthCheck(): Promise<boolean>;\n  getMetrics(): ProcessingUnitMetrics;\n  \n  // Event Handlers\n  onSignalReceived?(signal: Signal): void;\n  onProcessingComplete?(result: Signal | Signal[]): void;\n  onError?(error: Error): void;\n}\n\nexport interface ProcessingUnitMetrics {\n  uptime: number;\n  throughput: number; // signals per second\n  latency: number; // average processing time\n  errorRate: number;\n  resourceEfficiency: number;\n}\n\nexport abstract class BaseProcessingUnit implements ProcessingUnitInterface {\n  protected _state: ProcessingUnitState;\n  \n  constructor(public readonly config: ProcessingUnitConfig) {\n    this._state = {\n      status: 'idle',\n      load: 0,\n      lastActivity: Date.now(),\n      errorCount: 0,\n      processedSignals: 0\n    };\n  }\n  \n  get state(): ProcessingUnitState {\n    return { ...this._state };\n  }\n  \n  async initialize(): Promise<void> {\n    this._state.status = 'idle';\n  }\n  \n  async start(): Promise<void> {\n    this._state.status = 'idle';\n  }\n  \n  async stop(): Promise<void> {\n    this._state.status = 'disabled';\n  }\n  \n  async reset(): Promise<void> {\n    this._state = {\n      status: 'idle',\n      load: 0,\n      lastActivity: Date.now(),\n      errorCount: 0,\n      processedSignals: 0\n    };\n  }\n  \n  abstract process(signal: Signal): Promise<Signal | Signal[] | null>;\n  abstract canProcess(signal: Signal): boolean;\n  abstract getResourceUsage(): { memory: number; cpu: number; gpu?: number };\n  \n  setResourceLimits(limits: Partial<ProcessingUnitConfig['resources']>): void {\n    Object.assign(this.config.resources, limits);\n  }\n  \n  async healthCheck(): Promise<boolean> {\n    return this._state.status !== 'error';\n  }\n  \n  getMetrics(): ProcessingUnitMetrics {\n    const now = Date.now();\n    return {\n      uptime: now - this._state.lastActivity,\n      throughput: this._state.processedSignals / ((now - this._state.lastActivity) / 1000),\n      latency: 0, // To be implemented by subclasses\n      errorRate: this._state.errorCount / Math.max(this._state.processedSignals, 1),\n      resourceEfficiency: 100 - this._state.load\n    };\n  }\n  \n  protected updateState(updates: Partial<ProcessingUnitState>): void {\n    this._state = { ...this._state, ...updates, lastActivity: Date.now() };\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAqEM,MAAe;;IACV,OAA4B;IAEtC,YAAY,AAAgB,MAA4B,CAAE;aAA9B,SAAA;QAC1B,IAAI,CAAC,MAAM,GAAG;YACZ,QAAQ;YACR,MAAM;YACN,cAAc,KAAK,GAAG;YACtB,YAAY;YACZ,kBAAkB;QACpB;IACF;IAEA,IAAI,QAA6B;QAC/B,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,MAAM,aAA4B;QAChC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;IACvB;IAEA,MAAM,QAAuB;QAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;IACvB;IAEA,MAAM,OAAsB;QAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;IACvB;IAEA,MAAM,QAAuB;QAC3B,IAAI,CAAC,MAAM,GAAG;YACZ,QAAQ;YACR,MAAM;YACN,cAAc,KAAK,GAAG;YACtB,YAAY;YACZ,kBAAkB;QACpB;IACF;IAMA,kBAAkB,MAAkD,EAAQ;QAC1E,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;IACvC;IAEA,MAAM,cAAgC;QACpC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK;IAChC;IAEA,aAAoC;QAClC,MAAM,MAAM,KAAK,GAAG;QACpB,OAAO;YACL,QAAQ,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY;YACtC,YAAY,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI;YACnF,SAAS;YACT,WAAW,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE;YAC3E,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;QAC5C;IACF;IAEU,YAAY,OAAqC,EAAQ;QACjE,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,OAAO;YAAE,cAAc,KAAK,GAAG;QAAG;IACvE;AACF", "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/fpga-core/processing-units/crypto-pu/crypto-pu.ts"], "sourcesContent": ["/**\n * Crypto Processing Unit - FPGA-style AI module for cryptocurrency analysis\n */\n\nimport { BaseProcessingUnit, ProcessingUnitConfig } from '../../interfaces/pu-interface';\nimport { TypedSignal, CryptoSignal, createSignal } from '../../interfaces/signal-types';\nimport OpenAI from 'openai';\n\nexport class CryptoProcessingUnit extends BaseProcessingUnit {\n  private openai: OpenAI;\n  private priceCache: Map<string, { price: number; timestamp: number }> = new Map();\n  private analysisCache: Map<string, { analysis: string; timestamp: number }> = new Map();\n\n  constructor() {\n    const config: ProcessingUnitConfig = {\n      id: 'crypto-pu',\n      name: 'Crypto Analysis Processing Unit',\n      version: '1.0.0',\n      capabilities: [\n        'crypto.analysis',\n        'crypto.price',\n        'crypto.portfolio',\n        'market.sentiment',\n        'trading.signals'\n      ],\n      resources: {\n        memory: 256, // MB\n        cpu: 30,     // % usage\n        gpu: 10      // % usage (for ML models)\n      },\n      dependencies: ['openai']\n    };\n\n    super(config);\n    \n    this.openai = new OpenAI({\n      apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY,\n      dangerouslyAllowBrowser: true\n    });\n  }\n\n  async initialize(): Promise<void> {\n    await super.initialize();\n    console.log('🪙 Crypto PU initialized');\n  }\n\n  canProcess(signal: TypedSignal): boolean {\n    return signal.type.startsWith('crypto.') || \n           signal.type === 'system.status' ||\n           (signal.type === 'support.query' && this.isCryptoRelated(signal.data));\n  }\n\n  async process(signal: TypedSignal): Promise<TypedSignal | TypedSignal[] | null> {\n    this.updateState({ status: 'processing', load: 50 });\n    \n    try {\n      let result: TypedSignal | TypedSignal[] | null = null;\n\n      switch (signal.type) {\n        case 'crypto.analysis':\n          result = await this.analyzeCrypto(signal);\n          break;\n        case 'crypto.price':\n          result = await this.getPriceData(signal);\n          break;\n        case 'crypto.portfolio':\n          result = await this.analyzePortfolio(signal);\n          break;\n        case 'support.query':\n          if (this.isCryptoRelated(signal.data)) {\n            result = await this.handleCryptoQuery(signal);\n          }\n          break;\n        default:\n          console.warn(`🪙 Crypto PU: Unsupported signal type ${signal.type}`);\n      }\n\n      this.updateState({ \n        status: 'idle', \n        load: 0, \n        processedSignals: this.state.processedSignals + 1 \n      });\n\n      return result;\n\n    } catch (error) {\n      console.error('🪙 Crypto PU processing error:', error);\n      this.updateState({ \n        status: 'error', \n        load: 0, \n        errorCount: this.state.errorCount + 1 \n      });\n      return null;\n    }\n  }\n\n  private async analyzeCrypto(signal: TypedSignal): Promise<TypedSignal> {\n    const data = signal.data as CryptoSignal;\n    const cacheKey = `${data.symbol}-analysis`;\n    \n    // Check cache (5 minute expiry)\n    const cached = this.analysisCache.get(cacheKey);\n    if (cached && Date.now() - cached.timestamp < 300000) {\n      return createSignal('crypto.analysis', {\n        ...data,\n        analysis: cached.analysis,\n        confidence: 0.85\n      }, this.config.id, 'normal');\n    }\n\n    try {\n      const prompt = `Analyze ${data.symbol} cryptocurrency:\n        Current Price: $${data.price || 'N/A'}\n        Volume: ${data.volume || 'N/A'}\n        Market Cap: $${data.marketCap || 'N/A'}\n        \n        Provide a concise technical analysis with:\n        1. Price trend assessment\n        2. Support/resistance levels\n        3. Trading recommendation (buy/sell/hold)\n        4. Risk assessment\n        \n        Keep response under 200 words.`;\n\n      const response = await this.openai.chat.completions.create({\n        model: 'gpt-3.5-turbo',\n        messages: [{ role: 'user', content: prompt }],\n        max_tokens: 300,\n        temperature: 0.7\n      });\n\n      const analysis = response.choices[0]?.message?.content || 'Analysis unavailable';\n      \n      // Extract recommendation\n      const recommendation = this.extractRecommendation(analysis);\n      \n      // Cache result\n      this.analysisCache.set(cacheKey, { analysis, timestamp: Date.now() });\n\n      return createSignal('crypto.analysis', {\n        ...data,\n        analysis,\n        recommendation,\n        confidence: 0.85\n      }, this.config.id, 'normal');\n\n    } catch (error) {\n      console.error('🪙 Crypto analysis error:', error);\n      return createSignal('crypto.analysis', {\n        ...data,\n        analysis: 'Analysis temporarily unavailable',\n        recommendation: 'hold',\n        confidence: 0.1\n      }, this.config.id, 'low');\n    }\n  }\n\n  private async getPriceData(signal: TypedSignal): Promise<TypedSignal> {\n    const data = signal.data as CryptoSignal;\n    \n    try {\n      // Simulate price fetch (in real implementation, use actual API)\n      const mockPrice = Math.random() * 50000 + 20000; // BTC-like price\n      const mockVolume = Math.random() * 1000000000;\n      \n      const priceData: CryptoSignal = {\n        ...data,\n        price: mockPrice,\n        volume: mockVolume,\n        marketCap: mockPrice * 19000000 // Approximate BTC supply\n      };\n\n      return createSignal('crypto.price', priceData, this.config.id, 'normal');\n\n    } catch (error) {\n      console.error('🪙 Price fetch error:', error);\n      return createSignal('system.error', {\n        component: this.config.id,\n        error: {\n          code: 'PRICE_FETCH_ERROR',\n          message: 'Failed to fetch price data'\n        }\n      }, this.config.id, 'high');\n    }\n  }\n\n  private async analyzePortfolio(signal: TypedSignal): Promise<TypedSignal[]> {\n    const data = signal.data as any; // Portfolio data\n    \n    try {\n      const prompt = `Analyze this crypto portfolio and provide insights:\n        ${JSON.stringify(data, null, 2)}\n        \n        Provide:\n        1. Portfolio diversification assessment\n        2. Risk analysis\n        3. Rebalancing recommendations\n        4. Overall performance outlook`;\n\n      const response = await this.openai.chat.completions.create({\n        model: 'gpt-3.5-turbo',\n        messages: [{ role: 'user', content: prompt }],\n        max_tokens: 400,\n        temperature: 0.7\n      });\n\n      const analysis = response.choices[0]?.message?.content || 'Portfolio analysis unavailable';\n\n      return [\n        createSignal('crypto.analysis', {\n          symbol: 'PORTFOLIO',\n          analysis,\n          recommendation: 'hold',\n          confidence: 0.8\n        }, this.config.id, 'normal')\n      ];\n\n    } catch (error) {\n      console.error('🪙 Portfolio analysis error:', error);\n      return [\n        createSignal('system.error', {\n          component: this.config.id,\n          error: {\n            code: 'PORTFOLIO_ANALYSIS_ERROR',\n            message: 'Failed to analyze portfolio'\n          }\n        }, this.config.id, 'high')\n      ];\n    }\n  }\n\n  private async handleCryptoQuery(signal: TypedSignal): Promise<TypedSignal> {\n    const query = (signal.data as any).query;\n    \n    try {\n      const response = await this.openai.chat.completions.create({\n        model: 'gpt-3.5-turbo',\n        messages: [\n          {\n            role: 'system',\n            content: 'You are a cryptocurrency expert. Provide helpful, accurate information about crypto markets, trading, and blockchain technology.'\n          },\n          { role: 'user', content: query }\n        ],\n        max_tokens: 300,\n        temperature: 0.7\n      });\n\n      const answer = response.choices[0]?.message?.content || 'Unable to process crypto query';\n\n      return createSignal('support.response', {\n        query,\n        response: answer,\n        sentiment: 'neutral',\n        resolved: true\n      }, this.config.id, 'normal');\n\n    } catch (error) {\n      console.error('🪙 Crypto query error:', error);\n      return createSignal('support.response', {\n        query,\n        response: 'I apologize, but I cannot process your crypto query at the moment.',\n        sentiment: 'neutral',\n        resolved: false\n      }, this.config.id, 'low');\n    }\n  }\n\n  private isCryptoRelated(data: any): boolean {\n    const query = data.query?.toLowerCase() || '';\n    const cryptoKeywords = [\n      'bitcoin', 'btc', 'ethereum', 'eth', 'crypto', 'cryptocurrency',\n      'blockchain', 'trading', 'price', 'market', 'portfolio', 'wallet'\n    ];\n    return cryptoKeywords.some(keyword => query.includes(keyword));\n  }\n\n  private extractRecommendation(analysis: string): 'buy' | 'sell' | 'hold' {\n    const lower = analysis.toLowerCase();\n    if (lower.includes('buy') || lower.includes('bullish')) return 'buy';\n    if (lower.includes('sell') || lower.includes('bearish')) return 'sell';\n    return 'hold';\n  }\n\n  getResourceUsage(): { memory: number; cpu: number; gpu?: number } {\n    return {\n      memory: this.priceCache.size * 0.1 + this.analysisCache.size * 0.5, // Approximate MB\n      cpu: this.state.load,\n      gpu: this.state.status === 'processing' ? 10 : 0\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;AAAA;;;;AAEO,MAAM,6BAA6B,oJAAA,CAAA,qBAAkB;IAClD,OAAe;IACf,aAAgE,IAAI,MAAM;IAC1E,gBAAsE,IAAI,MAAM;IAExF,aAAc;QACZ,MAAM,SAA+B;YACnC,IAAI;YACJ,MAAM;YACN,SAAS;YACT,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;aACD;YACD,WAAW;gBACT,QAAQ;gBACR,KAAK;gBACL,KAAK,GAAQ,0BAA0B;YACzC;YACA,cAAc;gBAAC;aAAS;QAC1B;QAEA,KAAK,CAAC;QAEN,IAAI,CAAC,MAAM,GAAG,IAAI,sKAAA,CAAA,UAAM,CAAC;YACvB,MAAM;YACN,yBAAyB;QAC3B;IACF;IAEA,MAAM,aAA4B;QAChC,MAAM,KAAK,CAAC;QACZ,QAAQ,GAAG,CAAC;IACd;IAEA,WAAW,MAAmB,EAAW;QACvC,OAAO,OAAO,IAAI,CAAC,UAAU,CAAC,cACvB,OAAO,IAAI,KAAK,mBACf,OAAO,IAAI,KAAK,mBAAmB,IAAI,CAAC,eAAe,CAAC,OAAO,IAAI;IAC7E;IAEA,MAAM,QAAQ,MAAmB,EAA+C;QAC9E,IAAI,CAAC,WAAW,CAAC;YAAE,QAAQ;YAAc,MAAM;QAAG;QAElD,IAAI;YACF,IAAI,SAA6C;YAEjD,OAAQ,OAAO,IAAI;gBACjB,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC;oBAClC;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC;oBACjC;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,gBAAgB,CAAC;oBACrC;gBACF,KAAK;oBACH,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,IAAI,GAAG;wBACrC,SAAS,MAAM,IAAI,CAAC,iBAAiB,CAAC;oBACxC;oBACA;gBACF;oBACE,QAAQ,IAAI,CAAC,CAAC,sCAAsC,EAAE,OAAO,IAAI,EAAE;YACvE;YAEA,IAAI,CAAC,WAAW,CAAC;gBACf,QAAQ;gBACR,MAAM;gBACN,kBAAkB,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG;YAClD;YAEA,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,IAAI,CAAC,WAAW,CAAC;gBACf,QAAQ;gBACR,MAAM;gBACN,YAAY,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;YACtC;YACA,OAAO;QACT;IACF;IAEA,MAAc,cAAc,MAAmB,EAAwB;QACrE,MAAM,OAAO,OAAO,IAAI;QACxB,MAAM,WAAW,GAAG,KAAK,MAAM,CAAC,SAAS,CAAC;QAE1C,gCAAgC;QAChC,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QACtC,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,QAAQ;YACpD,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB;gBACrC,GAAG,IAAI;gBACP,UAAU,OAAO,QAAQ;gBACzB,YAAY;YACd,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QACrB;QAEA,IAAI;YACF,MAAM,SAAS,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC;wBACpB,EAAE,KAAK,KAAK,IAAI,MAAM;gBAC9B,EAAE,KAAK,MAAM,IAAI,MAAM;qBAClB,EAAE,KAAK,SAAS,IAAI,MAAM;;;;;;;;sCAQT,CAAC;YAEjC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO;gBACP,UAAU;oBAAC;wBAAE,MAAM;wBAAQ,SAAS;oBAAO;iBAAE;gBAC7C,YAAY;gBACZ,aAAa;YACf;YAEA,MAAM,WAAW,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;YAE1D,yBAAyB;YACzB,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;YAElD,eAAe;YACf,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU;gBAAE;gBAAU,WAAW,KAAK,GAAG;YAAG;YAEnE,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB;gBACrC,GAAG,IAAI;gBACP;gBACA;gBACA,YAAY;YACd,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QAErB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB;gBACrC,GAAG,IAAI;gBACP,UAAU;gBACV,gBAAgB;gBAChB,YAAY;YACd,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QACrB;IACF;IAEA,MAAc,aAAa,MAAmB,EAAwB;QACpE,MAAM,OAAO,OAAO,IAAI;QAExB,IAAI;YACF,gEAAgE;YAChE,MAAM,YAAY,KAAK,MAAM,KAAK,QAAQ,OAAO,iBAAiB;YAClE,MAAM,aAAa,KAAK,MAAM,KAAK;YAEnC,MAAM,YAA0B;gBAC9B,GAAG,IAAI;gBACP,OAAO;gBACP,QAAQ;gBACR,WAAW,YAAY,SAAS,yBAAyB;YAC3D;YAEA,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,WAAW,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QAEjE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;gBAClC,WAAW,IAAI,CAAC,MAAM,CAAC,EAAE;gBACzB,OAAO;oBACL,MAAM;oBACN,SAAS;gBACX;YACF,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QACrB;IACF;IAEA,MAAc,iBAAiB,MAAmB,EAA0B;QAC1E,MAAM,OAAO,OAAO,IAAI,EAAS,iBAAiB;QAElD,IAAI;YACF,MAAM,SAAS,CAAC;QACd,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,GAAG;;;;;;sCAMF,CAAC;YAEjC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO;gBACP,UAAU;oBAAC;wBAAE,MAAM;wBAAQ,SAAS;oBAAO;iBAAE;gBAC7C,YAAY;gBACZ,aAAa;YACf;YAEA,MAAM,WAAW,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;YAE1D,OAAO;gBACL,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB;oBAC9B,QAAQ;oBACR;oBACA,gBAAgB;oBAChB,YAAY;gBACd,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;aACpB;QAEH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBACL,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;oBAC3B,WAAW,IAAI,CAAC,MAAM,CAAC,EAAE;oBACzB,OAAO;wBACL,MAAM;wBACN,SAAS;oBACX;gBACF,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;aACpB;QACH;IACF;IAEA,MAAc,kBAAkB,MAAmB,EAAwB;QACzE,MAAM,QAAQ,AAAC,OAAO,IAAI,CAAS,KAAK;QAExC,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBAAE,MAAM;wBAAQ,SAAS;oBAAM;iBAChC;gBACD,YAAY;gBACZ,aAAa;YACf;YAEA,MAAM,SAAS,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;YAExD,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB;gBACtC;gBACA,UAAU;gBACV,WAAW;gBACX,UAAU;YACZ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QAErB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB;gBACtC;gBACA,UAAU;gBACV,WAAW;gBACX,UAAU;YACZ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QACrB;IACF;IAEQ,gBAAgB,IAAS,EAAW;QAC1C,MAAM,QAAQ,KAAK,KAAK,EAAE,iBAAiB;QAC3C,MAAM,iBAAiB;YACrB;YAAW;YAAO;YAAY;YAAO;YAAU;YAC/C;YAAc;YAAW;YAAS;YAAU;YAAa;SAC1D;QACD,OAAO,eAAe,IAAI,CAAC,CAAA,UAAW,MAAM,QAAQ,CAAC;IACvD;IAEQ,sBAAsB,QAAgB,EAA2B;QACvE,MAAM,QAAQ,SAAS,WAAW;QAClC,IAAI,MAAM,QAAQ,CAAC,UAAU,MAAM,QAAQ,CAAC,YAAY,OAAO;QAC/D,IAAI,MAAM,QAAQ,CAAC,WAAW,MAAM,QAAQ,CAAC,YAAY,OAAO;QAChE,OAAO;IACT;IAEA,mBAAkE;QAChE,OAAO;YACL,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG;YAC/D,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;YACpB,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,eAAe,KAAK;QACjD;IACF;AACF", "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/fpga-core/processing-units/support-pu/support-pu.ts"], "sourcesContent": ["/**\n * Support Processing Unit - AI customer support module\n */\n\nimport { BaseProcessingUnit, ProcessingUnitConfig } from '../../interfaces/pu-interface';\nimport { TypedSignal, SupportSignal, createSignal } from '../../interfaces/signal-types';\nimport OpenAI from 'openai';\n\nexport class SupportProcessingUnit extends BaseProcessingUnit {\n  private openai: OpenAI;\n  private conversationHistory: Map<string, any[]> = new Map();\n\n  constructor() {\n    const config: ProcessingUnitConfig = {\n      id: 'support-pu',\n      name: 'Customer Support Processing Unit',\n      version: '1.0.0',\n      capabilities: [\n        'support.query',\n        'support.response',\n        'support.escalate',\n        'sentiment.analysis',\n        'conversation.management'\n      ],\n      resources: {\n        memory: 128,\n        cpu: 25\n      },\n      dependencies: ['openai']\n    };\n\n    super(config);\n    \n    this.openai = new OpenAI({\n      apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY,\n      dangerouslyAllowBrowser: true\n    });\n  }\n\n  async initialize(): Promise<void> {\n    await super.initialize();\n    console.log('🎧 Support PU initialized');\n  }\n\n  canProcess(signal: TypedSignal): boolean {\n    return signal.type.startsWith('support.') || signal.type === 'system.status';\n  }\n\n  async process(signal: TypedSignal): Promise<TypedSignal | TypedSignal[] | null> {\n    this.updateState({ status: 'processing', load: 40 });\n    \n    try {\n      let result: TypedSignal | TypedSignal[] | null = null;\n\n      switch (signal.type) {\n        case 'support.query':\n          result = await this.handleQuery(signal);\n          break;\n        case 'support.escalate':\n          result = await this.handleEscalation(signal);\n          break;\n        default:\n          console.warn(`🎧 Support PU: Unsupported signal type ${signal.type}`);\n      }\n\n      this.updateState({ \n        status: 'idle', \n        load: 0, \n        processedSignals: this.state.processedSignals + 1 \n      });\n\n      return result;\n\n    } catch (error) {\n      console.error('🎧 Support PU processing error:', error);\n      this.updateState({ \n        status: 'error', \n        load: 0, \n        errorCount: this.state.errorCount + 1 \n      });\n      return null;\n    }\n  }\n\n  private async handleQuery(signal: TypedSignal): Promise<TypedSignal> {\n    const data = signal.data as SupportSignal;\n    const userId = data.user || 'anonymous';\n    \n    try {\n      // Get conversation history\n      const history = this.conversationHistory.get(userId) || [];\n      \n      // Analyze sentiment\n      const sentiment = this.analyzeSentiment(data.query);\n      \n      // Generate response\n      const messages = [\n        {\n          role: 'system' as const,\n          content: `You are Connectouch, a helpful AI assistant for the Chainsight platform. \n                   Provide friendly, professional support for crypto, finance, legal, design, and Web3 questions.\n                   Keep responses concise and helpful. If you need to escalate, mention it clearly.`\n        },\n        ...history.slice(-6), // Last 3 exchanges\n        { role: 'user' as const, content: data.query }\n      ];\n\n      const response = await this.openai.chat.completions.create({\n        model: 'gpt-3.5-turbo',\n        messages,\n        max_tokens: 300,\n        temperature: 0.7\n      });\n\n      const answer = response.choices[0]?.message?.content || 'I apologize, but I cannot process your request at the moment.';\n      \n      // Update conversation history\n      history.push(\n        { role: 'user', content: data.query },\n        { role: 'assistant', content: answer }\n      );\n      this.conversationHistory.set(userId, history);\n\n      // Determine if escalation is needed\n      const needsEscalation = this.shouldEscalate(data.query, answer, sentiment);\n\n      const responseData: SupportSignal = {\n        query: data.query,\n        context: data.context,\n        user: userId,\n        priority: data.priority,\n        response: answer,\n        sentiment,\n        resolved: !needsEscalation\n      };\n\n      const results: TypedSignal[] = [\n        createSignal('support.response', responseData, this.config.id, 'normal')\n      ];\n\n      // Add escalation signal if needed\n      if (needsEscalation) {\n        results.push(\n          createSignal('support.escalate', {\n            ...responseData,\n            priority: 'high'\n          }, this.config.id, 'high')\n        );\n      }\n\n      return results.length === 1 ? results[0] : results as any;\n\n    } catch (error) {\n      console.error('🎧 Query handling error:', error);\n      return createSignal('support.response', {\n        query: data.query,\n        user: userId,\n        priority: data.priority,\n        response: 'I apologize, but I\\'m experiencing technical difficulties. Please try again later.',\n        sentiment: 'neutral',\n        resolved: false\n      }, this.config.id, 'low');\n    }\n  }\n\n  private async handleEscalation(signal: TypedSignal): Promise<TypedSignal> {\n    const data = signal.data as SupportSignal;\n    \n    // Log escalation\n    console.log(`🚨 Support escalation for user ${data.user}: ${data.query}`);\n    \n    // In a real system, this would notify human agents\n    return createSignal('system.status', {\n      component: 'support-escalation',\n      status: 'online',\n      metrics: {\n        cpu: 0,\n        memory: 0,\n        latency: 0,\n        throughput: 1\n      }\n    }, this.config.id, 'high');\n  }\n\n  private analyzeSentiment(text: string): 'positive' | 'neutral' | 'negative' {\n    const positiveWords = ['good', 'great', 'excellent', 'love', 'amazing', 'perfect', 'thank'];\n    const negativeWords = ['bad', 'terrible', 'hate', 'awful', 'broken', 'error', 'problem', 'issue'];\n    \n    const words = text.toLowerCase().split(/\\s+/);\n    let score = 0;\n    \n    words.forEach(word => {\n      if (positiveWords.includes(word)) score++;\n      if (negativeWords.includes(word)) score--;\n    });\n    \n    if (score > 0) return 'positive';\n    if (score < 0) return 'negative';\n    return 'neutral';\n  }\n\n  private shouldEscalate(query: string, response: string, sentiment: string): boolean {\n    const escalationKeywords = [\n      'urgent', 'emergency', 'critical', 'bug', 'broken', 'not working',\n      'refund', 'cancel', 'complaint', 'manager', 'supervisor'\n    ];\n    \n    const queryLower = query.toLowerCase();\n    const hasEscalationKeyword = escalationKeywords.some(keyword => \n      queryLower.includes(keyword)\n    );\n    \n    const isNegativeSentiment = sentiment === 'negative';\n    const responseIndicatesEscalation = response.toLowerCase().includes('escalate') ||\n                                       response.toLowerCase().includes('human agent');\n    \n    return hasEscalationKeyword || isNegativeSentiment || responseIndicatesEscalation;\n  }\n\n  getResourceUsage(): { memory: number; cpu: number } {\n    return {\n      memory: this.conversationHistory.size * 0.5, // Approximate MB per conversation\n      cpu: this.state.load\n    };\n  }\n\n  // Cleanup old conversations\n  private cleanupConversations(): void {\n    const maxAge = 24 * 60 * 60 * 1000; // 24 hours\n    const now = Date.now();\n    \n    for (const [userId, history] of this.conversationHistory.entries()) {\n      if (history.length === 0) continue;\n      \n      const lastMessage = history[history.length - 1];\n      if (now - (lastMessage.timestamp || 0) > maxAge) {\n        this.conversationHistory.delete(userId);\n      }\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;AAAA;;;;AAEO,MAAM,8BAA8B,oJAAA,CAAA,qBAAkB;IACnD,OAAe;IACf,sBAA0C,IAAI,MAAM;IAE5D,aAAc;QACZ,MAAM,SAA+B;YACnC,IAAI;YACJ,MAAM;YACN,SAAS;YACT,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;aACD;YACD,WAAW;gBACT,QAAQ;gBACR,KAAK;YACP;YACA,cAAc;gBAAC;aAAS;QAC1B;QAEA,KAAK,CAAC;QAEN,IAAI,CAAC,MAAM,GAAG,IAAI,sKAAA,CAAA,UAAM,CAAC;YACvB,MAAM;YACN,yBAAyB;QAC3B;IACF;IAEA,MAAM,aAA4B;QAChC,MAAM,KAAK,CAAC;QACZ,QAAQ,GAAG,CAAC;IACd;IAEA,WAAW,MAAmB,EAAW;QACvC,OAAO,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,OAAO,IAAI,KAAK;IAC/D;IAEA,MAAM,QAAQ,MAAmB,EAA+C;QAC9E,IAAI,CAAC,WAAW,CAAC;YAAE,QAAQ;YAAc,MAAM;QAAG;QAElD,IAAI;YACF,IAAI,SAA6C;YAEjD,OAAQ,OAAO,IAAI;gBACjB,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;oBAChC;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,gBAAgB,CAAC;oBACrC;gBACF;oBACE,QAAQ,IAAI,CAAC,CAAC,uCAAuC,EAAE,OAAO,IAAI,EAAE;YACxE;YAEA,IAAI,CAAC,WAAW,CAAC;gBACf,QAAQ;gBACR,MAAM;gBACN,kBAAkB,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG;YAClD;YAEA,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,IAAI,CAAC,WAAW,CAAC;gBACf,QAAQ;gBACR,MAAM;gBACN,YAAY,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;YACtC;YACA,OAAO;QACT;IACF;IAEA,MAAc,YAAY,MAAmB,EAAwB;QACnE,MAAM,OAAO,OAAO,IAAI;QACxB,MAAM,SAAS,KAAK,IAAI,IAAI;QAE5B,IAAI;YACF,2BAA2B;YAC3B,MAAM,UAAU,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW,EAAE;YAE1D,oBAAoB;YACpB,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK;YAElD,oBAAoB;YACpB,MAAM,WAAW;gBACf;oBACE,MAAM;oBACN,SAAS,CAAC;;mGAE+E,CAAC;gBAC5F;mBACG,QAAQ,KAAK,CAAC,CAAC;gBAClB;oBAAE,MAAM;oBAAiB,SAAS,KAAK,KAAK;gBAAC;aAC9C;YAED,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO;gBACP;gBACA,YAAY;gBACZ,aAAa;YACf;YAEA,MAAM,SAAS,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;YAExD,8BAA8B;YAC9B,QAAQ,IAAI,CACV;gBAAE,MAAM;gBAAQ,SAAS,KAAK,KAAK;YAAC,GACpC;gBAAE,MAAM;gBAAa,SAAS;YAAO;YAEvC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ;YAErC,oCAAoC;YACpC,MAAM,kBAAkB,IAAI,CAAC,cAAc,CAAC,KAAK,KAAK,EAAE,QAAQ;YAEhE,MAAM,eAA8B;gBAClC,OAAO,KAAK,KAAK;gBACjB,SAAS,KAAK,OAAO;gBACrB,MAAM;gBACN,UAAU,KAAK,QAAQ;gBACvB,UAAU;gBACV;gBACA,UAAU,CAAC;YACb;YAEA,MAAM,UAAyB;gBAC7B,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB,cAAc,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;aAChE;YAED,kCAAkC;YAClC,IAAI,iBAAiB;gBACnB,QAAQ,IAAI,CACV,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB;oBAC/B,GAAG,YAAY;oBACf,UAAU;gBACZ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;YAEvB;YAEA,OAAO,QAAQ,MAAM,KAAK,IAAI,OAAO,CAAC,EAAE,GAAG;QAE7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB;gBACtC,OAAO,KAAK,KAAK;gBACjB,MAAM;gBACN,UAAU,KAAK,QAAQ;gBACvB,UAAU;gBACV,WAAW;gBACX,UAAU;YACZ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QACrB;IACF;IAEA,MAAc,iBAAiB,MAAmB,EAAwB;QACxE,MAAM,OAAO,OAAO,IAAI;QAExB,iBAAiB;QACjB,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,KAAK,EAAE;QAExE,mDAAmD;QACnD,OAAO,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;YACnC,WAAW;YACX,QAAQ;YACR,SAAS;gBACP,KAAK;gBACL,QAAQ;gBACR,SAAS;gBACT,YAAY;YACd;QACF,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;IACrB;IAEQ,iBAAiB,IAAY,EAAuC;QAC1E,MAAM,gBAAgB;YAAC;YAAQ;YAAS;YAAa;YAAQ;YAAW;YAAW;SAAQ;QAC3F,MAAM,gBAAgB;YAAC;YAAO;YAAY;YAAQ;YAAS;YAAU;YAAS;YAAW;SAAQ;QAEjG,MAAM,QAAQ,KAAK,WAAW,GAAG,KAAK,CAAC;QACvC,IAAI,QAAQ;QAEZ,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,cAAc,QAAQ,CAAC,OAAO;YAClC,IAAI,cAAc,QAAQ,CAAC,OAAO;QACpC;QAEA,IAAI,QAAQ,GAAG,OAAO;QACtB,IAAI,QAAQ,GAAG,OAAO;QACtB,OAAO;IACT;IAEQ,eAAe,KAAa,EAAE,QAAgB,EAAE,SAAiB,EAAW;QAClF,MAAM,qBAAqB;YACzB;YAAU;YAAa;YAAY;YAAO;YAAU;YACpD;YAAU;YAAU;YAAa;YAAW;SAC7C;QAED,MAAM,aAAa,MAAM,WAAW;QACpC,MAAM,uBAAuB,mBAAmB,IAAI,CAAC,CAAA,UACnD,WAAW,QAAQ,CAAC;QAGtB,MAAM,sBAAsB,cAAc;QAC1C,MAAM,8BAA8B,SAAS,WAAW,GAAG,QAAQ,CAAC,eACjC,SAAS,WAAW,GAAG,QAAQ,CAAC;QAEnE,OAAO,wBAAwB,uBAAuB;IACxD;IAEA,mBAAoD;QAClD,OAAO;YACL,QAAQ,IAAI,CAAC,mBAAmB,CAAC,IAAI,GAAG;YACxC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;QACtB;IACF;IAEA,4BAA4B;IACpB,uBAA6B;QACnC,MAAM,SAAS,KAAK,KAAK,KAAK,MAAM,WAAW;QAC/C,MAAM,MAAM,KAAK,GAAG;QAEpB,KAAK,MAAM,CAAC,QAAQ,QAAQ,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,GAAI;YAClE,IAAI,QAAQ,MAAM,KAAK,GAAG;YAE1B,MAAM,cAAc,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;YAC/C,IAAI,MAAM,CAAC,YAAY,SAAS,IAAI,CAAC,IAAI,QAAQ;gBAC/C,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAClC;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1116, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/fpga-core/chainsight-fpga.ts"], "sourcesContent": ["/**\n * Chainsight FPGA System - Main controller for the FPGA-inspired architecture\n */\n\nimport { InterconnectFabric, globalFabric } from './interconnect/fabric';\nimport { CryptoProcessingUnit } from './processing-units/crypto-pu/crypto-pu';\nimport { SupportProcessingUnit } from './processing-units/support-pu/support-pu';\nimport { TypedSignal, createSignal, SignalType } from './interfaces/signal-types';\n\nexport interface ChainsightConfig {\n  enabledModules: string[];\n  openaiApiKey?: string;\n  debugMode: boolean;\n  maxConcurrentSignals: number;\n}\n\nexport class ChainsightFPGA {\n  private fabric: InterconnectFabric;\n  private config: ChainsightConfig;\n  private isInitialized = false;\n  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();\n\n  constructor(config: Partial<ChainsightConfig> = {}) {\n    this.config = {\n      enabledModules: ['crypto', 'support', 'finance', 'legal', 'design', 'face', 'web3'],\n      debugMode: process.env.NODE_ENV === 'development',\n      maxConcurrentSignals: 100,\n      ...config\n    };\n    \n    this.fabric = globalFabric;\n  }\n\n  async initialize(): Promise<void> {\n    if (this.isInitialized) return;\n    \n    console.log('🚀 Initializing Chainsight FPGA System...');\n    \n    try {\n      // Initialize the interconnect fabric\n      await this.fabric.initialize();\n      \n      // Register processing units\n      await this.registerProcessingUnits();\n      \n      // Setup event subscriptions\n      this.setupEventSubscriptions();\n      \n      this.isInitialized = true;\n      console.log('✅ Chainsight FPGA System initialized successfully');\n      \n      // Emit system ready signal\n      await this.emitSignal('system.status', {\n        component: 'chainsight-fpga',\n        status: 'online',\n        metrics: {\n          cpu: 0,\n          memory: 0,\n          latency: 0,\n          throughput: 0\n        }\n      });\n      \n    } catch (error) {\n      console.error('❌ Failed to initialize Chainsight FPGA:', error);\n      throw error;\n    }\n  }\n\n  private async registerProcessingUnits(): Promise<void> {\n    const registrations: Promise<void>[] = [];\n    \n    // Register Crypto PU\n    if (this.config.enabledModules.includes('crypto')) {\n      const cryptoPU = new CryptoProcessingUnit();\n      registrations.push(this.fabric.registerProcessingUnit(cryptoPU));\n    }\n    \n    // Register Support PU\n    if (this.config.enabledModules.includes('support')) {\n      const supportPU = new SupportProcessingUnit();\n      registrations.push(this.fabric.registerProcessingUnit(supportPU));\n    }\n    \n    // Wait for all registrations to complete\n    await Promise.all(registrations);\n    \n    console.log(`✅ Registered ${registrations.length} processing units`);\n  }\n\n  private setupEventSubscriptions(): void {\n    // Subscribe to all signal types for event forwarding\n    const signalTypes: SignalType[] = [\n      'crypto.analysis', 'crypto.price', 'crypto.portfolio',\n      'support.query', 'support.response', 'support.escalate',\n      'system.status', 'system.error', 'system.metric'\n    ];\n    \n    signalTypes.forEach(signalType => {\n      this.fabric.subscribeToSignals(signalType, (signal) => {\n        this.forwardEvent(signalType, signal);\n      });\n    });\n  }\n\n  // Public API Methods\n  async analyzeCrypto(symbol: string, data?: any): Promise<any> {\n    const signal = createSignal('crypto.analysis', {\n      symbol,\n      ...data\n    }, 'chainsight-api', 'normal');\n    \n    return this.emitAndWaitForResponse(signal, 'crypto.analysis');\n  }\n\n  async getCryptoPrice(symbol: string): Promise<any> {\n    const signal = createSignal('crypto.price', {\n      symbol\n    }, 'chainsight-api', 'normal');\n    \n    return this.emitAndWaitForResponse(signal, 'crypto.price');\n  }\n\n  async askSupport(query: string, user?: string, priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal'): Promise<any> {\n    const signal = createSignal('support.query', {\n      query,\n      user,\n      priority\n    }, 'chainsight-api', priority);\n    \n    return this.emitAndWaitForResponse(signal, 'support.response');\n  }\n\n  async emitSignal(type: SignalType, data: any, priority: 'low' | 'normal' | 'high' | 'critical' = 'normal'): Promise<void> {\n    const signal = createSignal(type, data, 'chainsight-api', priority);\n    await this.fabric.emitSignal(signal);\n  }\n\n  private async emitAndWaitForResponse(signal: TypedSignal, expectedResponseType: SignalType, timeout = 10000): Promise<any> {\n    return new Promise((resolve, reject) => {\n      const timeoutId = setTimeout(() => {\n        reject(new Error(`Timeout waiting for ${expectedResponseType} response`));\n      }, timeout);\n      \n      // Subscribe to response\n      const unsubscribe = this.fabric.subscribeToSignals(expectedResponseType, (responseSignal) => {\n        clearTimeout(timeoutId);\n        unsubscribe();\n        resolve(responseSignal.data);\n      });\n      \n      // Emit the signal\n      this.fabric.emitSignal(signal).catch(reject);\n    });\n  }\n\n  // Event System\n  addEventListener(eventType: string, callback: (data: any) => void): () => void {\n    if (!this.eventListeners.has(eventType)) {\n      this.eventListeners.set(eventType, new Set());\n    }\n    \n    this.eventListeners.get(eventType)!.add(callback);\n    \n    // Return unsubscribe function\n    return () => {\n      this.eventListeners.get(eventType)?.delete(callback);\n    };\n  }\n\n  private forwardEvent(eventType: string, signal: TypedSignal): void {\n    const listeners = this.eventListeners.get(eventType);\n    if (listeners) {\n      listeners.forEach(callback => {\n        try {\n          callback(signal.data);\n        } catch (error) {\n          console.error(`❌ Event listener error for ${eventType}:`, error);\n        }\n      });\n    }\n  }\n\n  // System Information\n  getSystemMetrics(): any {\n    return {\n      fabric: this.fabric.getMetrics(),\n      processingUnits: this.fabric.getProcessingUnits().map(pu => ({\n        id: pu.config.id,\n        name: pu.config.name,\n        state: pu.state,\n        metrics: pu.getMetrics(),\n        resourceUsage: pu.getResourceUsage()\n      }))\n    };\n  }\n\n  getProcessingUnits(): any[] {\n    return this.fabric.getProcessingUnits().map(pu => ({\n      id: pu.config.id,\n      name: pu.config.name,\n      capabilities: pu.config.capabilities,\n      state: pu.state\n    }));\n  }\n\n  isReady(): boolean {\n    return this.isInitialized;\n  }\n\n  // Shutdown\n  async shutdown(): Promise<void> {\n    console.log('🔄 Shutting down Chainsight FPGA System...');\n    \n    try {\n      await this.fabric.shutdown();\n      this.eventListeners.clear();\n      this.isInitialized = false;\n      \n      console.log('✅ Chainsight FPGA System shutdown complete');\n    } catch (error) {\n      console.error('❌ Error during shutdown:', error);\n    }\n  }\n}\n\n// Global instance\nlet globalChainsight: ChainsightFPGA | null = null;\n\nexport function getChainsightFPGA(config?: Partial<ChainsightConfig>): ChainsightFPGA {\n  if (!globalChainsight) {\n    globalChainsight = new ChainsightFPGA(config);\n  }\n  return globalChainsight;\n}\n\nexport async function initializeChainsight(config?: Partial<ChainsightConfig>): Promise<ChainsightFPGA> {\n  const chainsight = getChainsightFPGA(config);\n  await chainsight.initialize();\n  return chainsight;\n}\n\n// React Hook for easy integration\nexport function useChainsightFPGA() {\n  const chainsight = getChainsightFPGA();\n  \n  return {\n    chainsight,\n    isReady: chainsight.isReady(),\n    analyzeCrypto: chainsight.analyzeCrypto.bind(chainsight),\n    getCryptoPrice: chainsight.getCryptoPrice.bind(chainsight),\n    askSupport: chainsight.askSupport.bind(chainsight),\n    getSystemMetrics: chainsight.getSystemMetrics.bind(chainsight),\n    getProcessingUnits: chainsight.getProcessingUnits.bind(chainsight),\n    addEventListener: chainsight.addEventListener.bind(chainsight)\n  };\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AAED;AACA;AACA;AACA;;;;;AASO,MAAM;IACH,OAA2B;IAC3B,OAAyB;IACzB,gBAAgB,MAAM;IACtB,iBAAwD,IAAI,MAAM;IAE1E,YAAY,SAAoC,CAAC,CAAC,CAAE;QAClD,IAAI,CAAC,MAAM,GAAG;YACZ,gBAAgB;gBAAC;gBAAU;gBAAW;gBAAW;gBAAS;gBAAU;gBAAQ;aAAO;YACnF,WAAW,oDAAyB;YACpC,sBAAsB;YACtB,GAAG,MAAM;QACX;QAEA,IAAI,CAAC,MAAM,GAAG,6IAAA,CAAA,eAAY;IAC5B;IAEA,MAAM,aAA4B;QAChC,IAAI,IAAI,CAAC,aAAa,EAAE;QAExB,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,qCAAqC;YACrC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU;YAE5B,4BAA4B;YAC5B,MAAM,IAAI,CAAC,uBAAuB;YAElC,4BAA4B;YAC5B,IAAI,CAAC,uBAAuB;YAE5B,IAAI,CAAC,aAAa,GAAG;YACrB,QAAQ,GAAG,CAAC;YAEZ,2BAA2B;YAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB;gBACrC,WAAW;gBACX,QAAQ;gBACR,SAAS;oBACP,KAAK;oBACL,QAAQ;oBACR,SAAS;oBACT,YAAY;gBACd;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;QACR;IACF;IAEA,MAAc,0BAAyC;QACrD,MAAM,gBAAiC,EAAE;QAEzC,qBAAqB;QACrB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW;YACjD,MAAM,WAAW,IAAI,0KAAA,CAAA,uBAAoB;YACzC,cAAc,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACxD;QAEA,sBAAsB;QACtB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,YAAY;YAClD,MAAM,YAAY,IAAI,4KAAA,CAAA,wBAAqB;YAC3C,cAAc,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACxD;QAEA,yCAAyC;QACzC,MAAM,QAAQ,GAAG,CAAC;QAElB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc,MAAM,CAAC,iBAAiB,CAAC;IACrE;IAEQ,0BAAgC;QACtC,qDAAqD;QACrD,MAAM,cAA4B;YAChC;YAAmB;YAAgB;YACnC;YAAiB;YAAoB;YACrC;YAAiB;YAAgB;SAClC;QAED,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC;gBAC1C,IAAI,CAAC,YAAY,CAAC,YAAY;YAChC;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,cAAc,MAAc,EAAE,IAAU,EAAgB;QAC5D,MAAM,SAAS,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB;YAC7C;YACA,GAAG,IAAI;QACT,GAAG,kBAAkB;QAErB,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ;IAC7C;IAEA,MAAM,eAAe,MAAc,EAAgB;QACjD,MAAM,SAAS,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;YAC1C;QACF,GAAG,kBAAkB;QAErB,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ;IAC7C;IAEA,MAAM,WAAW,KAAa,EAAE,IAAa,EAAE,WAAiD,QAAQ,EAAgB;QACtH,MAAM,SAAS,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;YAC3C;YACA;YACA;QACF,GAAG,kBAAkB;QAErB,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ;IAC7C;IAEA,MAAM,WAAW,IAAgB,EAAE,IAAS,EAAE,WAAmD,QAAQ,EAAiB;QACxH,MAAM,SAAS,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM,kBAAkB;QAC1D,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAC/B;IAEA,MAAc,uBAAuB,MAAmB,EAAE,oBAAgC,EAAE,UAAU,KAAK,EAAgB;QACzH,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,YAAY,WAAW;gBAC3B,OAAO,IAAI,MAAM,CAAC,oBAAoB,EAAE,qBAAqB,SAAS,CAAC;YACzE,GAAG;YAEH,wBAAwB;YACxB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,sBAAsB,CAAC;gBACxE,aAAa;gBACb;gBACA,QAAQ,eAAe,IAAI;YAC7B;YAEA,kBAAkB;YAClB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,KAAK,CAAC;QACvC;IACF;IAEA,eAAe;IACf,iBAAiB,SAAiB,EAAE,QAA6B,EAAc;QAC7E,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY;YACvC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,IAAI;QACzC;QAEA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAY,GAAG,CAAC;QAExC,8BAA8B;QAC9B,OAAO;YACL,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,OAAO;QAC7C;IACF;IAEQ,aAAa,SAAiB,EAAE,MAAmB,EAAQ;QACjE,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAC1C,IAAI,WAAW;YACb,UAAU,OAAO,CAAC,CAAA;gBAChB,IAAI;oBACF,SAAS,OAAO,IAAI;gBACtB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,UAAU,CAAC,CAAC,EAAE;gBAC5D;YACF;QACF;IACF;IAEA,qBAAqB;IACrB,mBAAwB;QACtB,OAAO;YACL,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU;YAC9B,iBAAiB,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,GAAG,CAAC,CAAA,KAAM,CAAC;oBAC3D,IAAI,GAAG,MAAM,CAAC,EAAE;oBAChB,MAAM,GAAG,MAAM,CAAC,IAAI;oBACpB,OAAO,GAAG,KAAK;oBACf,SAAS,GAAG,UAAU;oBACtB,eAAe,GAAG,gBAAgB;gBACpC,CAAC;QACH;IACF;IAEA,qBAA4B;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,GAAG,CAAC,CAAA,KAAM,CAAC;gBACjD,IAAI,GAAG,MAAM,CAAC,EAAE;gBAChB,MAAM,GAAG,MAAM,CAAC,IAAI;gBACpB,cAAc,GAAG,MAAM,CAAC,YAAY;gBACpC,OAAO,GAAG,KAAK;YACjB,CAAC;IACH;IAEA,UAAmB;QACjB,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,WAAW;IACX,MAAM,WAA0B;QAC9B,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC1B,IAAI,CAAC,cAAc,CAAC,KAAK;YACzB,IAAI,CAAC,aAAa,GAAG;YAErB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;AACF;AAEA,kBAAkB;AAClB,IAAI,mBAA0C;AAEvC,SAAS,kBAAkB,MAAkC;IAClE,IAAI,CAAC,kBAAkB;QACrB,mBAAmB,IAAI,eAAe;IACxC;IACA,OAAO;AACT;AAEO,eAAe,qBAAqB,MAAkC;IAC3E,MAAM,aAAa,kBAAkB;IACrC,MAAM,WAAW,UAAU;IAC3B,OAAO;AACT;AAGO,SAAS;IACd,MAAM,aAAa;IAEnB,OAAO;QACL;QACA,SAAS,WAAW,OAAO;QAC3B,eAAe,WAAW,aAAa,CAAC,IAAI,CAAC;QAC7C,gBAAgB,WAAW,cAAc,CAAC,IAAI,CAAC;QAC/C,YAAY,WAAW,UAAU,CAAC,IAAI,CAAC;QACvC,kBAAkB,WAAW,gBAAgB,CAAC,IAAI,CAAC;QACnD,oBAAoB,WAAW,kBAAkB,CAAC,IAAI,CAAC;QACvD,kBAAkB,WAAW,gBAAgB,CAAC,IAAI,CAAC;IACrD;AACF", "debugId": null}}, {"offset": {"line": 1350, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/FPGAChainsight.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useChainsightFPGA, initializeChainsight } from '../fpga-core/chainsight-fpga';\n\ninterface Message {\n  id: string;\n  type: 'user' | 'assistant';\n  content: string;\n  timestamp: number;\n  module?: string;\n}\n\nexport default function FPGAChainsight() {\n  const { chainsight, isReady, askSupport, analyzeCrypto, getCryptoPrice, getSystemMetrics } = useChainsightFPGA();\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [systemMetrics, setSystemMetrics] = useState<any>(null);\n  const [selectedModule, setSelectedModule] = useState<string>('support');\n\n  useEffect(() => {\n    // Initialize Chainsight FPGA system\n    const init = async () => {\n      try {\n        await initializeChainsight({\n          openaiApiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY,\n          debugMode: true\n        });\n        \n        // Add welcome message\n        setMessages([{\n          id: '1',\n          type: 'assistant',\n          content: '🚀 Welcome to Chainsight FPGA! I\\'m powered by a modular AI architecture. How can I help you today?',\n          timestamp: Date.now(),\n          module: 'system'\n        }]);\n        \n        // Update metrics periodically\n        const metricsInterval = setInterval(() => {\n          setSystemMetrics(getSystemMetrics());\n        }, 2000);\n        \n        return () => clearInterval(metricsInterval);\n      } catch (error) {\n        console.error('Failed to initialize Chainsight:', error);\n      }\n    };\n    \n    init();\n  }, []);\n\n  const handleSendMessage = async () => {\n    if (!input.trim() || isLoading || !isReady) return;\n    \n    const userMessage: Message = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: input,\n      timestamp: Date.now()\n    };\n    \n    setMessages(prev => [...prev, userMessage]);\n    setInput('');\n    setIsLoading(true);\n    \n    try {\n      let response: any;\n      let responseModule = selectedModule;\n      \n      // Route to appropriate module based on selection or content\n      if (selectedModule === 'crypto' || input.toLowerCase().includes('crypto') || input.toLowerCase().includes('bitcoin')) {\n        if (input.toLowerCase().includes('price')) {\n          response = await getCryptoPrice('BTC');\n          responseModule = 'crypto-price';\n        } else {\n          response = await analyzeCrypto('BTC', { query: input });\n          responseModule = 'crypto-analysis';\n        }\n      } else {\n        response = await askSupport(input, 'user', 'normal');\n        responseModule = 'support';\n      }\n      \n      const assistantMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: response.response || response.analysis || JSON.stringify(response, null, 2),\n        timestamp: Date.now(),\n        module: responseModule\n      };\n      \n      setMessages(prev => [...prev, assistantMessage]);\n      \n    } catch (error) {\n      console.error('Error processing message:', error);\n      const errorMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: 'I apologize, but I encountered an error processing your request. Please try again.',\n        timestamp: Date.now(),\n        module: 'error'\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const getModuleColor = (module?: string) => {\n    const colors = {\n      'crypto': 'text-yellow-400',\n      'crypto-price': 'text-green-400',\n      'crypto-analysis': 'text-orange-400',\n      'support': 'text-blue-400',\n      'system': 'text-purple-400',\n      'error': 'text-red-400'\n    };\n    return colors[module as keyof typeof colors] || 'text-gray-400';\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900\">\n      {/* Header */}\n      <div className=\"bg-black/20 backdrop-blur-sm border-b border-purple-500/30 p-4\">\n        <div className=\"max-w-6xl mx-auto flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n              Chainsight FPGA\n            </h1>\n            <p className=\"text-gray-400 text-sm\">\n              Status: {isReady ? '🟢 Online' : '🔴 Initializing...'}\n            </p>\n          </div>\n          \n          {/* Module Selector */}\n          <div className=\"flex gap-2\">\n            <select \n              value={selectedModule} \n              onChange={(e) => setSelectedModule(e.target.value)}\n              className=\"bg-gray-800 border border-purple-500/30 rounded px-3 py-1 text-white\"\n            >\n              <option value=\"support\">💬 Support</option>\n              <option value=\"crypto\">🪙 Crypto</option>\n              <option value=\"finance\">💰 Finance</option>\n              <option value=\"legal\">⚖️ Legal</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-6xl mx-auto p-4 grid grid-cols-1 lg:grid-cols-4 gap-4 h-[calc(100vh-100px)]\">\n        {/* Chat Area */}\n        <div className=\"lg:col-span-3 bg-black/20 backdrop-blur-sm rounded-lg border border-purple-500/30 flex flex-col\">\n          {/* Messages */}\n          <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n            {messages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-[80%] p-3 rounded-lg ${\n                    message.type === 'user'\n                      ? 'bg-purple-600 text-white'\n                      : 'bg-gray-800 text-gray-100 border border-gray-700'\n                  }`}\n                >\n                  {message.type === 'assistant' && message.module && (\n                    <div className={`text-xs ${getModuleColor(message.module)} mb-1`}>\n                      {message.module.toUpperCase()}\n                    </div>\n                  )}\n                  <div className=\"whitespace-pre-wrap\">{message.content}</div>\n                  <div className=\"text-xs opacity-60 mt-1\">\n                    {new Date(message.timestamp).toLocaleTimeString()}\n                  </div>\n                </div>\n              </div>\n            ))}\n            {isLoading && (\n              <div className=\"flex justify-start\">\n                <div className=\"bg-gray-800 text-gray-100 border border-gray-700 p-3 rounded-lg\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-purple-400\"></div>\n                    <span>Processing...</span>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Input Area */}\n          <div className=\"p-4 border-t border-purple-500/30\">\n            <div className=\"flex gap-2\">\n              <textarea\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder={`Ask ${selectedModule} module anything...`}\n                className=\"flex-1 bg-gray-800 border border-purple-500/30 rounded px-3 py-2 text-white placeholder-gray-400 resize-none\"\n                rows={2}\n                disabled={!isReady || isLoading}\n              />\n              <button\n                onClick={handleSendMessage}\n                disabled={!isReady || isLoading || !input.trim()}\n                className=\"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-4 py-2 rounded transition-colors\"\n              >\n                Send\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* System Metrics */}\n        <div className=\"bg-black/20 backdrop-blur-sm rounded-lg border border-purple-500/30 p-4\">\n          <h3 className=\"text-lg font-semibold text-white mb-4\">System Metrics</h3>\n          \n          {systemMetrics ? (\n            <div className=\"space-y-4 text-sm\">\n              {/* Fabric Metrics */}\n              <div>\n                <h4 className=\"text-purple-400 font-medium mb-2\">Interconnect Fabric</h4>\n                <div className=\"space-y-1 text-gray-300\">\n                  <div>Active PUs: {systemMetrics.fabric.activePUs}/{systemMetrics.fabric.registeredPUs}</div>\n                  <div>Signals/sec: {systemMetrics.fabric.signalsPerSecond.toFixed(1)}</div>\n                  <div>Avg Latency: {systemMetrics.fabric.averageLatency.toFixed(1)}ms</div>\n                  <div>Error Rate: {(systemMetrics.fabric.errorRate * 100).toFixed(1)}%</div>\n                </div>\n              </div>\n\n              {/* Processing Units */}\n              <div>\n                <h4 className=\"text-blue-400 font-medium mb-2\">Processing Units</h4>\n                <div className=\"space-y-2\">\n                  {systemMetrics.processingUnits.map((pu: any) => (\n                    <div key={pu.id} className=\"bg-gray-800/50 p-2 rounded\">\n                      <div className=\"text-white font-medium\">{pu.name}</div>\n                      <div className=\"text-xs text-gray-400\">\n                        Status: <span className={pu.state.status === 'idle' ? 'text-green-400' : 'text-yellow-400'}>\n                          {pu.state.status}\n                        </span>\n                      </div>\n                      <div className=\"text-xs text-gray-400\">\n                        Load: {pu.state.load}% | Processed: {pu.state.processedSignals}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-gray-400\">Loading metrics...</div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAae,SAAS;IACtB,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,oBAAiB,AAAD;IAC7G,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,MAAM,OAAO;YACX,IAAI;gBACF,MAAM,CAAA,GAAA,yIAAA,CAAA,uBAAoB,AAAD,EAAE;oBACzB,YAAY;oBACZ,WAAW;gBACb;gBAEA,sBAAsB;gBACtB,YAAY;oBAAC;wBACX,IAAI;wBACJ,MAAM;wBACN,SAAS;wBACT,WAAW,KAAK,GAAG;wBACnB,QAAQ;oBACV;iBAAE;gBAEF,8BAA8B;gBAC9B,MAAM,kBAAkB,YAAY;oBAClC,iBAAiB;gBACnB,GAAG;gBAEH,OAAO,IAAM,cAAc;YAC7B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM,IAAI,MAAM,aAAa,CAAC,SAAS;QAE5C,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,WAAW,KAAK,GAAG;QACrB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,SAAS;QACT,aAAa;QAEb,IAAI;YACF,IAAI;YACJ,IAAI,iBAAiB;YAErB,4DAA4D;YAC5D,IAAI,mBAAmB,YAAY,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY;gBACpH,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,UAAU;oBACzC,WAAW,MAAM,eAAe;oBAChC,iBAAiB;gBACnB,OAAO;oBACL,WAAW,MAAM,cAAc,OAAO;wBAAE,OAAO;oBAAM;oBACrD,iBAAiB;gBACnB;YACF,OAAO;gBACL,WAAW,MAAM,WAAW,OAAO,QAAQ;gBAC3C,iBAAiB;YACnB;YAEA,MAAM,mBAA4B;gBAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,SAAS,QAAQ,IAAI,SAAS,QAAQ,IAAI,KAAK,SAAS,CAAC,UAAU,MAAM;gBAClF,WAAW,KAAK,GAAG;gBACnB,QAAQ;YACV;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;QAEjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,eAAwB;gBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS;gBACT,WAAW,KAAK,GAAG;gBACnB,QAAQ;YACV;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,UAAU;YACV,gBAAgB;YAChB,mBAAmB;YACnB,WAAW;YACX,UAAU;YACV,SAAS;QACX;QACA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgG;;;;;;8CAG9G,8OAAC;oCAAE,WAAU;;wCAAwB;wCAC1B,UAAU,cAAc;;;;;;;;;;;;;sCAKrC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM9B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4CAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;sDAE9E,cAAA,8OAAC;gDACC,WAAW,CAAC,2BAA2B,EACrC,QAAQ,IAAI,KAAK,SACb,6BACA,oDACJ;;oDAED,QAAQ,IAAI,KAAK,eAAe,QAAQ,MAAM,kBAC7C,8OAAC;wDAAI,WAAW,CAAC,QAAQ,EAAE,eAAe,QAAQ,MAAM,EAAE,KAAK,CAAC;kEAC7D,QAAQ,MAAM,CAAC,WAAW;;;;;;kEAG/B,8OAAC;wDAAI,WAAU;kEAAuB,QAAQ,OAAO;;;;;;kEACrD,8OAAC;wDAAI,WAAU;kEACZ,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;2CAjB9C,QAAQ,EAAE;;;;;oCAsBlB,2BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,YAAY;4CACZ,aAAa,CAAC,IAAI,EAAE,eAAe,mBAAmB,CAAC;4CACvD,WAAU;4CACV,MAAM;4CACN,UAAU,CAAC,WAAW;;;;;;sDAExB,8OAAC;4CACC,SAAS;4CACT,UAAU,CAAC,WAAW,aAAa,CAAC,MAAM,IAAI;4CAC9C,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;4BAErD,8BACC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAI;4DAAa,cAAc,MAAM,CAAC,SAAS;4DAAC;4DAAE,cAAc,MAAM,CAAC,aAAa;;;;;;;kEACrF,8OAAC;;4DAAI;4DAAc,cAAc,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC;;;;;;;kEACjE,8OAAC;;4DAAI;4DAAc,cAAc,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEAClE,8OAAC;;4DAAI;4DAAa,CAAC,cAAc,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;kDAKxE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAI,WAAU;0DACZ,cAAc,eAAe,CAAC,GAAG,CAAC,CAAC,mBAClC,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEAAI,WAAU;0EAA0B,GAAG,IAAI;;;;;;0EAChD,8OAAC;gEAAI,WAAU;;oEAAwB;kFAC7B,8OAAC;wEAAK,WAAW,GAAG,KAAK,CAAC,MAAM,KAAK,SAAS,mBAAmB;kFACtE,GAAG,KAAK,CAAC,MAAM;;;;;;;;;;;;0EAGpB,8OAAC;gEAAI,WAAU;;oEAAwB;oEAC9B,GAAG,KAAK,CAAC,IAAI;oEAAC;oEAAgB,GAAG,KAAK,CAAC,gBAAgB;;;;;;;;uDARxD,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;qDAgBvB,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAM3C", "debugId": null}}, {"offset": {"line": 1898, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface ClientOnlyProps {\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false);\n\n  useEffect(() => {\n    setHasMounted(true);\n  }, []);\n\n  if (!hasMounted) {\n    return <>{fallback}</>;\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 1926, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/AI%20chain/chainsight-production/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport FPGAChainsight from '@/components/FPGAChainsight';\nimport { ClientOnly } from '@/components/ClientOnly';\n\nexport default function HomePage() {\n\n  return (\n    <ClientOnly>\n      <FPGAChainsight />\n    </ClientOnly>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMe,SAAS;IAEtB,qBACE,8OAAC,gIAAA,CAAA,aAAU;kBACT,cAAA,8OAAC,oIAAA,CAAA,UAAc;;;;;;;;;;AAGrB", "debugId": null}}]}