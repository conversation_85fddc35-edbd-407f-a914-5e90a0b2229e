@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 10, 10, 10;
  --background-end-rgb: 26, 26, 26;
  --luxury-gold: #d4af37;
  --luxury-silver: #c0c0c0;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, sans-serif;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
  color: white;
  min-height: 100vh;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #d4af37;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #f7dc6f;
}

/* Luxury button styles */
.luxury-button {
  @apply bg-gradient-to-r from-luxury-gold to-yellow-500 text-black font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-luxury-gold/50 hover:scale-105 active:scale-95;
}

.luxury-button-outline {
  @apply border-2 border-luxury-gold text-luxury-gold font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:bg-luxury-gold hover:text-black hover:shadow-lg hover:shadow-luxury-gold/50;
}

/* Glass morphism effect */
.glass {
  @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl;
}

/* Glow effects */
.glow-gold {
  box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
}

.glow-gold:hover {
  box-shadow: 0 0 30px rgba(212, 175, 55, 0.5);
}

/* Animation classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Loading spinner */
.spinner {
  border: 2px solid #1a1a1a;
  border-top: 2px solid #d4af37;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Chart styles */
.recharts-cartesian-axis-tick-value {
  fill: #9aa0a6 !important;
}

.recharts-legend-item-text {
  color: #9aa0a6 !important;
}

/* Form styles */
.luxury-input {
  @apply bg-luxury-charcoal border border-luxury-slate text-white placeholder-gray-400 rounded-lg px-4 py-3 focus:outline-none focus:border-luxury-gold focus:ring-1 focus:ring-luxury-gold transition-all duration-300;
}

.luxury-textarea {
  @apply bg-luxury-charcoal border border-luxury-slate text-white placeholder-gray-400 rounded-lg px-4 py-3 focus:outline-none focus:border-luxury-gold focus:ring-1 focus:ring-luxury-gold transition-all duration-300 resize-none;
}

/* Card styles */
.luxury-card {
  @apply bg-gradient-to-br from-luxury-charcoal to-luxury-slate border border-luxury-gold/20 rounded-xl p-6 shadow-xl hover:shadow-2xl hover:shadow-luxury-gold/10 transition-all duration-300;
}

/* Navigation styles */
.nav-link {
  @apply text-gray-300 hover:text-luxury-gold transition-colors duration-300 font-medium;
}

.nav-link.active {
  @apply text-luxury-gold;
}

/* Modal styles */
.modal-overlay {
  @apply fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4;
}

.modal-content {
  @apply bg-luxury-charcoal border border-luxury-gold/30 rounded-xl p-6 max-w-md w-full shadow-2xl;
}
