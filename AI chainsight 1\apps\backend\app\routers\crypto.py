from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel
from typing import Optional, List
from sqlalchemy.orm import Session
import json
import asyncio
import logging

from ..core.database import get_db, TokenDeployment
from ..core.security import get_current_user_optional
from ..services.crypto_service import CryptoService

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize crypto service
crypto_service = CryptoService()

# Pydantic models
class TokenDeployRequest(BaseModel):
    name: str
    symbol: str
    totalSupply: str
    decimals: int = 18
    description: Optional[str] = None
    deployer: str

class TokenDeployResponse(BaseModel):
    contractAddress: str
    transactionHash: str
    blockNumber: int
    gasUsed: str
    deploymentCost: str
    message: str

class LiquidityRequest(BaseModel):
    tokenA: str
    tokenB: str
    amountA: str
    amountB: str
    slippage: float
    userAddress: str

class TokenInfoResponse(BaseModel):
    name: str
    symbol: str
    decimals: int
    totalSupply: str
    contractAddress: str

class WalletBalanceResponse(BaseModel):
    address: str
    ethBalance: str
    tokenBalances: List[dict]

@router.post("/deploy-token", response_model=TokenDeployResponse)
async def deploy_token(
    request: TokenDeployRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Deploy a new ERC20 token"""
    try:
        logger.info(f"Deploying token: {request.name} ({request.symbol})")
        
        # Validate request
        if not request.name.strip():
            raise HTTPException(status_code=400, detail="Token name is required")
        
        if not request.symbol.strip():
            raise HTTPException(status_code=400, detail="Token symbol is required")
        
        if float(request.totalSupply) <= 0:
            raise HTTPException(status_code=400, detail="Total supply must be greater than 0")
        
        # Deploy token using crypto service
        deployment_result = await crypto_service.deploy_token({
            "name": request.name,
            "symbol": request.symbol,
            "initialSupply": request.totalSupply,
            "decimals": request.decimals,
            "owner": request.deployer
        })
        
        # Save deployment to database
        token_deployment = TokenDeployment(
            contract_address=deployment_result["contractAddress"],
            transaction_hash=deployment_result["transactionHash"],
            token_name=request.name,
            token_symbol=request.symbol,
            total_supply=request.totalSupply,
            decimals=request.decimals,
            deployer_address=request.deployer,
            network="localhost"
        )
        
        db.add(token_deployment)
        db.commit()
        
        logger.info(f"Token deployed successfully: {deployment_result['contractAddress']}")
        
        return TokenDeployResponse(
            contractAddress=deployment_result["contractAddress"],
            transactionHash=deployment_result["transactionHash"],
            blockNumber=deployment_result["blockNumber"],
            gasUsed=deployment_result["gasUsed"],
            deploymentCost=deployment_result["deploymentCost"],
            message="Token deployed successfully"
        )
        
    except Exception as e:
        logger.error(f"Token deployment failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/add-liquidity")
async def add_liquidity(
    request: LiquidityRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Add liquidity to a DEX pool"""
    try:
        logger.info(f"Adding liquidity: {request.amountA} {request.tokenA} + {request.amountB} {request.tokenB}")
        
        # Validate addresses
        if not crypto_service.is_valid_address(request.tokenA):
            raise HTTPException(status_code=400, detail="Invalid token A address")
        
        if not crypto_service.is_valid_address(request.tokenB):
            raise HTTPException(status_code=400, detail="Invalid token B address")
        
        if not crypto_service.is_valid_address(request.userAddress):
            raise HTTPException(status_code=400, detail="Invalid user address")
        
        # Add liquidity using crypto service
        result = await crypto_service.add_liquidity(
            token_a=request.tokenA,
            token_b=request.tokenB,
            amount_a=request.amountA,
            amount_b=request.amountB,
            slippage=request.slippage,
            user_address=request.userAddress
        )
        
        return {
            "transactionHash": result["transactionHash"],
            "liquidityTokens": result.get("liquidityTokens", "0"),
            "message": "Liquidity added successfully"
        }
        
    except Exception as e:
        logger.error(f"Add liquidity failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/remove-liquidity")
async def remove_liquidity(
    request: LiquidityRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Remove liquidity from a DEX pool"""
    try:
        logger.info(f"Removing liquidity: {request.amountA} {request.tokenA} + {request.amountB} {request.tokenB}")
        
        # Remove liquidity using crypto service
        result = await crypto_service.remove_liquidity(
            token_a=request.tokenA,
            token_b=request.tokenB,
            amount_a=request.amountA,
            amount_b=request.amountB,
            user_address=request.userAddress
        )
        
        return {
            "transactionHash": result["transactionHash"],
            "amountA": result.get("amountA", "0"),
            "amountB": result.get("amountB", "0"),
            "message": "Liquidity removed successfully"
        }
        
    except Exception as e:
        logger.error(f"Remove liquidity failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/token-info/{contract_address}", response_model=TokenInfoResponse)
async def get_token_info(
    contract_address: str,
    db: Session = Depends(get_db)
):
    """Get token information"""
    try:
        if not crypto_service.is_valid_address(contract_address):
            raise HTTPException(status_code=400, detail="Invalid contract address")
        
        token_info = await crypto_service.get_token_info(contract_address)
        
        return TokenInfoResponse(**token_info)
        
    except Exception as e:
        logger.error(f"Get token info failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/wallet-balance/{wallet_address}", response_model=WalletBalanceResponse)
async def get_wallet_balance(
    wallet_address: str,
    token_addresses: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get wallet balance for ETH and tokens"""
    try:
        if not crypto_service.is_valid_address(wallet_address):
            raise HTTPException(status_code=400, detail="Invalid wallet address")
        
        # Get ETH balance
        eth_balance = await crypto_service.get_eth_balance(wallet_address)
        
        # Get token balances
        token_balances = []
        if token_addresses:
            addresses = token_addresses.split(",")
            for address in addresses:
                if crypto_service.is_valid_address(address.strip()):
                    try:
                        balance = await crypto_service.get_token_balance(address.strip(), wallet_address)
                        token_info = await crypto_service.get_token_info(address.strip())
                        token_balances.append({
                            "address": address.strip(),
                            "symbol": token_info["symbol"],
                            "balance": balance,
                            "name": token_info["name"]
                        })
                    except Exception as e:
                        logger.warning(f"Failed to get balance for token {address}: {e}")
        
        return WalletBalanceResponse(
            address=wallet_address,
            ethBalance=eth_balance,
            tokenBalances=token_balances
        )
        
    except Exception as e:
        logger.error(f"Get wallet balance failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/deployments")
async def get_deployments(
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """Get token deployment history"""
    try:
        deployments = db.query(TokenDeployment)\
            .order_by(TokenDeployment.created_at.desc())\
            .offset(offset)\
            .limit(limit)\
            .all()
        
        return {
            "deployments": [
                {
                    "id": d.id,
                    "contractAddress": d.contract_address,
                    "transactionHash": d.transaction_hash,
                    "tokenName": d.token_name,
                    "tokenSymbol": d.token_symbol,
                    "totalSupply": d.total_supply,
                    "decimals": d.decimals,
                    "deployerAddress": d.deployer_address,
                    "network": d.network,
                    "createdAt": d.created_at.isoformat()
                }
                for d in deployments
            ],
            "total": len(deployments)
        }
        
    except Exception as e:
        logger.error(f"Get deployments failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/network-info")
async def get_network_info():
    """Get current network information"""
    try:
        network_info = await crypto_service.get_network_info()
        return network_info
        
    except Exception as e:
        logger.error(f"Get network info failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/transfer-tokens")
async def transfer_tokens(
    contract_address: str,
    to_address: str,
    amount: str,
    from_address: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Transfer tokens between addresses"""
    try:
        if not crypto_service.is_valid_address(contract_address):
            raise HTTPException(status_code=400, detail="Invalid contract address")
        
        if not crypto_service.is_valid_address(to_address):
            raise HTTPException(status_code=400, detail="Invalid recipient address")
        
        if not crypto_service.is_valid_address(from_address):
            raise HTTPException(status_code=400, detail="Invalid sender address")
        
        tx_hash = await crypto_service.transfer_tokens(
            contract_address=contract_address,
            to_address=to_address,
            amount=amount,
            from_address=from_address
        )
        
        return {
            "transactionHash": tx_hash,
            "message": "Tokens transferred successfully"
        }
        
    except Exception as e:
        logger.error(f"Token transfer failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
