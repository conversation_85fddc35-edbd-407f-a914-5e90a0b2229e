import { Animation, AnimationFrame, Layer, Shape, Motion, ExportOptions, ExportResult, Template } from '../types';
import { SVGGenerator } from '../generators/SVGGenerator';
import { LottieGenerator } from '../generators/LottieGenerator';
import { AnimationExporter } from '../export/AnimationExporter';
import { MotionEngine } from '../motion/MotionEngine';
import { ShapeLibrary } from '../shapes/ShapeLibrary';
import { TemplateManager } from '../templates/TemplateManager';
import { v4 as uuidv4 } from 'uuid';

export class AnimationDesigner {
  private svgGenerator: SVGGenerator;
  private lottieGenerator: LottieGenerator;
  private exporter: AnimationExporter;
  private motionEngine: MotionEngine;
  private shapeLibrary: ShapeLibrary;
  private templateManager: TemplateManager;
  private animations: Map<string, Animation>;

  constructor() {
    this.svgGenerator = new SVGGenerator();
    this.lottieGenerator = new LottieGenerator();
    this.exporter = new AnimationExporter();
    this.motionEngine = new MotionEngine();
    this.shapeLibrary = new ShapeLibrary();
    this.templateManager = new TemplateManager();
    this.animations = new Map();
  }

  async createAnimation(config: {
    name: string;
    type: 'svg' | 'lottie';
    width: number;
    height: number;
    duration: number;
    fps?: number;
  }): Promise<Animation> {
    try {
      const animation: Animation = {
        id: uuidv4(),
        name: config.name,
        type: config.type,
        duration: config.duration,
        fps: config.fps || 30,
        width: config.width,
        height: config.height,
        frames: [],
        layers: [],
        config: {
          auto_play: true,
          loop: true,
          reverse: false,
          bounce: false,
          speed: 1,
          quality: 'high',
          preload: true
        },
        metadata: {
          created_at: new Date(),
          updated_at: new Date(),
          author: 'Chainsight Animation Designer',
          version: '1.0.0',
          tags: [],
          complexity_score: 0
        }
      };

      this.animations.set(animation.id, animation);
      console.log(`Created animation: ${animation.name} (${animation.id})`);
      
      return animation;

    } catch (error) {
      console.error('Error creating animation:', error);
      throw new Error(`Failed to create animation: ${error.message}`);
    }
  }

  async createFromTemplate(templateId: string, customizations?: Record<string, any>): Promise<Animation> {
    try {
      const template = await this.templateManager.getTemplate(templateId);
      if (!template) {
        throw new Error(`Template ${templateId} not found`);
      }

      // Clone template animation
      const animation = JSON.parse(JSON.stringify(template.animation));
      animation.id = uuidv4();
      animation.name = `${template.name} - ${new Date().toISOString()}`;
      animation.metadata.created_at = new Date();
      animation.metadata.updated_at = new Date();

      // Apply customizations
      if (customizations) {
        await this.applyCustomizations(animation, template, customizations);
      }

      this.animations.set(animation.id, animation);
      console.log(`Created animation from template: ${template.name}`);
      
      return animation;

    } catch (error) {
      console.error('Error creating animation from template:', error);
      throw new Error(`Failed to create animation from template: ${error.message}`);
    }
  }

  async addLayer(animationId: string, layerConfig: {
    name: string;
    type: 'shape' | 'text' | 'image' | 'group';
    visible?: boolean;
    opacity?: number;
  }): Promise<Layer> {
    try {
      const animation = this.animations.get(animationId);
      if (!animation) {
        throw new Error(`Animation ${animationId} not found`);
      }

      const layer: Layer = {
        id: uuidv4(),
        name: layerConfig.name,
        type: layerConfig.type,
        visible: layerConfig.visible !== false,
        locked: false,
        opacity: layerConfig.opacity || 1,
        blend_mode: { type: 'normal' },
        transform: {
          translate_x: 0,
          translate_y: 0,
          scale_x: 1,
          scale_y: 1,
          rotation: 0,
          skew_x: 0,
          skew_y: 0,
          origin_x: 0.5,
          origin_y: 0.5
        },
        effects: []
      };

      animation.layers.push(layer);
      animation.metadata.updated_at = new Date();

      console.log(`Added layer: ${layer.name} to animation ${animation.name}`);
      return layer;

    } catch (error) {
      console.error('Error adding layer:', error);
      throw new Error(`Failed to add layer: ${error.message}`);
    }
  }

  async addShape(animationId: string, layerId: string, shapeConfig: {
    type: string;
    properties: any;
    style?: any;
  }): Promise<string> {
    try {
      const animation = this.animations.get(animationId);
      if (!animation) {
        throw new Error(`Animation ${animationId} not found`);
      }

      const layer = animation.layers.find(l => l.id === layerId);
      if (!layer) {
        throw new Error(`Layer ${layerId} not found`);
      }

      // Create shape using shape library
      const shape = await this.shapeLibrary.createShape(
        shapeConfig.type,
        shapeConfig.properties
      );

      // Add to first frame or create new frame
      let frame = animation.frames[0];
      if (!frame) {
        frame = {
          id: uuidv4(),
          timestamp: 0,
          elements: [],
          transitions: []
        };
        animation.frames.push(frame);
      }

      const element = {
        id: uuidv4(),
        layer_id: layerId,
        shape,
        style: shapeConfig.style || this.getDefaultStyle(),
        transform: {
          translate_x: 0,
          translate_y: 0,
          scale_x: 1,
          scale_y: 1,
          rotation: 0,
          skew_x: 0,
          skew_y: 0,
          origin_x: 0.5,
          origin_y: 0.5
        },
        animations: []
      };

      frame.elements.push(element);
      animation.metadata.updated_at = new Date();

      console.log(`Added shape ${shapeConfig.type} to layer ${layer.name}`);
      return element.id;

    } catch (error) {
      console.error('Error adding shape:', error);
      throw new Error(`Failed to add shape: ${error.message}`);
    }
  }

  async addMotion(
    animationId: string,
    elementId: string,
    motion: Motion
  ): Promise<void> {
    try {
      const animation = this.animations.get(animationId);
      if (!animation) {
        throw new Error(`Animation ${animationId} not found`);
      }

      // Find element across all frames
      let targetElement = null;
      for (const frame of animation.frames) {
        targetElement = frame.elements.find(e => e.id === elementId);
        if (targetElement) break;
      }

      if (!targetElement) {
        throw new Error(`Element ${elementId} not found`);
      }

      // Create property animation from motion
      const propertyAnimation = await this.motionEngine.createPropertyAnimation(motion);
      targetElement.animations.push(propertyAnimation);

      animation.metadata.updated_at = new Date();
      console.log(`Added motion ${motion.type} to element ${elementId}`);

    } catch (error) {
      console.error('Error adding motion:', error);
      throw new Error(`Failed to add motion: ${error.message}`);
    }
  }

  async generatePreview(animationId: string, options?: {
    width?: number;
    height?: number;
    format?: 'svg' | 'gif';
  }): Promise<string> {
    try {
      const animation = this.animations.get(animationId);
      if (!animation) {
        throw new Error(`Animation ${animationId} not found`);
      }

      const previewOptions = {
        width: options?.width || animation.width,
        height: options?.height || animation.height,
        format: options?.format || 'svg'
      };

      if (previewOptions.format === 'svg') {
        return await this.svgGenerator.generatePreview(animation, previewOptions);
      } else {
        // Generate GIF preview
        return await this.exporter.generatePreview(animation, previewOptions);
      }

    } catch (error) {
      console.error('Error generating preview:', error);
      throw new Error(`Failed to generate preview: ${error.message}`);
    }
  }

  async exportAnimation(
    animationId: string,
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      const animation = this.animations.get(animationId);
      if (!animation) {
        throw new Error(`Animation ${animationId} not found`);
      }

      console.log(`Exporting animation ${animation.name} as ${options.format}`);
      
      const result = await this.exporter.export(animation, options);
      
      if (result.success) {
        console.log(`Export completed: ${result.file_path}`);
      } else {
        console.error(`Export failed: ${result.error}`);
      }

      return result;

    } catch (error) {
      console.error('Error exporting animation:', error);
      return {
        success: false,
        format: options.format,
        error: error.message,
        metadata: {
          export_time: 0
        }
      };
    }
  }

  async duplicateAnimation(animationId: string, newName?: string): Promise<Animation> {
    try {
      const original = this.animations.get(animationId);
      if (!original) {
        throw new Error(`Animation ${animationId} not found`);
      }

      // Deep clone the animation
      const duplicate = JSON.parse(JSON.stringify(original));
      duplicate.id = uuidv4();
      duplicate.name = newName || `${original.name} - Copy`;
      duplicate.metadata.created_at = new Date();
      duplicate.metadata.updated_at = new Date();

      // Generate new IDs for all nested objects
      this.regenerateIds(duplicate);

      this.animations.set(duplicate.id, duplicate);
      console.log(`Duplicated animation: ${duplicate.name}`);
      
      return duplicate;

    } catch (error) {
      console.error('Error duplicating animation:', error);
      throw new Error(`Failed to duplicate animation: ${error.message}`);
    }
  }

  async deleteAnimation(animationId: string): Promise<boolean> {
    try {
      const animation = this.animations.get(animationId);
      if (!animation) {
        return false;
      }

      this.animations.delete(animationId);
      console.log(`Deleted animation: ${animation.name}`);
      return true;

    } catch (error) {
      console.error('Error deleting animation:', error);
      return false;
    }
  }

  async getAnimation(animationId: string): Promise<Animation | null> {
    return this.animations.get(animationId) || null;
  }

  async listAnimations(): Promise<Animation[]> {
    return Array.from(this.animations.values());
  }

  async getAnimationStats(animationId: string): Promise<any> {
    try {
      const animation = this.animations.get(animationId);
      if (!animation) {
        throw new Error(`Animation ${animationId} not found`);
      }

      const totalElements = animation.frames.reduce(
        (sum, frame) => sum + frame.elements.length, 0
      );

      const totalAnimations = animation.frames.reduce(
        (sum, frame) => sum + frame.elements.reduce(
          (elemSum, elem) => elemSum + elem.animations.length, 0
        ), 0
      );

      const complexityScore = this.calculateComplexityScore(animation);
      const estimatedFileSize = this.estimateFileSize(animation);

      return {
        total_frames: animation.frames.length,
        total_layers: animation.layers.length,
        total_elements: totalElements,
        total_animations: totalAnimations,
        complexity_score: complexityScore,
        estimated_file_size: estimatedFileSize,
        render_time_estimate: this.estimateRenderTime(animation)
      };

    } catch (error) {
      console.error('Error getting animation stats:', error);
      throw new Error(`Failed to get animation stats: ${error.message}`);
    }
  }

  private async applyCustomizations(
    animation: Animation,
    template: Template,
    customizations: Record<string, any>
  ): Promise<void> {
    for (const property of template.customizable_properties) {
      const value = customizations[property.name];
      if (value !== undefined) {
        await this.applyCustomization(animation, property, value);
      }
    }
  }

  private async applyCustomization(
    animation: Animation,
    property: any,
    value: any
  ): Promise<void> {
    // Apply customization based on property type
    switch (property.type) {
      case 'color':
        await this.updateColors(animation, property.name, value);
        break;
      case 'text':
        await this.updateText(animation, property.name, value);
        break;
      case 'number':
        await this.updateNumericProperty(animation, property.name, value);
        break;
      default:
        console.warn(`Unknown customization type: ${property.type}`);
    }
  }

  private async updateColors(animation: Animation, propertyName: string, color: string): Promise<void> {
    // Update colors in all frames and elements
    for (const frame of animation.frames) {
      for (const element of frame.elements) {
        if (element.style.fill.type === 'solid') {
          element.style.fill.color = color;
        }
      }
    }
  }

  private async updateText(animation: Animation, propertyName: string, text: string): Promise<void> {
    // Update text in text elements
    for (const frame of animation.frames) {
      for (const element of frame.elements) {
        if (element.shape.type === 'text') {
          element.shape.properties.text = text;
        }
      }
    }
  }

  private async updateNumericProperty(animation: Animation, propertyName: string, value: number): Promise<void> {
    // Update numeric properties like size, opacity, etc.
    // Implementation depends on specific property
  }

  private getDefaultStyle(): any {
    return {
      fill: {
        type: 'solid',
        color: '#000000',
        opacity: 1
      },
      stroke: {
        color: '#000000',
        width: 0,
        opacity: 1,
        line_cap: 'butt',
        line_join: 'miter'
      },
      shadow: {
        color: '#000000',
        offset_x: 0,
        offset_y: 0,
        blur: 0,
        opacity: 0
      },
      filters: []
    };
  }

  private regenerateIds(obj: any): void {
    if (obj && typeof obj === 'object') {
      if (obj.id) {
        obj.id = uuidv4();
      }
      
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          this.regenerateIds(obj[key]);
        }
      }
    }
  }

  private calculateComplexityScore(animation: Animation): number {
    let score = 0;
    
    // Base score from frames and layers
    score += animation.frames.length * 2;
    score += animation.layers.length * 3;
    
    // Score from elements and animations
    for (const frame of animation.frames) {
      score += frame.elements.length * 5;
      for (const element of frame.elements) {
        score += element.animations.length * 10;
      }
    }
    
    return Math.min(100, score);
  }

  private estimateFileSize(animation: Animation): number {
    // Rough estimation in bytes
    let size = 1000; // Base size
    
    size += animation.frames.length * 500;
    size += animation.layers.length * 200;
    
    for (const frame of animation.frames) {
      size += frame.elements.length * 1000;
    }
    
    return size;
  }

  private estimateRenderTime(animation: Animation): number {
    // Rough estimation in milliseconds
    const complexity = this.calculateComplexityScore(animation);
    return complexity * 10 + animation.duration * 0.1;
  }
}
