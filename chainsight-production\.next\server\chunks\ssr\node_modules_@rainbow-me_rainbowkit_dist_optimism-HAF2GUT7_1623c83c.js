module.exports = {

"[project]/node_modules/@rainbow-me/rainbowkit/dist/optimism-HAF2GUT7.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>optimism_default)
});
"use client";
// src/components/RainbowKitProvider/chainIcons/optimism.svg
var optimism_default = "data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2028%2028%22%3E%3Crect%20width%3D%2228%22%20height%3D%2228%22%20fill%3D%22%23FF0420%22%20rx%3D%2214%22%2F%3E%3Cpath%20fill%3D%22%23fff%22%20fill-rule%3D%22evenodd%22%20d%3D%22M14%2026.25v-5.185c5.333%200%209.658-4.324%209.658-9.657S19.333%201.75%2014%201.75v5.185c-5.333%200-9.658%204.324-9.658%209.657S8.667%2026.25%2014%2026.25Zm4.778-12.205v-.09c-2.106-1.049-3.684-2.627-4.733-4.733h-.09c-1.049%202.106-2.627%203.684-4.733%204.733v.09c2.106%201.049%203.684%202.627%204.733%204.733h.09c1.049-2.106%202.627-3.684%204.733-4.733Z%22%20clip-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E";
;
}}),

};

//# sourceMappingURL=node_modules_%40rainbow-me_rainbowkit_dist_optimism-HAF2GUT7_1623c83c.js.map