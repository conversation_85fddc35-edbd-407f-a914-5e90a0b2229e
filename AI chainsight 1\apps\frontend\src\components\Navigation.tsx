'use client'

import { useState } from 'react'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { useWeb3Modal } from '@web3modal/react'
import { useAccount } from 'wagmi'
import { 
  Menu, 
  X, 
  Zap, 
  Brain, 
  Camera, 
  TrendingUp, 
  FileText, 
  MessageSquare, 
  Palette, 
  Settings 
} from 'lucide-react'

const navigationItems = [
  { name: 'Dashboard', href: '/', icon: Zap },
  { name: 'Crypto Engine', href: '/crypto', icon: Zap },
  { name: 'AI Core', href: '/ai', icon: Brain },
  { name: 'Face Scanner', href: '/face-scanner', icon: Camera },
  { name: 'Finance', href: '/finance', icon: TrendingUp },
  { name: 'Legal HR', href: '/legal-hr', icon: FileText },
  { name: 'Chat Assistant', href: '/chat', icon: MessageSquare },
  { name: 'Animation', href: '/animation', icon: Palette },
]

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  const { open } = useWeb3Modal()
  const { address, isConnected } = useAccount()

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`
  }

  return (
    <nav className="bg-luxury-black/95 backdrop-blur-md border-b border-luxury-gold/20 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-luxury-gold to-yellow-400 rounded-lg flex items-center justify-center">
              <Zap className="w-5 h-5 text-black" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-luxury-gold to-yellow-400 bg-clip-text text-transparent">
              Chainsight
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigationItems.slice(0, 4).map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="nav-link flex items-center space-x-1"
              >
                <item.icon className="w-4 h-4" />
                <span>{item.name}</span>
              </Link>
            ))}
            
            {/* More dropdown */}
            <div className="relative group">
              <button className="nav-link flex items-center space-x-1">
                <Settings className="w-4 h-4" />
                <span>More</span>
              </button>
              <div className="absolute top-full left-0 mt-2 w-48 bg-luxury-charcoal border border-luxury-gold/20 rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                {navigationItems.slice(4).map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="flex items-center space-x-2 px-4 py-3 text-gray-300 hover:text-luxury-gold hover:bg-luxury-slate/50 transition-colors duration-200"
                  >
                    <item.icon className="w-4 h-4" />
                    <span>{item.name}</span>
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* Wallet Connection & Mobile Menu */}
          <div className="flex items-center space-x-4">
            {/* Wallet Button */}
            <button
              onClick={() => open()}
              className={`luxury-button-outline text-sm ${
                isConnected ? 'bg-luxury-gold/10' : ''
              }`}
            >
              {isConnected ? formatAddress(address!) : 'Connect Wallet'}
            </button>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="md:hidden text-gray-300 hover:text-luxury-gold transition-colors duration-200"
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden bg-luxury-charcoal border-t border-luxury-gold/20"
          >
            <div className="px-4 py-4 space-y-2">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setIsOpen(false)}
                  className="flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-luxury-gold hover:bg-luxury-slate/50 rounded-lg transition-colors duration-200"
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </Link>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  )
}
