(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@rainbow-me/rainbowkit/dist/manta-SI27YFEJ.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>manta_default)
});
"use client";
// src/components/RainbowKitProvider/chainIcons/manta.svg
var manta_default = "data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2028%2028%22%3E%3Cg%20clip-path%3D%22url(%23a)%22%3E%3Cpath%20fill%3D%22%23F9F7EC%22%20d%3D%22M14%2028c7.732%200%2014-6.268%2014-14S21.732%200%2014%200%200%206.268%200%2014s6.268%2014%2014%2014Z%22%2F%3E%3Cg%20clip-path%3D%22url(%23b)%22%3E%3Cmask%20id%3D%22c%22%20width%3D%2228%22%20height%3D%2228%22%20x%3D%220%22%20y%3D%220%22%20maskUnits%3D%22userSpaceOnUse%22%20style%3D%22mask-type%3Aluminance%22%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M28%200H0v28h28V0Z%22%2F%3E%3C%2Fmask%3E%3Cg%20mask%3D%22url(%23c)%22%3E%3Cpath%20fill%3D%22url(%23d)%22%20fill-rule%3D%22evenodd%22%20d%3D%22M6.51%2024.04A12.508%2012.508%200%200%201%201.473%2014C1.474%207.082%207.082%201.474%2014%201.474c5.71%200%2011.945%203.412%2013.453%208.637C25.768%204.272%2020.383%200%2014%200%206.268%200%200%206.268%200%2014s6.268%2014%2014%2014%2014-6.268%2014-14v-.737h-.77l-.102-.001c-.09-.002-.222-.004-.387-.01-.33-.011-.789-.034-1.303-.078-1.05-.092-2.248-.27-3.064-.596-1.313-.524-2.054-1.219-2.9-2.032l-.05-.047c-.848-.815-1.8-1.73-3.452-2.508-1.628-.766-3.427-.643-4.749-.37a12.04%2012.04%200%200%200-2.138.656c-.665.28-1.31.611-1.964.919%200%200%201.281.351%201.915.547l.106.034a7.416%207.416%200%200%201%201.605.767c.683.44%201.25.992%201.482%201.671-1.451.19-2.812.828-3.83%201.426a15.679%2015.679%200%200%200-1.91%201.33c-.084.068-1.394%201.222-1.394%201.222s1.69.18%202.524.348c.555.112%201.303.292%202.083.564.784.274%201.576.632%202.233%201.09.659.46%201.14.992%201.379%201.6.693%201.771.013%203.497-1.353%204.467-1.35.96-3.4%201.187-5.452-.221Zm4.776%202.192a5.647%205.647%200%200%200%201.529-.769c1.858-1.32%202.836-3.74%201.871-6.205-.379-.969-1.103-1.71-1.907-2.27-.806-.563-1.733-.975-2.591-1.274A16.895%2016.895%200%200%200%208.6%2015.25c.17-.11.352-.225.546-.339%201.134-.667%202.562-1.28%203.932-1.28%202.064%200%203.602.634%205.07%201.314l.402.188c1.312.615%202.69%201.262%204.291%201.262a7.463%207.463%200%200%200%203.595-.893C25.695%2021.712%2020.41%2026.526%2014%2026.526c-.932%200-1.84-.101-2.714-.294Zm13.559-11.635a6.172%206.172%200%200%201-2.003.324c-1.256%200-2.335-.503-3.705-1.142l-.368-.171c-1.372-.636-2.959-1.309-5.024-1.43-.277-1.368-1.314-2.303-2.202-2.873a7.855%207.855%200%200%200-.295-.181c.088-.021.18-.041.272-.06%201.193-.246%202.618-.307%203.824.26%201.432.674%202.24%201.45%203.08%202.257l.029.028c.864.83%201.779%201.7%203.374%202.338.887.354%202.034.544%203.018.65Z%22%20clip-rule%3D%22evenodd%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3Cdefs%3E%3CclipPath%20id%3D%22a%22%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M0%200h28v28H0z%22%2F%3E%3C%2FclipPath%3E%3CclipPath%20id%3D%22b%22%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M0%200h28v28H0z%22%2F%3E%3C%2FclipPath%3E%3ClinearGradient%20id%3D%22d%22%20x1%3D%220%22%20x2%3D%2228.502%22%20y1%3D%2228%22%20y2%3D%2227.479%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%2329CCB9%22%2F%3E%3Cstop%20offset%3D%22.495%22%20stop-color%3D%22%230091FF%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23FF66B7%22%2F%3E%3C%2FlinearGradient%3E%3C%2Fdefs%3E%3C%2Fsvg%3E";
;
}}),
}]);

//# sourceMappingURL=node_modules_%40rainbow-me_rainbowkit_dist_manta-SI27YFEJ_921215b0.js.map