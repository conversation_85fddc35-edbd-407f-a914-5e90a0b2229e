export { ChatbotEngine } from './core/ChatbotEngine';
export { ConversationManager } from './conversation/ConversationManager';
export { ContextManager } from './context/ContextManager';
export { IntentClassifier } from './nlp/IntentClassifier';
export { ResponseGenerator } from './generation/ResponseGenerator';
export { KnowledgeBase } from './knowledge/KnowledgeBase';
export { PersonalityEngine } from './personality/PersonalityEngine';
export { MemoryManager } from './memory/MemoryManager';

// Types
export type {
  ChatMessage,
  Conversation,
  ChatContext,
  Intent,
  Entity,
  Response,
  Personality,
  KnowledgeEntry,
  Memory,
  ChatbotConfig
} from './types';

// Constants
export {
  INTENT_TYPES,
  ENTITY_TYPES,
  RESPONSE_TYPES,
  PERSONALITY_TRAITS,
  MEMORY_TYPES
} from './constants';
