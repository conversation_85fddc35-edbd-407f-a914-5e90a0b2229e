# Chainsight by Connectouch

A modular fullstack AI platform combining Web3, AI/ML, finance, design, and HR capabilities in a luxury black-themed interface.

## 🚀 Features

- **Crypto Engine**: ERC20 token creation and LP provisioning
- **AI Core**: Financial prediction with LSTM/transformer models
- **Face Scanner**: Real-time face recognition with webcam integration
- **Finance Analyzer**: Stock and crypto market analysis
- **Legal HR Core**: Resume parsing and contract interpretation
- **Chatbot Engine**: OpenAI-powered customer support
- **Animation Designer**: SVG/Lottie generation with export
- **Web3 DApp Core**: Wallet connection and EVM signing
- **Task Orchestrator**: Modular plugin management system

## 🏗️ Architecture

```
chainsight/
├── apps/                    # Main applications
│   ├── frontend/           # Next.js + Tailwind + Framer Motion
│   └── backend/            # FastAPI + Web3.py
├── packages/               # Core business logic
│   ├── crypto-engine/      # Blockchain & token deployment
│   ├── ai-core/           # ML models & predictions
│   ├── face-scanner/      # Computer vision
│   ├── finance-analyzer/  # Market analysis
│   └── legal-hr-core/     # Document processing
├── plugins/               # Modular features
│   ├── web3-dapp-core/   # Web3 integration
│   ├── chatbot-engine/   # AI assistant
│   ├── animation-designer/ # Creative tools
│   └── task-orchestrator/ # Plugin management
├── libs/                  # Shared utilities
│   ├── ui-lib/           # Reusable components
│   ├── api-sdk/          # API clients
│   └── db-utils/         # Database helpers
└── deployments/          # Docker & deployment configs
```

## 🛠️ Quick Start

### Prerequisites
- Node.js 18+
- Python 3.9+
- Docker & Docker Compose
- PostgreSQL
- Redis

### Installation

1. **Clone and setup**
```bash
git clone <repository>
cd chainsight
npm install
```

2. **Environment setup**
```bash
cp .env.example .env
# Edit .env with your API keys and configuration
```

3. **Start development**
```bash
# Start all services
npm run dev

# Or use Docker
npm run docker:up
```

4. **Access the platform**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Docs: http://localhost:8000/docs

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development servers
- `npm run build` - Build all packages
- `npm run test` - Run tests
- `npm run lint` - Lint code
- `npm run docker:build` - Build Docker images
- `npm run docker:up` - Start with Docker Compose

### Module Development
Each package/plugin is independently developed with its own:
- Package.json with specific dependencies
- TypeScript configuration
- Test suite
- Documentation

## 🚀 Deployment

### Docker Deployment
```bash
npm run docker:build
npm run docker:up
```

### Production Deployment
1. Build all packages: `npm run build`
2. Configure environment variables
3. Deploy using provided Docker configurations
4. Set up reverse proxy (nginx config included)

## 📦 Packages

### Core Packages
- **crypto-engine**: Hardhat, Solidity, ERC20 deployment
- **ai-core**: TensorFlow/PyTorch models for predictions
- **face-scanner**: MediaPipe/OpenCV integration
- **finance-analyzer**: Real-time market data analysis
- **legal-hr-core**: NLP for document processing

### Plugins
- **web3-dapp-core**: Wallet connectivity & blockchain interaction
- **chatbot-engine**: OpenAI GPT integration
- **animation-designer**: Creative content generation
- **task-orchestrator**: Plugin lifecycle management

### Libraries
- **ui-lib**: Shared React components with luxury black theme
- **api-sdk**: Type-safe API clients
- **db-utils**: Database connection and query helpers

## 🔐 Security

- JWT-based authentication
- Environment variable encryption
- Secure Web3 wallet integration
- Input validation and sanitization
- Rate limiting on API endpoints

## 📄 License

MIT License - see LICENSE file for details

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [docs.chainsight.io](https://docs.chainsight.io)
- Issues: GitHub Issues
