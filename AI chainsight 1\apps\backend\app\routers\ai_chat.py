from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional
from sqlalchemy.orm import Session
import logging

from ..core.database import get_db, ChatMessage
from ..core.security import get_current_user_optional
from ..services.ai_service import AIService

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize AI service
ai_service = AIService()

# Pydantic models
class ChatRequest(BaseModel):
    message: str
    sessionId: Optional[str] = None
    context: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    sessionId: str
    timestamp: str

class ChatHistory(BaseModel):
    messages: List[dict]
    sessionId: str

@router.post("/send-message", response_model=ChatResponse)
async def send_message(
    request: ChatRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Send message to AI assistant"""
    try:
        logger.info(f"Processing chat message: {request.message[:50]}...")
        
        # Validate message
        if not request.message.strip():
            raise HTTPException(status_code=400, detail="Message cannot be empty")
        
        # Generate AI response
        response = await ai_service.generate_response(
            message=request.message,
            session_id=request.sessionId,
            context=request.context
        )
        
        # Save to database
        chat_message = ChatMessage(
            session_id=response["sessionId"],
            message=request.message,
            response=response["response"],
            user_id=current_user.id if current_user else None
        )
        
        db.add(chat_message)
        db.commit()
        
        logger.info(f"Chat response generated for session: {response['sessionId']}")
        
        return ChatResponse(
            response=response["response"],
            sessionId=response["sessionId"],
            timestamp=response["timestamp"]
        )
        
    except Exception as e:
        logger.error(f"Chat message processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history/{session_id}", response_model=ChatHistory)
async def get_chat_history(
    session_id: str,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Get chat history for a session"""
    try:
        messages = db.query(ChatMessage)\
            .filter(ChatMessage.session_id == session_id)\
            .order_by(ChatMessage.created_at.desc())\
            .limit(limit)\
            .all()
        
        formatted_messages = []
        for msg in reversed(messages):  # Reverse to get chronological order
            formatted_messages.extend([
                {
                    "type": "user",
                    "content": msg.message,
                    "timestamp": msg.created_at.isoformat()
                },
                {
                    "type": "assistant",
                    "content": msg.response,
                    "timestamp": msg.created_at.isoformat()
                }
            ])
        
        return ChatHistory(
            messages=formatted_messages,
            sessionId=session_id
        )
        
    except Exception as e:
        logger.error(f"Get chat history failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/history/{session_id}")
async def clear_chat_history(
    session_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Clear chat history for a session"""
    try:
        deleted_count = db.query(ChatMessage)\
            .filter(ChatMessage.session_id == session_id)\
            .delete()
        
        db.commit()
        
        return {
            "message": f"Cleared {deleted_count} messages from session {session_id}",
            "deletedCount": deleted_count
        }
        
    except Exception as e:
        logger.error(f"Clear chat history failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions")
async def get_chat_sessions(
    limit: int = 20,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_optional)
):
    """Get list of chat sessions"""
    try:
        # Get unique session IDs with latest message
        sessions = db.query(ChatMessage.session_id, 
                          ChatMessage.created_at,
                          ChatMessage.message)\
            .order_by(ChatMessage.created_at.desc())\
            .limit(limit * 5)\
            .all()  # Get more to filter unique sessions
        
        # Group by session and get latest
        unique_sessions = {}
        for session_id, created_at, message in sessions:
            if session_id not in unique_sessions:
                unique_sessions[session_id] = {
                    "sessionId": session_id,
                    "lastMessage": message[:100] + "..." if len(message) > 100 else message,
                    "lastActivity": created_at.isoformat()
                }
        
        # Return limited number of unique sessions
        session_list = list(unique_sessions.values())[:limit]
        
        return {
            "sessions": session_list,
            "total": len(session_list)
        }
        
    except Exception as e:
        logger.error(f"Get chat sessions failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyze-sentiment")
async def analyze_sentiment(
    text: str,
    db: Session = Depends(get_db)
):
    """Analyze sentiment of text"""
    try:
        if not text.strip():
            raise HTTPException(status_code=400, detail="Text cannot be empty")
        
        sentiment = await ai_service.analyze_sentiment(text)
        
        return {
            "text": text,
            "sentiment": sentiment["sentiment"],
            "confidence": sentiment["confidence"],
            "scores": sentiment["scores"]
        }
        
    except Exception as e:
        logger.error(f"Sentiment analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/summarize")
async def summarize_text(
    text: str,
    max_length: int = 150,
    db: Session = Depends(get_db)
):
    """Summarize text"""
    try:
        if not text.strip():
            raise HTTPException(status_code=400, detail="Text cannot be empty")
        
        summary = await ai_service.summarize_text(text, max_length)
        
        return {
            "originalText": text,
            "summary": summary["summary"],
            "originalLength": len(text),
            "summaryLength": len(summary["summary"]),
            "compressionRatio": len(summary["summary"]) / len(text)
        }
        
    except Exception as e:
        logger.error(f"Text summarization failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/generate-content")
async def generate_content(
    prompt: str,
    content_type: str = "general",
    max_tokens: int = 500,
    db: Session = Depends(get_db)
):
    """Generate content based on prompt"""
    try:
        if not prompt.strip():
            raise HTTPException(status_code=400, detail="Prompt cannot be empty")
        
        content = await ai_service.generate_content(
            prompt=prompt,
            content_type=content_type,
            max_tokens=max_tokens
        )
        
        return {
            "prompt": prompt,
            "contentType": content_type,
            "generatedContent": content["content"],
            "tokensUsed": content.get("tokensUsed", 0)
        }
        
    except Exception as e:
        logger.error(f"Content generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
