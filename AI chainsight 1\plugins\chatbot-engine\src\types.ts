export interface ChatMessage {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    intent?: string;
    entities?: Entity[];
    confidence?: number;
    processing_time?: number;
    model_used?: string;
  };
}

export interface Conversation {
  id: string;
  user_id: string;
  title: string;
  messages: ChatMessage[];
  context: ChatContext;
  created_at: Date;
  updated_at: Date;
  is_active: boolean;
  metadata?: Record<string, any>;
}

export interface ChatContext {
  user_profile: UserProfile;
  session_data: Record<string, any>;
  conversation_history: string[];
  current_topic?: string;
  user_preferences: UserPreferences;
  market_context?: MarketContext;
}

export interface UserProfile {
  id: string;
  name?: string;
  email?: string;
  preferences: UserPreferences;
  interaction_history: InteractionSummary[];
  personality_insights: PersonalityInsights;
}

export interface UserPreferences {
  language: string;
  communication_style: 'formal' | 'casual' | 'technical' | 'friendly';
  response_length: 'short' | 'medium' | 'detailed';
  topics_of_interest: string[];
  notification_settings: NotificationSettings;
}

export interface NotificationSettings {
  email_alerts: boolean;
  push_notifications: boolean;
  market_updates: boolean;
  news_alerts: boolean;
}

export interface InteractionSummary {
  date: Date;
  topic: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  satisfaction_score?: number;
  key_points: string[];
}

export interface PersonalityInsights {
  communication_style: string;
  expertise_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  risk_tolerance: 'conservative' | 'moderate' | 'aggressive';
  decision_making_style: 'analytical' | 'intuitive' | 'collaborative';
  preferred_information_depth: 'summary' | 'detailed' | 'comprehensive';
}

export interface Intent {
  name: string;
  confidence: number;
  parameters: Record<string, any>;
  entities: Entity[];
}

export interface Entity {
  type: string;
  value: string;
  confidence: number;
  start_index?: number;
  end_index?: number;
  metadata?: Record<string, any>;
}

export interface Response {
  content: string;
  type: 'text' | 'rich' | 'action' | 'suggestion';
  confidence: number;
  suggestions?: string[];
  actions?: Action[];
  rich_content?: RichContent;
  metadata?: {
    sources?: string[];
    reasoning?: string;
    alternatives?: string[];
  };
}

export interface Action {
  type: string;
  label: string;
  payload: Record<string, any>;
  confirmation_required?: boolean;
}

export interface RichContent {
  type: 'chart' | 'table' | 'card' | 'carousel' | 'image';
  data: any;
  title?: string;
  description?: string;
}

export interface Personality {
  name: string;
  traits: PersonalityTrait[];
  communication_style: CommunicationStyle;
  expertise_areas: string[];
  response_patterns: ResponsePattern[];
}

export interface PersonalityTrait {
  name: string;
  value: number; // 0-1 scale
  description: string;
}

export interface CommunicationStyle {
  formality: number; // 0-1 scale
  enthusiasm: number;
  technical_depth: number;
  empathy: number;
  humor: number;
}

export interface ResponsePattern {
  trigger: string;
  template: string;
  variables: string[];
  conditions?: Record<string, any>;
}

export interface KnowledgeEntry {
  id: string;
  topic: string;
  content: string;
  type: 'fact' | 'procedure' | 'concept' | 'example';
  tags: string[];
  confidence: number;
  source: string;
  last_updated: Date;
  usage_count: number;
}

export interface Memory {
  id: string;
  type: 'short_term' | 'long_term' | 'episodic' | 'semantic';
  content: string;
  context: string;
  importance: number; // 0-1 scale
  created_at: Date;
  last_accessed: Date;
  access_count: number;
  decay_rate: number;
}

export interface ChatbotConfig {
  name: string;
  personality: Personality;
  openai_config: OpenAIConfig;
  response_config: ResponseConfig;
  memory_config: MemoryConfig;
  knowledge_config: KnowledgeConfig;
}

export interface OpenAIConfig {
  api_key: string;
  model: string;
  temperature: number;
  max_tokens: number;
  top_p: number;
  frequency_penalty: number;
  presence_penalty: number;
}

export interface ResponseConfig {
  max_response_time: number; // milliseconds
  fallback_responses: string[];
  confidence_threshold: number;
  enable_suggestions: boolean;
  max_suggestions: number;
}

export interface MemoryConfig {
  max_short_term_memories: number;
  max_long_term_memories: number;
  decay_enabled: boolean;
  consolidation_threshold: number;
}

export interface KnowledgeConfig {
  auto_update: boolean;
  confidence_threshold: number;
  max_entries_per_topic: number;
  enable_learning: boolean;
}

export interface MarketContext {
  current_prices: Record<string, number>;
  market_sentiment: 'bullish' | 'bearish' | 'neutral';
  trending_topics: string[];
  recent_news: NewsItem[];
  user_portfolio?: PortfolioSummary;
}

export interface NewsItem {
  title: string;
  summary: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  relevance_score: number;
  published_at: Date;
  source: string;
}

export interface PortfolioSummary {
  total_value: number;
  daily_change: number;
  top_holdings: Array<{
    symbol: string;
    value: number;
    change: number;
  }>;
}

export interface ConversationAnalytics {
  total_conversations: number;
  avg_conversation_length: number;
  most_common_intents: Array<{ intent: string; count: number }>;
  user_satisfaction: number;
  response_accuracy: number;
  avg_response_time: number;
}

export interface LearningData {
  user_feedback: UserFeedback[];
  conversation_outcomes: ConversationOutcome[];
  knowledge_gaps: KnowledgeGap[];
  improvement_suggestions: string[];
}

export interface UserFeedback {
  message_id: string;
  rating: number; // 1-5 scale
  feedback_text?: string;
  feedback_type: 'helpful' | 'not_helpful' | 'incorrect' | 'incomplete';
  timestamp: Date;
}

export interface ConversationOutcome {
  conversation_id: string;
  goal_achieved: boolean;
  user_satisfaction: number;
  resolution_time: number;
  follow_up_required: boolean;
}

export interface KnowledgeGap {
  topic: string;
  frequency: number;
  user_questions: string[];
  suggested_content: string;
}
